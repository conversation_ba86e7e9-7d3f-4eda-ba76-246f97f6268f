{"version": 3, "file": "index.js", "sources": ["../src/httpHeaders.ts", "../src/util/base64.ts", "../src/util/constants.ts", "../src/util/serializer.common.ts", "../src/util/utils.ts", "../src/serializer.ts", "../src/webResource.ts", "../src/util/inspect.ts", "../src/url.ts", "../src/util/sanitizer.ts", "../src/restError.ts", "../src/log.ts", "../src/fetchHttpClient.ts", "../src/proxyAgent.ts", "../src/nodeFetchHttpClient.ts", "../src/httpPipelineLogLevel.ts", "../src/operationOptions.ts", "../src/policies/requestPolicy.ts", "../src/policies/logPolicy.ts", "../src/operationParameter.ts", "../src/operationSpec.ts", "../src/util/xml.ts", "../src/policies/deserializationPolicy.ts", "../src/util/exponentialBackoffStrategy.ts", "../src/util/typeguards.ts", "../src/util/delay.ts", "../src/policies/exponentialRetryPolicy.ts", "../src/policies/generateClientRequestIdPolicy.ts", "../src/policies/msRestUserAgentPolicy.ts", "../src/policies/userAgentPolicy.ts", "../src/policies/redirectPolicy.ts", "../src/policies/rpRegistrationPolicy.ts", "../src/policies/bearerTokenAuthenticationPolicy.ts", "../src/policies/systemErrorRetryPolicy.ts", "../src/queryCollectionFormat.ts", "../src/policies/proxyPolicy.ts", "../src/util/throttlingRetryStrategy.ts", "../src/policies/throttlingRetryPolicy.ts", "../src/policies/signingPolicy.ts", "../src/policies/keepAlivePolicy.ts", "../src/policies/tracingPolicy.ts", "../src/policies/disableResponseDecompressionPolicy.ts", "../src/policies/ndJsonPolicy.ts", "../src/httpClientCache.ts", "../src/serviceClient.ts", "../src/createSpanLegacy.ts", "../src/credentials/accessTokenCache.ts", "../src/credentials/accessTokenRefresher.ts", "../src/credentials/basicAuthenticationCredentials.ts", "../src/credentials/apiKeyCredentials.ts", "../src/credentials/topicCredentials.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * A collection of HttpHeaders that can be sent with a HTTP request.\n */\nfunction getHeaderKey(headerName: string): string {\n  return headerName.toLowerCase();\n}\n\n/**\n * An individual header within a HttpHeaders collection.\n */\nexport interface HttpHeader {\n  /**\n   * The name of the header.\n   */\n  name: string;\n\n  /**\n   * The value of the header.\n   */\n  value: string;\n}\n\n/**\n * A HttpHeaders collection represented as a simple JSON object.\n */\nexport type RawHttpHeaders = { [headerName: string]: string };\n\n/**\n * A collection of HTTP header key/value pairs.\n */\nexport interface HttpHeadersLike {\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param headerName - The name of the header to set. This value is case-insensitive.\n   * @param headerValue - The value of the header to set.\n   */\n  set(headerName: string, headerValue: string | number): void;\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param headerName - The name of the header.\n   */\n  get(headerName: string): string | undefined;\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   */\n  contains(headerName: string): boolean;\n  /**\n   * Remove the header with the provided headerName. Return whether or not the header existed and\n   * was removed.\n   * @param headerName - The name of the header to remove.\n   */\n  remove(headerName: string): boolean;\n  /**\n   * Get the headers that are contained this collection as an object.\n   */\n  rawHeaders(): RawHttpHeaders;\n  /**\n   * Get the headers that are contained in this collection as an array.\n   */\n  headersArray(): HttpHeader[];\n  /**\n   * Get the header names that are contained in this collection.\n   */\n  headerNames(): string[];\n  /**\n   * Get the header values that are contained in this collection.\n   */\n  headerValues(): string[];\n  /**\n   * Create a deep clone/copy of this HttpHeaders collection.\n   */\n  clone(): HttpHeadersLike;\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   * The result is the same as `rawHeaders()`.\n   */\n  toJson(): RawHttpHeaders;\n}\n\nexport function isHttpHeadersLike(object?: unknown): object is HttpHeadersLike {\n  if (object && typeof object === \"object\") {\n    const castObject = object as {\n      rawHeaders: unknown;\n      clone: unknown;\n      get: unknown;\n      set: unknown;\n      contains: unknown;\n      remove: unknown;\n      headersArray: unknown;\n      headerValues: unknown;\n      headerNames: unknown;\n      toJson: unknown;\n    };\n    if (\n      typeof castObject.rawHeaders === \"function\" &&\n      typeof castObject.clone === \"function\" &&\n      typeof castObject.get === \"function\" &&\n      typeof castObject.set === \"function\" &&\n      typeof castObject.contains === \"function\" &&\n      typeof castObject.remove === \"function\" &&\n      typeof castObject.headersArray === \"function\" &&\n      typeof castObject.headerValues === \"function\" &&\n      typeof castObject.headerNames === \"function\" &&\n      typeof castObject.toJson === \"function\"\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * A collection of HTTP header key/value pairs.\n */\nexport class HttpHeaders implements HttpHeadersLike {\n  private readonly _headersMap: { [headerKey: string]: HttpHeader };\n\n  constructor(rawHeaders?: RawHttpHeaders) {\n    this._headersMap = {};\n    if (rawHeaders) {\n      for (const headerName in rawHeaders) {\n        this.set(headerName, rawHeaders[headerName]);\n      }\n    }\n  }\n\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param headerName - The name of the header to set. This value is case-insensitive.\n   * @param headerValue - The value of the header to set.\n   */\n  public set(headerName: string, headerValue: string | number): void {\n    this._headersMap[getHeaderKey(headerName)] = {\n      name: headerName,\n      value: headerValue.toString()\n    };\n  }\n\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param headerName - The name of the header.\n   */\n  public get(headerName: string): string | undefined {\n    const header: HttpHeader = this._headersMap[getHeaderKey(headerName)];\n    return !header ? undefined : header.value;\n  }\n\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   */\n  public contains(headerName: string): boolean {\n    return !!this._headersMap[getHeaderKey(headerName)];\n  }\n\n  /**\n   * Remove the header with the provided headerName. Return whether or not the header existed and\n   * was removed.\n   * @param headerName - The name of the header to remove.\n   */\n  public remove(headerName: string): boolean {\n    const result: boolean = this.contains(headerName);\n    delete this._headersMap[getHeaderKey(headerName)];\n    return result;\n  }\n\n  /**\n   * Get the headers that are contained this collection as an object.\n   */\n  public rawHeaders(): RawHttpHeaders {\n    const result: RawHttpHeaders = {};\n    for (const headerKey in this._headersMap) {\n      const header: HttpHeader = this._headersMap[headerKey];\n      result[header.name.toLowerCase()] = header.value;\n    }\n    return result;\n  }\n\n  /**\n   * Get the headers that are contained in this collection as an array.\n   */\n  public headersArray(): HttpHeader[] {\n    const headers: HttpHeader[] = [];\n    for (const headerKey in this._headersMap) {\n      headers.push(this._headersMap[headerKey]);\n    }\n    return headers;\n  }\n\n  /**\n   * Get the header names that are contained in this collection.\n   */\n  public headerNames(): string[] {\n    const headerNames: string[] = [];\n    const headers: HttpHeader[] = this.headersArray();\n    for (let i = 0; i < headers.length; ++i) {\n      headerNames.push(headers[i].name);\n    }\n    return headerNames;\n  }\n\n  /**\n   * Get the header values that are contained in this collection.\n   */\n  public headerValues(): string[] {\n    const headerValues: string[] = [];\n    const headers: HttpHeader[] = this.headersArray();\n    for (let i = 0; i < headers.length; ++i) {\n      headerValues.push(headers[i].value);\n    }\n    return headerValues;\n  }\n\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   */\n  public toJson(): RawHttpHeaders {\n    return this.rawHeaders();\n  }\n\n  /**\n   * Get the string representation of this HTTP header collection.\n   */\n  public toString(): string {\n    return JSON.stringify(this.toJson());\n  }\n\n  /**\n   * Create a deep clone/copy of this HttpHeaders collection.\n   */\n  public clone(): HttpHeaders {\n    const resultPreservingCasing: RawHttpHeaders = {};\n    for (const headerKey in this._headersMap) {\n      const header: HttpHeader = this._headersMap[headerKey];\n      resultPreservingCasing[header.name] = header.value;\n    }\n    return new HttpHeaders(resultPreservingCasing);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Encodes a string in base64 format.\n * @param value - The string to encode\n */\nexport function encodeString(value: string): string {\n  return Buffer.from(value).toString(\"base64\");\n}\n\n/**\n * Encodes a byte array in base64 format.\n * @param value - The Uint8Aray to encode\n */\nexport function encodeByteArray(value: Uint8Array): string {\n  // Buffer.from accepts <ArrayBuffer> | <SharedArrayBuffer>-- the TypeScript definition is off here\n  // https://nodejs.org/api/buffer.html#buffer_class_method_buffer_from_arraybuffer_byteoffset_length\n  const bufferValue = value instanceof Buffer ? value : Buffer.from(value.buffer as ArrayBuffer);\n  return bufferValue.toString(\"base64\");\n}\n\n/**\n * Decodes a base64 string into a byte array.\n * @param value - The base64 string to decode\n */\nexport function decodeString(value: string): Uint8Array {\n  return Buffer.from(value, \"base64\");\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport const Constants = {\n  /**\n   * The core-http version\n   */\n  coreHttpVersion: \"2.2.2\",\n\n  /**\n   * Specifies HTTP.\n   */\n  HTTP: \"http:\",\n\n  /**\n   * Specifies HTTPS.\n   */\n  HTTPS: \"https:\",\n\n  /**\n   * Specifies HTTP Proxy.\n   */\n  HTTP_PROXY: \"HTTP_PROXY\",\n\n  /**\n   * Specifies HTTPS Proxy.\n   */\n  HTTPS_PROXY: \"HTTPS_PROXY\",\n\n  /**\n   * Specifies NO Proxy.\n   */\n  NO_PROXY: \"NO_PROXY\",\n\n  /**\n   * Specifies ALL Proxy.\n   */\n  ALL_PROXY: \"ALL_PROXY\",\n\n  HttpConstants: {\n    /**\n     * Http Verbs\n     */\n    HttpVerbs: {\n      PUT: \"PUT\",\n      GET: \"GET\",\n      DELETE: \"DELETE\",\n      POST: \"POST\",\n      MERGE: \"MERGE\",\n      HEAD: \"HEAD\",\n      PATCH: \"PATCH\"\n    },\n\n    StatusCodes: {\n      TooManyRequests: 429,\n      ServiceUnavailable: 503\n    }\n  },\n\n  /**\n   * Defines constants for use with HTTP headers.\n   */\n  HeaderConstants: {\n    /**\n     * The Authorization header.\n     */\n    AUTHORIZATION: \"authorization\",\n\n    AUTHORIZATION_SCHEME: \"Bearer\",\n\n    /**\n     * The Retry-After response-header field can be used with a 503 (Service\n     * Unavailable) or 349 (Too Many Requests) responses to indicate how long\n     * the service is expected to be unavailable to the requesting client.\n     */\n    RETRY_AFTER: \"Retry-After\",\n\n    /**\n     * The UserAgent header.\n     */\n    USER_AGENT: \"User-Agent\"\n  }\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Default key used to access the XML attributes.\n */\nexport const XML_ATTRKEY = \"$\";\n/**\n * Default key used to access the XML value content.\n */\nexport const XML_CHARKEY = \"_\";\n\n/**\n * Options to govern behavior of xml parser and builder.\n */\nexport interface SerializerOptions {\n  /**\n   * indicates the name of the root element in the resulting XML when building XML.\n   */\n  rootName?: string;\n  /**\n   * indicates whether the root element is to be included or not in the output when parsing XML.\n   */\n  includeRoot?: boolean;\n  /**\n   * key used to access the XML value content when parsing XML.\n   */\n  xmlCharKey?: string;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { v4 as uuidv4 } from \"uuid\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { RestError } from \"../restError\";\nimport { WebResourceLike } from \"../webResource\";\nimport { Constants } from \"./constants\";\nimport { XML_ATTRKEY } from \"./serializer.common\";\n\nconst validUuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i;\n\n/**\n * A constant that indicates whether the environment is node.js or browser based.\n */\nexport const isNode =\n  typeof process !== \"undefined\" &&\n  !!process.version &&\n  !!process.versions &&\n  !!process.versions.node;\n\n/**\n * Checks if a parsed URL is HTTPS\n *\n * @param urlToCheck - The url to check\n * @returns True if the URL is HTTPS; false otherwise.\n */\nexport function urlIsHTTPS(urlToCheck: { protocol: string }): boolean {\n  return urlToCheck.protocol.toLowerCase() === Constants.HTTPS;\n}\n\n/**\n * Encodes an URI.\n *\n * @param uri - The URI to be encoded.\n * @returns The encoded URI.\n */\nexport function encodeUri(uri: string): string {\n  return encodeURIComponent(uri)\n    .replace(/!/g, \"%21\")\n    .replace(/\"/g, \"%27\")\n    .replace(/\\(/g, \"%28\")\n    .replace(/\\)/g, \"%29\")\n    .replace(/\\*/g, \"%2A\");\n}\n\n/**\n * Returns a stripped version of the Http Response which only contains body,\n * headers and the status.\n *\n * @param response - The Http Response\n * @returns The stripped version of Http Response.\n */\nexport function stripResponse(response: HttpOperationResponse): any {\n  const strippedResponse: any = {};\n  strippedResponse.body = response.bodyAsText;\n  strippedResponse.headers = response.headers;\n  strippedResponse.status = response.status;\n  return strippedResponse;\n}\n\n/**\n * Returns a stripped version of the Http Request that does not contain the\n * Authorization header.\n *\n * @param request - The Http Request object\n * @returns The stripped version of Http Request.\n */\nexport function stripRequest(request: WebResourceLike): WebResourceLike {\n  const strippedRequest = request.clone();\n  if (strippedRequest.headers) {\n    strippedRequest.headers.remove(\"authorization\");\n  }\n  return strippedRequest;\n}\n\n/**\n * Validates the given uuid as a string\n *\n * @param uuid - The uuid as a string that needs to be validated\n * @returns True if the uuid is valid; false otherwise.\n */\nexport function isValidUuid(uuid: string): boolean {\n  return validUuidRegex.test(uuid);\n}\n\n/**\n * Generated UUID\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function generateUuid(): string {\n  return uuidv4();\n}\n\n/**\n * Executes an array of promises sequentially. Inspiration of this method is here:\n * https://pouchdb.com/2015/05/18/we-have-a-problem-with-promises.html. An awesome blog on promises!\n *\n * @param promiseFactories - An array of promise factories(A function that return a promise)\n * @param kickstart - Input to the first promise that is used to kickstart the promise chain.\n * If not provided then the promise chain starts with undefined.\n * @returns A chain of resolved or rejected promises\n */\nexport function executePromisesSequentially(\n  promiseFactories: Array<any>,\n  kickstart: unknown\n): Promise<any> {\n  let result = Promise.resolve(kickstart);\n  promiseFactories.forEach((promiseFactory) => {\n    result = result.then(promiseFactory);\n  });\n  return result;\n}\n\n/**\n * Service callback that is returned for REST requests initiated by the service client.\n */\nexport interface ServiceCallback<TResult> {\n  /**\n   * A method that will be invoked as a callback to a service function.\n   * @param err - The error occurred if any, while executing the request; otherwise null.\n   * @param result - The deserialized response body if an error did not occur.\n   * @param request - The raw/actual request sent to the server if an error did not occur.\n   * @param response - The raw/actual response from the server if an error did not occur.\n   */\n  (\n    err: Error | RestError | null,\n    result?: TResult,\n    request?: WebResourceLike,\n    response?: HttpOperationResponse\n  ): void;\n}\n\n/**\n * Converts a Promise to a callback.\n * @param promise - The Promise to be converted to a callback\n * @returns A function that takes the callback `(cb: Function) => void`\n * @deprecated generated code should instead depend on responseToBody\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function promiseToCallback(promise: Promise<any>): (cb: Function) => void {\n  if (typeof promise.then !== \"function\") {\n    throw new Error(\"The provided input is not a Promise.\");\n  }\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  return (cb: Function): void => {\n    promise\n      .then((data: any) => {\n        // eslint-disable-next-line promise/no-callback-in-promise\n        return cb(undefined, data);\n      })\n      .catch((err: Error) => {\n        // eslint-disable-next-line promise/no-callback-in-promise\n        cb(err);\n      });\n  };\n}\n\n/**\n * Converts a Promise to a service callback.\n * @param promise - The Promise of HttpOperationResponse to be converted to a service callback\n * @returns A function that takes the service callback (cb: ServiceCallback<T>): void\n */\nexport function promiseToServiceCallback<T>(\n  promise: Promise<HttpOperationResponse>\n): (cb: ServiceCallback<T>) => void {\n  if (typeof promise.then !== \"function\") {\n    throw new Error(\"The provided input is not a Promise.\");\n  }\n  return (cb: ServiceCallback<T>): void => {\n    promise\n      .then((data: HttpOperationResponse) => {\n        return process.nextTick(cb, undefined, data.parsedBody as T, data.request, data);\n      })\n      .catch((err: Error) => {\n        process.nextTick(cb, err);\n      });\n  };\n}\n\nexport function prepareXMLRootList(\n  obj: unknown,\n  elementName: string,\n  xmlNamespaceKey?: string,\n  xmlNamespace?: string\n): { [s: string]: any } {\n  if (!Array.isArray(obj)) {\n    obj = [obj];\n  }\n\n  if (!xmlNamespaceKey || !xmlNamespace) {\n    return { [elementName]: obj };\n  }\n\n  const result = { [elementName]: obj };\n  result[XML_ATTRKEY] = { [xmlNamespaceKey]: xmlNamespace };\n  return result;\n}\n\n/**\n * Applies the properties on the prototype of sourceCtors to the prototype of targetCtor\n * @param targetCtor - The target object on which the properties need to be applied.\n * @param sourceCtors - An array of source objects from which the properties need to be taken.\n */\nexport function applyMixins(targetCtorParam: unknown, sourceCtors: any[]): void {\n  const castTargetCtorParam = targetCtorParam as {\n    prototype: Record<string, unknown>;\n  };\n  sourceCtors.forEach((sourceCtor) => {\n    Object.getOwnPropertyNames(sourceCtor.prototype).forEach((name) => {\n      castTargetCtorParam.prototype[name] = sourceCtor.prototype[name];\n    });\n  });\n}\n\nconst validateISODuration = /^(-|\\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;\n\n/**\n * Indicates whether the given string is in ISO 8601 format.\n * @param value - The value to be validated for ISO 8601 duration format.\n * @returns `true` if valid, `false` otherwise.\n */\nexport function isDuration(value: string): boolean {\n  return validateISODuration.test(value);\n}\n\n/**\n * Replace all of the instances of searchValue in value with the provided replaceValue.\n * @param value - The value to search and replace in.\n * @param searchValue - The value to search for in the value argument.\n * @param replaceValue - The value to replace searchValue with in the value argument.\n * @returns The value where each instance of searchValue was replaced with replacedValue.\n */\nexport function replaceAll(\n  value: string | undefined,\n  searchValue: string,\n  replaceValue: string\n): string | undefined {\n  return !value || !searchValue ? value : value.split(searchValue).join(replaceValue || \"\");\n}\n\n/**\n * Determines whether the given entity is a basic/primitive type\n * (string, number, boolean, null, undefined).\n * @param value - Any entity\n * @returns true is it is primitive type, false otherwise.\n */\nexport function isPrimitiveType(value: unknown): boolean {\n  return (typeof value !== \"object\" && typeof value !== \"function\") || value === null;\n}\n\nexport function getEnvironmentValue(name: string): string | undefined {\n  if (process.env[name]) {\n    return process.env[name];\n  } else if (process.env[name.toLowerCase()]) {\n    return process.env[name.toLowerCase()];\n  }\n  return undefined;\n}\n\n/**\n * @internal\n */\nexport type UnknownObject = { [s: string]: unknown };\n\n/**\n * @internal\n * @returns true when input is an object type that is not null, Array, RegExp, or Date.\n */\nexport function isObject(input: unknown): input is UnknownObject {\n  return (\n    typeof input === \"object\" &&\n    input !== null &&\n    !Array.isArray(input) &&\n    !(input instanceof RegExp) &&\n    !(input instanceof Date)\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/* eslint-disable eqeqeq */\n\nimport * as base64 from \"./util/base64\";\nimport * as utils from \"./util/utils\";\nimport { XML_ATTRKEY, XML_CHARKEY, SerializerOptions } from \"./util/serializer.common\";\n\nexport class Serializer {\n  constructor(\n    public readonly modelMappers: { [key: string]: any } = {},\n    public readonly isXML?: boolean\n  ) {}\n\n  validateConstraints(mapper: Mapper, value: unknown, objectName: string): void {\n    const failValidation = (\n      constraintName: keyof MapperConstraints,\n      constraintValue: any\n    ): Error => {\n      throw new Error(\n        `\"${objectName}\" with value \"${value}\" should satisfy the constraint \"${constraintName}\": ${constraintValue}.`\n      );\n    };\n    if (mapper.constraints && value != undefined) {\n      const valueAsNumber = value as number;\n      const {\n        ExclusiveMaximum,\n        ExclusiveMinimum,\n        InclusiveMaximum,\n        InclusiveMinimum,\n        MaxItems,\n        <PERSON>Length,\n        <PERSON><PERSON>tems,\n        <PERSON><PERSON>eng<PERSON>,\n        MultipleOf,\n        Pattern,\n        UniqueItems\n      } = mapper.constraints;\n      if (ExclusiveMaximum != undefined && valueAsNumber >= ExclusiveMaximum) {\n        failValidation(\"ExclusiveMaximum\", ExclusiveMaximum);\n      }\n      if (ExclusiveMinimum != undefined && valueAsNumber <= ExclusiveMinimum) {\n        failValidation(\"ExclusiveMinimum\", ExclusiveMinimum);\n      }\n      if (InclusiveMaximum != undefined && valueAsNumber > InclusiveMaximum) {\n        failValidation(\"InclusiveMaximum\", InclusiveMaximum);\n      }\n      if (InclusiveMinimum != undefined && valueAsNumber < InclusiveMinimum) {\n        failValidation(\"InclusiveMinimum\", InclusiveMinimum);\n      }\n      const valueAsArray = value as any[];\n      if (MaxItems != undefined && valueAsArray.length > MaxItems) {\n        failValidation(\"MaxItems\", MaxItems);\n      }\n      if (MaxLength != undefined && valueAsArray.length > MaxLength) {\n        failValidation(\"MaxLength\", MaxLength);\n      }\n      if (MinItems != undefined && valueAsArray.length < MinItems) {\n        failValidation(\"MinItems\", MinItems);\n      }\n      if (MinLength != undefined && valueAsArray.length < MinLength) {\n        failValidation(\"MinLength\", MinLength);\n      }\n      if (MultipleOf != undefined && valueAsNumber % MultipleOf !== 0) {\n        failValidation(\"MultipleOf\", MultipleOf);\n      }\n      if (Pattern) {\n        const pattern: RegExp = typeof Pattern === \"string\" ? new RegExp(Pattern) : Pattern;\n        if (typeof value !== \"string\" || value.match(pattern) === null) {\n          failValidation(\"Pattern\", Pattern);\n        }\n      }\n      if (\n        UniqueItems &&\n        valueAsArray.some((item: any, i: number, ar: Array<any>) => ar.indexOf(item) !== i)\n      ) {\n        failValidation(\"UniqueItems\", UniqueItems);\n      }\n    }\n  }\n\n  /**\n   * Serialize the given object based on its metadata defined in the mapper\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object\n   * @param object - A valid Javascript object to be serialized\n   * @param objectName - Name of the serialized object\n   * @param options - additional options to deserialization\n   * @returns A valid serialized Javascript object\n   */\n  serialize(\n    mapper: Mapper,\n    object: unknown,\n    objectName?: string,\n    options: SerializerOptions = {}\n  ): any {\n    const updatedOptions: Required<SerializerOptions> = {\n      rootName: options.rootName ?? \"\",\n      includeRoot: options.includeRoot ?? false,\n      xmlCharKey: options.xmlCharKey ?? XML_CHARKEY\n    };\n    let payload: any = {};\n    const mapperType = mapper.type.name as string;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n    if (mapperType.match(/^Sequence$/i) !== null) {\n      payload = [];\n    }\n\n    if (mapper.isConstant) {\n      object = mapper.defaultValue;\n    }\n\n    // This table of allowed values should help explain\n    // the mapper.required and mapper.nullable properties.\n    // X means \"neither undefined or null are allowed\".\n    //           || required\n    //           || true      | false\n    //  nullable || ==========================\n    //      true || null      | undefined/null\n    //     false || X         | undefined\n    // undefined || X         | undefined/null\n\n    const { required, nullable } = mapper;\n\n    if (required && nullable && object === undefined) {\n      throw new Error(`${objectName} cannot be undefined.`);\n    }\n    if (required && !nullable && object == undefined) {\n      throw new Error(`${objectName} cannot be null or undefined.`);\n    }\n    if (!required && nullable === false && object === null) {\n      throw new Error(`${objectName} cannot be null.`);\n    }\n\n    if (object == undefined) {\n      payload = object;\n    } else {\n      // Validate Constraints if any\n      this.validateConstraints(mapper, object, objectName);\n      if (mapperType.match(/^any$/i) !== null) {\n        payload = object;\n      } else if (mapperType.match(/^(Number|String|Boolean|Object|Stream|Uuid)$/i) !== null) {\n        payload = serializeBasicTypes(mapperType, objectName, object);\n      } else if (mapperType.match(/^Enum$/i) !== null) {\n        const enumMapper: EnumMapper = mapper as EnumMapper;\n        payload = serializeEnumType(objectName, enumMapper.type.allowedValues, object);\n      } else if (\n        mapperType.match(/^(Date|DateTime|TimeSpan|DateTimeRfc1123|UnixTime)$/i) !== null\n      ) {\n        payload = serializeDateTypes(mapperType, object, objectName);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = serializeByteArrayType(objectName, object as Uint8Array);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = serializeBase64UrlType(objectName, object as Uint8Array);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = serializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = serializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Composite$/i) !== null) {\n        payload = serializeCompositeType(\n          this,\n          mapper as CompositeMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      }\n    }\n    return payload;\n  }\n\n  /**\n   * Deserialize the given object based on its metadata defined in the mapper\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object\n   * @param responseBody - A valid Javascript entity to be deserialized\n   * @param objectName - Name of the deserialized object\n   * @param options - Controls behavior of XML parser and builder.\n   * @returns A valid deserialized Javascript object\n   */\n  deserialize(\n    mapper: Mapper,\n    responseBody: unknown,\n    objectName: string,\n    options: SerializerOptions = {}\n  ): any {\n    const updatedOptions: Required<SerializerOptions> = {\n      rootName: options.rootName ?? \"\",\n      includeRoot: options.includeRoot ?? false,\n      xmlCharKey: options.xmlCharKey ?? XML_CHARKEY\n    };\n    if (responseBody == undefined) {\n      if (this.isXML && mapper.type.name === \"Sequence\" && !mapper.xmlIsWrapped) {\n        // Edge case for empty XML non-wrapped lists. xml2js can't distinguish\n        // between the list being empty versus being missing,\n        // so let's do the more user-friendly thing and return an empty list.\n        responseBody = [];\n      }\n      // specifically check for undefined as default value can be a falsey value `0, \"\", false, null`\n      if (mapper.defaultValue !== undefined) {\n        responseBody = mapper.defaultValue;\n      }\n      return responseBody;\n    }\n\n    let payload: any;\n    const mapperType = mapper.type.name;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n\n    if (mapperType.match(/^Composite$/i) !== null) {\n      payload = deserializeCompositeType(\n        this,\n        mapper as CompositeMapper,\n        responseBody,\n        objectName,\n        updatedOptions\n      );\n    } else {\n      if (this.isXML) {\n        const xmlCharKey = updatedOptions.xmlCharKey;\n        const castResponseBody = responseBody as Record<string, unknown>;\n        /**\n         * If the mapper specifies this as a non-composite type value but the responseBody contains\n         * both header (\"$\" i.e., XML_ATTRKEY) and body (\"#\" i.e., XML_CHARKEY) properties,\n         * then just reduce the responseBody value to the body (\"#\" i.e., XML_CHARKEY) property.\n         */\n        if (\n          castResponseBody[XML_ATTRKEY] != undefined &&\n          castResponseBody[xmlCharKey] != undefined\n        ) {\n          responseBody = castResponseBody[xmlCharKey];\n        }\n      }\n\n      if (mapperType.match(/^Number$/i) !== null) {\n        payload = parseFloat(responseBody as string);\n        if (isNaN(payload)) {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^Boolean$/i) !== null) {\n        if (responseBody === \"true\") {\n          payload = true;\n        } else if (responseBody === \"false\") {\n          payload = false;\n        } else {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^(String|Enum|Object|Stream|Uuid|TimeSpan|any)$/i) !== null) {\n        payload = responseBody;\n      } else if (mapperType.match(/^(Date|DateTime|DateTimeRfc1123)$/i) !== null) {\n        payload = new Date(responseBody as string);\n      } else if (mapperType.match(/^UnixTime$/i) !== null) {\n        payload = unixTimeToDate(responseBody as number);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = base64.decodeString(responseBody as string);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = base64UrlToByteArray(responseBody as string);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = deserializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          responseBody,\n          objectName,\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = deserializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          responseBody,\n          objectName,\n          updatedOptions\n        );\n      }\n    }\n\n    if (mapper.isConstant) {\n      payload = mapper.defaultValue;\n    }\n\n    return payload;\n  }\n}\n\nfunction trimEnd(str: string, ch: string): string {\n  let len = str.length;\n  while (len - 1 >= 0 && str[len - 1] === ch) {\n    --len;\n  }\n  return str.substr(0, len);\n}\n\nfunction bufferToBase64Url(buffer: any): string | undefined {\n  if (!buffer) {\n    return undefined;\n  }\n  if (!(buffer instanceof Uint8Array)) {\n    throw new Error(`Please provide an input of type Uint8Array for converting to Base64Url.`);\n  }\n  // Uint8Array to Base64.\n  const str = base64.encodeByteArray(buffer);\n  // Base64 to Base64Url.\n  return trimEnd(str, \"=\")\n    .replace(/\\+/g, \"-\")\n    .replace(/\\//g, \"_\");\n}\n\nfunction base64UrlToByteArray(str: string): Uint8Array | undefined {\n  if (!str) {\n    return undefined;\n  }\n  if (str && typeof str.valueOf() !== \"string\") {\n    throw new Error(\"Please provide an input of type string for converting to Uint8Array\");\n  }\n  // Base64Url to Base64.\n  str = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  // Base64 to Uint8Array.\n  return base64.decodeString(str);\n}\n\nfunction splitSerializeName(prop: string | undefined): string[] {\n  const classes: string[] = [];\n  let partialclass = \"\";\n  if (prop) {\n    const subwords = prop.split(\".\");\n\n    for (const item of subwords) {\n      if (item.charAt(item.length - 1) === \"\\\\\") {\n        partialclass += item.substr(0, item.length - 1) + \".\";\n      } else {\n        partialclass += item;\n        classes.push(partialclass);\n        partialclass = \"\";\n      }\n    }\n  }\n\n  return classes;\n}\n\nfunction dateToUnixTime(d: string | Date): number | undefined {\n  if (!d) {\n    return undefined;\n  }\n\n  if (typeof d.valueOf() === \"string\") {\n    d = new Date(d as string);\n  }\n  return Math.floor((d as Date).getTime() / 1000);\n}\n\nfunction unixTimeToDate(n: number): Date | undefined {\n  if (!n) {\n    return undefined;\n  }\n  return new Date(n * 1000);\n}\n\nfunction serializeBasicTypes(typeName: string, objectName: string, value: any): any {\n  if (value !== null && value !== undefined) {\n    if (typeName.match(/^Number$/i) !== null) {\n      if (typeof value !== \"number\") {\n        throw new Error(`${objectName} with value ${value} must be of type number.`);\n      }\n    } else if (typeName.match(/^String$/i) !== null) {\n      if (typeof value.valueOf() !== \"string\") {\n        throw new Error(`${objectName} with value \"${value}\" must be of type string.`);\n      }\n    } else if (typeName.match(/^Uuid$/i) !== null) {\n      if (!(typeof value.valueOf() === \"string\" && utils.isValidUuid(value))) {\n        throw new Error(\n          `${objectName} with value \"${value}\" must be of type string and a valid uuid.`\n        );\n      }\n    } else if (typeName.match(/^Boolean$/i) !== null) {\n      if (typeof value !== \"boolean\") {\n        throw new Error(`${objectName} with value ${value} must be of type boolean.`);\n      }\n    } else if (typeName.match(/^Stream$/i) !== null) {\n      const objectType = typeof value;\n      if (\n        objectType !== \"string\" &&\n        objectType !== \"function\" &&\n        !(value instanceof ArrayBuffer) &&\n        !ArrayBuffer.isView(value) &&\n        !((typeof Blob === \"function\" || typeof Blob === \"object\") && value instanceof Blob)\n      ) {\n        throw new Error(\n          `${objectName} must be a string, Blob, ArrayBuffer, ArrayBufferView, or a function returning NodeJS.ReadableStream.`\n        );\n      }\n    }\n  }\n\n  return value;\n}\n\nfunction serializeEnumType(objectName: string, allowedValues: Array<any>, value: any): any {\n  if (!allowedValues) {\n    throw new Error(\n      `Please provide a set of allowedValues to validate ${objectName} as an Enum Type.`\n    );\n  }\n  const isPresent = allowedValues.some((item) => {\n    if (typeof item.valueOf() === \"string\") {\n      return item.toLowerCase() === value.toLowerCase();\n    }\n    return item === value;\n  });\n  if (!isPresent) {\n    throw new Error(\n      `${value} is not a valid value for ${objectName}. The valid values are: ${JSON.stringify(\n        allowedValues\n      )}.`\n    );\n  }\n  return value;\n}\n\nfunction serializeByteArrayType(objectName: string, value: Uint8Array): string {\n  let returnValue: string = \"\";\n  if (value != undefined) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    returnValue = base64.encodeByteArray(value);\n  }\n  return returnValue;\n}\n\nfunction serializeBase64UrlType(objectName: string, value: Uint8Array): string {\n  let returnValue: string = \"\";\n  if (value != undefined) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    returnValue = bufferToBase64Url(value) || \"\";\n  }\n  return returnValue;\n}\n\nfunction serializeDateTypes(typeName: string, value: any, objectName: string): any {\n  if (value != undefined) {\n    if (typeName.match(/^Date$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value =\n        value instanceof Date\n          ? value.toISOString().substring(0, 10)\n          : new Date(value).toISOString().substring(0, 10);\n    } else if (typeName.match(/^DateTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value = value instanceof Date ? value.toISOString() : new Date(value).toISOString();\n    } else if (typeName.match(/^DateTimeRfc1123$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in RFC-1123 format.`);\n      }\n      value = value instanceof Date ? value.toUTCString() : new Date(value).toUTCString();\n    } else if (typeName.match(/^UnixTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(\n          `${objectName} must be an instanceof Date or a string in RFC-1123/ISO8601 format ` +\n            `for it to be serialized in UnixTime/Epoch format.`\n        );\n      }\n      value = dateToUnixTime(value);\n    } else if (typeName.match(/^TimeSpan$/i) !== null) {\n      if (!utils.isDuration(value)) {\n        throw new Error(\n          `${objectName} must be a string in ISO 8601 format. Instead was \"${value}\".`\n        );\n      }\n    }\n  }\n  return value;\n}\n\nfunction serializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): any[] {\n  if (!Array.isArray(object)) {\n    throw new Error(`${objectName} must be of type Array.`);\n  }\n  const elementType = mapper.type.element;\n  if (!elementType || typeof elementType !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`\n    );\n  }\n  const tempArray = [];\n  for (let i = 0; i < object.length; i++) {\n    const serializedValue = serializer.serialize(elementType, object[i], objectName, options);\n\n    if (isXml && elementType.xmlNamespace) {\n      const xmlnsKey = elementType.xmlNamespacePrefix\n        ? `xmlns:${elementType.xmlNamespacePrefix}`\n        : \"xmlns\";\n      if (elementType.type.name === \"Composite\") {\n        tempArray[i] = { ...serializedValue };\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      } else {\n        tempArray[i] = {};\n        tempArray[i][options.xmlCharKey] = serializedValue;\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      }\n    } else {\n      tempArray[i] = serializedValue;\n    }\n  }\n  return tempArray;\n}\n\nfunction serializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): { [key: string]: any } {\n  if (typeof object !== \"object\") {\n    throw new Error(`${objectName} must be of type object.`);\n  }\n  const valueType = mapper.type.value;\n  if (!valueType || typeof valueType !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`\n    );\n  }\n  const tempDictionary: { [key: string]: any } = {};\n  for (const key of Object.keys(object)) {\n    const serializedValue = serializer.serialize(valueType, object[key], objectName, options);\n    // If the element needs an XML namespace we need to add it within the $ property\n    tempDictionary[key] = getXmlObjectValue(valueType, serializedValue, isXml, options);\n  }\n\n  // Add the namespace to the root element if needed\n  if (isXml && mapper.xmlNamespace) {\n    const xmlnsKey = mapper.xmlNamespacePrefix ? `xmlns:${mapper.xmlNamespacePrefix}` : \"xmlns\";\n\n    const result = tempDictionary;\n    result[XML_ATTRKEY] = { [xmlnsKey]: mapper.xmlNamespace };\n    return result;\n  }\n\n  return tempDictionary;\n}\n\n/**\n * Resolves the additionalProperties property from a referenced mapper\n * @param serializer - The serializer containing the entire set of mappers\n * @param mapper - The composite mapper to resolve\n * @param objectName - Name of the object being serialized\n */\nfunction resolveAdditionalProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): SequenceMapper | BaseMapper | CompositeMapper | DictionaryMapper | EnumMapper | undefined {\n  const additionalProperties = mapper.type.additionalProperties;\n\n  if (!additionalProperties && mapper.type.className) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    return modelMapper?.type.additionalProperties;\n  }\n\n  return additionalProperties;\n}\n\n/**\n * Finds the mapper referenced by className\n * @param serializer - The serializer containing the entire set of mappers\n * @param mapper - The composite mapper to resolve\n * @param objectName - Name of the object being serialized\n */\nfunction resolveReferencedMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): CompositeMapper | undefined {\n  const className = mapper.type.className;\n  if (!className) {\n    throw new Error(\n      `Class name for model \"${objectName}\" is not provided in the mapper \"${JSON.stringify(\n        mapper,\n        undefined,\n        2\n      )}\".`\n    );\n  }\n\n  return serializer.modelMappers[className];\n}\n\n/**\n * Resolves a composite mapper's modelProperties.\n * @param serializer - The serializer containing the entire set of mappers\n * @param mapper - The composite mapper to resolve\n */\nfunction resolveModelProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): { [propertyName: string]: Mapper } {\n  let modelProps = mapper.type.modelProperties;\n  if (!modelProps) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    if (!modelMapper) {\n      throw new Error(`mapper() cannot be null or undefined for model \"${mapper.type.className}\".`);\n    }\n    modelProps = modelMapper?.type.modelProperties;\n    if (!modelProps) {\n      throw new Error(\n        `modelProperties cannot be null or undefined in the ` +\n          `mapper \"${JSON.stringify(modelMapper)}\" of type \"${\n            mapper.type.className\n          }\" for object \"${objectName}\".`\n      );\n    }\n  }\n\n  return modelProps;\n}\n\nfunction serializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): any {\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, object, \"clientName\");\n  }\n\n  if (object != undefined) {\n    const payload: any = {};\n    const modelProps = resolveModelProperties(serializer, mapper, objectName);\n    for (const key of Object.keys(modelProps)) {\n      const propertyMapper = modelProps[key];\n      if (propertyMapper.readOnly) {\n        continue;\n      }\n\n      let propName: string | undefined;\n      let parentObject: any = payload;\n      if (serializer.isXML) {\n        if (propertyMapper.xmlIsWrapped) {\n          propName = propertyMapper.xmlName;\n        } else {\n          propName = propertyMapper.xmlElementName || propertyMapper.xmlName;\n        }\n      } else {\n        const paths = splitSerializeName(propertyMapper.serializedName!);\n        propName = paths.pop();\n\n        for (const pathName of paths) {\n          const childObject = parentObject[pathName];\n          if (\n            childObject == undefined &&\n            (object[key] != undefined || propertyMapper.defaultValue !== undefined)\n          ) {\n            parentObject[pathName] = {};\n          }\n          parentObject = parentObject[pathName];\n        }\n      }\n\n      if (parentObject != undefined) {\n        if (isXml && mapper.xmlNamespace) {\n          const xmlnsKey = mapper.xmlNamespacePrefix\n            ? `xmlns:${mapper.xmlNamespacePrefix}`\n            : \"xmlns\";\n          parentObject[XML_ATTRKEY] = {\n            ...parentObject[XML_ATTRKEY],\n            [xmlnsKey]: mapper.xmlNamespace\n          };\n        }\n        const propertyObjectName =\n          propertyMapper.serializedName !== \"\"\n            ? objectName + \".\" + propertyMapper.serializedName\n            : objectName;\n\n        let toSerialize = object[key];\n        const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n        if (\n          polymorphicDiscriminator &&\n          polymorphicDiscriminator.clientName === key &&\n          toSerialize == undefined\n        ) {\n          toSerialize = mapper.serializedName;\n        }\n\n        const serializedValue = serializer.serialize(\n          propertyMapper,\n          toSerialize,\n          propertyObjectName,\n          options\n        );\n\n        if (serializedValue !== undefined && propName != undefined) {\n          const value = getXmlObjectValue(propertyMapper, serializedValue, isXml, options);\n          if (isXml && propertyMapper.xmlIsAttribute) {\n            // XML_ATTRKEY, i.e., $ is the key attributes are kept under in xml2js.\n            // This keeps things simple while preventing name collision\n            // with names in user documents.\n            parentObject[XML_ATTRKEY] = parentObject[XML_ATTRKEY] || {};\n            parentObject[XML_ATTRKEY][propName] = serializedValue;\n          } else if (isXml && propertyMapper.xmlIsWrapped) {\n            parentObject[propName] = { [propertyMapper.xmlElementName!]: value };\n          } else {\n            parentObject[propName] = value;\n          }\n        }\n      }\n    }\n\n    const additionalPropertiesMapper = resolveAdditionalProperties(serializer, mapper, objectName);\n    if (additionalPropertiesMapper) {\n      const propNames = Object.keys(modelProps);\n      for (const clientPropName in object) {\n        const isAdditionalProperty = propNames.every((pn) => pn !== clientPropName);\n        if (isAdditionalProperty) {\n          payload[clientPropName] = serializer.serialize(\n            additionalPropertiesMapper,\n            object[clientPropName],\n            objectName + '[\"' + clientPropName + '\"]',\n            options\n          );\n        }\n      }\n    }\n\n    return payload;\n  }\n  return object;\n}\n\nfunction getXmlObjectValue(\n  propertyMapper: Mapper,\n  serializedValue: any,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): any {\n  if (!isXml || !propertyMapper.xmlNamespace) {\n    return serializedValue;\n  }\n\n  const xmlnsKey = propertyMapper.xmlNamespacePrefix\n    ? `xmlns:${propertyMapper.xmlNamespacePrefix}`\n    : \"xmlns\";\n  const xmlNamespace = { [xmlnsKey]: propertyMapper.xmlNamespace };\n\n  if ([\"Composite\"].includes(propertyMapper.type.name)) {\n    if (serializedValue[XML_ATTRKEY]) {\n      return serializedValue;\n    } else {\n      const result: any = { ...serializedValue };\n      result[XML_ATTRKEY] = xmlNamespace;\n      return result;\n    }\n  }\n  const result: any = {};\n  result[options.xmlCharKey] = serializedValue;\n  result[XML_ATTRKEY] = xmlNamespace;\n  return result;\n}\n\nfunction isSpecialXmlProperty(propertyName: string, options: Required<SerializerOptions>): boolean {\n  return [XML_ATTRKEY, options.xmlCharKey].includes(propertyName);\n}\n\nfunction deserializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  responseBody: any,\n  objectName: string,\n  options: Required<SerializerOptions>\n): any {\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, responseBody, \"serializedName\");\n  }\n\n  const modelProps = resolveModelProperties(serializer, mapper, objectName);\n  let instance: { [key: string]: any } = {};\n  const handledPropertyNames: string[] = [];\n\n  for (const key of Object.keys(modelProps)) {\n    const propertyMapper = modelProps[key];\n    const paths = splitSerializeName(modelProps[key].serializedName!);\n    handledPropertyNames.push(paths[0]);\n    const { serializedName, xmlName, xmlElementName } = propertyMapper;\n    let propertyObjectName = objectName;\n    if (serializedName !== \"\" && serializedName !== undefined) {\n      propertyObjectName = objectName + \".\" + serializedName;\n    }\n\n    const headerCollectionPrefix = (propertyMapper as DictionaryMapper).headerCollectionPrefix;\n    if (headerCollectionPrefix) {\n      const dictionary: any = {};\n      for (const headerKey of Object.keys(responseBody)) {\n        if (headerKey.startsWith(headerCollectionPrefix)) {\n          dictionary[headerKey.substring(headerCollectionPrefix.length)] = serializer.deserialize(\n            (propertyMapper as DictionaryMapper).type.value,\n            responseBody[headerKey],\n            propertyObjectName,\n            options\n          );\n        }\n\n        handledPropertyNames.push(headerKey);\n      }\n      instance[key] = dictionary;\n    } else if (serializer.isXML) {\n      if (propertyMapper.xmlIsAttribute && responseBody[XML_ATTRKEY]) {\n        instance[key] = serializer.deserialize(\n          propertyMapper,\n          responseBody[XML_ATTRKEY][xmlName!],\n          propertyObjectName,\n          options\n        );\n      } else {\n        const propertyName = xmlElementName || xmlName || serializedName;\n        if (propertyMapper.xmlIsWrapped) {\n          /* a list of <xmlElementName> wrapped by <xmlName>\n            For the xml example below\n              <Cors>\n                <CorsRule>...</CorsRule>\n                <CorsRule>...</CorsRule>\n              </Cors>\n            the responseBody has\n              {\n                Cors: {\n                  CorsRule: [{...}, {...}]\n                }\n              }\n            xmlName is \"Cors\" and xmlElementName is\"CorsRule\".\n          */\n          const wrapped = responseBody[xmlName!];\n          const elementList = wrapped?.[xmlElementName!] ?? [];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            elementList,\n            propertyObjectName,\n            options\n          );\n        } else {\n          const property = responseBody[propertyName!];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            property,\n            propertyObjectName,\n            options\n          );\n        }\n      }\n    } else {\n      // deserialize the property if it is present in the provided responseBody instance\n      let propertyInstance;\n      let res = responseBody;\n      // traversing the object step by step.\n      for (const item of paths) {\n        if (!res) break;\n        res = res[item];\n      }\n      propertyInstance = res;\n      const polymorphicDiscriminator = mapper.type.polymorphicDiscriminator;\n      // checking that the model property name (key)(ex: \"fishtype\") and the\n      // clientName of the polymorphicDiscriminator {metadata} (ex: \"fishtype\")\n      // instead of the serializedName of the polymorphicDiscriminator (ex: \"fish.type\")\n      // is a better approach. The generator is not consistent with escaping '\\.' in the\n      // serializedName of the property (ex: \"fish\\.type\") that is marked as polymorphic discriminator\n      // and the serializedName of the metadata polymorphicDiscriminator (ex: \"fish.type\"). However,\n      // the clientName transformation of the polymorphicDiscriminator (ex: \"fishtype\") and\n      // the transformation of model property name (ex: \"fishtype\") is done consistently.\n      // Hence, it is a safer bet to rely on the clientName of the polymorphicDiscriminator.\n      if (\n        polymorphicDiscriminator &&\n        key === polymorphicDiscriminator.clientName &&\n        propertyInstance == undefined\n      ) {\n        propertyInstance = mapper.serializedName;\n      }\n\n      let serializedValue;\n      // paging\n      if (Array.isArray(responseBody[key]) && modelProps[key].serializedName === \"\") {\n        propertyInstance = responseBody[key];\n        const arrayInstance = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options\n        );\n        // Copy over any properties that have already been added into the instance, where they do\n        // not exist on the newly de-serialized array\n        for (const [k, v] of Object.entries(instance)) {\n          if (!Object.prototype.hasOwnProperty.call(arrayInstance, k)) {\n            arrayInstance[k] = v;\n          }\n        }\n        instance = arrayInstance;\n      } else if (propertyInstance !== undefined || propertyMapper.defaultValue !== undefined) {\n        serializedValue = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options\n        );\n        instance[key] = serializedValue;\n      }\n    }\n  }\n\n  const additionalPropertiesMapper = mapper.type.additionalProperties;\n  if (additionalPropertiesMapper) {\n    const isAdditionalProperty = (responsePropName: string): boolean => {\n      for (const clientPropName in modelProps) {\n        const paths = splitSerializeName(modelProps[clientPropName].serializedName);\n        if (paths[0] === responsePropName) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    for (const responsePropName in responseBody) {\n      if (isAdditionalProperty(responsePropName)) {\n        instance[responsePropName] = serializer.deserialize(\n          additionalPropertiesMapper,\n          responseBody[responsePropName],\n          objectName + '[\"' + responsePropName + '\"]',\n          options\n        );\n      }\n    }\n  } else if (responseBody) {\n    for (const key of Object.keys(responseBody)) {\n      if (\n        instance[key] === undefined &&\n        !handledPropertyNames.includes(key) &&\n        !isSpecialXmlProperty(key, options)\n      ) {\n        instance[key] = responseBody[key];\n      }\n    }\n  }\n\n  return instance;\n}\n\nfunction deserializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  responseBody: any,\n  objectName: string,\n  options: Required<SerializerOptions>\n): { [key: string]: any } {\n  const value = mapper.type.value;\n  if (!value || typeof value !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`\n    );\n  }\n  if (responseBody) {\n    const tempDictionary: { [key: string]: any } = {};\n    for (const key of Object.keys(responseBody)) {\n      tempDictionary[key] = serializer.deserialize(value, responseBody[key], objectName, options);\n    }\n    return tempDictionary;\n  }\n  return responseBody;\n}\n\nfunction deserializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  responseBody: any,\n  objectName: string,\n  options: Required<SerializerOptions>\n): any[] {\n  const element = mapper.type.element;\n  if (!element || typeof element !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`\n    );\n  }\n  if (responseBody) {\n    if (!Array.isArray(responseBody)) {\n      // xml2js will interpret a single element array as just the element, so force it to be an array\n      responseBody = [responseBody];\n    }\n\n    const tempArray = [];\n    for (let i = 0; i < responseBody.length; i++) {\n      tempArray[i] = serializer.deserialize(\n        element,\n        responseBody[i],\n        `${objectName}[${i}]`,\n        options\n      );\n    }\n    return tempArray;\n  }\n  return responseBody;\n}\n\nfunction getPolymorphicMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  polymorphicPropertyName: \"clientName\" | \"serializedName\"\n): CompositeMapper {\n  const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n  if (polymorphicDiscriminator) {\n    const discriminatorName = polymorphicDiscriminator[polymorphicPropertyName];\n    if (discriminatorName != undefined) {\n      const discriminatorValue = object[discriminatorName];\n      if (discriminatorValue != undefined) {\n        const typeName = mapper.type.uberParent || mapper.type.className;\n        const indexDiscriminator =\n          discriminatorValue === typeName\n            ? discriminatorValue\n            : typeName + \".\" + discriminatorValue;\n        const polymorphicMapper = serializer.modelMappers.discriminators[indexDiscriminator];\n        if (polymorphicMapper) {\n          mapper = polymorphicMapper;\n        }\n      }\n    }\n  }\n  return mapper;\n}\n\nfunction getPolymorphicDiscriminatorRecursively(\n  serializer: Serializer,\n  mapper: CompositeMapper\n): PolymorphicDiscriminator | undefined {\n  return (\n    mapper.type.polymorphicDiscriminator ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.uberParent) ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.className)\n  );\n}\n\nfunction getPolymorphicDiscriminatorSafely(serializer: Serializer, typeName?: string): any {\n  return (\n    typeName &&\n    serializer.modelMappers[typeName] &&\n    serializer.modelMappers[typeName].type.polymorphicDiscriminator\n  );\n}\n\nexport interface MapperConstraints {\n  InclusiveMaximum?: number;\n  ExclusiveMaximum?: number;\n  InclusiveMinimum?: number;\n  ExclusiveMinimum?: number;\n  MaxLength?: number;\n  MinLength?: number;\n  Pattern?: RegExp;\n  MaxItems?: number;\n  MinItems?: number;\n  UniqueItems?: true;\n  MultipleOf?: number;\n}\n\nexport type MapperType =\n  | SimpleMapperType\n  | CompositeMapperType\n  | SequenceMapperType\n  | DictionaryMapperType\n  | EnumMapperType;\n\nexport interface SimpleMapperType {\n  name:\n    | \"Base64Url\"\n    | \"Boolean\"\n    | \"ByteArray\"\n    | \"Date\"\n    | \"DateTime\"\n    | \"DateTimeRfc1123\"\n    | \"Object\"\n    | \"Stream\"\n    | \"String\"\n    | \"TimeSpan\"\n    | \"UnixTime\"\n    | \"Uuid\"\n    | \"Number\"\n    | \"any\";\n}\n\nexport interface CompositeMapperType {\n  name: \"Composite\";\n\n  // Only one of the two below properties should be present.\n  // Use className to reference another type definition,\n  // and use modelProperties/additionalProperties when the reference to the other type has been resolved.\n  className?: string;\n\n  modelProperties?: { [propertyName: string]: Mapper };\n  additionalProperties?: Mapper;\n\n  uberParent?: string;\n  polymorphicDiscriminator?: PolymorphicDiscriminator;\n}\n\nexport interface SequenceMapperType {\n  name: \"Sequence\";\n  element: Mapper;\n}\n\nexport interface DictionaryMapperType {\n  name: \"Dictionary\";\n  value: Mapper;\n}\n\nexport interface EnumMapperType {\n  name: \"Enum\";\n  allowedValues: any[];\n}\n\nexport interface BaseMapper {\n  /**\n   * Name for the xml element\n   */\n  xmlName?: string;\n  /**\n   * Xml element namespace\n   */\n  xmlNamespace?: string;\n  /**\n   * Xml element namespace prefix\n   */\n  xmlNamespacePrefix?: string;\n  /**\n   * Determines if the current property should be serialized as an attribute of the parent xml element\n   */\n  xmlIsAttribute?: boolean;\n  /**\n   * Name for the xml elements when serializing an array\n   */\n  xmlElementName?: string;\n  /**\n   * Whether or not the current property should have a wrapping XML element\n   */\n  xmlIsWrapped?: boolean;\n  /**\n   * Whether or not the current property is readonly\n   */\n  readOnly?: boolean;\n  /**\n   * Whether or not the current property is a constant\n   */\n  isConstant?: boolean;\n  /**\n   * Whether or not the current property is required\n   */\n  required?: boolean;\n  /**\n   * Whether or not the current property allows mull as a value\n   */\n  nullable?: boolean;\n  /**\n   * The name to use when serializing\n   */\n  serializedName?: string;\n  /**\n   * Type of the mapper\n   */\n  type: MapperType;\n  /**\n   * Default value when one is not explicitly provided\n   */\n  defaultValue?: any;\n  /**\n   * Constraints to test the current value against\n   */\n  constraints?: MapperConstraints;\n}\n\nexport type Mapper = BaseMapper | CompositeMapper | SequenceMapper | DictionaryMapper | EnumMapper;\n\nexport interface PolymorphicDiscriminator {\n  serializedName: string;\n  clientName: string;\n  [key: string]: string;\n}\n\nexport interface CompositeMapper extends BaseMapper {\n  type: CompositeMapperType;\n}\n\nexport interface SequenceMapper extends BaseMapper {\n  type: SequenceMapperType;\n}\n\nexport interface DictionaryMapper extends BaseMapper {\n  type: DictionaryMapperType;\n  headerCollectionPrefix?: string;\n}\n\nexport interface EnumMapper extends BaseMapper {\n  type: EnumMapperType;\n}\n\nexport interface UrlParameterValue {\n  value: string;\n  skipUrlEncoding: boolean;\n}\n\n// TODO: why is this here?\nexport function serializeObject(toSerialize: unknown): any {\n  const castToSerialize = toSerialize as Record<string, unknown>;\n  if (toSerialize == undefined) return undefined;\n  if (toSerialize instanceof Uint8Array) {\n    toSerialize = base64.encodeByteArray(toSerialize);\n    return toSerialize;\n  } else if (toSerialize instanceof Date) {\n    return toSerialize.toISOString();\n  } else if (Array.isArray(toSerialize)) {\n    const array = [];\n    for (let i = 0; i < toSerialize.length; i++) {\n      array.push(serializeObject(toSerialize[i]));\n    }\n    return array;\n  } else if (typeof toSerialize === \"object\") {\n    const dictionary: { [key: string]: any } = {};\n    for (const property in toSerialize) {\n      dictionary[property] = serializeObject(castToSerialize[property]);\n    }\n    return dictionary;\n  }\n  return toSerialize;\n}\n\n/**\n * Utility function to create a K:V from a list of strings\n */\nfunction strEnum<T extends string>(o: Array<T>): { [K in T]: K } {\n  const result: any = {};\n  for (const key of o) {\n    result[key] = key;\n  }\n  return result;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const MapperType = strEnum([\n  \"Base64Url\",\n  \"Boolean\",\n  \"ByteArray\",\n  \"Composite\",\n  \"Date\",\n  \"DateTime\",\n  \"DateTimeRfc1123\",\n  \"Dictionary\",\n  \"Enum\",\n  \"Number\",\n  \"Object\",\n  \"Sequence\",\n  \"String\",\n  \"Stream\",\n  \"TimeSpan\",\n  \"UnixTime\"\n]);\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpHeaders, HttpHeadersLike, isHttpHeadersLike } from \"./httpHeaders\";\nimport { OperationSpec } from \"./operationSpec\";\nimport { Mapper, Serializer } from \"./serializer\";\nimport { generateUuid } from \"./util/utils\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { OperationResponse } from \"./operationResponse\";\nimport { ProxySettings } from \"./serviceClient\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { SpanOptions, Context } from \"@azure/core-tracing\";\nimport { SerializerOptions } from \"./util/serializer.common\";\n\nexport type HttpMethods =\n  | \"GET\"\n  | \"PUT\"\n  | \"POST\"\n  | \"DELETE\"\n  | \"PATCH\"\n  | \"HEAD\"\n  | \"OPTIONS\"\n  | \"TRACE\";\nexport type HttpRequestBody =\n  | Blob\n  | string\n  | ArrayBuffer\n  | ArrayBufferView\n  | (() => NodeJS.ReadableStream);\n\n/**\n * Fired in response to upload or download progress.\n */\nexport type TransferProgressEvent = {\n  /**\n   * The number of bytes loaded so far.\n   */\n  loadedBytes: number;\n};\n\nexport interface WebResourceLike {\n  /**\n   * The URL being accessed by the request.\n   */\n  url: string;\n  /**\n   * The HTTP method to use when making the request.\n   */\n  method: HttpMethods;\n  /**\n   * The HTTP body contents of the request.\n   */\n  body?: any;\n  /**\n   * The HTTP headers to use when making the request.\n   */\n  headers: HttpHeadersLike;\n  /**\n   * @deprecated Use streamResponseStatusCodes property instead.\n   * Whether or not the body of the HttpOperationResponse should be treated as a stream.\n   */\n  streamResponseBody?: boolean;\n  /**\n   * A list of response status codes whose corresponding HttpOperationResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n  /**\n   * A function that returns the proper OperationResponse for the given OperationSpec and\n   * HttpOperationResponse combination. If this is undefined, then a simple status code lookup will\n   * be used.\n   */\n  operationResponseGetter?: (\n    operationSpec: OperationSpec,\n    response: HttpOperationResponse\n  ) => undefined | OperationResponse;\n  formData?: any;\n  /**\n   * A query string represented as an object.\n   */\n  query?: { [key: string]: any };\n  /**\n   * Used to parse the response.\n   */\n  operationSpec?: OperationSpec;\n  /**\n   * If credentials (cookies) should be sent along during an XHR.\n   */\n  withCredentials: boolean;\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   */\n  timeout: number;\n  /**\n   * Proxy configuration.\n   */\n  proxySettings?: ProxySettings;\n  /**\n   * If the connection should be reused.\n   */\n  keepAlive?: boolean;\n  /**\n   * Whether or not to decompress response according to Accept-Encoding header (node-fetch only)\n   */\n  decompressResponse?: boolean;\n  /**\n   * A unique identifier for the request. Used for logging and tracing.\n   */\n  requestId: string;\n\n  /**\n   * Used to abort the request later.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Tracing: Context used when creating spans.\n   */\n  tracingContext?: Context;\n\n  /**\n   * Validates that the required properties such as method, url, headers[\"Content-Type\"],\n   * headers[\"accept-language\"] are defined. It will throw an error if one of the above\n   * mentioned properties are not defined.\n   */\n  validateRequestProperties(): void;\n\n  /**\n   * Sets options on the request.\n   */\n  prepare(options: RequestPrepareOptions): WebResourceLike;\n  /**\n   * Clone this request object.\n   */\n  clone(): WebResourceLike;\n}\n\nexport function isWebResourceLike(object: unknown): object is WebResourceLike {\n  if (object && typeof object === \"object\") {\n    const castObject = object as {\n      url: unknown;\n      method: unknown;\n      headers: unknown;\n      validateRequestProperties: unknown;\n      prepare: unknown;\n      clone: unknown;\n    };\n    if (\n      typeof castObject.url === \"string\" &&\n      typeof castObject.method === \"string\" &&\n      typeof castObject.headers === \"object\" &&\n      isHttpHeadersLike(castObject.headers) &&\n      typeof castObject.validateRequestProperties === \"function\" &&\n      typeof castObject.prepare === \"function\" &&\n      typeof castObject.clone === \"function\"\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Creates a new WebResource object.\n *\n * This class provides an abstraction over a REST call by being library / implementation agnostic and wrapping the necessary\n * properties to initiate a request.\n */\nexport class WebResource implements WebResourceLike {\n  url: string;\n  method: HttpMethods;\n  body?: any;\n  headers: HttpHeadersLike;\n  /**\n   * @deprecated Use streamResponseStatusCodes property instead.\n   * Whether or not the body of the HttpOperationResponse should be treated as a stream.\n   */\n  streamResponseBody?: boolean;\n  /**\n   * A list of status codes whose corresponding HttpOperationResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n  /**\n   * A function that returns the proper OperationResponse for the given OperationSpec and\n   * HttpOperationResponse combination. If this is undefined, then a simple status code lookup will\n   * be used.\n   */\n  operationResponseGetter?: (\n    operationSpec: OperationSpec,\n    response: HttpOperationResponse\n  ) => undefined | OperationResponse;\n  formData?: any;\n  query?: { [key: string]: any };\n  operationSpec?: OperationSpec;\n  withCredentials: boolean;\n  timeout: number;\n  proxySettings?: ProxySettings;\n  keepAlive?: boolean;\n  /**\n   * Whether or not to decompress response according to Accept-Encoding header (node-fetch only)\n   */\n  decompressResponse?: boolean;\n  requestId: string;\n\n  abortSignal?: AbortSignalLike;\n\n  /** Callback which fires upon upload progress. */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Tracing: Options used to create a span when tracing is enabled.\n   */\n  spanOptions?: SpanOptions;\n\n  /**\n   * Tracing: Context used when creating Spans.\n   */\n  tracingContext?: Context;\n\n  constructor(\n    url?: string,\n    method?: HttpMethods,\n    body?: unknown,\n    query?: { [key: string]: any },\n    headers?: { [key: string]: any } | HttpHeadersLike,\n    streamResponseBody?: boolean,\n    withCredentials?: boolean,\n    abortSignal?: AbortSignalLike,\n    timeout?: number,\n    onUploadProgress?: (progress: TransferProgressEvent) => void,\n    onDownloadProgress?: (progress: TransferProgressEvent) => void,\n    proxySettings?: ProxySettings,\n    keepAlive?: boolean,\n    decompressResponse?: boolean,\n    streamResponseStatusCodes?: Set<number>\n  ) {\n    this.streamResponseBody = streamResponseBody;\n    this.streamResponseStatusCodes = streamResponseStatusCodes;\n    this.url = url || \"\";\n    this.method = method || \"GET\";\n    this.headers = isHttpHeadersLike(headers) ? headers : new HttpHeaders(headers);\n    this.body = body;\n    this.query = query;\n    this.formData = undefined;\n    this.withCredentials = withCredentials || false;\n    this.abortSignal = abortSignal;\n    this.timeout = timeout || 0;\n    this.onUploadProgress = onUploadProgress;\n    this.onDownloadProgress = onDownloadProgress;\n    this.proxySettings = proxySettings;\n    this.keepAlive = keepAlive;\n    this.decompressResponse = decompressResponse;\n    this.requestId = this.headers.get(\"x-ms-client-request-id\") || generateUuid();\n  }\n\n  /**\n   * Validates that the required properties such as method, url, headers[\"Content-Type\"],\n   * headers[\"accept-language\"] are defined. It will throw an error if one of the above\n   * mentioned properties are not defined.\n   */\n  validateRequestProperties(): void {\n    if (!this.method) {\n      throw new Error(\"WebResource.method is required.\");\n    }\n    if (!this.url) {\n      throw new Error(\"WebResource.url is required.\");\n    }\n  }\n\n  /**\n   * Prepares the request.\n   * @param options - Options to provide for preparing the request.\n   * @returns Returns the prepared WebResource (HTTP Request) object that needs to be given to the request pipeline.\n   */\n  prepare(options: RequestPrepareOptions): WebResource {\n    if (!options) {\n      throw new Error(\"options object is required\");\n    }\n\n    if (\n      options.method === undefined ||\n      options.method === null ||\n      typeof options.method.valueOf() !== \"string\"\n    ) {\n      throw new Error(\"options.method must be a string.\");\n    }\n\n    if (options.url && options.pathTemplate) {\n      throw new Error(\n        \"options.url and options.pathTemplate are mutually exclusive. Please provide exactly one of them.\"\n      );\n    }\n\n    if (\n      (options.pathTemplate === undefined ||\n        options.pathTemplate === null ||\n        typeof options.pathTemplate.valueOf() !== \"string\") &&\n      (options.url === undefined ||\n        options.url === null ||\n        typeof options.url.valueOf() !== \"string\")\n    ) {\n      throw new Error(\"Please provide exactly one of options.pathTemplate or options.url.\");\n    }\n\n    // set the url if it is provided.\n    if (options.url) {\n      if (typeof options.url !== \"string\") {\n        throw new Error('options.url must be of type \"string\".');\n      }\n      this.url = options.url;\n    }\n\n    // set the method\n    if (options.method) {\n      const validMethods = [\"GET\", \"PUT\", \"HEAD\", \"DELETE\", \"OPTIONS\", \"POST\", \"PATCH\", \"TRACE\"];\n      if (validMethods.indexOf(options.method.toUpperCase()) === -1) {\n        throw new Error(\n          'The provided method \"' +\n            options.method +\n            '\" is invalid. Supported HTTP methods are: ' +\n            JSON.stringify(validMethods)\n        );\n      }\n    }\n    this.method = options.method.toUpperCase() as HttpMethods;\n\n    // construct the url if path template is provided\n    if (options.pathTemplate) {\n      const { pathTemplate, pathParameters } = options;\n      if (typeof pathTemplate !== \"string\") {\n        throw new Error('options.pathTemplate must be of type \"string\".');\n      }\n      if (!options.baseUrl) {\n        options.baseUrl = \"https://management.azure.com\";\n      }\n      const baseUrl = options.baseUrl;\n      let url =\n        baseUrl +\n        (baseUrl.endsWith(\"/\") ? \"\" : \"/\") +\n        (pathTemplate.startsWith(\"/\") ? pathTemplate.slice(1) : pathTemplate);\n      const segments = url.match(/({[\\w-]*\\s*[\\w-]*})/gi);\n      if (segments && segments.length) {\n        if (!pathParameters) {\n          throw new Error(\n            `pathTemplate: ${pathTemplate} has been provided. Hence, options.pathParameters must also be provided.`\n          );\n        }\n        segments.forEach(function(item) {\n          const pathParamName = item.slice(1, -1);\n          const pathParam = (pathParameters as { [key: string]: any })[pathParamName];\n          if (\n            pathParam === null ||\n            pathParam === undefined ||\n            !(typeof pathParam === \"string\" || typeof pathParam === \"object\")\n          ) {\n            const stringifiedPathParameters = JSON.stringify(pathParameters, undefined, 2);\n            throw new Error(\n              `pathTemplate: ${pathTemplate} contains the path parameter ${pathParamName}` +\n                ` however, it is not present in parameters: ${stringifiedPathParameters}.` +\n                `The value of the path parameter can either be a \"string\" of the form { ${pathParamName}: \"some sample value\" } or ` +\n                `it can be an \"object\" of the form { \"${pathParamName}\": { value: \"some sample value\", skipUrlEncoding: true } }.`\n            );\n          }\n\n          if (typeof pathParam.valueOf() === \"string\") {\n            url = url.replace(item, encodeURIComponent(pathParam));\n          }\n\n          if (typeof pathParam.valueOf() === \"object\") {\n            if (!pathParam.value) {\n              throw new Error(\n                `options.pathParameters[${pathParamName}] is of type \"object\" but it does not contain a \"value\" property.`\n              );\n            }\n            if (pathParam.skipUrlEncoding) {\n              url = url.replace(item, pathParam.value);\n            } else {\n              url = url.replace(item, encodeURIComponent(pathParam.value));\n            }\n          }\n        });\n      }\n      this.url = url;\n    }\n\n    // append query parameters to the url if they are provided. They can be provided with pathTemplate or url option.\n    if (options.queryParameters) {\n      const queryParameters = options.queryParameters;\n      if (typeof queryParameters !== \"object\") {\n        throw new Error(\n          `options.queryParameters must be of type object. It should be a JSON object ` +\n            `of \"query-parameter-name\" as the key and the \"query-parameter-value\" as the value. ` +\n            `The \"query-parameter-value\" may be fo type \"string\" or an \"object\" of the form { value: \"query-parameter-value\", skipUrlEncoding: true }.`\n        );\n      }\n      // append question mark if it is not present in the url\n      if (this.url && this.url.indexOf(\"?\") === -1) {\n        this.url += \"?\";\n      }\n      // construct queryString\n      const queryParams = [];\n      // We need to populate this.query as a dictionary if the request is being used for Sway's validateRequest().\n      this.query = {};\n      for (const queryParamName in queryParameters) {\n        const queryParam: any = queryParameters[queryParamName];\n        if (queryParam) {\n          if (typeof queryParam === \"string\") {\n            queryParams.push(queryParamName + \"=\" + encodeURIComponent(queryParam));\n            this.query[queryParamName] = encodeURIComponent(queryParam);\n          } else if (typeof queryParam === \"object\") {\n            if (!queryParam.value) {\n              throw new Error(\n                `options.queryParameters[${queryParamName}] is of type \"object\" but it does not contain a \"value\" property.`\n              );\n            }\n            if (queryParam.skipUrlEncoding) {\n              queryParams.push(queryParamName + \"=\" + queryParam.value);\n              this.query[queryParamName] = queryParam.value;\n            } else {\n              queryParams.push(queryParamName + \"=\" + encodeURIComponent(queryParam.value));\n              this.query[queryParamName] = encodeURIComponent(queryParam.value);\n            }\n          }\n        }\n      } // end-of-for\n      // append the queryString\n      this.url += queryParams.join(\"&\");\n    }\n\n    // add headers to the request if they are provided\n    if (options.headers) {\n      const headers = options.headers;\n      for (const headerName of Object.keys(options.headers)) {\n        this.headers.set(headerName, headers[headerName]);\n      }\n    }\n    // ensure accept-language is set correctly\n    if (!this.headers.get(\"accept-language\")) {\n      this.headers.set(\"accept-language\", \"en-US\");\n    }\n    // ensure the request-id is set correctly\n    if (!this.headers.get(\"x-ms-client-request-id\") && !options.disableClientRequestId) {\n      this.headers.set(\"x-ms-client-request-id\", this.requestId);\n    }\n\n    // default\n    if (!this.headers.get(\"Content-Type\")) {\n      this.headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n    }\n\n    // set the request body. request.js automatically sets the Content-Length request header, so we need not set it explicitly\n    this.body = options.body;\n    if (options.body !== undefined && options.body !== null) {\n      // body as a stream special case. set the body as-is and check for some special request headers specific to sending a stream.\n      if (options.bodyIsStream) {\n        if (!this.headers.get(\"Transfer-Encoding\")) {\n          this.headers.set(\"Transfer-Encoding\", \"chunked\");\n        }\n        if (this.headers.get(\"Content-Type\") !== \"application/octet-stream\") {\n          this.headers.set(\"Content-Type\", \"application/octet-stream\");\n        }\n      } else {\n        if (options.serializationMapper) {\n          this.body = new Serializer(options.mappers).serialize(\n            options.serializationMapper,\n            options.body,\n            \"requestBody\"\n          );\n        }\n        if (!options.disableJsonStringifyOnBody) {\n          this.body = JSON.stringify(options.body);\n        }\n      }\n    }\n\n    if (options.spanOptions) {\n      this.spanOptions = options.spanOptions;\n    }\n\n    if (options.tracingContext) {\n      this.tracingContext = options.tracingContext;\n    }\n\n    this.abortSignal = options.abortSignal;\n    this.onDownloadProgress = options.onDownloadProgress;\n    this.onUploadProgress = options.onUploadProgress;\n\n    return this;\n  }\n\n  /**\n   * Clone this WebResource HTTP request object.\n   * @returns The clone of this WebResource HTTP request object.\n   */\n  clone(): WebResource {\n    const result = new WebResource(\n      this.url,\n      this.method,\n      this.body,\n      this.query,\n      this.headers && this.headers.clone(),\n      this.streamResponseBody,\n      this.withCredentials,\n      this.abortSignal,\n      this.timeout,\n      this.onUploadProgress,\n      this.onDownloadProgress,\n      this.proxySettings,\n      this.keepAlive,\n      this.decompressResponse,\n      this.streamResponseStatusCodes\n    );\n\n    if (this.formData) {\n      result.formData = this.formData;\n    }\n\n    if (this.operationSpec) {\n      result.operationSpec = this.operationSpec;\n    }\n\n    if (this.shouldDeserialize) {\n      result.shouldDeserialize = this.shouldDeserialize;\n    }\n\n    if (this.operationResponseGetter) {\n      result.operationResponseGetter = this.operationResponseGetter;\n    }\n\n    return result;\n  }\n}\n\nexport interface RequestPrepareOptions {\n  /**\n   * The HTTP request method. Valid values are \"GET\", \"PUT\", \"HEAD\", \"DELETE\", \"OPTIONS\", \"POST\",\n   * or \"PATCH\".\n   */\n  method: HttpMethods;\n  /**\n   * The request url. It may or may not have query parameters in it. Either provide the \"url\" or\n   * provide the \"pathTemplate\" in the options object. Both the options are mutually exclusive.\n   */\n  url?: string;\n  /**\n   * A dictionary of query parameters to be appended to the url, where\n   * the \"key\" is the \"query-parameter-name\" and the \"value\" is the \"query-parameter-value\".\n   * The \"query-parameter-value\" can be of type \"string\" or it can be of type \"object\".\n   * The \"object\" format should be used when you want to skip url encoding. While using the object format,\n   * the object must have a property named value which provides the \"query-parameter-value\".\n   * Example:\n   *    - query-parameter-value in \"object\" format: `{ \"query-parameter-name\": { value: \"query-parameter-value\", skipUrlEncoding: true } }`\n   *    - query-parameter-value in \"string\" format: `{ \"query-parameter-name\": \"query-parameter-value\"}`.\n   * Note: \"If options.url already has some query parameters, then the value provided in options.queryParameters will be appended to the url.\n   */\n  queryParameters?: { [key: string]: any | ParameterValue };\n  /**\n   * The path template of the request url. Either provide the \"url\" or provide the \"pathTemplate\" in\n   * the options object. Both the options are mutually exclusive.\n   * Example: `/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Storage/storageAccounts/{accountName}`\n   */\n  pathTemplate?: string;\n  /**\n   * The base url of the request. Default value is: \"https://management.azure.com\". This is\n   * applicable only with pathTemplate. If you are providing options.url then it is expected that\n   * you provide the complete url.\n   */\n  baseUrl?: string;\n  /**\n   * A dictionary of path parameters that need to be replaced with actual values in the pathTemplate.\n   * Here the key is the \"path-parameter-name\" and the value is the \"path-parameter-value\".\n   * The \"path-parameter-value\" can be of type \"string\"  or it can be of type \"object\".\n   * The \"object\" format should be used when you want to skip url encoding. While using the object format,\n   * the object must have a property named value which provides the \"path-parameter-value\".\n   * Example:\n   *    - path-parameter-value in \"object\" format: `{ \"path-parameter-name\": { value: \"path-parameter-value\", skipUrlEncoding: true } }`\n   *    - path-parameter-value in \"string\" format: `{ \"path-parameter-name\": \"path-parameter-value\" }`.\n   */\n  pathParameters?: { [key: string]: any | ParameterValue };\n  formData?: { [key: string]: any };\n  /**\n   * A dictionary of request headers that need to be applied to the request.\n   * Here the key is the \"header-name\" and the value is the \"header-value\". The header-value MUST be of type string.\n   *  - ContentType must be provided with the key name as \"Content-Type\". Default value \"application/json; charset=utf-8\".\n   *  - \"Transfer-Encoding\" is set to \"chunked\" by default if \"options.bodyIsStream\" is set to true.\n   *  - \"Content-Type\" is set to \"application/octet-stream\" by default if \"options.bodyIsStream\" is set to true.\n   *  - \"accept-language\" by default is set to \"en-US\"\n   *  - \"x-ms-client-request-id\" by default is set to a new Guid. To not generate a guid for the request, please set options.disableClientRequestId to true\n   */\n  headers?: { [key: string]: any };\n  /**\n   * When set to true, instructs the client to not set \"x-ms-client-request-id\" header to a new Guid().\n   */\n  disableClientRequestId?: boolean;\n  /**\n   * The request body. It can be of any type. This value will be serialized if it is not a stream.\n   */\n  body?: any;\n  /**\n   * Provides information on how to serialize the request body.\n   */\n  serializationMapper?: Mapper;\n  /**\n   * A dictionary of mappers that may be used while [de]serialization.\n   */\n  mappers?: { [x: string]: any };\n  /**\n   * Provides information on how to deserialize the response body.\n   */\n  deserializationMapper?: Record<string, unknown>;\n  /**\n   * Indicates whether this method should JSON.stringify() the request body. Default value: false.\n   */\n  disableJsonStringifyOnBody?: boolean;\n  /**\n   * Indicates whether the request body is a stream (useful for file upload scenarios).\n   */\n  bodyIsStream?: boolean;\n  abortSignal?: AbortSignalLike;\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Tracing: Options used to create a span when tracing is enabled.\n   */\n  spanOptions?: SpanOptions;\n  /**\n   * Tracing: Context used when creating spans.\n   */\n  tracingContext?: Context;\n}\n\n/**\n * The Parameter value provided for path or query parameters in RequestPrepareOptions\n */\nexport interface ParameterValue {\n  value: any;\n  skipUrlEncoding: boolean;\n  [key: string]: any;\n}\n\n/**\n * Describes the base structure of the options object that will be used in every operation.\n */\nexport interface RequestOptionsBase {\n  /**\n   * will be applied before the request is sent.\n   */\n  customHeaders?: { [key: string]: string };\n\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n\n  /**\n   * Tracing: Context used when creating spans.\n   */\n  tracingContext?: Context;\n\n  [key: string]: any;\n\n  /**\n   * Options to override XML parsing/building behavior.\n   */\n  serializerOptions?: SerializerOptions;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { inspect } from \"util\";\n\nexport const custom = inspect.custom;\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { replaceAll } from \"./util/utils\";\n\nexport { URL } from \"./util/url\";\n\ntype URLQueryParseState = \"ParameterName\" | \"ParameterValue\";\n\n/**\n * A class that handles the query portion of a URLBuilder.\n */\nexport class URLQuery {\n  private readonly _rawQuery: { [queryParameterName: string]: string | string[] } = {};\n\n  /**\n   * Get whether or not there any query parameters in this URLQuery.\n   */\n  public any(): boolean {\n    return Object.keys(this._rawQuery).length > 0;\n  }\n\n  /**\n   * Get the keys of the query string.\n   */\n  public keys(): string[] {\n    return Object.keys(this._rawQuery);\n  }\n\n  /**\n   * Set a query parameter with the provided name and value. If the parameterValue is undefined or\n   * empty, then this will attempt to remove an existing query parameter with the provided\n   * parameterName.\n   */\n  public set(parameterName: string, parameterValue: unknown): void {\n    const caseParameterValue = parameterValue as {\n      toString: () => string;\n    };\n    if (parameterName) {\n      if (caseParameterValue !== undefined && caseParameterValue !== null) {\n        const newValue = Array.isArray(caseParameterValue)\n          ? caseParameterValue\n          : caseParameterValue.toString();\n        this._rawQuery[parameterName] = newValue;\n      } else {\n        delete this._rawQuery[parameterName];\n      }\n    }\n  }\n\n  /**\n   * Get the value of the query parameter with the provided name. If no parameter exists with the\n   * provided parameter name, then undefined will be returned.\n   */\n  public get(parameterName: string): string | string[] | undefined {\n    return parameterName ? this._rawQuery[parameterName] : undefined;\n  }\n\n  /**\n   * Get the string representation of this query. The return value will not start with a \"?\".\n   */\n  public toString(): string {\n    let result = \"\";\n    for (const parameterName in this._rawQuery) {\n      if (result) {\n        result += \"&\";\n      }\n      const parameterValue = this._rawQuery[parameterName];\n      if (Array.isArray(parameterValue)) {\n        const parameterStrings = [];\n        for (const parameterValueElement of parameterValue) {\n          parameterStrings.push(`${parameterName}=${parameterValueElement}`);\n        }\n        result += parameterStrings.join(\"&\");\n      } else {\n        result += `${parameterName}=${parameterValue}`;\n      }\n    }\n    return result;\n  }\n\n  /**\n   * Parse a URLQuery from the provided text.\n   */\n  public static parse(text: string): URLQuery {\n    const result = new URLQuery();\n\n    if (text) {\n      if (text.startsWith(\"?\")) {\n        text = text.substring(1);\n      }\n\n      let currentState: URLQueryParseState = \"ParameterName\";\n\n      let parameterName = \"\";\n      let parameterValue = \"\";\n      for (let i = 0; i < text.length; ++i) {\n        const currentCharacter: string = text[i];\n        switch (currentState) {\n          case \"ParameterName\":\n            switch (currentCharacter) {\n              case \"=\":\n                currentState = \"ParameterValue\";\n                break;\n\n              case \"&\":\n                parameterName = \"\";\n                parameterValue = \"\";\n                break;\n\n              default:\n                parameterName += currentCharacter;\n                break;\n            }\n            break;\n\n          case \"ParameterValue\":\n            switch (currentCharacter) {\n              case \"&\":\n                result.set(parameterName, parameterValue);\n                parameterName = \"\";\n                parameterValue = \"\";\n                currentState = \"ParameterName\";\n                break;\n\n              default:\n                parameterValue += currentCharacter;\n                break;\n            }\n            break;\n\n          default:\n            throw new Error(\"Unrecognized URLQuery parse state: \" + currentState);\n        }\n      }\n      if (currentState === \"ParameterValue\") {\n        result.set(parameterName, parameterValue);\n      }\n    }\n\n    return result;\n  }\n}\n\n/**\n * A class that handles creating, modifying, and parsing URLs.\n */\nexport class URLBuilder {\n  private _scheme: string | undefined;\n  private _host: string | undefined;\n  private _port: string | undefined;\n  private _path: string | undefined;\n  private _query: URLQuery | undefined;\n\n  /**\n   * Set the scheme/protocol for this URL. If the provided scheme contains other parts of a URL\n   * (such as a host, port, path, or query), those parts will be added to this URL as well.\n   */\n  public setScheme(scheme: string | undefined): void {\n    if (!scheme) {\n      this._scheme = undefined;\n    } else {\n      this.set(scheme, \"SCHEME\");\n    }\n  }\n\n  /**\n   * Get the scheme that has been set in this URL.\n   */\n  public getScheme(): string | undefined {\n    return this._scheme;\n  }\n\n  /**\n   * Set the host for this URL. If the provided host contains other parts of a URL (such as a\n   * port, path, or query), those parts will be added to this URL as well.\n   */\n  public setHost(host: string | undefined): void {\n    if (!host) {\n      this._host = undefined;\n    } else {\n      this.set(host, \"SCHEME_OR_HOST\");\n    }\n  }\n\n  /**\n   * Get the host that has been set in this URL.\n   */\n  public getHost(): string | undefined {\n    return this._host;\n  }\n\n  /**\n   * Set the port for this URL. If the provided port contains other parts of a URL (such as a\n   * path or query), those parts will be added to this URL as well.\n   */\n  public setPort(port: number | string | undefined): void {\n    if (port === undefined || port === null || port === \"\") {\n      this._port = undefined;\n    } else {\n      this.set(port.toString(), \"PORT\");\n    }\n  }\n\n  /**\n   * Get the port that has been set in this URL.\n   */\n  public getPort(): string | undefined {\n    return this._port;\n  }\n\n  /**\n   * Set the path for this URL. If the provided path contains a query, then it will be added to\n   * this URL as well.\n   */\n  public setPath(path: string | undefined): void {\n    if (!path) {\n      this._path = undefined;\n    } else {\n      const schemeIndex = path.indexOf(\"://\");\n      if (schemeIndex !== -1) {\n        const schemeStart = path.lastIndexOf(\"/\", schemeIndex);\n        // Make sure to only grab the URL part of the path before setting the state back to SCHEME\n        // this will handle cases such as \"/a/b/c/https://microsoft.com\" => \"https://microsoft.com\"\n        this.set(schemeStart === -1 ? path : path.substr(schemeStart + 1), \"SCHEME\");\n      } else {\n        this.set(path, \"PATH\");\n      }\n    }\n  }\n\n  /**\n   * Append the provided path to this URL's existing path. If the provided path contains a query,\n   * then it will be added to this URL as well.\n   */\n  public appendPath(path: string | undefined): void {\n    if (path) {\n      let currentPath: string | undefined = this.getPath();\n      if (currentPath) {\n        if (!currentPath.endsWith(\"/\")) {\n          currentPath += \"/\";\n        }\n\n        if (path.startsWith(\"/\")) {\n          path = path.substring(1);\n        }\n\n        path = currentPath + path;\n      }\n      this.set(path, \"PATH\");\n    }\n  }\n\n  /**\n   * Get the path that has been set in this URL.\n   */\n  public getPath(): string | undefined {\n    return this._path;\n  }\n\n  /**\n   * Set the query in this URL.\n   */\n  public setQuery(query: string | undefined): void {\n    if (!query) {\n      this._query = undefined;\n    } else {\n      this._query = URLQuery.parse(query);\n    }\n  }\n\n  /**\n   * Set a query parameter with the provided name and value in this URL's query. If the provided\n   * query parameter value is undefined or empty, then the query parameter will be removed if it\n   * existed.\n   */\n  public setQueryParameter(queryParameterName: string, queryParameterValue: unknown): void {\n    if (queryParameterName) {\n      if (!this._query) {\n        this._query = new URLQuery();\n      }\n      this._query.set(queryParameterName, queryParameterValue);\n    }\n  }\n\n  /**\n   * Get the value of the query parameter with the provided query parameter name. If no query\n   * parameter exists with the provided name, then undefined will be returned.\n   */\n  public getQueryParameterValue(queryParameterName: string): string | string[] | undefined {\n    return this._query ? this._query.get(queryParameterName) : undefined;\n  }\n\n  /**\n   * Get the query in this URL.\n   */\n  public getQuery(): string | undefined {\n    return this._query ? this._query.toString() : undefined;\n  }\n\n  /**\n   * Set the parts of this URL by parsing the provided text using the provided startState.\n   */\n  private set(text: string, startState: URLTokenizerState): void {\n    const tokenizer = new URLTokenizer(text, startState);\n\n    while (tokenizer.next()) {\n      const token: URLToken | undefined = tokenizer.current();\n      let tokenPath: string | undefined;\n      if (token) {\n        switch (token.type) {\n          case \"SCHEME\":\n            this._scheme = token.text || undefined;\n            break;\n\n          case \"HOST\":\n            this._host = token.text || undefined;\n            break;\n\n          case \"PORT\":\n            this._port = token.text || undefined;\n            break;\n\n          case \"PATH\":\n            tokenPath = token.text || undefined;\n            if (!this._path || this._path === \"/\" || tokenPath !== \"/\") {\n              this._path = tokenPath;\n            }\n            break;\n\n          case \"QUERY\":\n            this._query = URLQuery.parse(token.text);\n            break;\n\n          default:\n            throw new Error(`Unrecognized URLTokenType: ${token.type}`);\n        }\n      }\n    }\n  }\n\n  public toString(): string {\n    let result = \"\";\n\n    if (this._scheme) {\n      result += `${this._scheme}://`;\n    }\n\n    if (this._host) {\n      result += this._host;\n    }\n\n    if (this._port) {\n      result += `:${this._port}`;\n    }\n\n    if (this._path) {\n      if (!this._path.startsWith(\"/\")) {\n        result += \"/\";\n      }\n      result += this._path;\n    }\n\n    if (this._query && this._query.any()) {\n      result += `?${this._query.toString()}`;\n    }\n\n    return result;\n  }\n\n  /**\n   * If the provided searchValue is found in this URLBuilder, then replace it with the provided\n   * replaceValue.\n   */\n  public replaceAll(searchValue: string, replaceValue: string): void {\n    if (searchValue) {\n      this.setScheme(replaceAll(this.getScheme(), searchValue, replaceValue));\n      this.setHost(replaceAll(this.getHost(), searchValue, replaceValue));\n      this.setPort(replaceAll(this.getPort(), searchValue, replaceValue));\n      this.setPath(replaceAll(this.getPath(), searchValue, replaceValue));\n      this.setQuery(replaceAll(this.getQuery(), searchValue, replaceValue));\n    }\n  }\n\n  public static parse(text: string): URLBuilder {\n    const result = new URLBuilder();\n    result.set(text, \"SCHEME_OR_HOST\");\n    return result;\n  }\n}\n\ntype URLTokenizerState = \"SCHEME\" | \"SCHEME_OR_HOST\" | \"HOST\" | \"PORT\" | \"PATH\" | \"QUERY\" | \"DONE\";\n\ntype URLTokenType = \"SCHEME\" | \"HOST\" | \"PORT\" | \"PATH\" | \"QUERY\";\n\nexport class URLToken {\n  public constructor(public readonly text: string, public readonly type: URLTokenType) {}\n\n  public static scheme(text: string): URLToken {\n    return new URLToken(text, \"SCHEME\");\n  }\n\n  public static host(text: string): URLToken {\n    return new URLToken(text, \"HOST\");\n  }\n\n  public static port(text: string): URLToken {\n    return new URLToken(text, \"PORT\");\n  }\n\n  public static path(text: string): URLToken {\n    return new URLToken(text, \"PATH\");\n  }\n\n  public static query(text: string): URLToken {\n    return new URLToken(text, \"QUERY\");\n  }\n}\n\n/**\n * Get whether or not the provided character (single character string) is an alphanumeric (letter or\n * digit) character.\n */\nexport function isAlphaNumericCharacter(character: string): boolean {\n  const characterCode: number = character.charCodeAt(0);\n  return (\n    (48 /* '0' */ <= characterCode && characterCode <= 57) /* '9' */ ||\n    (65 /* 'A' */ <= characterCode && characterCode <= 90) /* 'Z' */ ||\n    (97 /* 'a' */ <= characterCode && characterCode <= 122) /* 'z' */\n  );\n}\n\n/**\n * A class that tokenizes URL strings.\n */\nexport class URLTokenizer {\n  readonly _textLength: number;\n  _currentState: URLTokenizerState;\n  _currentIndex: number;\n  _currentToken: URLToken | undefined;\n\n  public constructor(readonly _text: string, state?: URLTokenizerState) {\n    this._textLength = _text ? _text.length : 0;\n    this._currentState = state !== undefined && state !== null ? state : \"SCHEME_OR_HOST\";\n    this._currentIndex = 0;\n  }\n\n  /**\n   * Get the current URLToken this URLTokenizer is pointing at, or undefined if the URLTokenizer\n   * hasn't started or has finished tokenizing.\n   */\n  public current(): URLToken | undefined {\n    return this._currentToken;\n  }\n\n  /**\n   * Advance to the next URLToken and return whether or not a URLToken was found.\n   */\n  public next(): boolean {\n    if (!hasCurrentCharacter(this)) {\n      this._currentToken = undefined;\n    } else {\n      switch (this._currentState) {\n        case \"SCHEME\":\n          nextScheme(this);\n          break;\n\n        case \"SCHEME_OR_HOST\":\n          nextSchemeOrHost(this);\n          break;\n\n        case \"HOST\":\n          nextHost(this);\n          break;\n\n        case \"PORT\":\n          nextPort(this);\n          break;\n\n        case \"PATH\":\n          nextPath(this);\n          break;\n\n        case \"QUERY\":\n          nextQuery(this);\n          break;\n\n        default:\n          throw new Error(`Unrecognized URLTokenizerState: ${this._currentState}`);\n      }\n    }\n    return !!this._currentToken;\n  }\n}\n\n/**\n * Read the remaining characters from this Tokenizer's character stream.\n */\nfunction readRemaining(tokenizer: URLTokenizer): string {\n  let result = \"\";\n  if (tokenizer._currentIndex < tokenizer._textLength) {\n    result = tokenizer._text.substring(tokenizer._currentIndex);\n    tokenizer._currentIndex = tokenizer._textLength;\n  }\n  return result;\n}\n\n/**\n * Whether or not this URLTokenizer has a current character.\n */\nfunction hasCurrentCharacter(tokenizer: URLTokenizer): boolean {\n  return tokenizer._currentIndex < tokenizer._textLength;\n}\n\n/**\n * Get the character in the text string at the current index.\n */\nfunction getCurrentCharacter(tokenizer: URLTokenizer): string {\n  return tokenizer._text[tokenizer._currentIndex];\n}\n\n/**\n * Advance to the character in text that is \"step\" characters ahead. If no step value is provided,\n * then step will default to 1.\n */\nfunction nextCharacter(tokenizer: URLTokenizer, step?: number): void {\n  if (hasCurrentCharacter(tokenizer)) {\n    if (!step) {\n      step = 1;\n    }\n    tokenizer._currentIndex += step;\n  }\n}\n\n/**\n * Starting with the current character, peek \"charactersToPeek\" number of characters ahead in this\n * Tokenizer's stream of characters.\n */\nfunction peekCharacters(tokenizer: URLTokenizer, charactersToPeek: number): string {\n  let endIndex: number = tokenizer._currentIndex + charactersToPeek;\n  if (tokenizer._textLength < endIndex) {\n    endIndex = tokenizer._textLength;\n  }\n  return tokenizer._text.substring(tokenizer._currentIndex, endIndex);\n}\n\n/**\n * Read characters from this Tokenizer until the end of the stream or until the provided condition\n * is false when provided the current character.\n */\nfunction readWhile(tokenizer: URLTokenizer, condition: (character: string) => boolean): string {\n  let result = \"\";\n\n  while (hasCurrentCharacter(tokenizer)) {\n    const currentCharacter: string = getCurrentCharacter(tokenizer);\n    if (!condition(currentCharacter)) {\n      break;\n    } else {\n      result += currentCharacter;\n      nextCharacter(tokenizer);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Read characters from this Tokenizer until a non-alphanumeric character or the end of the\n * character stream is reached.\n */\nfunction readWhileLetterOrDigit(tokenizer: URLTokenizer): string {\n  return readWhile(tokenizer, (character: string) => isAlphaNumericCharacter(character));\n}\n\n/**\n * Read characters from this Tokenizer until one of the provided terminating characters is read or\n * the end of the character stream is reached.\n */\nfunction readUntilCharacter(tokenizer: URLTokenizer, ...terminatingCharacters: string[]): string {\n  return readWhile(\n    tokenizer,\n    (character: string) => terminatingCharacters.indexOf(character) === -1\n  );\n}\n\nfunction nextScheme(tokenizer: URLTokenizer): void {\n  const scheme: string = readWhileLetterOrDigit(tokenizer);\n  tokenizer._currentToken = URLToken.scheme(scheme);\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else {\n    tokenizer._currentState = \"HOST\";\n  }\n}\n\nfunction nextSchemeOrHost(tokenizer: URLTokenizer): void {\n  const schemeOrHost: string = readUntilCharacter(tokenizer, \":\", \"/\", \"?\");\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentToken = URLToken.host(schemeOrHost);\n    tokenizer._currentState = \"DONE\";\n  } else if (getCurrentCharacter(tokenizer) === \":\") {\n    if (peekCharacters(tokenizer, 3) === \"://\") {\n      tokenizer._currentToken = URLToken.scheme(schemeOrHost);\n      tokenizer._currentState = \"HOST\";\n    } else {\n      tokenizer._currentToken = URLToken.host(schemeOrHost);\n      tokenizer._currentState = \"PORT\";\n    }\n  } else {\n    tokenizer._currentToken = URLToken.host(schemeOrHost);\n    if (getCurrentCharacter(tokenizer) === \"/\") {\n      tokenizer._currentState = \"PATH\";\n    } else {\n      tokenizer._currentState = \"QUERY\";\n    }\n  }\n}\n\nfunction nextHost(tokenizer: URLTokenizer): void {\n  if (peekCharacters(tokenizer, 3) === \"://\") {\n    nextCharacter(tokenizer, 3);\n  }\n\n  const host: string = readUntilCharacter(tokenizer, \":\", \"/\", \"?\");\n  tokenizer._currentToken = URLToken.host(host);\n\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else if (getCurrentCharacter(tokenizer) === \":\") {\n    tokenizer._currentState = \"PORT\";\n  } else if (getCurrentCharacter(tokenizer) === \"/\") {\n    tokenizer._currentState = \"PATH\";\n  } else {\n    tokenizer._currentState = \"QUERY\";\n  }\n}\n\nfunction nextPort(tokenizer: URLTokenizer): void {\n  if (getCurrentCharacter(tokenizer) === \":\") {\n    nextCharacter(tokenizer);\n  }\n\n  const port: string = readUntilCharacter(tokenizer, \"/\", \"?\");\n  tokenizer._currentToken = URLToken.port(port);\n\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else if (getCurrentCharacter(tokenizer) === \"/\") {\n    tokenizer._currentState = \"PATH\";\n  } else {\n    tokenizer._currentState = \"QUERY\";\n  }\n}\n\nfunction nextPath(tokenizer: URLTokenizer): void {\n  const path: string = readUntilCharacter(tokenizer, \"?\");\n  tokenizer._currentToken = URLToken.path(path);\n\n  if (!hasCurrentCharacter(tokenizer)) {\n    tokenizer._currentState = \"DONE\";\n  } else {\n    tokenizer._currentState = \"QUERY\";\n  }\n}\n\nfunction nextQuery(tokenizer: URLTokenizer): void {\n  if (getCurrentCharacter(tokenizer) === \"?\") {\n    nextCharacter(tokenizer);\n  }\n\n  const query: string = readRemaining(tokenizer);\n  tokenizer._currentToken = URLToken.query(query);\n  tokenizer._currentState = \"DONE\";\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { URL<PERSON><PERSON><PERSON>, URLQuery } from \"../url\";\nimport { isObject, UnknownObject } from \"./utils\";\n\nexport interface SanitizerOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  allowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  allowedQueryParameters?: string[];\n}\n\nconst RedactedString = \"REDACTED\";\n\nconst defaultAllowedHeaderNames = [\n  \"x-ms-client-request-id\",\n  \"x-ms-return-client-request-id\",\n  \"x-ms-useragent\",\n  \"x-ms-correlation-request-id\",\n  \"x-ms-request-id\",\n  \"client-request-id\",\n  \"ms-cv\",\n  \"return-client-request-id\",\n  \"traceparent\",\n\n  \"Access-Control-Allow-Credentials\",\n  \"Access-Control-Allow-Headers\",\n  \"Access-Control-Allow-Methods\",\n  \"Access-Control-Allow-Origin\",\n  \"Access-Control-Expose-Headers\",\n  \"Access-Control-Max-Age\",\n  \"Access-Control-Request-Headers\",\n  \"Access-Control-Request-Method\",\n  \"Origin\",\n\n  \"Accept\",\n  \"Accept-Encoding\",\n  \"Cache-Control\",\n  \"Connection\",\n  \"Content-Length\",\n  \"Content-Type\",\n  \"Date\",\n  \"ETag\",\n  \"Expires\",\n  \"If-Match\",\n  \"If-Modified-Since\",\n  \"If-None-Match\",\n  \"If-Unmodified-Since\",\n  \"Last-Modified\",\n  \"Pragma\",\n  \"Request-Id\",\n  \"Retry-After\",\n  \"Server\",\n  \"Transfer-Encoding\",\n  \"User-Agent\"\n];\n\nconst defaultAllowedQueryParameters: string[] = [\"api-version\"];\n\nexport class Sanitizer {\n  public allowedHeaderNames: Set<string>;\n  public allowedQueryParameters: Set<string>;\n\n  constructor({ allowedHeaderNames = [], allowedQueryParameters = [] }: SanitizerOptions = {}) {\n    allowedHeaderNames = Array.isArray(allowedHeaderNames)\n      ? defaultAllowedHeaderNames.concat(allowedHeaderNames)\n      : defaultAllowedHeaderNames;\n\n    allowedQueryParameters = Array.isArray(allowedQueryParameters)\n      ? defaultAllowedQueryParameters.concat(allowedQueryParameters)\n      : defaultAllowedQueryParameters;\n\n    this.allowedHeaderNames = new Set(allowedHeaderNames.map((n) => n.toLowerCase()));\n    this.allowedQueryParameters = new Set(allowedQueryParameters.map((p) => p.toLowerCase()));\n  }\n\n  public sanitize(obj: unknown): string {\n    const seen = new Set<unknown>();\n    return JSON.stringify(\n      obj,\n      (key: string, value: unknown) => {\n        // Ensure Errors include their interesting non-enumerable members\n        if (value instanceof Error) {\n          return {\n            ...value,\n            name: value.name,\n            message: value.message\n          };\n        }\n\n        if (key === \"_headersMap\") {\n          return this.sanitizeHeaders(value as UnknownObject);\n        } else if (key === \"url\") {\n          return this.sanitizeUrl(value as string);\n        } else if (key === \"query\") {\n          return this.sanitizeQuery(value as UnknownObject);\n        } else if (key === \"body\") {\n          // Don't log the request body\n          return undefined;\n        } else if (key === \"response\") {\n          // Don't log response again\n          return undefined;\n        } else if (key === \"operationSpec\") {\n          // When using sendOperationRequest, the request carries a massive\n          // field with the autorest spec. No need to log it.\n          return undefined;\n        } else if (Array.isArray(value) || isObject(value)) {\n          if (seen.has(value)) {\n            return \"[Circular]\";\n          }\n          seen.add(value);\n        }\n\n        return value;\n      },\n      2\n    );\n  }\n\n  private sanitizeHeaders(value: UnknownObject): UnknownObject {\n    return this.sanitizeObject(value, this.allowedHeaderNames, (v, k) => v[k].value);\n  }\n\n  private sanitizeQuery(value: UnknownObject): UnknownObject {\n    return this.sanitizeObject(value, this.allowedQueryParameters, (v, k) => v[k]);\n  }\n\n  private sanitizeObject(\n    value: UnknownObject,\n    allowedKeys: Set<string>,\n    accessor: (value: any, key: string) => any\n  ): UnknownObject {\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n\n    const sanitized: UnknownObject = {};\n\n    for (const k of Object.keys(value)) {\n      if (allowedKeys.has(k.toLowerCase())) {\n        sanitized[k] = accessor(value, k);\n      } else {\n        sanitized[k] = RedactedString;\n      }\n    }\n\n    return sanitized;\n  }\n\n  private sanitizeUrl(value: string): string {\n    if (typeof value !== \"string\" || value === null) {\n      return value;\n    }\n\n    const urlBuilder = URLBuilder.parse(value);\n    const queryString = urlBuilder.getQuery();\n\n    if (!queryString) {\n      return value;\n    }\n\n    const query = URLQuery.parse(queryString);\n    for (const k of query.keys()) {\n      if (!this.allowedQueryParameters.has(k.toLowerCase())) {\n        query.set(k, RedactedString);\n      }\n    }\n\n    urlBuilder.setQuery(query.toString());\n    return urlBuilder.toString();\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { WebResourceLike } from \"./webResource\";\nimport { custom } from \"./util/inspect\";\nimport { Sanitizer } from \"./util/sanitizer\";\n\nconst errorSanitizer = new Sanitizer();\n\nexport class RestError extends Error {\n  static readonly REQUEST_SEND_ERROR: string = \"REQUEST_SEND_ERROR\";\n  static readonly PARSE_ERROR: string = \"PARSE_ERROR\";\n\n  code?: string;\n  statusCode?: number;\n  request?: WebResourceLike;\n  response?: HttpOperationResponse;\n  details?: unknown;\n  constructor(\n    message: string,\n    code?: string,\n    statusCode?: number,\n    request?: WebResourceLike,\n    response?: HttpOperationResponse\n  ) {\n    super(message);\n    this.name = \"RestError\";\n    this.code = code;\n    this.statusCode = statusCode;\n    this.request = request;\n    this.response = response;\n\n    Object.setPrototypeOf(this, RestError.prototype);\n  }\n\n  /**\n   * Logging method for util.inspect in Node\n   */\n  [custom](): string {\n    return `RestError: ${this.message} \\n ${errorSanitizer.sanitize(this)}`;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\nimport { createClientLogger } from \"@azure/logger\";\nexport const logger = createClientLogger(\"core-http\");\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Abort<PERSON>ontroller, AbortError } from \"@azure/abort-controller\";\nimport FormData from \"form-data\";\n\nimport { HttpClient } from \"./httpClient\";\nimport { TransferProgressEvent, WebResourceLike } from \"./webResource\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { HttpHeaders, HttpHeadersLike } from \"./httpHeaders\";\nimport { RestError } from \"./restError\";\nimport { Readable, Transform } from \"stream\";\nimport { logger } from \"./log\";\n\ninterface FetchError extends Error {\n  code?: string;\n  errno?: string;\n  type?: string;\n}\n\nexport type CommonRequestInfo = string; // We only ever call fetch() on string urls.\n\nexport type CommonRequestInit = Omit<RequestInit, \"body\" | \"headers\" | \"signal\"> & {\n  body?: any;\n  headers?: any;\n  signal?: any;\n};\n\nexport type CommonResponse = Omit<Response, \"body\" | \"trailer\" | \"formData\"> & {\n  body: any;\n  trailer: any;\n  formData: any;\n};\n\nexport class ReportTransform extends Transform {\n  private loadedBytes: number = 0;\n  _transform(chunk: string | Buffer, _encoding: string, callback: (arg: any) => void): void {\n    this.push(chunk);\n    this.loadedBytes += chunk.length;\n    this.progressCallback!({ loadedBytes: this.loadedBytes });\n    callback(undefined);\n  }\n\n  constructor(private progressCallback: (progress: TransferProgressEvent) => void) {\n    super();\n  }\n}\n\nexport abstract class FetchHttpClient implements HttpClient {\n  async sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!httpRequest && typeof httpRequest !== \"object\") {\n      throw new Error(\n        \"'httpRequest' (WebResourceLike) cannot be null or undefined and must be of type object.\"\n      );\n    }\n\n    const abortController = new AbortController();\n    let abortListener: ((event: any) => void) | undefined;\n    if (httpRequest.abortSignal) {\n      if (httpRequest.abortSignal.aborted) {\n        throw new AbortError(\"The operation was aborted.\");\n      }\n\n      abortListener = (event: Event) => {\n        if (event.type === \"abort\") {\n          abortController.abort();\n        }\n      };\n      httpRequest.abortSignal.addEventListener(\"abort\", abortListener);\n    }\n\n    if (httpRequest.timeout) {\n      setTimeout(() => {\n        abortController.abort();\n      }, httpRequest.timeout);\n    }\n\n    if (httpRequest.formData) {\n      const formData: any = httpRequest.formData;\n      const requestForm = new FormData();\n      const appendFormValue = (key: string, value: any): void => {\n        // value function probably returns a stream so we can provide a fresh stream on each retry\n        if (typeof value === \"function\") {\n          value = value();\n        }\n        if (\n          value &&\n          Object.prototype.hasOwnProperty.call(value, \"value\") &&\n          Object.prototype.hasOwnProperty.call(value, \"options\")\n        ) {\n          requestForm.append(key, value.value, value.options);\n        } else {\n          requestForm.append(key, value);\n        }\n      };\n      for (const formKey of Object.keys(formData)) {\n        const formValue = formData[formKey];\n        if (Array.isArray(formValue)) {\n          for (let j = 0; j < formValue.length; j++) {\n            appendFormValue(formKey, formValue[j]);\n          }\n        } else {\n          appendFormValue(formKey, formValue);\n        }\n      }\n\n      httpRequest.body = requestForm;\n      httpRequest.formData = undefined;\n      const contentType = httpRequest.headers.get(\"Content-Type\");\n      if (contentType && contentType.indexOf(\"multipart/form-data\") !== -1) {\n        if (typeof requestForm.getBoundary === \"function\") {\n          httpRequest.headers.set(\n            \"Content-Type\",\n            `multipart/form-data; boundary=${requestForm.getBoundary()}`\n          );\n        } else {\n          // browser will automatically apply a suitable content-type header\n          httpRequest.headers.remove(\"Content-Type\");\n        }\n      }\n    }\n\n    let body = httpRequest.body\n      ? typeof httpRequest.body === \"function\"\n        ? httpRequest.body()\n        : httpRequest.body\n      : undefined;\n    if (httpRequest.onUploadProgress && httpRequest.body) {\n      const onUploadProgress = httpRequest.onUploadProgress;\n      const uploadReportStream = new ReportTransform(onUploadProgress);\n      if (isReadableStream(body)) {\n        body.pipe(uploadReportStream);\n      } else {\n        uploadReportStream.end(body);\n      }\n\n      body = uploadReportStream;\n    }\n\n    const platformSpecificRequestInit: Partial<RequestInit> = await this.prepareRequest(\n      httpRequest\n    );\n\n    const requestInit: RequestInit = {\n      body: body,\n      headers: httpRequest.headers.rawHeaders(),\n      method: httpRequest.method,\n      signal: abortController.signal,\n      redirect: \"manual\",\n      ...platformSpecificRequestInit\n    };\n\n    let operationResponse: HttpOperationResponse | undefined;\n    try {\n      const response: CommonResponse = await this.fetch(httpRequest.url, requestInit);\n\n      const headers = parseHeaders(response.headers);\n\n      const streaming =\n        httpRequest.streamResponseStatusCodes?.has(response.status) ||\n        httpRequest.streamResponseBody;\n\n      operationResponse = {\n        headers: headers,\n        request: httpRequest,\n        status: response.status,\n        readableStreamBody: streaming\n          ? ((response.body as unknown) as NodeJS.ReadableStream)\n          : undefined,\n        bodyAsText: !streaming ? await response.text() : undefined\n      };\n\n      const onDownloadProgress = httpRequest.onDownloadProgress;\n      if (onDownloadProgress) {\n        const responseBody: ReadableStream<Uint8Array> | undefined = response.body || undefined;\n\n        if (isReadableStream(responseBody)) {\n          const downloadReportStream = new ReportTransform(onDownloadProgress);\n          responseBody.pipe(downloadReportStream);\n          operationResponse.readableStreamBody = downloadReportStream;\n        } else {\n          const length = parseInt(headers.get(\"Content-Length\")!) || undefined;\n          if (length) {\n            // Calling callback for non-stream response for consistency with browser\n            onDownloadProgress({ loadedBytes: length });\n          }\n        }\n      }\n\n      await this.processRequest(operationResponse);\n\n      return operationResponse;\n    } catch (error) {\n      const fetchError: FetchError = error;\n      if (fetchError.code === \"ENOTFOUND\") {\n        throw new RestError(\n          fetchError.message,\n          RestError.REQUEST_SEND_ERROR,\n          undefined,\n          httpRequest\n        );\n      } else if (fetchError.type === \"aborted\") {\n        throw new AbortError(\"The operation was aborted.\");\n      }\n\n      throw fetchError;\n    } finally {\n      // clean up event listener\n      if (httpRequest.abortSignal && abortListener) {\n        let uploadStreamDone = Promise.resolve();\n        if (isReadableStream(body)) {\n          uploadStreamDone = isStreamComplete(body);\n        }\n        let downloadStreamDone = Promise.resolve();\n        if (isReadableStream(operationResponse?.readableStreamBody)) {\n          downloadStreamDone = isStreamComplete(\n            operationResponse!.readableStreamBody,\n            abortController\n          );\n        }\n\n        Promise.all([uploadStreamDone, downloadStreamDone])\n          .then(() => {\n            httpRequest.abortSignal?.removeEventListener(\"abort\", abortListener!);\n            return;\n          })\n          .catch((e) => {\n            logger.warning(\"Error when cleaning up abortListener on httpRequest\", e);\n          });\n      }\n    }\n  }\n\n  abstract prepareRequest(httpRequest: WebResourceLike): Promise<Partial<RequestInit>>;\n  abstract processRequest(operationResponse: HttpOperationResponse): Promise<void>;\n  abstract fetch(input: CommonRequestInfo, init?: CommonRequestInit): Promise<CommonResponse>;\n}\n\nfunction isReadableStream(body: any): body is Readable {\n  return body && typeof body.pipe === \"function\";\n}\n\nfunction isStreamComplete(stream: Readable, aborter?: AbortController): Promise<void> {\n  return new Promise((resolve) => {\n    stream.once(\"close\", () => {\n      aborter?.abort();\n      resolve();\n    });\n    stream.once(\"end\", resolve);\n    stream.once(\"error\", resolve);\n  });\n}\n\nexport function parseHeaders(headers: Headers): HttpHeadersLike {\n  const httpHeaders = new HttpHeaders();\n\n  headers.forEach((value, key) => {\n    httpHeaders.set(key, value);\n  });\n\n  return httpHeaders;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport * as tunnel from \"tunnel\";\n\nimport { ProxySettings } from \"./serviceClient\";\nimport { URLBuilder } from \"./url\";\nimport { HttpHeadersLike } from \"./httpHeaders\";\n\nexport type ProxyAgent = { isHttps: boolean; agent: http.Agent | https.Agent };\nexport function createProxyAgent(\n  requestUrl: string,\n  proxySettings: ProxySettings,\n  headers?: HttpHeadersLike\n): ProxyAgent {\n  const host = URLBuilder.parse(proxySettings.host).getHost() as string;\n  if (!host) {\n    throw new Error(\"Expecting a non-empty host in proxy settings.\");\n  }\n  if (!isValidPort(proxySettings.port)) {\n    throw new Error(\"Expecting a valid port number in the range of [0, 65535] in proxy settings.\");\n  }\n  const tunnelOptions: tunnel.HttpsOverHttpsOptions = {\n    proxy: {\n      host: host,\n      port: proxySettings.port,\n      headers: (headers && headers.rawHeaders()) || {}\n    }\n  };\n\n  if (proxySettings.username && proxySettings.password) {\n    tunnelOptions.proxy!.proxyAuth = `${proxySettings.username}:${proxySettings.password}`;\n  } else if (proxySettings.username) {\n    tunnelOptions.proxy!.proxyAuth = `${proxySettings.username}`;\n  }\n\n  const isRequestHttps = isUrlHttps(requestUrl);\n  const isProxyHttps = isUrlHttps(proxySettings.host);\n\n  const proxyAgent = {\n    isHttps: isRequestHttps,\n    agent: createTunnel(isRequestHttps, isProxyHttps, tunnelOptions)\n  };\n\n  return proxyAgent;\n}\n\nexport function isUrlHttps(url: string): boolean {\n  const urlScheme = URLBuilder.parse(url).getScheme() || \"\";\n  return urlScheme.toLowerCase() === \"https\";\n}\n\nexport function createTunnel(\n  isRequestHttps: boolean,\n  isProxyHttps: boolean,\n  tunnelOptions: tunnel.HttpsOverHttpsOptions\n): http.Agent | https.Agent {\n  if (isRequestHttps && isProxyHttps) {\n    return tunnel.httpsOverHttps(tunnelOptions);\n  } else if (isRequestHttps && !isProxyHttps) {\n    return tunnel.httpsOverHttp(tunnelOptions);\n  } else if (!isRequestHttps && isProxyHttps) {\n    return tunnel.httpOverHttps(tunnelOptions);\n  } else {\n    return tunnel.httpOverHttp(tunnelOptions);\n  }\n}\n\nfunction isValidPort(port: number): boolean {\n  // any port in 0-65535 range is valid (RFC 793) even though almost all implementations\n  // will reserve 0 for a specific purpose, and a range of numbers for ephemeral ports\n  return 0 <= port && port <= 65535;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as tough from \"tough-cookie\";\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport node_fetch from \"node-fetch\";\n\nimport {\n  FetchHttpClient,\n  CommonRequestInfo,\n  CommonRequestInit,\n  CommonResponse\n} from \"./fetchHttpClient\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { WebResourceLike } from \"./webResource\";\nimport { createProxyAgent, ProxyAgent, isUrlHttps } from \"./proxyAgent\";\n\ninterface AgentCache {\n  httpAgent?: http.Agent;\n  httpsAgent?: https.Agent;\n}\n\nfunction getCachedAgent(\n  isHttps: boolean,\n  agentCache: AgentCache\n): http.Agent | https.Agent | undefined {\n  return isHttps ? agentCache.httpsAgent : agentCache.httpAgent;\n}\n\nexport class NodeFetchHttpClient extends FetchHttpClient {\n  // a mapping of proxy settings string `${host}:${port}:${username}:${password}` to agent\n  private proxyAgentMap: Map<string, AgentCache> = new Map();\n  private keepAliveAgents: AgentCache = {};\n\n  private readonly cookieJar = new tough.CookieJar(undefined, { looseMode: true });\n\n  private getOrCreateAgent(httpRequest: WebResourceLike): http.Agent | https.Agent {\n    const isHttps = isUrlHttps(httpRequest.url);\n\n    // At the moment, proxy settings and keepAlive are mutually\n    // exclusive because the 'tunnel' library currently lacks the\n    // ability to create a proxy with keepAlive turned on.\n    if (httpRequest.proxySettings) {\n      const { host, port, username, password } = httpRequest.proxySettings;\n      const key = `${host}:${port}:${username}:${password}`;\n      const proxyAgents = this.proxyAgentMap.get(key) ?? {};\n\n      let agent = getCachedAgent(isHttps, proxyAgents);\n      if (agent) {\n        return agent;\n      }\n\n      const tunnel: ProxyAgent = createProxyAgent(\n        httpRequest.url,\n        httpRequest.proxySettings,\n        httpRequest.headers\n      );\n\n      agent = tunnel.agent;\n      if (tunnel.isHttps) {\n        proxyAgents.httpsAgent = tunnel.agent as https.Agent;\n      } else {\n        proxyAgents.httpAgent = tunnel.agent;\n      }\n      this.proxyAgentMap.set(key, proxyAgents);\n\n      return agent;\n    } else if (httpRequest.keepAlive) {\n      let agent = getCachedAgent(isHttps, this.keepAliveAgents);\n      if (agent) {\n        return agent;\n      }\n\n      const agentOptions: http.AgentOptions | https.AgentOptions = {\n        keepAlive: httpRequest.keepAlive\n      };\n\n      if (isHttps) {\n        agent = this.keepAliveAgents.httpsAgent = new https.Agent(agentOptions);\n      } else {\n        agent = this.keepAliveAgents.httpAgent = new http.Agent(agentOptions);\n      }\n\n      return agent;\n    } else {\n      return isHttps ? https.globalAgent : http.globalAgent;\n    }\n  }\n\n  // eslint-disable-next-line @azure/azure-sdk/ts-apisurface-standardized-verbs\n  async fetch(input: CommonRequestInfo, init?: CommonRequestInit): Promise<CommonResponse> {\n    return (node_fetch(input, init) as unknown) as Promise<CommonResponse>;\n  }\n\n  async prepareRequest(httpRequest: WebResourceLike): Promise<Partial<RequestInit>> {\n    const requestInit: Partial<RequestInit & { agent?: any; compress?: boolean }> = {};\n\n    if (this.cookieJar && !httpRequest.headers.get(\"Cookie\")) {\n      const cookieString = await new Promise<string>((resolve, reject) => {\n        this.cookieJar!.getCookieString(httpRequest.url, (err, cookie) => {\n          if (err) {\n            reject(err);\n          } else {\n            resolve(cookie);\n          }\n        });\n      });\n\n      httpRequest.headers.set(\"Cookie\", cookieString);\n    }\n\n    // Set the http(s) agent\n    requestInit.agent = this.getOrCreateAgent(httpRequest);\n\n    requestInit.compress = httpRequest.decompressResponse;\n\n    return requestInit;\n  }\n\n  async processRequest(operationResponse: HttpOperationResponse): Promise<void> {\n    if (this.cookieJar) {\n      const setCookieHeader = operationResponse.headers.get(\"Set-Cookie\");\n      if (setCookieHeader !== undefined) {\n        await new Promise<void>((resolve, reject) => {\n          this.cookieJar!.setCookie(\n            setCookieHeader,\n            operationResponse.request.url,\n            { ignoreError: true },\n            (err) => {\n              if (err) {\n                reject(err);\n              } else {\n                resolve();\n              }\n            }\n          );\n        });\n      }\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The different levels of logs that can be used with the HttpPipelineLogger.\n */\nexport enum HttpPipelineLogLevel {\n  /**\n   * A log level that indicates that no logs will be logged.\n   */\n  OFF,\n\n  /**\n   * An error log.\n   */\n  ERROR,\n\n  /**\n   * A warning log.\n   */\n  WARNING,\n\n  /**\n   * An information log.\n   */\n  INFO\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { OperationTracingOptions } from \"@azure/core-tracing\";\nimport { TransferProgressEvent, RequestOptionsBase } from \"./webResource\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\n\n/**\n * The base options type for all operations.\n */\nexport interface OperationOptions {\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Options used when creating and sending HTTP requests for this operation.\n   */\n  requestOptions?: OperationRequestOptions;\n  /**\n   * Options used when tracing is enabled.\n   */\n  tracingOptions?: OperationTracingOptions;\n}\n\nexport interface OperationRequestOptions {\n  /**\n   * User defined custom request headers that will be applied before the request is sent.\n   */\n  customHeaders?: { [key: string]: string };\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n  /**\n   * Whether or not the HttpOperationResponse should be deserialized. If this is undefined, then the\n   * HttpOperationResponse should be deserialized.\n   */\n  shouldDeserialize?: boolean | ((response: HttpOperationResponse) => boolean);\n}\n\n/**\n * Converts an OperationOptions to a RequestOptionsBase\n *\n * @param opts - OperationOptions object to convert to RequestOptionsBase\n */\nexport function operationOptionsToRequestOptionsBase<T extends OperationOptions>(\n  opts: T\n): RequestOptionsBase {\n  const { requestOptions, tracingOptions, ...additionalOptions } = opts;\n\n  let result: RequestOptionsBase = additionalOptions;\n\n  if (requestOptions) {\n    result = { ...result, ...requestOptions };\n  }\n\n  if (tracingOptions) {\n    result.tracingContext = tracingOptions.tracingContext;\n    // By passing spanOptions if they exist at runtime, we're backwards compatible with @azure/core-tracing@preview.13 and earlier.\n    result.spanOptions = (tracingOptions as any)?.spanOptions;\n  }\n\n  return result;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { HttpPipelineLogger } from \"../httpPipelineLogger\";\nimport { HttpPipelineLogLevel } from \"../httpPipelineLogLevel\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Creates a new RequestPolicy per-request that uses the provided nextPolicy.\n */\nexport type RequestPolicyFactory = {\n  create(nextPolicy: RequestPolicy, options: RequestPolicyOptionsLike): RequestPolicy;\n};\n\nexport interface RequestPolicy {\n  sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse>;\n}\n\nexport abstract class BaseRequestPolicy implements RequestPolicy {\n  protected constructor(\n    readonly _nextPolicy: RequestPolicy,\n    readonly _options: RequestPolicyOptionsLike\n  ) {}\n\n  public abstract sendRequest(webResource: WebResourceLike): Promise<HttpOperationResponse>;\n\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  public shouldLog(logLevel: HttpPipelineLogLevel): boolean {\n    return this._options.shouldLog(logLevel);\n  }\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meat the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  public log(logLevel: HttpPipelineLogLevel, message: string): void {\n    this._options.log(logLevel, message);\n  }\n}\n\n/**\n * Optional properties that can be used when creating a RequestPolicy.\n */\nexport interface RequestPolicyOptionsLike {\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  shouldLog(logLevel: HttpPipelineLogLevel): boolean;\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meet the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  log(logLevel: HttpPipelineLogLevel, message: string): void;\n}\n\n/**\n * Optional properties that can be used when creating a RequestPolicy.\n */\nexport class RequestPolicyOptions {\n  constructor(private _logger?: HttpPipelineLogger) {}\n\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  public shouldLog(logLevel: HttpPipelineLogLevel): boolean {\n    return (\n      !!this._logger &&\n      logLevel !== HttpPipelineLogLevel.OFF &&\n      logLevel <= this._logger.minimumLogLevel\n    );\n  }\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meet the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  public log(logLevel: HttpPipelineLogLevel, message: string): void {\n    if (this._logger && this.shouldLog(logLevel)) {\n      this._logger.log(logLevel, message);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\nimport { Debugger } from \"@azure/logger\";\nimport { logger as coreLogger } from \"../log\";\nimport { Sanitizer } from \"../util/sanitizer\";\n\nexport interface LogPolicyOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  allowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  allowedQueryParameters?: string[];\n\n  /**\n   * The Debugger (logger) instance to use for writing pipeline logs.\n   */\n  logger?: Debugger;\n}\n\nexport function logPolicy(loggingOptions: LogPolicyOptions = {}): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new LogPolicy(nextPolicy, options, loggingOptions);\n    }\n  };\n}\n\nexport class LogPolicy extends BaseRequestPolicy {\n  logger: Debugger;\n  sanitizer: Sanitizer;\n\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   * @deprecated Pass these into the constructor instead.\n   */\n  public get allowedHeaderNames(): Set<string> {\n    return this.sanitizer.allowedHeaderNames;\n  }\n\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   * @deprecated Pass these into the constructor instead.\n   */\n  public set allowedHeaderNames(allowedHeaderNames: Set<string>) {\n    this.sanitizer.allowedHeaderNames = allowedHeaderNames;\n  }\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   * @deprecated Pass these into the constructor instead.\n   */\n  public get allowedQueryParameters(): Set<string> {\n    return this.sanitizer.allowedQueryParameters;\n  }\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   * @deprecated Pass these into the constructor instead.\n   */\n  public set allowedQueryParameters(allowedQueryParameters: Set<string>) {\n    this.sanitizer.allowedQueryParameters = allowedQueryParameters;\n  }\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    {\n      logger = coreLogger.info,\n      allowedHeaderNames = [],\n      allowedQueryParameters = []\n    }: LogPolicyOptions = {}\n  ) {\n    super(nextPolicy, options);\n    this.logger = logger;\n    this.sanitizer = new Sanitizer({ allowedHeaderNames, allowedQueryParameters });\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!this.logger.enabled) return this._nextPolicy.sendRequest(request);\n\n    this.logRequest(request);\n    return this._nextPolicy.sendRequest(request).then((response) => this.logResponse(response));\n  }\n\n  private logRequest(request: WebResourceLike): void {\n    this.logger(`Request: ${this.sanitizer.sanitize(request)}`);\n  }\n\n  private logResponse(response: HttpOperationResponse): HttpOperationResponse {\n    this.logger(`Response status code: ${response.status}`);\n    this.logger(`Headers: ${this.sanitizer.sanitize(response.headers)}`);\n    return response;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { QueryCollectionFormat } from \"./queryCollectionFormat\";\nimport { Mapper } from \"./serializer\";\n\nexport type ParameterPath = string | string[] | { [propertyName: string]: ParameterPath };\n\n/**\n * A common interface that all Operation parameter's extend.\n */\nexport interface OperationParameter {\n  /**\n   * The path to this parameter's value in OperationArguments or the object that contains paths for\n   * each property's value in OperationArguments.\n   */\n  parameterPath: ParameterPath;\n\n  /**\n   * The mapper that defines how to validate and serialize this parameter's value.\n   */\n  mapper: Mapper;\n}\n\n/**\n * A parameter for an operation that will be substituted into the operation's request URL.\n */\nexport interface OperationURLParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the URL parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n}\n\n/**\n * A parameter for an operation that will be added as a query parameter to the operation's HTTP\n * request.\n */\nexport interface OperationQueryParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the query parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n\n  /**\n   * If this query parameter's value is a collection, what type of format should the value be\n   * converted to.\n   */\n  collectionFormat?: QueryCollectionFormat;\n}\n\n/**\n * Get the path to this parameter's value as a dotted string (a.b.c).\n * @param parameter - The parameter to get the path string for.\n * @returns The path to this parameter's value as a dotted string.\n */\nexport function getPathStringFromParameter(parameter: OperationParameter): string {\n  return getPathStringFromParameterPath(parameter.parameterPath, parameter.mapper);\n}\n\nexport function getPathStringFromParameterPath(\n  parameterPath: ParameterPath,\n  mapper: Mapper\n): string {\n  let result: string;\n  if (typeof parameterPath === \"string\") {\n    result = parameterPath;\n  } else if (Array.isArray(parameterPath)) {\n    result = parameterPath.join(\".\");\n  } else {\n    result = mapper.serializedName!;\n  }\n  return result;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  OperationParameter,\n  OperationQueryParameter,\n  OperationURLParameter\n} from \"./operationParameter\";\nimport { OperationResponse } from \"./operationResponse\";\nimport { MapperType, Serializer } from \"./serializer\";\nimport { HttpMethods } from \"./webResource\";\n\n/**\n * A specification that defines an operation.\n */\nexport interface OperationSpec {\n  /**\n   * The serializer to use in this operation.\n   */\n  readonly serializer: Serializer;\n\n  /**\n   * The HTTP method that should be used by requests for this operation.\n   */\n  readonly httpMethod: HttpMethods;\n\n  /**\n   * The URL that was provided in the service's specification. This will still have all of the URL\n   * template variables in it. If this is not provided when the OperationSpec is created, then it\n   * will be populated by a \"baseUri\" property on the ServiceClient.\n   */\n  readonly baseUrl?: string;\n\n  /**\n   * The fixed path for this operation's URL. This will still have all of the URL template variables\n   * in it.\n   */\n  readonly path?: string;\n\n  /**\n   * The content type of the request body. This value will be used as the \"Content-Type\" header if\n   * it is provided.\n   */\n  readonly contentType?: string;\n\n  /**\n   * The media type of the request body.\n   * This value can be used to aide in serialization if it is provided.\n   */\n  readonly mediaType?:\n    | \"json\"\n    | \"xml\"\n    | \"form\"\n    | \"binary\"\n    | \"multipart\"\n    | \"text\"\n    | \"unknown\"\n    | string;\n  /**\n   * The parameter that will be used to construct the HTTP request's body.\n   */\n  readonly requestBody?: OperationParameter;\n\n  /**\n   * Whether or not this operation uses XML request and response bodies.\n   */\n  readonly isXML?: boolean;\n\n  /**\n   * The parameters to the operation method that will be substituted into the constructed URL.\n   */\n  readonly urlParameters?: ReadonlyArray<OperationURLParameter>;\n\n  /**\n   * The parameters to the operation method that will be added to the constructed URL's query.\n   */\n  readonly queryParameters?: ReadonlyArray<OperationQueryParameter>;\n\n  /**\n   * The parameters to the operation method that will be converted to headers on the operation's\n   * HTTP request.\n   */\n  readonly headerParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The parameters to the operation method that will be used to create a formdata body for the\n   * operation's HTTP request.\n   */\n  readonly formDataParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The different types of responses that this operation can return based on what status code is\n   * returned.\n   */\n  readonly responses: { [responseCode: string]: OperationResponse };\n}\n\n/**\n * Gets the list of status codes for streaming responses.\n * @internal\n */\nexport function getStreamResponseStatusCodes(operationSpec: OperationSpec): Set<number> {\n  const result = new Set<number>();\n  for (const statusCode in operationSpec.responses) {\n    const operationResponse = operationSpec.responses[statusCode];\n    if (\n      operationResponse.bodyMapper &&\n      operationResponse.bodyMapper.type.name === MapperType.Stream\n    ) {\n      result.add(Number(statusCode));\n    }\n  }\n  return result;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as xml2js from \"xml2js\";\nimport { XML_ATTRKEY, XML_CHARKEY, SerializerOptions } from \"./serializer.common\";\n\n// Note: The reason we re-define all of the xml2js default settings (version 2.0) here is because the default settings object exposed\n// by the xm2js library is mutable. See https://github.com/Leon<PERSON>-from-XIV/node-xml2js/issues/536\n// By creating a new copy of the settings each time we instantiate the parser,\n// we are safeguarding against the possibility of the default settings being mutated elsewhere unintentionally.\nconst xml2jsDefaultOptionsV2: xml2js.OptionsV2 = {\n  explicitCharkey: false,\n  trim: false,\n  normalize: false,\n  normalizeTags: false,\n  attrkey: XML_ATTRKEY,\n  explicitArray: true,\n  ignoreAttrs: false,\n  mergeAttrs: false,\n  explicitRoot: true,\n  validator: undefined,\n  xmlns: false,\n  explicitChildren: false,\n  preserveChildrenOrder: false,\n  childkey: \"$$\",\n  charsAsChildren: false,\n  includeWhiteChars: false,\n  async: false,\n  strict: true,\n  attrNameProcessors: undefined,\n  attrValueProcessors: undefined,\n  tagNameProcessors: undefined,\n  valueProcessors: undefined,\n  rootName: \"root\",\n  xmldec: {\n    version: \"1.0\",\n    encoding: \"UTF-8\",\n    standalone: true\n  },\n  doctype: undefined,\n  renderOpts: {\n    pretty: true,\n    indent: \"  \",\n    newline: \"\\n\"\n  },\n  headless: false,\n  chunkSize: 10000,\n  emptyTag: \"\",\n  cdata: false\n};\n\n// The xml2js settings for general XML parsing operations.\nconst xml2jsParserSettings: any = Object.assign({}, xml2jsDefaultOptionsV2);\nxml2jsParserSettings.explicitArray = false;\n\n// The xml2js settings for general XML building operations.\nconst xml2jsBuilderSettings: any = Object.assign({}, xml2jsDefaultOptionsV2);\nxml2jsBuilderSettings.explicitArray = false;\nxml2jsBuilderSettings.renderOpts = {\n  pretty: false\n};\n\n/**\n * Converts given JSON object to XML string\n * @param obj - JSON object to be converted into XML string\n * @param opts - Options that govern the parsing of given JSON object\n */\nexport function stringifyXML(obj: unknown, opts: SerializerOptions = {}): string {\n  xml2jsBuilderSettings.rootName = opts.rootName;\n  xml2jsBuilderSettings.charkey = opts.xmlCharKey ?? XML_CHARKEY;\n  const builder = new xml2js.Builder(xml2jsBuilderSettings);\n  return builder.buildObject(obj);\n}\n\n/**\n * Converts given XML string into JSON\n * @param str - String containing the XML content to be parsed into JSON\n * @param opts - Options that govern the parsing of given xml string\n */\nexport function parseXML(str: string, opts: SerializerOptions = {}): Promise<any> {\n  xml2jsParserSettings.explicitRoot = !!opts.includeRoot;\n  xml2jsParserSettings.charkey = opts.xmlCharKey ?? XML_CHARKEY;\n  const xmlParser = new xml2js.Parser(xml2jsParserSettings);\n  return new Promise((resolve, reject) => {\n    if (!str) {\n      reject(new Error(\"Document is empty\"));\n    } else {\n      xmlParser.parseString(str, (err?: Error, res?: any) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(res);\n        }\n      });\n    }\n  });\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { OperationResponse } from \"../operationResponse\";\nimport { OperationSpec } from \"../operationSpec\";\nimport { RestError } from \"../restError\";\nimport { MapperType } from \"../serializer\";\nimport { parseXML } from \"../util/xml\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\nimport { XML_CHARKEY, SerializerOptions } from \"../util/serializer.common\";\n\n/**\n * Options to configure API response deserialization.\n */\nexport interface DeserializationOptions {\n  /**\n   * Configures the expected content types for the deserialization of\n   * JSON and XML response bodies.\n   */\n  expectedContentTypes: DeserializationContentTypes;\n}\n\n/**\n * The content-types that will indicate that an operation response should be deserialized in a\n * particular way.\n */\nexport interface DeserializationContentTypes {\n  /**\n   * The content-types that indicate that an operation response should be deserialized as JSON.\n   * Defaults to [ \"application/json\", \"text/json\" ].\n   */\n  json?: string[];\n\n  /**\n   * The content-types that indicate that an operation response should be deserialized as XML.\n   * Defaults to [ \"application/xml\", \"application/atom+xml\" ].\n   */\n  xml?: string[];\n}\n\n/**\n * Create a new serialization RequestPolicyCreator that will serialized HTTP request bodies as they\n * pass through the HTTP pipeline.\n */\nexport function deserializationPolicy(\n  deserializationContentTypes?: DeserializationContentTypes,\n  parsingOptions?: SerializerOptions\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new DeserializationPolicy(\n        nextPolicy,\n        options,\n        deserializationContentTypes,\n        parsingOptions\n      );\n    }\n  };\n}\n\nexport const defaultJsonContentTypes = [\"application/json\", \"text/json\"];\nexport const defaultXmlContentTypes = [\"application/xml\", \"application/atom+xml\"];\n\nexport const DefaultDeserializationOptions: DeserializationOptions = {\n  expectedContentTypes: {\n    json: defaultJsonContentTypes,\n    xml: defaultXmlContentTypes\n  }\n};\n\n/**\n * A RequestPolicy that will deserialize HTTP response bodies and headers as they pass through the\n * HTTP pipeline.\n */\nexport class DeserializationPolicy extends BaseRequestPolicy {\n  public readonly jsonContentTypes: string[];\n  public readonly xmlContentTypes: string[];\n  public readonly xmlCharKey: string;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    requestPolicyOptions: RequestPolicyOptions,\n    deserializationContentTypes?: DeserializationContentTypes,\n    parsingOptions: SerializerOptions = {}\n  ) {\n    super(nextPolicy, requestPolicyOptions);\n\n    this.jsonContentTypes =\n      (deserializationContentTypes && deserializationContentTypes.json) || defaultJsonContentTypes;\n    this.xmlContentTypes =\n      (deserializationContentTypes && deserializationContentTypes.xml) || defaultXmlContentTypes;\n    this.xmlCharKey = parsingOptions.xmlCharKey ?? XML_CHARKEY;\n  }\n\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy.sendRequest(request).then((response: HttpOperationResponse) =>\n      deserializeResponseBody(this.jsonContentTypes, this.xmlContentTypes, response, {\n        xmlCharKey: this.xmlCharKey\n      })\n    );\n  }\n}\n\nfunction getOperationResponse(\n  parsedResponse: HttpOperationResponse\n): undefined | OperationResponse {\n  let result: OperationResponse | undefined;\n  const request: WebResourceLike = parsedResponse.request;\n  const operationSpec: OperationSpec | undefined = request.operationSpec;\n  if (operationSpec) {\n    const operationResponseGetter:\n      | undefined\n      | ((\n          operationSpec: OperationSpec,\n          response: HttpOperationResponse\n        ) => undefined | OperationResponse) = request.operationResponseGetter;\n    if (!operationResponseGetter) {\n      result = operationSpec.responses[parsedResponse.status];\n    } else {\n      result = operationResponseGetter(operationSpec, parsedResponse);\n    }\n  }\n  return result;\n}\n\nfunction shouldDeserializeResponse(parsedResponse: HttpOperationResponse): boolean {\n  const shouldDeserialize: undefined | boolean | ((response: HttpOperationResponse) => boolean) =\n    parsedResponse.request.shouldDeserialize;\n  let result: boolean;\n  if (shouldDeserialize === undefined) {\n    result = true;\n  } else if (typeof shouldDeserialize === \"boolean\") {\n    result = shouldDeserialize;\n  } else {\n    result = shouldDeserialize(parsedResponse);\n  }\n  return result;\n}\n\nexport function deserializeResponseBody(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  response: HttpOperationResponse,\n  options: SerializerOptions = {}\n): Promise<HttpOperationResponse> {\n  const updatedOptions: Required<SerializerOptions> = {\n    rootName: options.rootName ?? \"\",\n    includeRoot: options.includeRoot ?? false,\n    xmlCharKey: options.xmlCharKey ?? XML_CHARKEY\n  };\n  return parse(jsonContentTypes, xmlContentTypes, response, updatedOptions).then(\n    (parsedResponse) => {\n      if (!shouldDeserializeResponse(parsedResponse)) {\n        return parsedResponse;\n      }\n\n      const operationSpec = parsedResponse.request.operationSpec;\n      if (!operationSpec || !operationSpec.responses) {\n        return parsedResponse;\n      }\n\n      const responseSpec = getOperationResponse(parsedResponse);\n\n      const { error, shouldReturnResponse } = handleErrorResponse(\n        parsedResponse,\n        operationSpec,\n        responseSpec\n      );\n      if (error) {\n        throw error;\n      } else if (shouldReturnResponse) {\n        return parsedResponse;\n      }\n\n      // An operation response spec does exist for current status code, so\n      // use it to deserialize the response.\n      if (responseSpec) {\n        if (responseSpec.bodyMapper) {\n          let valueToDeserialize: any = parsedResponse.parsedBody;\n          if (operationSpec.isXML && responseSpec.bodyMapper.type.name === MapperType.Sequence) {\n            valueToDeserialize =\n              typeof valueToDeserialize === \"object\"\n                ? valueToDeserialize[responseSpec.bodyMapper.xmlElementName!]\n                : [];\n          }\n          try {\n            parsedResponse.parsedBody = operationSpec.serializer.deserialize(\n              responseSpec.bodyMapper,\n              valueToDeserialize,\n              \"operationRes.parsedBody\",\n              options\n            );\n          } catch (innerError) {\n            const restError = new RestError(\n              `Error ${innerError} occurred in deserializing the responseBody - ${parsedResponse.bodyAsText}`,\n              undefined,\n              parsedResponse.status,\n              parsedResponse.request,\n              parsedResponse\n            );\n            throw restError;\n          }\n        } else if (operationSpec.httpMethod === \"HEAD\") {\n          // head methods never have a body, but we return a boolean to indicate presence/absence of the resource\n          parsedResponse.parsedBody = response.status >= 200 && response.status < 300;\n        }\n\n        if (responseSpec.headersMapper) {\n          parsedResponse.parsedHeaders = operationSpec.serializer.deserialize(\n            responseSpec.headersMapper,\n            parsedResponse.headers.rawHeaders(),\n            \"operationRes.parsedHeaders\",\n            options\n          );\n        }\n      }\n\n      return parsedResponse;\n    }\n  );\n}\n\nfunction isOperationSpecEmpty(operationSpec: OperationSpec): boolean {\n  const expectedStatusCodes = Object.keys(operationSpec.responses);\n  return (\n    expectedStatusCodes.length === 0 ||\n    (expectedStatusCodes.length === 1 && expectedStatusCodes[0] === \"default\")\n  );\n}\n\nfunction handleErrorResponse(\n  parsedResponse: HttpOperationResponse,\n  operationSpec: OperationSpec,\n  responseSpec: OperationResponse | undefined\n): { error: RestError | null; shouldReturnResponse: boolean } {\n  const isSuccessByStatus = 200 <= parsedResponse.status && parsedResponse.status < 300;\n  const isExpectedStatusCode: boolean = isOperationSpecEmpty(operationSpec)\n    ? isSuccessByStatus\n    : !!responseSpec;\n\n  if (isExpectedStatusCode) {\n    if (responseSpec) {\n      if (!responseSpec.isError) {\n        return { error: null, shouldReturnResponse: false };\n      }\n    } else {\n      return { error: null, shouldReturnResponse: false };\n    }\n  }\n\n  const errorResponseSpec = responseSpec ?? operationSpec.responses.default;\n  const streaming =\n    parsedResponse.request.streamResponseStatusCodes?.has(parsedResponse.status) ||\n    parsedResponse.request.streamResponseBody;\n  const initialErrorMessage = streaming\n    ? `Unexpected status code: ${parsedResponse.status}`\n    : (parsedResponse.bodyAsText as string);\n\n  const error = new RestError(\n    initialErrorMessage,\n    undefined,\n    parsedResponse.status,\n    parsedResponse.request,\n    parsedResponse\n  );\n\n  // If the item failed but there's no error spec or default spec to deserialize the error,\n  // we should fail so we just throw the parsed response\n  if (!errorResponseSpec) {\n    throw error;\n  }\n\n  const defaultBodyMapper = errorResponseSpec.bodyMapper;\n  const defaultHeadersMapper = errorResponseSpec.headersMapper;\n\n  try {\n    // If error response has a body, try to deserialize it using default body mapper.\n    // Then try to extract error code & message from it\n    if (parsedResponse.parsedBody) {\n      const parsedBody = parsedResponse.parsedBody;\n      let parsedError;\n      if (defaultBodyMapper) {\n        let valueToDeserialize: any = parsedBody;\n        if (operationSpec.isXML && defaultBodyMapper.type.name === MapperType.Sequence) {\n          valueToDeserialize =\n            typeof parsedBody === \"object\" ? parsedBody[defaultBodyMapper.xmlElementName!] : [];\n        }\n        parsedError = operationSpec.serializer.deserialize(\n          defaultBodyMapper,\n          valueToDeserialize,\n          \"error.response.parsedBody\"\n        );\n      }\n\n      const internalError: any = parsedBody.error || parsedError || parsedBody;\n      error.code = internalError.code;\n      if (internalError.message) {\n        error.message = internalError.message;\n      }\n\n      if (defaultBodyMapper) {\n        error.response!.parsedBody = parsedError;\n      }\n    }\n\n    // If error response has headers, try to deserialize it using default header mapper\n    if (parsedResponse.headers && defaultHeadersMapper) {\n      error.response!.parsedHeaders = operationSpec.serializer.deserialize(\n        defaultHeadersMapper,\n        parsedResponse.headers.rawHeaders(),\n        \"operationRes.parsedHeaders\"\n      );\n    }\n  } catch (defaultError) {\n    error.message = `Error \"${defaultError.message}\" occurred in deserializing the responseBody - \"${parsedResponse.bodyAsText}\" for the default response.`;\n  }\n\n  return { error, shouldReturnResponse: false };\n}\n\nfunction parse(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  operationResponse: HttpOperationResponse,\n  opts: Required<SerializerOptions>\n): Promise<HttpOperationResponse> {\n  const errorHandler = (err: Error & { code: string }): Promise<never> => {\n    const msg = `Error \"${err}\" occurred while parsing the response body - ${operationResponse.bodyAsText}.`;\n    const errCode = err.code || RestError.PARSE_ERROR;\n    const e = new RestError(\n      msg,\n      errCode,\n      operationResponse.status,\n      operationResponse.request,\n      operationResponse\n    );\n    return Promise.reject(e);\n  };\n\n  const streaming =\n    operationResponse.request.streamResponseStatusCodes?.has(operationResponse.status) ||\n    operationResponse.request.streamResponseBody;\n  if (!streaming && operationResponse.bodyAsText) {\n    const text = operationResponse.bodyAsText;\n    const contentType: string = operationResponse.headers.get(\"Content-Type\") || \"\";\n    const contentComponents: string[] = !contentType\n      ? []\n      : contentType.split(\";\").map((component) => component.toLowerCase());\n    if (\n      contentComponents.length === 0 ||\n      contentComponents.some((component) => jsonContentTypes.indexOf(component) !== -1)\n    ) {\n      return new Promise<HttpOperationResponse>((resolve) => {\n        operationResponse.parsedBody = JSON.parse(text);\n        resolve(operationResponse);\n      }).catch(errorHandler);\n    } else if (contentComponents.some((component) => xmlContentTypes.indexOf(component) !== -1)) {\n      return parseXML(text, opts)\n        .then((body) => {\n          operationResponse.parsedBody = body;\n          return operationResponse;\n        })\n        .catch(errorHandler);\n    }\n  }\n\n  return Promise.resolve(operationResponse);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../coreHttp\";\n\nexport const DEFAULT_CLIENT_RETRY_COUNT = 3;\n// intervals are in ms\nexport const DEFAULT_CLIENT_RETRY_INTERVAL = 1000 * 30;\nexport const DEFAULT_CLIENT_MAX_RETRY_INTERVAL = 1000 * 90;\nexport const DEFAULT_CLIENT_MIN_RETRY_INTERVAL = 1000 * 3;\n\nexport function isNumber(n: unknown): n is number {\n  return typeof n === \"number\";\n}\nexport interface RetryData {\n  retryCount: number;\n  retryInterval: number;\n  error?: RetryError;\n}\n\nexport interface RetryError extends Error {\n  message: string;\n  code?: string;\n  innerError?: RetryError;\n}\n\n/**\n * @internal\n * Determines if the operation should be retried.\n *\n * @param retryLimit - Specifies the max number of retries.\n * @param predicate - Initial chekck on whether to retry based on given responses or errors\n * @param retryData -  The retry data.\n * @returns True if the operation qualifies for a retry; false otherwise.\n */\nexport function shouldRetry(\n  retryLimit: number,\n  predicate: (response?: HttpOperationResponse, error?: RetryError) => boolean,\n  retryData: RetryData,\n  response?: HttpOperationResponse,\n  error?: RetryError\n): boolean {\n  if (!predicate(response, error)) {\n    return false;\n  }\n\n  return retryData.retryCount < retryLimit;\n}\n\n/**\n * @internal\n * Updates the retry data for the next attempt.\n *\n * @param retryOptions - specifies retry interval, and its lower bound and upper bound.\n * @param retryData -  The retry data.\n * @param err - The operation\"s error, if any.\n */\nexport function updateRetryData(\n  retryOptions: { retryInterval: number; minRetryInterval: number; maxRetryInterval: number },\n  retryData: RetryData = { retryCount: 0, retryInterval: 0 },\n  err?: RetryError\n): RetryData {\n  if (err) {\n    if (retryData.error) {\n      err.innerError = retryData.error;\n    }\n\n    retryData.error = err;\n  }\n\n  // Adjust retry count\n  retryData.retryCount++;\n\n  // Adjust retry interval\n  let incrementDelta = Math.pow(2, retryData.retryCount - 1) - 1;\n  const boundedRandDelta =\n    retryOptions.retryInterval * 0.8 +\n    Math.floor(Math.random() * (retryOptions.retryInterval * 0.4));\n  incrementDelta *= boundedRandDelta;\n\n  retryData.retryInterval = Math.min(\n    retryOptions.minRetryInterval + incrementDelta,\n    retryOptions.maxRetryInterval\n  );\n\n  return retryData;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Helper TypeGuard that checks if the value is not null or undefined.\n * @param thing - Anything\n * @internal\n */\nexport function isDefined<T>(thing: T | undefined | null): thing is T {\n  return typeof thing !== \"undefined\" && thing !== null;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isDefined } from \"./typeguards\";\nimport { AbortError, AbortSignalLike } from \"@azure/abort-controller\";\nconst StandardAbortMessage = \"The operation was aborted.\";\n\n/**\n * A wrapper for setTimeout that resolves a promise after delayInMs milliseconds.\n * @param delayInMs - The number of milliseconds to be delayed.\n * @param value - The value to be resolved with after a timeout of t milliseconds.\n * @param options - The options for delay - currently abort options\n *   @param abortSignal - The abortSignal associated with containing operation.\n *   @param abortErrorMsg - The abort error message associated with containing operation.\n * @returns - Resolved promise\n */\nexport function delay<T>(\n  delayInMs: number,\n  value?: T,\n  options?: {\n    abortSignal?: AbortSignalLike;\n    abortErrorMsg?: string;\n  }\n): Promise<T | void> {\n  return new Promise((resolve, reject) => {\n    let timer: ReturnType<typeof setTimeout> | undefined = undefined;\n    let onAborted: (() => void) | undefined = undefined;\n\n    const rejectOnAbort = (): void => {\n      return reject(\n        new AbortError(options?.abortErrorMsg ? options?.abortErrorMsg : StandardAbortMessage)\n      );\n    };\n\n    const removeListeners = (): void => {\n      if (options?.abortSignal && onAborted) {\n        options.abortSignal.removeEventListener(\"abort\", onAborted);\n      }\n    };\n\n    onAborted = (): void => {\n      if (isDefined(timer)) {\n        clearTimeout(timer);\n      }\n      removeListeners();\n      return rejectOnAbort();\n    };\n\n    if (options?.abortSignal && options.abortSignal.aborted) {\n      return rejectOnAbort();\n    }\n\n    timer = setTimeout(() => {\n      removeListeners();\n      resolve(value);\n    }, delayInMs);\n\n    if (options?.abortSignal) {\n      options.abortSignal.addEventListener(\"abort\", onAborted);\n    }\n  });\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\nimport {\n  RetryData,\n  RetryError,\n  DEFAULT_CLIENT_MAX_RETRY_INTERVAL,\n  DEFAULT_CLIENT_RETRY_COUNT,\n  DEFAULT_CLIENT_RETRY_INTERVAL,\n  isNumber,\n  updateRetryData,\n  shouldRetry\n} from \"../util/exponentialBackoffStrategy\";\nimport { RestError } from \"../restError\";\nimport { logger } from \"../log\";\nimport { Constants } from \"../util/constants\";\nimport { delay } from \"../util/delay\";\n\nexport function exponentialRetryPolicy(\n  retryCount?: number,\n  retryInterval?: number,\n  maxRetryInterval?: number\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new ExponentialRetryPolicy(\n        nextPolicy,\n        options,\n        retryCount,\n        retryInterval,\n        maxRetryInterval\n      );\n    }\n  };\n}\n\n/**\n * Describes the Retry Mode type. Currently supporting only Exponential.\n */\nexport enum RetryMode {\n  Exponential\n}\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface RetryOptions {\n  /**\n   * The maximum number of retry attempts.  Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 30000\n   * (30 seconds). The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 90000 (90 seconds).\n   */\n  maxRetryDelayInMs?: number;\n\n  /**\n   * Currently supporting only Exponential mode.\n   */\n  mode?: RetryMode;\n}\n\nexport const DefaultRetryOptions: RetryOptions = {\n  maxRetries: DEFAULT_CLIENT_RETRY_COUNT,\n  retryDelayInMs: DEFAULT_CLIENT_RETRY_INTERVAL,\n  maxRetryDelayInMs: DEFAULT_CLIENT_MAX_RETRY_INTERVAL\n};\n\n/**\n * Instantiates a new \"ExponentialRetryPolicyFilter\" instance.\n */\nexport class ExponentialRetryPolicy extends BaseRequestPolicy {\n  /**\n   * The client retry count.\n   */\n  retryCount: number;\n  /**\n   * The client retry interval in milliseconds.\n   */\n  retryInterval: number;\n  /**\n   * The maximum retry interval in milliseconds.\n   */\n  maxRetryInterval: number;\n\n  /**\n   * @param nextPolicy - The next RequestPolicy in the pipeline chain.\n   * @param options - The options for this RequestPolicy.\n   * @param retryCount - The client retry count.\n   * @param retryInterval - The client retry interval, in milliseconds.\n   * @param minRetryInterval - The minimum retry interval, in milliseconds.\n   * @param maxRetryInterval - The maximum retry interval, in milliseconds.\n   */\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    retryCount?: number,\n    retryInterval?: number,\n    maxRetryInterval?: number\n  ) {\n    super(nextPolicy, options);\n    this.retryCount = isNumber(retryCount) ? retryCount : DEFAULT_CLIENT_RETRY_COUNT;\n    this.retryInterval = isNumber(retryInterval) ? retryInterval : DEFAULT_CLIENT_RETRY_INTERVAL;\n    this.maxRetryInterval = isNumber(maxRetryInterval)\n      ? maxRetryInterval\n      : DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request.clone())\n      .then((response) => retry(this, request, response))\n      .catch((error) => retry(this, request, error.response, undefined, error));\n  }\n}\n\nasync function retry(\n  policy: ExponentialRetryPolicy,\n  request: WebResourceLike,\n  response?: HttpOperationResponse,\n  retryData?: RetryData,\n  requestError?: RetryError\n): Promise<HttpOperationResponse> {\n  function shouldPolicyRetry(responseParam?: HttpOperationResponse): boolean {\n    const statusCode = responseParam?.status;\n    if (statusCode === 503 && response?.headers.get(Constants.HeaderConstants.RETRY_AFTER)) {\n      return false;\n    }\n\n    if (\n      statusCode === undefined ||\n      (statusCode < 500 && statusCode !== 408) ||\n      statusCode === 501 ||\n      statusCode === 505\n    ) {\n      return false;\n    }\n    return true;\n  }\n\n  retryData = updateRetryData(\n    {\n      retryInterval: policy.retryInterval,\n      minRetryInterval: 0,\n      maxRetryInterval: policy.maxRetryInterval\n    },\n    retryData,\n    requestError\n  );\n\n  const isAborted: boolean | undefined = request.abortSignal && request.abortSignal.aborted;\n  if (!isAborted && shouldRetry(policy.retryCount, shouldPolicyRetry, retryData, response)) {\n    logger.info(`Retrying request in ${retryData.retryInterval}`);\n    try {\n      await delay(retryData.retryInterval);\n      const res = await policy._nextPolicy.sendRequest(request.clone());\n      return retry(policy, request, res, retryData);\n    } catch (err) {\n      return retry(policy, request, response, retryData, err);\n    }\n  } else if (isAborted || requestError || !response) {\n    // If the operation failed in the end, return all errors instead of just the last one\n    const err =\n      retryData.error ||\n      new RestError(\n        \"Failed to send the request.\",\n        RestError.REQUEST_SEND_ERROR,\n        response && response.status,\n        response && response.request,\n        response\n      );\n    throw err;\n  } else {\n    return response;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\n\nexport function generateClientRequestIdPolicy(\n  requestIdHeaderName = \"x-ms-client-request-id\"\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new GenerateClientRequestIdPolicy(nextPolicy, options, requestIdHeaderName);\n    }\n  };\n}\n\nexport class GenerateClientRequestIdPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    private _requestIdHeaderName: string\n  ) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!request.headers.contains(this._requestIdHeaderName)) {\n      request.headers.set(this._requestIdHeaderName, request.requestId);\n    }\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as os from \"os\";\nimport { TelemetryInfo } from \"./userAgentPolicy\";\nimport { Constants } from \"../util/constants\";\n\nexport function getDefaultUserAgentKey(): string {\n  return Constants.HeaderConstants.USER_AGENT;\n}\n\nexport function getPlatformSpecificData(): TelemetryInfo[] {\n  const runtimeInfo = {\n    key: \"Node\",\n    value: process.version\n  };\n\n  const osInfo = {\n    key: \"OS\",\n    value: `(${os.arch()}-${os.type()}-${os.release()})`\n  };\n\n  return [runtimeInfo, osInfo];\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpHeaders } from \"../httpHeaders\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { Constants } from \"../util/constants\";\nimport { WebResourceLike } from \"../webResource\";\nimport { getDefaultUserAgentKey, getPlatformSpecificData } from \"./msRestUserAgentPolicy\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\n\nexport type TelemetryInfo = { key?: string; value?: string };\n\n/**\n * Options for adding user agent details to outgoing requests.\n */\nexport interface UserAgentOptions {\n  /*\n   * String prefix to add to the user agent for outgoing requests.\n   * Defaults to an empty string.\n   */\n  userAgentPrefix?: string;\n}\n\nfunction getRuntimeInfo(): TelemetryInfo[] {\n  const msRestRuntime = {\n    key: \"core-http\",\n    value: Constants.coreHttpVersion\n  };\n\n  return [msRestRuntime];\n}\n\nfunction getUserAgentString(\n  telemetryInfo: TelemetryInfo[],\n  keySeparator = \" \",\n  valueSeparator = \"/\"\n): string {\n  return telemetryInfo\n    .map((info) => {\n      const value = info.value ? `${valueSeparator}${info.value}` : \"\";\n      return `${info.key}${value}`;\n    })\n    .join(keySeparator);\n}\n\nexport const getDefaultUserAgentHeaderName = getDefaultUserAgentKey;\n\nexport function getDefaultUserAgentValue(): string {\n  const runtimeInfo = getRuntimeInfo();\n  const platformSpecificData = getPlatformSpecificData();\n  const userAgent = getUserAgentString(runtimeInfo.concat(platformSpecificData));\n  return userAgent;\n}\n\nexport function userAgentPolicy(userAgentData?: TelemetryInfo): RequestPolicyFactory {\n  const key: string =\n    !userAgentData || userAgentData.key === undefined || userAgentData.key === null\n      ? getDefaultUserAgentKey()\n      : userAgentData.key;\n  const value: string =\n    !userAgentData || userAgentData.value === undefined || userAgentData.value === null\n      ? getDefaultUserAgentValue()\n      : userAgentData.value;\n\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new UserAgentPolicy(nextPolicy, options, key, value);\n    }\n  };\n}\n\nexport class UserAgentPolicy extends BaseRequestPolicy {\n  constructor(\n    readonly _nextPolicy: RequestPolicy,\n    readonly _options: RequestPolicyOptions,\n    protected headerKey: string,\n    protected headerValue: string\n  ) {\n    super(_nextPolicy, _options);\n  }\n\n  sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    this.addUserAgentHeader(request);\n    return this._nextPolicy.sendRequest(request);\n  }\n\n  addUserAgentHeader(request: WebResourceLike): void {\n    if (!request.headers) {\n      request.headers = new HttpHeaders();\n    }\n\n    if (!request.headers.get(this.headerKey) && this.headerValue) {\n      request.headers.set(this.headerKey, this.headerValue);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { URLBuilder } from \"../url\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\n\n/**\n * Methods that are allowed to follow redirects 301 and 302\n */\nconst allowedRedirect = [\"GET\", \"HEAD\"];\n\n/**\n * Options for how redirect responses are handled.\n */\nexport interface RedirectOptions {\n  /*\n   * When true, redirect responses are followed.  Defaults to true.\n   */\n  handleRedirects: boolean;\n\n  /*\n   * The maximum number of times the redirect URL will be tried before\n   * failing.  Defaults to 20.\n   */\n  maxRetries?: number;\n}\n\nexport const DefaultRedirectOptions: RedirectOptions = {\n  handleRedirects: true,\n  maxRetries: 20\n};\n\nexport function redirectPolicy(maximumRetries = 20): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new RedirectPolicy(nextPolicy, options, maximumRetries);\n    }\n  };\n}\n\nexport class RedirectPolicy extends BaseRequestPolicy {\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions, readonly maxRetries = 20) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request)\n      .then((response) => handleRedirect(this, response, 0));\n  }\n}\n\nfunction handleRedirect(\n  policy: RedirectPolicy,\n  response: HttpOperationResponse,\n  currentRetries: number\n): Promise<HttpOperationResponse> {\n  const { request, status } = response;\n  const locationHeader = response.headers.get(\"location\");\n  if (\n    locationHeader &&\n    (status === 300 ||\n      (status === 301 && allowedRedirect.includes(request.method)) ||\n      (status === 302 && allowedRedirect.includes(request.method)) ||\n      (status === 303 && request.method === \"POST\") ||\n      status === 307) &&\n    (!policy.maxRetries || currentRetries < policy.maxRetries)\n  ) {\n    const builder = URLBuilder.parse(request.url);\n    builder.setPath(locationHeader);\n    request.url = builder.toString();\n\n    // POST request with Status code 303 should be converted into a\n    // redirected GET request if the redirect url is present in the location header\n    if (status === 303) {\n      request.method = \"GET\";\n      delete request.body;\n    }\n\n    return policy._nextPolicy\n      .sendRequest(request)\n      .then((res) => handleRedirect(policy, res, currentRetries + 1));\n  }\n\n  return Promise.resolve(response);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { delay } from \"../util/delay\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport * as utils from \"../util/utils\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\n\nexport function rpRegistrationPolicy(retryTimeout = 30): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new RPRegistrationPolicy(nextPolicy, options, retryTimeout);\n    }\n  };\n}\n\nexport class RPRegistrationPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    readonly _retryTimeout = 30\n  ) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request.clone())\n      .then((response) => registerIfNeeded(this, request, response));\n  }\n}\n\nfunction registerIfNeeded(\n  policy: RPRegistrationPolicy,\n  request: WebResourceLike,\n  response: HttpOperationResponse\n): Promise<HttpOperationResponse> {\n  if (response.status === 409) {\n    const rpName = checkRPNotRegisteredError(response.bodyAsText as string);\n    if (rpName) {\n      const urlPrefix = extractSubscriptionUrl(request.url);\n      return (\n        registerRP(policy, urlPrefix, rpName, request)\n          // Autoregistration of ${provider} failed for some reason. We will not return this error\n          // instead will return the initial response with 409 status code back to the user.\n          // do nothing here as we are returning the original response at the end of this method.\n          .catch(() => false)\n          .then((registrationStatus) => {\n            if (registrationStatus) {\n              // Retry the original request. We have to change the x-ms-client-request-id\n              // otherwise Azure endpoint will return the initial 409 (cached) response.\n              request.headers.set(\"x-ms-client-request-id\", utils.generateUuid());\n              return policy._nextPolicy.sendRequest(request.clone());\n            }\n            return response;\n          })\n      );\n    }\n  }\n\n  return Promise.resolve(response);\n}\n\n/**\n * Reuses the headers of the original request and url (if specified).\n * @param originalRequest - The original request\n * @param reuseUrlToo - Should the url from the original request be reused as well. Default false.\n * @returns A new request object with desired headers.\n */\nfunction getRequestEssentials(\n  originalRequest: WebResourceLike,\n  reuseUrlToo = false\n): WebResourceLike {\n  const reqOptions: WebResourceLike = originalRequest.clone();\n  if (reuseUrlToo) {\n    reqOptions.url = originalRequest.url;\n  }\n\n  // We have to change the x-ms-client-request-id otherwise Azure endpoint\n  // will return the initial 409 (cached) response.\n  reqOptions.headers.set(\"x-ms-client-request-id\", utils.generateUuid());\n\n  // Set content-type to application/json\n  reqOptions.headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n\n  return reqOptions;\n}\n\n/**\n * Validates the error code and message associated with 409 response status code. If it matches to that of\n * RP not registered then it returns the name of the RP else returns undefined.\n * @param body - The response body received after making the original request.\n * @returns The name of the RP if condition is satisfied else undefined.\n */\nfunction checkRPNotRegisteredError(body: string): string {\n  let result, responseBody;\n  if (body) {\n    try {\n      responseBody = JSON.parse(body);\n    } catch (err) {\n      // do nothing;\n    }\n    if (\n      responseBody &&\n      responseBody.error &&\n      responseBody.error.message &&\n      responseBody.error.code &&\n      responseBody.error.code === \"MissingSubscriptionRegistration\"\n    ) {\n      const matchRes = responseBody.error.message.match(/.*'(.*)'/i);\n      if (matchRes) {\n        result = matchRes.pop();\n      }\n    }\n  }\n  return result;\n}\n\n/**\n * Extracts the first part of the URL, just after subscription:\n * https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/\n * @param url - The original request url\n * @returns The url prefix as explained above.\n */\nfunction extractSubscriptionUrl(url: string): string {\n  let result;\n  const matchRes = url.match(/.*\\/subscriptions\\/[a-f0-9-]+\\//gi);\n  if (matchRes && matchRes[0]) {\n    result = matchRes[0];\n  } else {\n    throw new Error(`Unable to extract subscriptionId from the given url - ${url}.`);\n  }\n  return result;\n}\n\n/**\n * Registers the given provider.\n * @param policy - The RPRegistrationPolicy this function is being called against.\n * @param urlPrefix - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/\n * @param provider - The provider name to be registered.\n * @param originalRequest - The original request sent by the user that returned a 409 response\n * with a message that the provider is not registered.\n */\nasync function registerRP(\n  policy: RPRegistrationPolicy,\n  urlPrefix: string,\n  provider: string,\n  originalRequest: WebResourceLike\n): Promise<boolean> {\n  const postUrl = `${urlPrefix}providers/${provider}/register?api-version=2016-02-01`;\n  const getUrl = `${urlPrefix}providers/${provider}?api-version=2016-02-01`;\n  const reqOptions = getRequestEssentials(originalRequest);\n  reqOptions.method = \"POST\";\n  reqOptions.url = postUrl;\n\n  const response = await policy._nextPolicy.sendRequest(reqOptions);\n  if (response.status !== 200) {\n    throw new Error(`Autoregistration of ${provider} failed. Please try registering manually.`);\n  }\n  return getRegistrationStatus(policy, getUrl, originalRequest);\n}\n\n/**\n * Polls the registration status of the provider that was registered. Polling happens at an interval of 30 seconds.\n * Polling will happen till the registrationState property of the response body is \"Registered\".\n * @param policy - The RPRegistrationPolicy this function is being called against.\n * @param url - The request url for polling\n * @param originalRequest - The original request sent by the user that returned a 409 response\n * with a message that the provider is not registered.\n * @returns True if RP Registration is successful.\n */\nasync function getRegistrationStatus(\n  policy: RPRegistrationPolicy,\n  url: string,\n  originalRequest: WebResourceLike\n): Promise<boolean> {\n  const reqOptions: any = getRequestEssentials(originalRequest);\n  reqOptions.url = url;\n  reqOptions.method = \"GET\";\n\n  const res = await policy._nextPolicy.sendRequest(reqOptions);\n  const obj = res.parsedBody;\n  if (res.parsedBody && obj.registrationState && obj.registrationState === \"Registered\") {\n    return true;\n  } else {\n    await delay(policy._retryTimeout * 1000);\n    return getRegistrationStatus(policy, url, originalRequest);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { TokenCredential, GetTokenOptions, AccessToken } from \"@azure/core-auth\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyOptions,\n  RequestPolicyFactory\n} from \"../policies/requestPolicy\";\nimport { Constants } from \"../util/constants\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport { delay } from \"../util/delay\";\n\n// #region Access Token Cycler\n\n/**\n * A function that gets a promise of an access token and allows providing\n * options.\n *\n * @param options - the options to pass to the underlying token provider\n */\ntype AccessTokenGetter = (options: GetTokenOptions) => Promise<AccessToken>;\n\ninterface TokenCyclerOptions {\n  /**\n   * The window of time before token expiration during which the token will be\n   * considered unusable due to risk of the token expiring before sending the\n   * request.\n   *\n   * This will only become meaningful if the refresh fails for over\n   * (refreshWindow - forcedRefreshWindow) milliseconds.\n   */\n  forcedRefreshWindowInMs: number;\n  /**\n   * Interval in milliseconds to retry failed token refreshes.\n   */\n  retryIntervalInMs: number;\n  /**\n   * The window of time before token expiration during which\n   * we will attempt to refresh the token.\n   */\n  refreshWindowInMs: number;\n}\n\n// Default options for the cycler if none are provided\nexport const DEFAULT_CYCLER_OPTIONS: TokenCyclerOptions = {\n  forcedRefreshWindowInMs: 1000, // Force waiting for a refresh 1s before the token expires\n  retryIntervalInMs: 3000, // Allow refresh attempts every 3s\n  refreshWindowInMs: 1000 * 60 * 2 // Start refreshing 2m before expiry\n};\n\n/**\n * Converts an an unreliable access token getter (which may resolve with null)\n * into an AccessTokenGetter by retrying the unreliable getter in a regular\n * interval.\n *\n * @param getAccessToken - a function that produces a promise of an access\n * token that may fail by returning null\n * @param retryIntervalInMs - the time (in milliseconds) to wait between retry\n * attempts\n * @param timeoutInMs - the timestamp after which the refresh attempt will fail,\n * throwing an exception\n * @returns - a promise that, if it resolves, will resolve with an access token\n */\nasync function beginRefresh(\n  getAccessToken: () => Promise<AccessToken | null>,\n  retryIntervalInMs: number,\n  timeoutInMs: number\n): Promise<AccessToken> {\n  // This wrapper handles exceptions gracefully as long as we haven't exceeded\n  // the timeout.\n  async function tryGetAccessToken(): Promise<AccessToken | null> {\n    if (Date.now() < timeoutInMs) {\n      try {\n        return await getAccessToken();\n      } catch {\n        return null;\n      }\n    } else {\n      const finalToken = await getAccessToken();\n\n      // Timeout is up, so throw if it's still null\n      if (finalToken === null) {\n        throw new Error(\"Failed to refresh access token.\");\n      }\n\n      return finalToken;\n    }\n  }\n\n  let token: AccessToken | null = await tryGetAccessToken();\n\n  while (token === null) {\n    await delay(retryIntervalInMs);\n\n    token = await tryGetAccessToken();\n  }\n\n  return token;\n}\n\n/**\n * Creates a token cycler from a credential, scopes, and optional settings.\n *\n * A token cycler represents a way to reliably retrieve a valid access token\n * from a TokenCredential. It will handle initializing the token, refreshing it\n * when it nears expiration, and synchronizes refresh attempts to avoid\n * concurrency hazards.\n *\n * @param credential - the underlying TokenCredential that provides the access\n * token\n * @param scopes - the scopes to request authorization for\n * @param tokenCyclerOptions - optionally override default settings for the cycler\n *\n * @returns - a function that reliably produces a valid access token\n */\nfunction createTokenCycler(\n  credential: TokenCredential,\n  scopes: string | string[],\n  tokenCyclerOptions?: Partial<TokenCyclerOptions>\n): AccessTokenGetter {\n  let refreshWorker: Promise<AccessToken> | null = null;\n  let token: AccessToken | null = null;\n\n  const options = {\n    ...DEFAULT_CYCLER_OPTIONS,\n    ...tokenCyclerOptions\n  };\n\n  /**\n   * This little holder defines several predicates that we use to construct\n   * the rules of refreshing the token.\n   */\n  const cycler = {\n    /**\n     * Produces true if a refresh job is currently in progress.\n     */\n    get isRefreshing(): boolean {\n      return refreshWorker !== null;\n    },\n    /**\n     * Produces true if the cycler SHOULD refresh (we are within the refresh\n     * window and not already refreshing)\n     */\n    get shouldRefresh(): boolean {\n      return (\n        !cycler.isRefreshing &&\n        (token?.expiresOnTimestamp ?? 0) - options.refreshWindowInMs < Date.now()\n      );\n    },\n    /**\n     * Produces true if the cycler MUST refresh (null or nearly-expired\n     * token).\n     */\n    get mustRefresh(): boolean {\n      return (\n        token === null || token.expiresOnTimestamp - options.forcedRefreshWindowInMs < Date.now()\n      );\n    }\n  };\n\n  /**\n   * Starts a refresh job or returns the existing job if one is already\n   * running.\n   */\n  function refresh(getTokenOptions: GetTokenOptions): Promise<AccessToken> {\n    if (!cycler.isRefreshing) {\n      // We bind `scopes` here to avoid passing it around a lot\n      const tryGetAccessToken = (): Promise<AccessToken | null> =>\n        credential.getToken(scopes, getTokenOptions);\n\n      // Take advantage of promise chaining to insert an assignment to `token`\n      // before the refresh can be considered done.\n      refreshWorker = beginRefresh(\n        tryGetAccessToken,\n        options.retryIntervalInMs,\n        // If we don't have a token, then we should timeout immediately\n        token?.expiresOnTimestamp ?? Date.now()\n      )\n        .then((_token) => {\n          refreshWorker = null;\n          token = _token;\n          return token;\n        })\n        .catch((reason) => {\n          // We also should reset the refresher if we enter a failed state.  All\n          // existing awaiters will throw, but subsequent requests will start a\n          // new retry chain.\n          refreshWorker = null;\n          token = null;\n          throw reason;\n        });\n    }\n\n    return refreshWorker as Promise<AccessToken>;\n  }\n\n  return async (tokenOptions: GetTokenOptions): Promise<AccessToken> => {\n    //\n    // Simple rules:\n    // - If we MUST refresh, then return the refresh task, blocking\n    //   the pipeline until a token is available.\n    // - If we SHOULD refresh, then run refresh but don't return it\n    //   (we can still use the cached token).\n    // - Return the token, since it's fine if we didn't return in\n    //   step 1.\n    //\n\n    if (cycler.mustRefresh) return refresh(tokenOptions);\n\n    if (cycler.shouldRefresh) {\n      refresh(tokenOptions);\n    }\n\n    return token as AccessToken;\n  };\n}\n\n// #endregion\n\n/**\n * Creates a new factory for a RequestPolicy that applies a bearer token to\n * the requests' `Authorization` headers.\n *\n * @param credential - The TokenCredential implementation that can supply the bearer token.\n * @param scopes - The scopes for which the bearer token applies.\n */\nexport function bearerTokenAuthenticationPolicy(\n  credential: TokenCredential,\n  scopes: string | string[]\n): RequestPolicyFactory {\n  // This simple function encapsulates the entire process of reliably retrieving the token\n  const getToken = createTokenCycler(credential, scopes /* , options */);\n\n  class BearerTokenAuthenticationPolicy extends BaseRequestPolicy {\n    public constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n      super(nextPolicy, options);\n    }\n\n    public async sendRequest(webResource: WebResourceLike): Promise<HttpOperationResponse> {\n      if (!webResource.url.toLowerCase().startsWith(\"https://\")) {\n        throw new Error(\n          \"Bearer token authentication is not permitted for non-TLS protected (non-https) URLs.\"\n        );\n      }\n\n      const { token } = await getToken({\n        abortSignal: webResource.abortSignal,\n        tracingOptions: {\n          tracingContext: webResource.tracingContext\n        }\n      });\n      webResource.headers.set(Constants.HeaderConstants.AUTHORIZATION, `Bearer ${token}`);\n      return this._nextPolicy.sendRequest(webResource);\n    }\n  }\n\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new BearerTokenAuthenticationPolicy(nextPolicy, options);\n    }\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\nimport {\n  RetryData,\n  RetryError,\n  shouldRetry,\n  updateRetryData,\n  DEFAULT_CLIENT_MAX_RETRY_INTERVAL,\n  DEFAULT_CLIENT_RETRY_COUNT,\n  DEFAULT_CLIENT_RETRY_INTERVAL,\n  DEFAULT_CLIENT_MIN_RETRY_INTERVAL,\n  isNumber\n} from \"../util/exponentialBackoffStrategy\";\nimport { delay } from \"../util/delay\";\n\nexport function systemErrorRetryPolicy(\n  retryCount?: number,\n  retryInterval?: number,\n  minRetryInterval?: number,\n  maxRetryInterval?: number\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new SystemErrorRetryPolicy(\n        nextPolicy,\n        options,\n        retryCount,\n        retryInterval,\n        minRetryInterval,\n        maxRetryInterval\n      );\n    }\n  };\n}\n\n/**\n * @param retryCount - The client retry count.\n * @param retryInterval - The client retry interval, in milliseconds.\n * @param minRetryInterval - The minimum retry interval, in milliseconds.\n * @param maxRetryInterval - The maximum retry interval, in milliseconds.\n */\nexport class SystemErrorRetryPolicy extends BaseRequestPolicy {\n  retryCount: number;\n  retryInterval: number;\n  minRetryInterval: number;\n  maxRetryInterval: number;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    retryCount?: number,\n    retryInterval?: number,\n    minRetryInterval?: number,\n    maxRetryInterval?: number\n  ) {\n    super(nextPolicy, options);\n    this.retryCount = isNumber(retryCount) ? retryCount : DEFAULT_CLIENT_RETRY_COUNT;\n    this.retryInterval = isNumber(retryInterval) ? retryInterval : DEFAULT_CLIENT_RETRY_INTERVAL;\n    this.minRetryInterval = isNumber(minRetryInterval)\n      ? minRetryInterval\n      : DEFAULT_CLIENT_MIN_RETRY_INTERVAL;\n    this.maxRetryInterval = isNumber(maxRetryInterval)\n      ? maxRetryInterval\n      : DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request.clone())\n      .catch((error) => retry(this, request, error.response, error));\n  }\n}\n\nasync function retry(\n  policy: SystemErrorRetryPolicy,\n  request: WebResourceLike,\n  operationResponse: HttpOperationResponse,\n  err?: RetryError,\n  retryData?: RetryData\n): Promise<HttpOperationResponse> {\n  retryData = updateRetryData(policy, retryData, err);\n\n  function shouldPolicyRetry(_response?: HttpOperationResponse, error?: RetryError): boolean {\n    if (\n      error &&\n      error.code &&\n      (error.code === \"ETIMEDOUT\" ||\n        error.code === \"ESOCKETTIMEDOUT\" ||\n        error.code === \"ECONNREFUSED\" ||\n        error.code === \"ECONNRESET\" ||\n        error.code === \"ENOENT\")\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  if (shouldRetry(policy.retryCount, shouldPolicyRetry, retryData, operationResponse, err)) {\n    // If previous operation ended with an error and the policy allows a retry, do that\n    try {\n      await delay(retryData.retryInterval);\n      return policy._nextPolicy.sendRequest(request.clone());\n    } catch (nestedErr) {\n      return retry(policy, request, operationResponse, nestedErr, retryData);\n    }\n  } else {\n    if (err) {\n      // If the operation failed in the end, return all errors instead of just the last one\n      return Promise.reject(retryData.error);\n    }\n    return operationResponse;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The format that will be used to join an array of values together for a query parameter value.\n */\nexport enum QueryCollectionFormat {\n  Csv = \",\",\n  Ssv = \" \",\n  Tsv = \"\\t\",\n  Pipes = \"|\",\n  Multi = \"Multi\"\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { ProxySettings } from \"../serviceClient\";\nimport { WebResourceLike } from \"../webResource\";\nimport { Constants } from \"../util/constants\";\nimport { URLBuilder } from \"../url\";\nimport { getEnvironmentValue } from \"../util/utils\";\n\n/**\n * Stores the patterns specified in NO_PROXY environment variable.\n * @internal\n */\nexport const globalNoProxyList: string[] = [];\nlet noProxyListLoaded: boolean = false;\n\n/** A cache of whether a host should bypass the proxy. */\nconst globalBypassedMap: Map<string, boolean> = new Map();\n\nfunction loadEnvironmentProxyValue(): string | undefined {\n  if (!process) {\n    return undefined;\n  }\n\n  const httpsProxy = getEnvironmentValue(Constants.HTTPS_PROXY);\n  const allProxy = getEnvironmentValue(Constants.ALL_PROXY);\n  const httpProxy = getEnvironmentValue(Constants.HTTP_PROXY);\n\n  return httpsProxy || allProxy || httpProxy;\n}\n\n/**\n * Check whether the host of a given `uri` matches any pattern in the no proxy list.\n * If there's a match, any request sent to the same host shouldn't have the proxy settings set.\n * This implementation is a port of https://github.com/Azure/azure-sdk-for-net/blob/8cca811371159e527159c7eb65602477898683e2/sdk/core/Azure.Core/src/Pipeline/Internal/HttpEnvironmentProxy.cs#L210\n */\nfunction isBypassed(\n  uri: string,\n  noProxyList: string[],\n  bypassedMap?: Map<string, boolean>\n): boolean | undefined {\n  if (noProxyList.length === 0) {\n    return false;\n  }\n  const host = URLBuilder.parse(uri).getHost()!;\n  if (bypassedMap?.has(host)) {\n    return bypassedMap.get(host);\n  }\n  let isBypassedFlag = false;\n  for (const pattern of noProxyList) {\n    if (pattern[0] === \".\") {\n      // This should match either domain it self or any subdomain or host\n      // .foo.com will match foo.com it self or *.foo.com\n      if (host.endsWith(pattern)) {\n        isBypassedFlag = true;\n      } else {\n        if (host.length === pattern.length - 1 && host === pattern.slice(1)) {\n          isBypassedFlag = true;\n        }\n      }\n    } else {\n      if (host === pattern) {\n        isBypassedFlag = true;\n      }\n    }\n  }\n  bypassedMap?.set(host, isBypassedFlag);\n  return isBypassedFlag;\n}\n\n/**\n * @internal\n */\nexport function loadNoProxy(): string[] {\n  const noProxy = getEnvironmentValue(Constants.NO_PROXY);\n  noProxyListLoaded = true;\n  if (noProxy) {\n    return noProxy\n      .split(\",\")\n      .map((item) => item.trim())\n      .filter((item) => item.length);\n  }\n\n  return [];\n}\n\nexport function getDefaultProxySettings(proxyUrl?: string): ProxySettings | undefined {\n  if (!proxyUrl) {\n    proxyUrl = loadEnvironmentProxyValue();\n    if (!proxyUrl) {\n      return undefined;\n    }\n  }\n\n  const { username, password, urlWithoutAuth } = extractAuthFromUrl(proxyUrl);\n  const parsedUrl = URLBuilder.parse(urlWithoutAuth);\n  const schema = parsedUrl.getScheme() ? parsedUrl.getScheme() + \"://\" : \"\";\n  return {\n    host: schema + parsedUrl.getHost(),\n    port: Number.parseInt(parsedUrl.getPort() || \"80\"),\n    username,\n    password\n  };\n}\n\n/**\n * A policy that allows one to apply proxy settings to all requests.\n * If not passed static settings, they will be retrieved from the HTTPS_PROXY\n * or HTTP_PROXY environment variables.\n * @param proxySettings - ProxySettings to use on each request.\n * @param options - additional settings, for example, custom NO_PROXY patterns\n */\nexport function proxyPolicy(\n  proxySettings?: ProxySettings,\n  options?: {\n    /** a list of patterns to override those loaded from NO_PROXY environment variable. */\n    customNoProxyList?: string[];\n  }\n): RequestPolicyFactory {\n  if (!proxySettings) {\n    proxySettings = getDefaultProxySettings();\n  }\n  if (!noProxyListLoaded) {\n    globalNoProxyList.push(...loadNoProxy());\n  }\n  return {\n    create: (nextPolicy: RequestPolicy, requestPolicyOptions: RequestPolicyOptions) => {\n      return new ProxyPolicy(\n        nextPolicy,\n        requestPolicyOptions,\n        proxySettings!,\n        options?.customNoProxyList\n      );\n    }\n  };\n}\n\nfunction extractAuthFromUrl(\n  url: string\n): { username?: string; password?: string; urlWithoutAuth: string } {\n  const atIndex = url.indexOf(\"@\");\n  if (atIndex === -1) {\n    return { urlWithoutAuth: url };\n  }\n\n  const schemeIndex = url.indexOf(\"://\");\n  const authStart = schemeIndex !== -1 ? schemeIndex + 3 : 0;\n  const auth = url.substring(authStart, atIndex);\n  const colonIndex = auth.indexOf(\":\");\n  const hasPassword = colonIndex !== -1;\n  const username = hasPassword ? auth.substring(0, colonIndex) : auth;\n  const password = hasPassword ? auth.substring(colonIndex + 1) : undefined;\n  const urlWithoutAuth = url.substring(0, authStart) + url.substring(atIndex + 1);\n  return {\n    username,\n    password,\n    urlWithoutAuth\n  };\n}\n\nexport class ProxyPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    public proxySettings: ProxySettings,\n    private customNoProxyList?: string[]\n  ) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (\n      !request.proxySettings &&\n      !isBypassed(\n        request.url,\n        this.customNoProxyList ?? globalNoProxyList,\n        this.customNoProxyList ? undefined : globalBypassedMap\n      )\n    ) {\n      request.proxySettings = this.proxySettings;\n    }\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Maximum number of retries for the throttling retry policy\n */\nexport const DEFAULT_CLIENT_MAX_RETRY_COUNT = 3;\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortError } from \"@azure/abort-controller\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyOptions,\n  RequestPolicyFactory\n} from \"./requestPolicy\";\nimport { WebResourceLike } from \"../webResource\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { Constants } from \"../util/constants\";\nimport { DEFAULT_CLIENT_MAX_RETRY_COUNT } from \"../util/throttlingRetryStrategy\";\nimport { delay } from \"../util/delay\";\n\ntype ResponseHandler = (\n  httpRequest: WebResourceLike,\n  response: HttpOperationResponse\n) => Promise<HttpOperationResponse>;\nconst StatusCodes = Constants.HttpConstants.StatusCodes;\n\nexport function throttlingRetryPolicy(): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new ThrottlingRetryPolicy(nextPolicy, options);\n    }\n  };\n}\n\nconst StandardAbortMessage = \"The operation was aborted.\";\n\n/**\n * To learn more, please refer to\n * https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-manager-request-limits,\n * https://docs.microsoft.com/en-us/azure/azure-subscription-service-limits and\n * https://docs.microsoft.com/en-us/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors\n */\nexport class ThrottlingRetryPolicy extends BaseRequestPolicy {\n  private _handleResponse: ResponseHandler;\n  private numberOfRetries = 0;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    _handleResponse?: ResponseHandler\n  ) {\n    super(nextPolicy, options);\n    this._handleResponse = _handleResponse || this._defaultResponseHandler;\n  }\n\n  public async sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse> {\n    const response = await this._nextPolicy.sendRequest(httpRequest.clone());\n    if (\n      response.status !== StatusCodes.TooManyRequests &&\n      response.status !== StatusCodes.ServiceUnavailable\n    ) {\n      return response;\n    } else {\n      return this._handleResponse(httpRequest, response);\n    }\n  }\n\n  private async _defaultResponseHandler(\n    httpRequest: WebResourceLike,\n    httpResponse: HttpOperationResponse\n  ): Promise<HttpOperationResponse> {\n    const retryAfterHeader: string | undefined = httpResponse.headers.get(\n      Constants.HeaderConstants.RETRY_AFTER\n    );\n\n    if (retryAfterHeader) {\n      const delayInMs: number | undefined = ThrottlingRetryPolicy.parseRetryAfterHeader(\n        retryAfterHeader\n      );\n      if (delayInMs) {\n        this.numberOfRetries += 1;\n\n        await delay(delayInMs, undefined, {\n          abortSignal: httpRequest.abortSignal,\n          abortErrorMsg: StandardAbortMessage\n        });\n\n        if (httpRequest.abortSignal?.aborted) {\n          throw new AbortError(StandardAbortMessage);\n        }\n\n        if (this.numberOfRetries < DEFAULT_CLIENT_MAX_RETRY_COUNT) {\n          return this.sendRequest(httpRequest);\n        } else {\n          return this._nextPolicy.sendRequest(httpRequest);\n        }\n      }\n    }\n\n    return httpResponse;\n  }\n\n  public static parseRetryAfterHeader(headerValue: string): number | undefined {\n    const retryAfterInSeconds = Number(headerValue);\n    if (Number.isNaN(retryAfterInSeconds)) {\n      return ThrottlingRetryPolicy.parseDateRetryAfterHeader(headerValue);\n    } else {\n      return retryAfterInSeconds * 1000;\n    }\n  }\n\n  public static parseDateRetryAfterHeader(headerValue: string): number | undefined {\n    try {\n      const now: number = Date.now();\n      const date: number = Date.parse(headerValue);\n      const diff = date - now;\n\n      return Number.isNaN(diff) ? undefined : diff;\n    } catch (error) {\n      return undefined;\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { ServiceClientCredentials } from \"../credentials/serviceClientCredentials\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\n\nexport function signingPolicy(\n  authenticationProvider: ServiceClientCredentials\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new SigningPolicy(nextPolicy, options, authenticationProvider);\n    }\n  };\n}\n\nexport class SigningPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    public authenticationProvider: ServiceClientCredentials\n  ) {\n    super(nextPolicy, options);\n  }\n\n  signRequest(request: WebResourceLike): Promise<WebResourceLike> {\n    return this.authenticationProvider.signRequest(request);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this.signRequest(request).then((nextRequest) =>\n      this._nextPolicy.sendRequest(nextRequest)\n    );\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyOptions,\n  RequestPolicyFactory\n} from \"./requestPolicy\";\nimport { WebResourceLike } from \"../webResource\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\n\n/**\n * Options for how HTTP connections should be maintained for future\n * requests.\n */\nexport interface KeepAliveOptions {\n  /*\n   * When true, connections will be kept alive for multiple requests.\n   * Defaults to true.\n   */\n  enable: boolean;\n}\n\nexport const DefaultKeepAliveOptions: KeepAliveOptions = {\n  enable: true\n};\n\nexport function keepAlivePolicy(keepAliveOptions?: KeepAliveOptions): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new KeepAlivePolicy(nextPolicy, options, keepAliveOptions || DefaultKeepAliveOptions);\n    }\n  };\n}\n\n/**\n * KeepAlivePolicy is a policy used to control keep alive settings for every request.\n */\nexport class KeepAlivePolicy extends BaseRequestPolicy {\n  /**\n   * Creates an instance of KeepAlivePolicy.\n   *\n   * @param nextPolicy -\n   * @param options -\n   * @param keepAliveOptions -\n   */\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    private readonly keepAliveOptions: KeepAliveOptions\n  ) {\n    super(nextPolicy, options);\n  }\n\n  /**\n   * Sends out request.\n   *\n   * @param request -\n   * @returns\n   */\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    request.keepAlive = this.keepAliveOptions.enable;\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  getTraceParentHeader,\n  createSpanFunction,\n  SpanKind,\n  SpanStatusCode,\n  isSpanContextValid,\n  Span\n} from \"@azure/core-tracing\";\nimport {\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptions,\n  BaseRequestPolicy\n} from \"./requestPolicy\";\nimport { WebResourceLike } from \"../webResource\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { URLBuilder } from \"../url\";\nimport { logger } from \"../log\";\n\nconst createSpan = createSpanFunction({\n  packagePrefix: \"\",\n  namespace: \"\"\n});\n\nexport interface TracingPolicyOptions {\n  userAgent?: string;\n}\n\nexport function tracingPolicy(tracingOptions: TracingPolicyOptions = {}): RequestPolicyFactory {\n  return {\n    create(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n      return new TracingPolicy(nextPolicy, options, tracingOptions);\n    }\n  };\n}\n\nexport class TracingPolicy extends BaseRequestPolicy {\n  private userAgent?: string;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    tracingOptions: TracingPolicyOptions\n  ) {\n    super(nextPolicy, options);\n    this.userAgent = tracingOptions.userAgent;\n  }\n\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!request.tracingContext) {\n      return this._nextPolicy.sendRequest(request);\n    }\n\n    const span = this.tryCreateSpan(request);\n\n    if (!span) {\n      return this._nextPolicy.sendRequest(request);\n    }\n\n    try {\n      const response = await this._nextPolicy.sendRequest(request);\n      this.tryProcessResponse(span, response);\n      return response;\n    } catch (err) {\n      this.tryProcessError(span, err);\n      throw err;\n    }\n  }\n\n  tryCreateSpan(request: WebResourceLike): Span | undefined {\n    try {\n      const path = URLBuilder.parse(request.url).getPath() || \"/\";\n\n      // Passing spanOptions as part of tracingOptions to maintain compatibility @azure/core-tracing@preview.13 and earlier.\n      // We can pass this as a separate parameter once we upgrade to the latest core-tracing.\n      const { span } = createSpan(path, {\n        tracingOptions: {\n          spanOptions: {\n            ...(request as any).spanOptions,\n            kind: SpanKind.CLIENT\n          },\n          tracingContext: request.tracingContext\n        }\n      });\n\n      // If the span is not recording, don't do any more work.\n      if (!span.isRecording()) {\n        span.end();\n        return undefined;\n      }\n\n      const namespaceFromContext = request.tracingContext?.getValue(Symbol.for(\"az.namespace\"));\n\n      if (typeof namespaceFromContext === \"string\") {\n        span.setAttribute(\"az.namespace\", namespaceFromContext);\n      }\n\n      span.setAttributes({\n        \"http.method\": request.method,\n        \"http.url\": request.url,\n        requestId: request.requestId\n      });\n\n      if (this.userAgent) {\n        span.setAttribute(\"http.user_agent\", this.userAgent);\n      }\n\n      // set headers\n      const spanContext = span.spanContext();\n      const traceParentHeader = getTraceParentHeader(spanContext);\n      if (traceParentHeader && isSpanContextValid(spanContext)) {\n        request.headers.set(\"traceparent\", traceParentHeader);\n        const traceState = spanContext.traceState && spanContext.traceState.serialize();\n        // if tracestate is set, traceparent MUST be set, so only set tracestate after traceparent\n        if (traceState) {\n          request.headers.set(\"tracestate\", traceState);\n        }\n      }\n      return span;\n    } catch (error) {\n      logger.warning(`Skipping creating a tracing span due to an error: ${error.message}`);\n      return undefined;\n    }\n  }\n\n  private tryProcessError(span: Span, err: any): void {\n    try {\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message\n      });\n\n      if (err.statusCode) {\n        span.setAttribute(\"http.status_code\", err.statusCode);\n      }\n      span.end();\n    } catch (error) {\n      logger.warning(`Skipping tracing span processing due to an error: ${error.message}`);\n    }\n  }\n\n  private tryProcessResponse(span: Span, response: HttpOperationResponse): void {\n    try {\n      span.setAttribute(\"http.status_code\", response.status);\n      const serviceRequestId = response.headers.get(\"x-ms-request-id\");\n      if (serviceRequestId) {\n        span.setAttribute(\"serviceRequestId\", serviceRequestId);\n      }\n      span.setStatus({\n        code: SpanStatusCode.OK\n      });\n      span.end();\n    } catch (error) {\n      logger.warning(`Skipping tracing span processing due to an error: ${error.message}`);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyOptions,\n  RequestPolicyFactory\n} from \"./requestPolicy\";\nimport { WebResource } from \"../webResource\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\n\n/**\n * Returns a request policy factory that can be used to create an instance of\n * {@link DisableResponseDecompressionPolicy}.\n */\nexport function disableResponseDecompressionPolicy(): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new DisableResponseDecompressionPolicy(nextPolicy, options);\n    }\n  };\n}\n\n/**\n * A policy to disable response decompression according to Accept-Encoding header\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding\n */\nexport class DisableResponseDecompressionPolicy extends BaseRequestPolicy {\n  /**\n   * Creates an instance of DisableResponseDecompressionPolicy.\n   *\n   * @param nextPolicy -\n   * @param options -\n   */\n  // The parent constructor is protected.\n  /* eslint-disable-next-line @typescript-eslint/no-useless-constructor */\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n    super(nextPolicy, options);\n  }\n\n  /**\n   * Sends out request.\n   *\n   * @param request -\n   * @returns\n   */\n  public async sendRequest(request: WebResource): Promise<HttpOperationResponse> {\n    request.decompressResponse = false;\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// BaseRequestPolicy has a protected constructor.\n/* eslint-disable @typescript-eslint/no-useless-constructor */\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyOptions,\n  RequestPolicyFactory\n} from \"./requestPolicy\";\nimport { WebResourceLike } from \"../webResource\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\n\nexport function ndJsonPolicy(): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new NdJsonPolicy(nextPolicy, options);\n    }\n  };\n}\n\n/**\n * NdJsonPolicy that formats a JSON array as newline-delimited JSON\n */\nclass NdJsonPolicy extends BaseRequestPolicy {\n  /**\n   * Creates an instance of KeepAlivePolicy.\n   */\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n    super(nextPolicy, options);\n  }\n\n  /**\n   * Sends a request.\n   */\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    // There currently isn't a good way to bypass the serializer\n    if (typeof request.body === \"string\" && request.body.startsWith(\"[\")) {\n      const body = JSON.parse(request.body);\n      if (Array.isArray(body)) {\n        request.body = body.map((item) => JSON.stringify(item) + \"\\n\").join(\"\");\n      }\n    }\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpClient } from \"./httpClient\";\nimport { DefaultHttpClient } from \"./defaultHttpClient\";\n\nlet cachedHttpClient: HttpClient | undefined;\n\nexport function getCachedDefaultHttpClient(): HttpClient {\n  if (!cachedHttpClient) {\n    cachedHttpClient = new DefaultHttpClient();\n  }\n\n  return cachedHttpClient;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { TokenCredential, isTokenCredential } from \"@azure/core-auth\";\nimport { HttpClient } from \"./httpClient\";\nimport { HttpOperationResponse, RestResponse } from \"./httpOperationResponse\";\nimport { HttpPipelineLogger } from \"./httpPipelineLogger\";\nimport { logPolicy, LogPolicyOptions } from \"./policies/logPolicy\";\nimport { OperationArguments } from \"./operationArguments\";\nimport {\n  getPathStringFromParameter,\n  getPathStringFromParameterPath,\n  OperationParameter,\n  ParameterPath\n} from \"./operationParameter\";\nimport { getStreamResponseStatusCodes, OperationSpec } from \"./operationSpec\";\nimport {\n  deserializationPolicy,\n  DeserializationContentTypes,\n  DefaultDeserializationOptions\n} from \"./policies/deserializationPolicy\";\nimport { exponentialRetryPolicy, DefaultRetryOptions } from \"./policies/exponentialRetryPolicy\";\nimport { generateClientRequestIdPolicy } from \"./policies/generateClientRequestIdPolicy\";\nimport {\n  userAgentPolicy,\n  getDefaultUserAgentHeaderName,\n  getDefaultUserAgentValue\n} from \"./policies/userAgentPolicy\";\nimport { redirectPolicy, DefaultRedirectOptions } from \"./policies/redirectPolicy\";\nimport {\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./policies/requestPolicy\";\nimport { rpRegistrationPolicy } from \"./policies/rpRegistrationPolicy\";\nimport { bearerTokenAuthenticationPolicy } from \"./policies/bearerTokenAuthenticationPolicy\";\nimport { systemErrorRetryPolicy } from \"./policies/systemErrorRetryPolicy\";\nimport { QueryCollectionFormat } from \"./queryCollectionFormat\";\nimport { CompositeMapper, DictionaryMapper, Mapper, MapperType, Serializer } from \"./serializer\";\nimport { URLBuilder } from \"./url\";\nimport * as utils from \"./util/utils\";\nimport { stringifyXML } from \"./util/xml\";\nimport {\n  RequestOptionsBase,\n  RequestPrepareOptions,\n  WebResource,\n  WebResourceLike,\n  isWebResourceLike\n} from \"./webResource\";\nimport { OperationResponse } from \"./operationResponse\";\nimport { ServiceCallback, isNode } from \"./util/utils\";\nimport { proxyPolicy } from \"./policies/proxyPolicy\";\nimport { throttlingRetryPolicy } from \"./policies/throttlingRetryPolicy\";\nimport { ServiceClientCredentials } from \"./credentials/serviceClientCredentials\";\nimport { signingPolicy } from \"./policies/signingPolicy\";\nimport { logger } from \"./log\";\nimport { InternalPipelineOptions } from \"./pipelineOptions\";\nimport { DefaultKeepAliveOptions, keepAlivePolicy } from \"./policies/keepAlivePolicy\";\nimport { tracingPolicy } from \"./policies/tracingPolicy\";\nimport { disableResponseDecompressionPolicy } from \"./policies/disableResponseDecompressionPolicy\";\nimport { ndJsonPolicy } from \"./policies/ndJsonPolicy\";\nimport { XML_ATTRKEY, SerializerOptions, XML_CHARKEY } from \"./util/serializer.common\";\nimport { URL } from \"./url\";\nimport { getCachedDefaultHttpClient } from \"./httpClientCache\";\n\n/**\n * Options to configure a proxy for outgoing requests (Node.js only).\n */\nexport interface ProxySettings {\n  /**\n   * The proxy's host address.\n   */\n  host: string;\n\n  /**\n   * The proxy host's port.\n   */\n  port: number;\n\n  /**\n   * The user name to authenticate with the proxy, if required.\n   */\n  username?: string;\n\n  /**\n   * The password to authenticate with the proxy, if required.\n   */\n  password?: string;\n}\n\nexport type ProxyOptions = ProxySettings; // Alias ProxySettings as ProxyOptions for future use.\n\n/**\n * Options to be provided while creating the client.\n */\nexport interface ServiceClientOptions {\n  /**\n   * An array of factories which get called to create the RequestPolicy pipeline used to send a HTTP\n   * request on the wire, or a function that takes in the defaultRequestPolicyFactories and returns\n   * the requestPolicyFactories that will be used.\n   */\n  requestPolicyFactories?:\n    | RequestPolicyFactory[]\n    | ((defaultRequestPolicyFactories: RequestPolicyFactory[]) => void | RequestPolicyFactory[]);\n  /**\n   * The HttpClient that will be used to send HTTP requests.\n   */\n  httpClient?: HttpClient;\n  /**\n   * The HttpPipelineLogger that can be used to debug RequestPolicies within the HTTP pipeline.\n   */\n  httpPipelineLogger?: HttpPipelineLogger;\n  /**\n   * If set to true, turn off the default retry policy.\n   */\n  noRetryPolicy?: boolean;\n  /**\n   * Gets or sets the retry timeout in seconds for AutomaticRPRegistration. Default value is 30.\n   */\n  rpRegistrationRetryTimeout?: number;\n  /**\n   * Whether or not to generate a client request ID header for each HTTP request.\n   */\n  generateClientRequestIdHeader?: boolean;\n  /**\n   * Whether to include credentials in CORS requests in the browser.\n   * See https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/withCredentials for more information.\n   */\n  withCredentials?: boolean;\n  /**\n   * If specified, a GenerateRequestIdPolicy will be added to the HTTP pipeline that will add a\n   * header to all outgoing requests with this header name and a random UUID as the request ID.\n   */\n  clientRequestIdHeaderName?: string;\n  /**\n   * The content-types that will be associated with JSON or XML serialization.\n   */\n  deserializationContentTypes?: DeserializationContentTypes;\n  /**\n   * The header name to use for the telemetry header while sending the request. If this is not\n   * specified, then \"User-Agent\" will be used when running on Node.js and \"x-ms-useragent\" will\n   * be used when running in a browser.\n   */\n  userAgentHeaderName?: string | ((defaultUserAgentHeaderName: string) => string);\n  /**\n   * The string to be set to the telemetry header while sending the request, or a function that\n   * takes in the default user-agent string and returns the user-agent string that will be used.\n   */\n  userAgent?: string | ((defaultUserAgent: string) => string);\n  /**\n   * Proxy settings which will be used for every HTTP request (Node.js only).\n   */\n  proxySettings?: ProxySettings;\n  /**\n   * If specified, will be used to build the BearerTokenAuthenticationPolicy.\n   */\n  credentialScopes?: string | string[];\n}\n\n/**\n * ServiceClient sends service requests and receives responses.\n */\nexport class ServiceClient {\n  /**\n   * If specified, this is the base URI that requests will be made against for this ServiceClient.\n   * If it is not specified, then all OperationSpecs must contain a baseUrl property.\n   */\n  protected baseUri?: string;\n\n  /**\n   * The default request content type for the service.\n   * Used if no requestContentType is present on an OperationSpec.\n   */\n  protected requestContentType?: string;\n\n  /**\n   * The HTTP client that will be used to send requests.\n   */\n  private readonly _httpClient: HttpClient;\n  private readonly _requestPolicyOptions: RequestPolicyOptions;\n\n  private readonly _requestPolicyFactories: RequestPolicyFactory[];\n  private readonly _withCredentials: boolean;\n\n  /**\n   * The ServiceClient constructor\n   * @param credentials - The credentials used for authentication with the service.\n   * @param options - The service client options that govern the behavior of the client.\n   */\n  constructor(\n    credentials?: TokenCredential | ServiceClientCredentials,\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options */\n    options?: ServiceClientOptions\n  ) {\n    if (!options) {\n      options = {};\n    }\n\n    this._withCredentials = options.withCredentials || false;\n    this._httpClient = options.httpClient || getCachedDefaultHttpClient();\n    this._requestPolicyOptions = new RequestPolicyOptions(options.httpPipelineLogger);\n\n    let requestPolicyFactories: RequestPolicyFactory[];\n    if (Array.isArray(options.requestPolicyFactories)) {\n      logger.info(\"ServiceClient: using custom request policies\");\n      requestPolicyFactories = options.requestPolicyFactories;\n    } else {\n      let authPolicyFactory: RequestPolicyFactory | undefined = undefined;\n      if (isTokenCredential(credentials)) {\n        logger.info(\n          \"ServiceClient: creating bearer token authentication policy from provided credentials\"\n        );\n        // Create a wrapped RequestPolicyFactory here so that we can provide the\n        // correct scope to the BearerTokenAuthenticationPolicy at the first time\n        // one is requested.  This is needed because generated ServiceClient\n        // implementations do not set baseUri until after ServiceClient's constructor\n        // is finished, leaving baseUri empty at the time when it is needed to\n        // build the correct scope name.\n        const wrappedPolicyFactory: () => RequestPolicyFactory = () => {\n          let bearerTokenPolicyFactory: RequestPolicyFactory | undefined = undefined;\n          // eslint-disable-next-line @typescript-eslint/no-this-alias\n          const serviceClient = this;\n          const serviceClientOptions = options;\n          return {\n            create(nextPolicy: RequestPolicy, createOptions: RequestPolicyOptions): RequestPolicy {\n              const credentialScopes = getCredentialScopes(\n                serviceClientOptions,\n                serviceClient.baseUri\n              );\n\n              if (!credentialScopes) {\n                throw new Error(\n                  `When using credential, the ServiceClient must contain a baseUri or a credentialScopes in ServiceClientOptions. Unable to create a bearerTokenAuthenticationPolicy`\n                );\n              }\n\n              if (bearerTokenPolicyFactory === undefined || bearerTokenPolicyFactory === null) {\n                bearerTokenPolicyFactory = bearerTokenAuthenticationPolicy(\n                  credentials,\n                  credentialScopes\n                );\n              }\n\n              return bearerTokenPolicyFactory.create(nextPolicy, createOptions);\n            }\n          };\n        };\n\n        authPolicyFactory = wrappedPolicyFactory();\n      } else if (credentials && typeof credentials.signRequest === \"function\") {\n        logger.info(\"ServiceClient: creating signing policy from provided credentials\");\n        authPolicyFactory = signingPolicy(credentials);\n      } else if (credentials !== undefined && credentials !== null) {\n        throw new Error(\"The credentials argument must implement the TokenCredential interface\");\n      }\n\n      logger.info(\"ServiceClient: using default request policies\");\n      requestPolicyFactories = createDefaultRequestPolicyFactories(authPolicyFactory, options);\n      if (options.requestPolicyFactories) {\n        // options.requestPolicyFactories can also be a function that manipulates\n        // the default requestPolicyFactories array\n        const newRequestPolicyFactories:\n          | void\n          | RequestPolicyFactory[] = options.requestPolicyFactories(requestPolicyFactories);\n        if (newRequestPolicyFactories) {\n          requestPolicyFactories = newRequestPolicyFactories;\n        }\n      }\n    }\n    this._requestPolicyFactories = requestPolicyFactories;\n  }\n\n  /**\n   * Send the provided httpRequest.\n   */\n  sendRequest(options: RequestPrepareOptions | WebResourceLike): Promise<HttpOperationResponse> {\n    if (options === null || options === undefined || typeof options !== \"object\") {\n      throw new Error(\"options cannot be null or undefined and it must be of type object.\");\n    }\n\n    let httpRequest: WebResourceLike;\n    try {\n      if (isWebResourceLike(options)) {\n        options.validateRequestProperties();\n        httpRequest = options;\n      } else {\n        httpRequest = new WebResource();\n        httpRequest = httpRequest.prepare(options);\n      }\n    } catch (error) {\n      return Promise.reject(error);\n    }\n\n    let httpPipeline: RequestPolicy = this._httpClient;\n    if (this._requestPolicyFactories && this._requestPolicyFactories.length > 0) {\n      for (let i = this._requestPolicyFactories.length - 1; i >= 0; --i) {\n        httpPipeline = this._requestPolicyFactories[i].create(\n          httpPipeline,\n          this._requestPolicyOptions\n        );\n      }\n    }\n    return httpPipeline.sendRequest(httpRequest);\n  }\n\n  /**\n   * Send an HTTP request that is populated using the provided OperationSpec.\n   * @param operationArguments - The arguments that the HTTP request's templated values will be populated from.\n   * @param operationSpec - The OperationSpec to use to populate the httpRequest.\n   * @param callback - The callback to call when the response is received.\n   */\n  async sendOperationRequest(\n    operationArguments: OperationArguments,\n    operationSpec: OperationSpec,\n    callback?: ServiceCallback<any>\n  ): Promise<RestResponse> {\n    if (typeof operationArguments.options === \"function\") {\n      callback = operationArguments.options;\n      operationArguments.options = undefined;\n    }\n\n    const serializerOptions = operationArguments.options?.serializerOptions;\n    const httpRequest: WebResourceLike = new WebResource();\n\n    let result: Promise<RestResponse>;\n    try {\n      const baseUri: string | undefined = operationSpec.baseUrl || this.baseUri;\n      if (!baseUri) {\n        throw new Error(\n          \"If operationSpec.baseUrl is not specified, then the ServiceClient must have a baseUri string property that contains the base URL to use.\"\n        );\n      }\n\n      httpRequest.method = operationSpec.httpMethod;\n      httpRequest.operationSpec = operationSpec;\n\n      const requestUrl: URLBuilder = URLBuilder.parse(baseUri);\n      if (operationSpec.path) {\n        requestUrl.appendPath(operationSpec.path);\n      }\n      if (operationSpec.urlParameters && operationSpec.urlParameters.length > 0) {\n        for (const urlParameter of operationSpec.urlParameters) {\n          let urlParameterValue: string = getOperationArgumentValueFromParameter(\n            this,\n            operationArguments,\n            urlParameter,\n            operationSpec.serializer\n          );\n          urlParameterValue = operationSpec.serializer.serialize(\n            urlParameter.mapper,\n            urlParameterValue,\n            getPathStringFromParameter(urlParameter),\n            serializerOptions\n          );\n          if (!urlParameter.skipEncoding) {\n            urlParameterValue = encodeURIComponent(urlParameterValue);\n          }\n          requestUrl.replaceAll(\n            `{${urlParameter.mapper.serializedName || getPathStringFromParameter(urlParameter)}}`,\n            urlParameterValue\n          );\n        }\n      }\n      if (operationSpec.queryParameters && operationSpec.queryParameters.length > 0) {\n        for (const queryParameter of operationSpec.queryParameters) {\n          let queryParameterValue: any = getOperationArgumentValueFromParameter(\n            this,\n            operationArguments,\n            queryParameter,\n            operationSpec.serializer\n          );\n          if (queryParameterValue !== undefined && queryParameterValue !== null) {\n            queryParameterValue = operationSpec.serializer.serialize(\n              queryParameter.mapper,\n              queryParameterValue,\n              getPathStringFromParameter(queryParameter),\n              serializerOptions\n            );\n            if (\n              queryParameter.collectionFormat !== undefined &&\n              queryParameter.collectionFormat !== null\n            ) {\n              if (queryParameter.collectionFormat === QueryCollectionFormat.Multi) {\n                if (queryParameterValue.length === 0) {\n                  // The collection is empty, no need to try serializing the current queryParam\n                  continue;\n                } else {\n                  for (const index in queryParameterValue) {\n                    const item = queryParameterValue[index];\n                    queryParameterValue[index] =\n                      item === undefined || item === null ? \"\" : item.toString();\n                  }\n                }\n              } else if (\n                queryParameter.collectionFormat === QueryCollectionFormat.Ssv ||\n                queryParameter.collectionFormat === QueryCollectionFormat.Tsv\n              ) {\n                queryParameterValue = queryParameterValue.join(queryParameter.collectionFormat);\n              }\n            }\n            if (!queryParameter.skipEncoding) {\n              if (Array.isArray(queryParameterValue)) {\n                for (const index in queryParameterValue) {\n                  if (\n                    queryParameterValue[index] !== undefined &&\n                    queryParameterValue[index] !== null\n                  ) {\n                    queryParameterValue[index] = encodeURIComponent(queryParameterValue[index]);\n                  }\n                }\n              } else {\n                queryParameterValue = encodeURIComponent(queryParameterValue);\n              }\n            }\n            if (\n              queryParameter.collectionFormat !== undefined &&\n              queryParameter.collectionFormat !== null &&\n              queryParameter.collectionFormat !== QueryCollectionFormat.Multi &&\n              queryParameter.collectionFormat !== QueryCollectionFormat.Ssv &&\n              queryParameter.collectionFormat !== QueryCollectionFormat.Tsv\n            ) {\n              queryParameterValue = queryParameterValue.join(queryParameter.collectionFormat);\n            }\n            requestUrl.setQueryParameter(\n              queryParameter.mapper.serializedName || getPathStringFromParameter(queryParameter),\n              queryParameterValue\n            );\n          }\n        }\n      }\n      httpRequest.url = requestUrl.toString();\n\n      const contentType = operationSpec.contentType || this.requestContentType;\n      if (contentType && operationSpec.requestBody) {\n        httpRequest.headers.set(\"Content-Type\", contentType);\n      }\n\n      if (operationSpec.headerParameters) {\n        for (const headerParameter of operationSpec.headerParameters) {\n          let headerValue: any = getOperationArgumentValueFromParameter(\n            this,\n            operationArguments,\n            headerParameter,\n            operationSpec.serializer\n          );\n          if (headerValue !== undefined && headerValue !== null) {\n            headerValue = operationSpec.serializer.serialize(\n              headerParameter.mapper,\n              headerValue,\n              getPathStringFromParameter(headerParameter),\n              serializerOptions\n            );\n            const headerCollectionPrefix = (headerParameter.mapper as DictionaryMapper)\n              .headerCollectionPrefix;\n            if (headerCollectionPrefix) {\n              for (const key of Object.keys(headerValue)) {\n                httpRequest.headers.set(headerCollectionPrefix + key, headerValue[key]);\n              }\n            } else {\n              httpRequest.headers.set(\n                headerParameter.mapper.serializedName ||\n                  getPathStringFromParameter(headerParameter),\n                headerValue\n              );\n            }\n          }\n        }\n      }\n\n      const options: RequestOptionsBase | undefined = operationArguments.options;\n      if (options) {\n        if (options.customHeaders) {\n          for (const customHeaderName in options.customHeaders) {\n            httpRequest.headers.set(customHeaderName, options.customHeaders[customHeaderName]);\n          }\n        }\n\n        if (options.abortSignal) {\n          httpRequest.abortSignal = options.abortSignal;\n        }\n\n        if (options.timeout) {\n          httpRequest.timeout = options.timeout;\n        }\n\n        if (options.onUploadProgress) {\n          httpRequest.onUploadProgress = options.onUploadProgress;\n        }\n\n        if (options.onDownloadProgress) {\n          httpRequest.onDownloadProgress = options.onDownloadProgress;\n        }\n\n        if (options.spanOptions) {\n          // By passing spanOptions if they exist at runtime, we're backwards compatible with @azure/core-tracing@preview.13 and earlier.\n          (httpRequest as any).spanOptions = options.spanOptions;\n        }\n\n        if (options.tracingContext) {\n          httpRequest.tracingContext = options.tracingContext;\n        }\n\n        if (options.shouldDeserialize !== undefined && options.shouldDeserialize !== null) {\n          httpRequest.shouldDeserialize = options.shouldDeserialize;\n        }\n      }\n\n      httpRequest.withCredentials = this._withCredentials;\n\n      serializeRequestBody(this, httpRequest, operationArguments, operationSpec);\n\n      if (httpRequest.streamResponseStatusCodes === undefined) {\n        httpRequest.streamResponseStatusCodes = getStreamResponseStatusCodes(operationSpec);\n      }\n\n      let rawResponse: HttpOperationResponse;\n      let sendRequestError;\n      try {\n        rawResponse = await this.sendRequest(httpRequest);\n      } catch (error) {\n        sendRequestError = error;\n      }\n      if (sendRequestError) {\n        if (sendRequestError.response) {\n          sendRequestError.details = flattenResponse(\n            sendRequestError.response,\n            operationSpec.responses[sendRequestError.statusCode] ||\n              operationSpec.responses[\"default\"]\n          );\n        }\n        result = Promise.reject(sendRequestError);\n      } else {\n        result = Promise.resolve(\n          flattenResponse(rawResponse!, operationSpec.responses[rawResponse!.status])\n        );\n      }\n    } catch (error) {\n      result = Promise.reject(error);\n    }\n\n    const cb = callback;\n    if (cb) {\n      result\n        .then((res) => cb(null, res._response.parsedBody, res._response.request, res._response))\n        .catch((err) => cb(err));\n    }\n\n    return result;\n  }\n}\n\nexport function serializeRequestBody(\n  serviceClient: ServiceClient,\n  httpRequest: WebResourceLike,\n  operationArguments: OperationArguments,\n  operationSpec: OperationSpec\n): void {\n  const serializerOptions = operationArguments.options?.serializerOptions ?? {};\n  const updatedOptions: Required<SerializerOptions> = {\n    rootName: serializerOptions.rootName ?? \"\",\n    includeRoot: serializerOptions.includeRoot ?? false,\n    xmlCharKey: serializerOptions.xmlCharKey ?? XML_CHARKEY\n  };\n\n  const xmlCharKey = serializerOptions.xmlCharKey;\n  if (operationSpec.requestBody && operationSpec.requestBody.mapper) {\n    httpRequest.body = getOperationArgumentValueFromParameter(\n      serviceClient,\n      operationArguments,\n      operationSpec.requestBody,\n      operationSpec.serializer\n    );\n\n    const bodyMapper = operationSpec.requestBody.mapper;\n    const {\n      required,\n      xmlName,\n      xmlElementName,\n      serializedName,\n      xmlNamespace,\n      xmlNamespacePrefix\n    } = bodyMapper;\n    const typeName = bodyMapper.type.name;\n\n    try {\n      if ((httpRequest.body !== undefined && httpRequest.body !== null) || required) {\n        const requestBodyParameterPathString: string = getPathStringFromParameter(\n          operationSpec.requestBody\n        );\n        httpRequest.body = operationSpec.serializer.serialize(\n          bodyMapper,\n          httpRequest.body,\n          requestBodyParameterPathString,\n          updatedOptions\n        );\n\n        const isStream = typeName === MapperType.Stream;\n\n        if (operationSpec.isXML) {\n          const xmlnsKey = xmlNamespacePrefix ? `xmlns:${xmlNamespacePrefix}` : \"xmlns\";\n          const value = getXmlValueWithNamespace(\n            xmlNamespace,\n            xmlnsKey,\n            typeName,\n            httpRequest.body,\n            updatedOptions\n          );\n          if (typeName === MapperType.Sequence) {\n            httpRequest.body = stringifyXML(\n              utils.prepareXMLRootList(\n                value,\n                xmlElementName || xmlName || serializedName!,\n                xmlnsKey,\n                xmlNamespace\n              ),\n              {\n                rootName: xmlName || serializedName,\n                xmlCharKey\n              }\n            );\n          } else if (!isStream) {\n            httpRequest.body = stringifyXML(value, {\n              rootName: xmlName || serializedName,\n              xmlCharKey\n            });\n          }\n        } else if (\n          typeName === MapperType.String &&\n          (operationSpec.contentType?.match(\"text/plain\") || operationSpec.mediaType === \"text\")\n        ) {\n          // the String serializer has validated that request body is a string\n          // so just send the string.\n          return;\n        } else if (!isStream) {\n          httpRequest.body = JSON.stringify(httpRequest.body);\n        }\n      }\n    } catch (error) {\n      throw new Error(\n        `Error \"${error.message}\" occurred in serializing the payload - ${JSON.stringify(\n          serializedName,\n          undefined,\n          \"  \"\n        )}.`\n      );\n    }\n  } else if (operationSpec.formDataParameters && operationSpec.formDataParameters.length > 0) {\n    httpRequest.formData = {};\n    for (const formDataParameter of operationSpec.formDataParameters) {\n      const formDataParameterValue: any = getOperationArgumentValueFromParameter(\n        serviceClient,\n        operationArguments,\n        formDataParameter,\n        operationSpec.serializer\n      );\n      if (formDataParameterValue !== undefined && formDataParameterValue !== null) {\n        const formDataParameterPropertyName: string =\n          formDataParameter.mapper.serializedName || getPathStringFromParameter(formDataParameter);\n        httpRequest.formData[formDataParameterPropertyName] = operationSpec.serializer.serialize(\n          formDataParameter.mapper,\n          formDataParameterValue,\n          getPathStringFromParameter(formDataParameter),\n          updatedOptions\n        );\n      }\n    }\n  }\n}\n\n/**\n * Adds an xml namespace to the xml serialized object if needed, otherwise it just returns the value itself\n */\nfunction getXmlValueWithNamespace(\n  xmlNamespace: string | undefined,\n  xmlnsKey: string,\n  typeName: string,\n  serializedValue: any,\n  options: Required<SerializerOptions>\n): any {\n  // Composite and Sequence schemas already got their root namespace set during serialization\n  // We just need to add xmlns to the other schema types\n  if (xmlNamespace && ![\"Composite\", \"Sequence\", \"Dictionary\"].includes(typeName)) {\n    const result: any = {};\n    result[options.xmlCharKey] = serializedValue;\n    result[XML_ATTRKEY] = { [xmlnsKey]: xmlNamespace };\n    return result;\n  }\n\n  return serializedValue;\n}\n\nfunction getValueOrFunctionResult(\n  value: undefined | string | ((defaultValue: string) => string),\n  defaultValueCreator: () => string\n): string {\n  let result: string;\n  if (typeof value === \"string\") {\n    result = value;\n  } else {\n    result = defaultValueCreator();\n    if (typeof value === \"function\") {\n      result = value(result);\n    }\n  }\n  return result;\n}\n\nfunction createDefaultRequestPolicyFactories(\n  authPolicyFactory: RequestPolicyFactory | undefined,\n  options: ServiceClientOptions\n): RequestPolicyFactory[] {\n  const factories: RequestPolicyFactory[] = [];\n\n  if (options.generateClientRequestIdHeader) {\n    factories.push(generateClientRequestIdPolicy(options.clientRequestIdHeaderName));\n  }\n\n  if (authPolicyFactory) {\n    factories.push(authPolicyFactory);\n  }\n\n  const userAgentHeaderName: string = getValueOrFunctionResult(\n    options.userAgentHeaderName,\n    getDefaultUserAgentHeaderName\n  );\n  const userAgentHeaderValue: string = getValueOrFunctionResult(\n    options.userAgent,\n    getDefaultUserAgentValue\n  );\n  if (userAgentHeaderName && userAgentHeaderValue) {\n    factories.push(userAgentPolicy({ key: userAgentHeaderName, value: userAgentHeaderValue }));\n  }\n  factories.push(redirectPolicy());\n  factories.push(rpRegistrationPolicy(options.rpRegistrationRetryTimeout));\n\n  if (!options.noRetryPolicy) {\n    factories.push(exponentialRetryPolicy());\n    factories.push(systemErrorRetryPolicy());\n    factories.push(throttlingRetryPolicy());\n  }\n\n  factories.push(deserializationPolicy(options.deserializationContentTypes));\n\n  if (isNode) {\n    factories.push(proxyPolicy(options.proxySettings));\n  }\n\n  factories.push(logPolicy({ logger: logger.info }));\n\n  return factories;\n}\n\nexport function createPipelineFromOptions(\n  pipelineOptions: InternalPipelineOptions,\n  authPolicyFactory?: RequestPolicyFactory\n): ServiceClientOptions {\n  const requestPolicyFactories: RequestPolicyFactory[] = [];\n\n  if (pipelineOptions.sendStreamingJson) {\n    requestPolicyFactories.push(ndJsonPolicy());\n  }\n\n  let userAgentValue = undefined;\n  if (pipelineOptions.userAgentOptions && pipelineOptions.userAgentOptions.userAgentPrefix) {\n    const userAgentInfo: string[] = [];\n    userAgentInfo.push(pipelineOptions.userAgentOptions.userAgentPrefix);\n\n    // Add the default user agent value if it isn't already specified\n    // by the userAgentPrefix option.\n    const defaultUserAgentInfo = getDefaultUserAgentValue();\n    if (userAgentInfo.indexOf(defaultUserAgentInfo) === -1) {\n      userAgentInfo.push(defaultUserAgentInfo);\n    }\n\n    userAgentValue = userAgentInfo.join(\" \");\n  }\n\n  const keepAliveOptions = {\n    ...DefaultKeepAliveOptions,\n    ...pipelineOptions.keepAliveOptions\n  };\n\n  const retryOptions = {\n    ...DefaultRetryOptions,\n    ...pipelineOptions.retryOptions\n  };\n\n  const redirectOptions = {\n    ...DefaultRedirectOptions,\n    ...pipelineOptions.redirectOptions\n  };\n\n  if (isNode) {\n    requestPolicyFactories.push(proxyPolicy(pipelineOptions.proxyOptions));\n  }\n\n  const deserializationOptions = {\n    ...DefaultDeserializationOptions,\n    ...pipelineOptions.deserializationOptions\n  };\n\n  const loggingOptions: LogPolicyOptions = {\n    ...pipelineOptions.loggingOptions\n  };\n\n  requestPolicyFactories.push(\n    tracingPolicy({ userAgent: userAgentValue }),\n    keepAlivePolicy(keepAliveOptions),\n    userAgentPolicy({ value: userAgentValue }),\n    generateClientRequestIdPolicy(),\n    deserializationPolicy(deserializationOptions.expectedContentTypes),\n    throttlingRetryPolicy(),\n    systemErrorRetryPolicy(),\n    exponentialRetryPolicy(\n      retryOptions.maxRetries,\n      retryOptions.retryDelayInMs,\n      retryOptions.maxRetryDelayInMs\n    )\n  );\n\n  if (redirectOptions.handleRedirects) {\n    requestPolicyFactories.push(redirectPolicy(redirectOptions.maxRetries));\n  }\n\n  if (authPolicyFactory) {\n    requestPolicyFactories.push(authPolicyFactory);\n  }\n\n  requestPolicyFactories.push(logPolicy(loggingOptions));\n\n  if (isNode && pipelineOptions.decompressResponse === false) {\n    requestPolicyFactories.push(disableResponseDecompressionPolicy());\n  }\n\n  return {\n    httpClient: pipelineOptions.httpClient,\n    requestPolicyFactories\n  };\n}\n\nexport type PropertyParent = { [propertyName: string]: any };\n\n/**\n * Get the property parent for the property at the provided path when starting with the provided\n * parent object.\n */\nexport function getPropertyParent(parent: PropertyParent, propertyPath: string[]): PropertyParent {\n  if (parent && propertyPath) {\n    const propertyPathLength: number = propertyPath.length;\n    for (let i = 0; i < propertyPathLength - 1; ++i) {\n      const propertyName: string = propertyPath[i];\n      if (!parent[propertyName]) {\n        parent[propertyName] = {};\n      }\n      parent = parent[propertyName];\n    }\n  }\n  return parent;\n}\n\nfunction getOperationArgumentValueFromParameter(\n  serviceClient: ServiceClient,\n  operationArguments: OperationArguments,\n  parameter: OperationParameter,\n  serializer: Serializer\n): any {\n  return getOperationArgumentValueFromParameterPath(\n    serviceClient,\n    operationArguments,\n    parameter.parameterPath,\n    parameter.mapper,\n    serializer\n  );\n}\n\nexport function getOperationArgumentValueFromParameterPath(\n  serviceClient: ServiceClient,\n  operationArguments: OperationArguments,\n  parameterPath: ParameterPath,\n  parameterMapper: Mapper,\n  serializer: Serializer\n): any {\n  let value: any;\n  if (typeof parameterPath === \"string\") {\n    parameterPath = [parameterPath];\n  }\n  const serializerOptions = operationArguments.options?.serializerOptions;\n  if (Array.isArray(parameterPath)) {\n    if (parameterPath.length > 0) {\n      if (parameterMapper.isConstant) {\n        value = parameterMapper.defaultValue;\n      } else {\n        let propertySearchResult: PropertySearchResult = getPropertyFromParameterPath(\n          operationArguments,\n          parameterPath\n        );\n        if (!propertySearchResult.propertyFound) {\n          propertySearchResult = getPropertyFromParameterPath(serviceClient, parameterPath);\n        }\n\n        let useDefaultValue = false;\n        if (!propertySearchResult.propertyFound) {\n          useDefaultValue =\n            parameterMapper.required ||\n            (parameterPath[0] === \"options\" && parameterPath.length === 2);\n        }\n        value = useDefaultValue ? parameterMapper.defaultValue : propertySearchResult.propertyValue;\n      }\n\n      // Serialize just for validation purposes.\n      const parameterPathString: string = getPathStringFromParameterPath(\n        parameterPath,\n        parameterMapper\n      );\n      serializer.serialize(parameterMapper, value, parameterPathString, serializerOptions);\n    }\n  } else {\n    if (parameterMapper.required) {\n      value = {};\n    }\n\n    for (const propertyName in parameterPath) {\n      const propertyMapper: Mapper = (parameterMapper as CompositeMapper).type.modelProperties![\n        propertyName\n      ];\n      const propertyPath: ParameterPath = parameterPath[propertyName];\n      const propertyValue: any = getOperationArgumentValueFromParameterPath(\n        serviceClient,\n        operationArguments,\n        propertyPath,\n        propertyMapper,\n        serializer\n      );\n      // Serialize just for validation purposes.\n      const propertyPathString: string = getPathStringFromParameterPath(\n        propertyPath,\n        propertyMapper\n      );\n      serializer.serialize(propertyMapper, propertyValue, propertyPathString, serializerOptions);\n      if (propertyValue !== undefined && propertyValue !== null) {\n        if (!value) {\n          value = {};\n        }\n        value[propertyName] = propertyValue;\n      }\n    }\n  }\n  return value;\n}\n\ninterface PropertySearchResult {\n  propertyValue?: any;\n  propertyFound: boolean;\n}\n\nfunction getPropertyFromParameterPath(\n  parent: { [parameterName: string]: any },\n  parameterPath: string[]\n): PropertySearchResult {\n  const result: PropertySearchResult = { propertyFound: false };\n  let i = 0;\n  for (; i < parameterPath.length; ++i) {\n    const parameterPathPart: string = parameterPath[i];\n    // Make sure to check inherited properties too, so don't use hasOwnProperty().\n    if (parent !== undefined && parent !== null && parameterPathPart in parent) {\n      parent = parent[parameterPathPart];\n    } else {\n      break;\n    }\n  }\n  if (i === parameterPath.length) {\n    result.propertyValue = parent;\n    result.propertyFound = true;\n  }\n  return result;\n}\n\nexport function flattenResponse(\n  _response: HttpOperationResponse,\n  responseSpec: OperationResponse | undefined\n): RestResponse {\n  const parsedHeaders = _response.parsedHeaders;\n  const bodyMapper = responseSpec && responseSpec.bodyMapper;\n\n  const addOperationResponse = (\n    obj: Record<string, unknown>\n  ): {\n    _response: HttpOperationResponse;\n  } => {\n    return Object.defineProperty(obj, \"_response\", {\n      value: _response\n    });\n  };\n\n  if (bodyMapper) {\n    const typeName = bodyMapper.type.name;\n    if (typeName === \"Stream\") {\n      return addOperationResponse({\n        ...parsedHeaders,\n        blobBody: _response.blobBody,\n        readableStreamBody: _response.readableStreamBody\n      });\n    }\n\n    const modelProperties =\n      (typeName === \"Composite\" && (bodyMapper as CompositeMapper).type.modelProperties) || {};\n    const isPageableResponse = Object.keys(modelProperties).some(\n      (k) => modelProperties[k].serializedName === \"\"\n    );\n    if (typeName === \"Sequence\" || isPageableResponse) {\n      const arrayResponse = [...(_response.parsedBody || [])] as RestResponse & any[];\n\n      for (const key of Object.keys(modelProperties)) {\n        if (modelProperties[key].serializedName) {\n          arrayResponse[key] = _response.parsedBody[key];\n        }\n      }\n\n      if (parsedHeaders) {\n        for (const key of Object.keys(parsedHeaders)) {\n          arrayResponse[key] = parsedHeaders[key];\n        }\n      }\n      addOperationResponse(arrayResponse);\n      return arrayResponse;\n    }\n\n    if (typeName === \"Composite\" || typeName === \"Dictionary\") {\n      return addOperationResponse({\n        ...parsedHeaders,\n        ..._response.parsedBody\n      });\n    }\n  }\n\n  if (\n    bodyMapper ||\n    _response.request.method === \"HEAD\" ||\n    utils.isPrimitiveType(_response.parsedBody)\n  ) {\n    // primitive body types and HEAD booleans\n    return addOperationResponse({\n      ...parsedHeaders,\n      body: _response.parsedBody\n    });\n  }\n\n  return addOperationResponse({\n    ...parsedHeaders,\n    ..._response.parsedBody\n  });\n}\n\nfunction getCredentialScopes(\n  options?: ServiceClientOptions,\n  baseUri?: string\n): string | string[] | undefined {\n  if (options?.credentialScopes) {\n    const scopes = options.credentialScopes;\n    return Array.isArray(scopes)\n      ? scopes.map((scope) => new URL(scope).toString())\n      : new URL(scopes).toString();\n  }\n\n  if (baseUri) {\n    return `${baseUri}/.default`;\n  }\n  return undefined;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// NOTE: we've moved this code into core-tracing but these functions\n// were a part of the GA'd library and can't be removed until the next major\n// release. They currently get called always, even if tracing is not enabled.\n\nimport { createSpanFunction as coreTracingCreateSpanFunction, Span } from \"@azure/core-tracing\";\nimport { OperationOptions } from \"./operationOptions\";\n\n/**\n * This function is only here for compatibility. Use createSpanFunction in core-tracing.\n *\n * @deprecated This function is only here for compatibility. Use core-tracing instead.\n * @hidden\n */\nexport interface SpanConfig {\n  /**\n   * Package name prefix\n   */\n  packagePrefix: string;\n  /**\n   * Service namespace\n   */\n  namespace: string;\n}\n\n/**\n * This function is only here for compatibility. Use createSpanFunction in core-tracing.\n * \n * @deprecated This function is only here for compatibility. Use createSpanFunction in core-tracing.\n * @hidden \n\n * @param spanConfig - The name of the operation being performed.\n * @param tracingOptions - The options for the underlying http request.\n */\nexport function createSpanFunction(\n  args: SpanConfig\n): <T extends OperationOptions>(\n  operationName: string,\n  operationOptions: T\n) => { span: Span; updatedOptions: T } {\n  return coreTracingCreateSpanFunction(args);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken } from \"@azure/core-auth\";\n\n/**\n * Defines the default token refresh buffer duration.\n */\nexport const TokenRefreshBufferMs = 2 * 60 * 1000; // 2 Minutes\n\n/**\n * Provides a cache for an AccessToken that was that\n * was returned from a TokenCredential.\n */\nexport interface AccessTokenCache {\n  /**\n   * Sets the cached token.\n   *\n   * @param accessToken - The {@link AccessToken} to be cached or null to\n   *        clear the cached token.\n   */\n  setCachedToken(accessToken: AccessToken | undefined): void;\n\n  /**\n   * Returns the cached {@link AccessToken} or undefined if nothing is cached.\n   */\n  getCachedToken(): AccessToken | undefined;\n}\n\n/**\n * Provides an {@link AccessTokenCache} implementation which clears\n * the cached {@link AccessToken}'s after the expiresOnTimestamp has\n * passed.\n *\n * @deprecated No longer used in the bearer authorization policy.\n */\nexport class ExpiringAccessTokenCache implements AccessTokenCache {\n  private tokenRefreshBufferMs: number;\n  private cachedToken?: AccessToken = undefined;\n\n  /**\n   * Constructs an instance of {@link ExpiringAccessTokenCache} with\n   * an optional expiration buffer time.\n   */\n  constructor(tokenRefreshBufferMs: number = TokenRefreshBufferMs) {\n    this.tokenRefreshBufferMs = tokenRefreshBufferMs;\n  }\n\n  setCachedToken(accessToken: AccessToken | undefined): void {\n    this.cachedToken = accessToken;\n  }\n\n  getCachedToken(): AccessToken | undefined {\n    if (\n      this.cachedToken &&\n      Date.now() + this.tokenRefreshBufferMs >= this.cachedToken.expiresOnTimestamp\n    ) {\n      this.cachedToken = undefined;\n    }\n\n    return this.cachedToken;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, TokenCredential, GetTokenOptions } from \"@azure/core-auth\";\n\n/**\n * Helps the core-http token authentication policies with requesting a new token if we're not currently waiting for a new token.\n *\n * @deprecated No longer used in the bearer authorization policy.\n */\nexport class AccessTokenRefresher {\n  private promise: Promise<AccessToken | undefined> | undefined;\n  private lastCalled = 0;\n\n  constructor(\n    private credential: TokenCredential,\n    private scopes: string | string[],\n    private requiredMillisecondsBeforeNewRefresh: number = 30000\n  ) {}\n\n  /**\n   * Returns true if the required milliseconds(defaulted to 30000) have been passed signifying\n   * that we are ready for a new refresh.\n   */\n  public isReady(): boolean {\n    // We're only ready for a new refresh if the required milliseconds have passed.\n    return (\n      !this.lastCalled || Date.now() - this.lastCalled > this.requiredMillisecondsBeforeNewRefresh\n    );\n  }\n\n  /**\n   * Stores the time in which it is called,\n   * then requests a new token,\n   * then sets this.promise to undefined,\n   * then returns the token.\n   */\n  private async getToken(options: GetTokenOptions): Promise<AccessToken | undefined> {\n    this.lastCalled = Date.now();\n    const token = await this.credential.getToken(this.scopes, options);\n    this.promise = undefined;\n    return token || undefined;\n  }\n\n  /**\n   * Requests a new token if we're not currently waiting for a new token.\n   * Returns null if the required time between each call hasn't been reached.\n   */\n  public refresh(options: GetTokenOptions): Promise<AccessToken | undefined> {\n    if (!this.promise) {\n      this.promise = this.getToken(options);\n    }\n\n    return this.promise;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpHeaders } from \"../httpHeaders\";\nimport * as base64 from \"../util/base64\";\nimport { Constants } from \"../util/constants\";\nimport { WebResourceLike } from \"../webResource\";\nimport { ServiceClientCredentials } from \"./serviceClientCredentials\";\nconst HeaderConstants = Constants.HeaderConstants;\nconst DEFAULT_AUTHORIZATION_SCHEME = \"Basic\";\n\nexport class BasicAuthenticationCredentials implements ServiceClientCredentials {\n  userName: string;\n  password: string;\n  authorizationScheme: string = DEFAULT_AUTHORIZATION_SCHEME;\n\n  /**\n   * Creates a new BasicAuthenticationCredentials object.\n   *\n   * @param userName - User name.\n   * @param password - Password.\n   * @param authorizationScheme - The authorization scheme.\n   */\n  constructor(\n    userName: string,\n    password: string,\n    authorizationScheme: string = DEFAULT_AUTHORIZATION_SCHEME\n  ) {\n    if (userName === null || userName === undefined || typeof userName.valueOf() !== \"string\") {\n      throw new Error(\"userName cannot be null or undefined and must be of type string.\");\n    }\n    if (password === null || password === undefined || typeof password.valueOf() !== \"string\") {\n      throw new Error(\"password cannot be null or undefined and must be of type string.\");\n    }\n    this.userName = userName;\n    this.password = password;\n    this.authorizationScheme = authorizationScheme;\n  }\n\n  /**\n   * Signs a request with the Authentication header.\n   *\n   * @param webResource - The WebResourceLike to be signed.\n   * @returns The signed request object.\n   */\n  signRequest(webResource: WebResourceLike): Promise<WebResourceLike> {\n    const credentials = `${this.userName}:${this.password}`;\n    const encodedCredentials = `${this.authorizationScheme} ${base64.encodeString(credentials)}`;\n    if (!webResource.headers) webResource.headers = new HttpHeaders();\n    webResource.headers.set(HeaderConstants.AUTHORIZATION, encodedCredentials);\n    return Promise.resolve(webResource);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpHeaders } from \"../httpHeaders\";\nimport { WebResourceLike } from \"../webResource\";\nimport { ServiceClientCredentials } from \"./serviceClientCredentials\";\n\n/**\n * Describes the options to be provided while creating an instance of ApiKeyCredentials\n */\nexport interface ApiKeyCredentialOptions {\n  /**\n   * A key value pair of the header parameters that need to be applied to the request.\n   */\n  inHeader?: { [x: string]: any };\n  /**\n   * A key value pair of the query parameters that need to be applied to the request.\n   */\n  inQuery?: { [x: string]: any };\n}\n\n/**\n * Authenticates to a service using an API key.\n */\nexport class ApiKeyCredentials implements ServiceClientCredentials {\n  /**\n   * A key value pair of the header parameters that need to be applied to the request.\n   */\n  private readonly inHeader?: { [x: string]: any };\n  /**\n   * A key value pair of the query parameters that need to be applied to the request.\n   */\n  private readonly inQuery?: { [x: string]: any };\n\n  /**\n   * @param options - Specifies the options to be provided for auth. Either header or query needs to be provided.\n   */\n  constructor(options: ApiKeyCredentialOptions) {\n    if (!options || (options && !options.inHeader && !options.inQuery)) {\n      throw new Error(\n        `options cannot be null or undefined. Either \"inHeader\" or \"inQuery\" property of the options object needs to be provided.`\n      );\n    }\n    this.inHeader = options.inHeader;\n    this.inQuery = options.inQuery;\n  }\n\n  /**\n   * Signs a request with the values provided in the inHeader and inQuery parameter.\n   *\n   * @param webResource - The WebResourceLike to be signed.\n   * @returns The signed request object.\n   */\n  signRequest(webResource: WebResourceLike): Promise<WebResourceLike> {\n    if (!webResource) {\n      return Promise.reject(\n        new Error(`webResource cannot be null or undefined and must be of type \"object\".`)\n      );\n    }\n\n    if (this.inHeader) {\n      if (!webResource.headers) {\n        webResource.headers = new HttpHeaders();\n      }\n      for (const headerName in this.inHeader) {\n        webResource.headers.set(headerName, this.inHeader[headerName]);\n      }\n    }\n\n    if (this.inQuery) {\n      if (!webResource.url) {\n        return Promise.reject(new Error(`url cannot be null in the request object.`));\n      }\n      if (webResource.url.indexOf(\"?\") < 0) {\n        webResource.url += \"?\";\n      }\n      for (const key in this.inQuery) {\n        if (!webResource.url.endsWith(\"?\")) {\n          webResource.url += \"&\";\n        }\n        webResource.url += `${key}=${this.inQuery[key]}`;\n      }\n    }\n\n    return Promise.resolve(webResource);\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { ApiKeyCredentials, ApiKeyCredentialOptions } from \"./apiKeyCredentials\";\n\nexport class TopicCredentials extends ApiKeyCredentials {\n  /**\n   * Creates a new EventGrid TopicCredentials object.\n   *\n   * @param topicKey - The EventGrid topic key\n   */\n  constructor(topicKey: string) {\n    if (!topicKey || (topicKey && typeof topicKey !== \"string\")) {\n      throw new Error(\"topicKey cannot be null or undefined and must be of type string.\");\n    }\n    const options: ApiKeyCredentialOptions = {\n      inHeader: {\n        \"aeg-sas-key\": topicKey\n      }\n    };\n    super(options);\n  }\n}\n"], "names": ["uuidv4", "base64.decodeString", "base64.encodeByteArray", "utils.isValidUuid", "utils.isDuration", "inspect", "createClientLogger", "Transform", "abortController", "AbortController", "AbortError", "tunnel.httpsOverHttps", "tunnel.httpsOverHttp", "tunnel.httpOverHttps", "tunnel.httpOverHttp", "tough.<PERSON><PERSON><PERSON><PERSON>", "https.Agent", "http.Agent", "https.globalAgent", "http.globalAgent", "HttpPipelineLogLevel", "logger", "core<PERSON>ogger", "xml2js.Builder", "xml2js.Parser", "RetryMode", "os.arch", "os.type", "os.release", "utils.generateUuid", "retry", "QueryCollectionFormat", "StandardAbortMessage", "createSpanFunction", "SpanKind", "getTraceParentHeader", "isSpanContextValid", "SpanStatusCode", "DefaultHttpClient", "isTokenCredential", "utils.prepareXMLRootList", "utils.isPrimitiveType", "URL", "coreTracingCreateSpanFunction", "base64.encodeString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAEA;;;AAGA,SAAS,YAAY,CAAC,UAAkB;IACtC,OAAO,UAAU,CAAC,WAAW,EAAE,CAAC;AAClC,CAAC;SA4Ee,iBAAiB,CAAC,MAAgB;IAChD,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACxC,MAAM,UAAU,GAAG,MAWlB,CAAC;QACF,IACE,OAAO,UAAU,CAAC,UAAU,KAAK,UAAU;YAC3C,OAAO,UAAU,CAAC,KAAK,KAAK,UAAU;YACtC,OAAO,UAAU,CAAC,GAAG,KAAK,UAAU;YACpC,OAAO,UAAU,CAAC,GAAG,KAAK,UAAU;YACpC,OAAO,UAAU,CAAC,QAAQ,KAAK,UAAU;YACzC,OAAO,UAAU,CAAC,MAAM,KAAK,UAAU;YACvC,OAAO,UAAU,CAAC,YAAY,KAAK,UAAU;YAC7C,OAAO,UAAU,CAAC,YAAY,KAAK,UAAU;YAC7C,OAAO,UAAU,CAAC,WAAW,KAAK,UAAU;YAC5C,OAAO,UAAU,CAAC,MAAM,KAAK,UAAU,EACvC;YACA,OAAO,IAAI,CAAC;SACb;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;MAGa,WAAW;IAGtB,YAAY,UAA2B;QACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,UAAU,EAAE;YACd,KAAK,MAAM,UAAU,IAAI,UAAU,EAAE;gBACnC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;aAC9C;SACF;KACF;;;;;;;IAQM,GAAG,CAAC,UAAkB,EAAE,WAA4B;QACzD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,GAAG;YAC3C,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;SAC9B,CAAC;KACH;;;;;;IAOM,GAAG,CAAC,UAAkB;QAC3B,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QACtE,OAAO,CAAC,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;KAC3C;;;;IAKM,QAAQ,CAAC,UAAkB;QAChC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;KACrD;;;;;;IAOM,MAAM,CAAC,UAAkB;QAC9B,MAAM,MAAM,GAAY,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC;KACf;;;;IAKM,UAAU;QACf,MAAM,MAAM,GAAmB,EAAE,CAAC;QAClC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;SAClD;QACD,OAAO,MAAM,CAAC;KACf;;;;IAKM,YAAY;QACjB,MAAM,OAAO,GAAiB,EAAE,CAAC;QACjC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;SAC3C;QACD,OAAO,OAAO,CAAC;KAChB;;;;IAKM,WAAW;QAChB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACnC;QACD,OAAO,WAAW,CAAC;KACpB;;;;IAKM,YAAY;QACjB,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SACrC;QACD,OAAO,YAAY,CAAC;KACrB;;;;IAKM,MAAM;QACX,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;KAC1B;;;;IAKM,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;KACtC;;;;IAKM,KAAK;QACV,MAAM,sBAAsB,GAAmB,EAAE,CAAC;QAClD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACvD,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;SACpD;QACD,OAAO,IAAI,WAAW,CAAC,sBAAsB,CAAC,CAAC;KAChD;;;ACpPH;AACA;AAEA;;;;AAIA,SAAgB,YAAY,CAAC,KAAa;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC/C,CAAC;AAED;;;;AAIA,SAAgB,eAAe,CAAC,KAAiB;;;IAG/C,MAAM,WAAW,GAAG,KAAK,YAAY,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;IAC/F,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED;;;;AAIA,SAAgB,YAAY,CAAC,KAAa;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACtC,CAAC;;AC5BD;AACA;AAEA,MAAa,SAAS,GAAG;;;;IAIvB,eAAe,EAAE,OAAO;;;;IAKxB,IAAI,EAAE,OAAO;;;;IAKb,KAAK,EAAE,QAAQ;;;;IAKf,UAAU,EAAE,YAAY;;;;IAKxB,WAAW,EAAE,aAAa;;;;IAK1B,QAAQ,EAAE,UAAU;;;;IAKpB,SAAS,EAAE,WAAW;IAEtB,aAAa,EAAE;;;;QAIb,SAAS,EAAE;YACT,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,KAAK;YACV,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO;SACf;QAED,WAAW,EAAE;YACX,eAAe,EAAE,GAAG;YACpB,kBAAkB,EAAE,GAAG;SACxB;KACF;;;;IAKD,eAAe,EAAE;;;;QAIf,aAAa,EAAE,eAAe;QAE9B,oBAAoB,EAAE,QAAQ;;;;;;QAO9B,WAAW,EAAE,aAAa;;;;QAK1B,UAAU,EAAE,YAAY;KACzB;CACF;;AClFD;AACA;AAEA;;;AAGA,MAAa,WAAW,GAAG,GAAG,CAAC;AAC/B;;;AAGA,MAAa,WAAW,GAAG,GAAG;;ACV9B;AACA,AASA,MAAM,cAAc,GAAG,gFAAgF,CAAC;AAExG;;;AAGA,MAAa,MAAM,GACjB,OAAO,OAAO,KAAK,WAAW;IAC9B,CAAC,CAAC,OAAO,CAAC,OAAO;IACjB,CAAC,CAAC,OAAO,CAAC,QAAQ;IAClB,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;AAE1B,AAUA;;;;;;AAMA,SAAgB,SAAS,CAAC,GAAW;IACnC,OAAO,kBAAkB,CAAC,GAAG,CAAC;SAC3B,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;SACpB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;SACpB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;SACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;SACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED;;;;;;;AAOA,SAAgB,aAAa,CAAC,QAA+B;IAC3D,MAAM,gBAAgB,GAAQ,EAAE,CAAC;IACjC,gBAAgB,CAAC,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC;IAC5C,gBAAgB,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;IAC5C,gBAAgB,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC1C,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;;;;;;AAOA,SAAgB,YAAY,CAAC,OAAwB;IACnD,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;IACxC,IAAI,eAAe,CAAC,OAAO,EAAE;QAC3B,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;KACjD;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;;;;AAMA,SAAgB,WAAW,CAAC,IAAY;IACtC,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC;AAED;;;;;AAKA,SAAgB,YAAY;IAC1B,OAAOA,OAAM,EAAE,CAAC;AAClB,CAAC;AAED;;;;;;;;;AASA,SAAgB,2BAA2B,CACzC,gBAA4B,EAC5B,SAAkB;IAElB,IAAI,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACxC,gBAAgB,CAAC,OAAO,CAAC,CAAC,cAAc;QACtC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACtC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAqBD;;;;;;AAMA;AACA,SAAgB,iBAAiB,CAAC,OAAqB;IACrD,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;KACzD;;IAED,OAAO,CAAC,EAAY;QAClB,OAAO;aACJ,IAAI,CAAC,CAAC,IAAS;;YAEd,OAAO,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SAC5B,CAAC;aACD,KAAK,CAAC,CAAC,GAAU;;YAEhB,EAAE,CAAC,GAAG,CAAC,CAAC;SACT,CAAC,CAAC;KACN,CAAC;AACJ,CAAC;AAED;;;;;AAKA,SAAgB,wBAAwB,CACtC,OAAuC;IAEvC,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;KACzD;IACD,OAAO,CAAC,EAAsB;QAC5B,OAAO;aACJ,IAAI,CAAC,CAAC,IAA2B;YAChC,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,UAAe,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SAClF,CAAC;aACD,KAAK,CAAC,CAAC,GAAU;YAChB,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;SAC3B,CAAC,CAAC;KACN,CAAC;AACJ,CAAC;AAED,SAAgB,kBAAkB,CAChC,GAAY,EACZ,WAAmB,EACnB,eAAwB,EACxB,YAAqB;IAErB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACvB,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;KACb;IAED,IAAI,CAAC,eAAe,IAAI,CAAC,YAAY,EAAE;QACrC,OAAO,EAAE,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;KAC/B;IAED,MAAM,MAAM,GAAG,EAAE,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;IACtC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,eAAe,GAAG,YAAY,EAAE,CAAC;IAC1D,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;AAKA,SAAgB,WAAW,CAAC,eAAwB,EAAE,WAAkB;IACtE,MAAM,mBAAmB,GAAG,eAE3B,CAAC;IACF,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU;QAC7B,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI;YAC5D,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SAClE,CAAC,CAAC;KACJ,CAAC,CAAC;AACL,CAAC;AAED,MAAM,mBAAmB,GAAG,qKAAqK,CAAC;AAElM;;;;;AAKA,SAAgB,UAAU,CAAC,KAAa;IACtC,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAED;;;;;;;AAOA,SAAgB,UAAU,CACxB,KAAyB,EACzB,WAAmB,EACnB,YAAoB;IAEpB,OAAO,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;AAC5F,CAAC;AAED;;;;;;AAMA,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,IAAI,CAAC;AACtF,CAAC;AAED,SAAgB,mBAAmB,CAAC,IAAY;IAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACrB,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC1B;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;QAC1C,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KACxC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAOD;;;;AAIA,SAAgB,QAAQ,CAAC,KAAc;IACrC,QACE,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACrB,EAAE,KAAK,YAAY,MAAM,CAAC;QAC1B,EAAE,KAAK,YAAY,IAAI,CAAC,EACxB;AACJ,CAAC;;ACtRD;AACA,MAOa,UAAU;IACrB,YACkB,eAAuC,EAAE,EACzC,KAAe;QADf,iBAAY,GAAZ,YAAY,CAA6B;QACzC,UAAK,GAAL,KAAK,CAAU;KAC7B;IAEJ,mBAAmB,CAAC,MAAc,EAAE,KAAc,EAAE,UAAkB;QACpE,MAAM,cAAc,GAAG,CACrB,cAAuC,EACvC,eAAoB;YAEpB,MAAM,IAAI,KAAK,CACb,IAAI,UAAU,iBAAiB,KAAK,oCAAoC,cAAc,MAAM,eAAe,GAAG,CAC/G,CAAC;SACH,CAAC;QACF,IAAI,MAAM,CAAC,WAAW,IAAI,KAAK,IAAI,SAAS,EAAE;YAC5C,MAAM,aAAa,GAAG,KAAe,CAAC;YACtC,MAAM,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,OAAO,EACP,WAAW,EACZ,GAAG,MAAM,CAAC,WAAW,CAAC;YACvB,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,IAAI,gBAAgB,EAAE;gBACtE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACtD;YACD,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,IAAI,gBAAgB,EAAE;gBACtE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACtD;YACD,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,GAAG,gBAAgB,EAAE;gBACrE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACtD;YACD,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,GAAG,gBAAgB,EAAE;gBACrE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACtD;YACD,MAAM,YAAY,GAAG,KAAc,CAAC;YACpC,IAAI,QAAQ,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,QAAQ,EAAE;gBAC3D,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aACtC;YACD,IAAI,SAAS,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,SAAS,EAAE;gBAC7D,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;aACxC;YACD,IAAI,QAAQ,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,QAAQ,EAAE;gBAC3D,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aACtC;YACD,IAAI,SAAS,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,SAAS,EAAE;gBAC7D,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;aACxC;YACD,IAAI,UAAU,IAAI,SAAS,IAAI,aAAa,GAAG,UAAU,KAAK,CAAC,EAAE;gBAC/D,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;aAC1C;YACD,IAAI,OAAO,EAAE;gBACX,MAAM,OAAO,GAAW,OAAO,OAAO,KAAK,QAAQ,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;gBACpF,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;oBAC9D,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;iBACpC;aACF;YACD,IACE,WAAW;gBACX,YAAY,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,CAAS,EAAE,EAAc,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EACnF;gBACA,cAAc,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;aAC5C;SACF;KACF;;;;;;;;;;IAWD,SAAS,CACP,MAAc,EACd,MAAe,EACf,UAAmB,EACnB,UAA6B,EAAE;;QAE/B,MAAM,cAAc,GAAgC;YAClD,QAAQ,EAAE,MAAA,OAAO,CAAC,QAAQ,mCAAI,EAAE;YAChC,WAAW,EAAE,MAAA,OAAO,CAAC,WAAW,mCAAI,KAAK;YACzC,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,WAAW;SAC9C,CAAC;QACF,IAAI,OAAO,GAAQ,EAAE,CAAC;QACtB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAc,CAAC;QAC9C,IAAI,CAAC,UAAU,EAAE;YACf,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;SACrC;QACD,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YAC5C,OAAO,GAAG,EAAE,CAAC;SACd;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;SAC9B;;;;;;;;;;QAYD,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;QAEtC,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,uBAAuB,CAAC,CAAC;SACvD;QACD,IAAI,QAAQ,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,SAAS,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,+BAA+B,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,EAAE;YACtD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,kBAAkB,CAAC,CAAC;SAClD;QAED,IAAI,MAAM,IAAI,SAAS,EAAE;YACvB,OAAO,GAAG,MAAM,CAAC;SAClB;aAAM;;YAEL,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;YACrD,IAAI,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;gBACvC,OAAO,GAAG,MAAM,CAAC;aAClB;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,+CAA+C,CAAC,KAAK,IAAI,EAAE;gBACrF,OAAO,GAAG,mBAAmB,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;aAC/D;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;gBAC/C,MAAM,UAAU,GAAe,MAAoB,CAAC;gBACpD,OAAO,GAAG,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;aAChF;iBAAM,IACL,UAAU,CAAC,KAAK,CAAC,sDAAsD,CAAC,KAAK,IAAI,EACjF;gBACA,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;aAC9D;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAoB,CAAC,CAAC;aACpE;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAoB,CAAC,CAAC;aACpE;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;gBACnD,OAAO,GAAG,qBAAqB,CAC7B,IAAI,EACJ,MAAwB,EACxB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;aACH;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;gBACrD,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAA0B,EAC1B,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;aACH;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,sBAAsB,CAC9B,IAAI,EACJ,MAAyB,EACzB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;aACH;SACF;QACD,OAAO,OAAO,CAAC;KAChB;;;;;;;;;;IAWD,WAAW,CACT,MAAc,EACd,YAAqB,EACrB,UAAkB,EAClB,UAA6B,EAAE;;QAE/B,MAAM,cAAc,GAAgC;YAClD,QAAQ,EAAE,MAAA,OAAO,CAAC,QAAQ,mCAAI,EAAE;YAChC,WAAW,EAAE,MAAA,OAAO,CAAC,WAAW,mCAAI,KAAK;YACzC,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,WAAW;SAC9C,CAAC;QACF,IAAI,YAAY,IAAI,SAAS,EAAE;YAC7B,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;;;;gBAIzE,YAAY,GAAG,EAAE,CAAC;aACnB;;YAED,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE;gBACrC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;aACpC;YACD,OAAO,YAAY,CAAC;SACrB;QAED,IAAI,OAAY,CAAC;QACjB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,UAAU,EAAE;YACf,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;SACrC;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;YAC7C,OAAO,GAAG,wBAAwB,CAChC,IAAI,EACJ,MAAyB,EACzB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;SACH;aAAM;YACL,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;gBAC7C,MAAM,gBAAgB,GAAG,YAAuC,CAAC;;;;;;gBAMjE,IACE,gBAAgB,CAAC,WAAW,CAAC,IAAI,SAAS;oBAC1C,gBAAgB,CAAC,UAAU,CAAC,IAAI,SAAS,EACzC;oBACA,YAAY,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;iBAC7C;aACF;YAED,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;gBAC1C,OAAO,GAAG,UAAU,CAAC,YAAsB,CAAC,CAAC;gBAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;oBAClB,OAAO,GAAG,YAAY,CAAC;iBACxB;aACF;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;gBAClD,IAAI,YAAY,KAAK,MAAM,EAAE;oBAC3B,OAAO,GAAG,IAAI,CAAC;iBAChB;qBAAM,IAAI,YAAY,KAAK,OAAO,EAAE;oBACnC,OAAO,GAAG,KAAK,CAAC;iBACjB;qBAAM;oBACL,OAAO,GAAG,YAAY,CAAC;iBACxB;aACF;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,kDAAkD,CAAC,KAAK,IAAI,EAAE;gBACxF,OAAO,GAAG,YAAY,CAAC;aACxB;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,oCAAoC,CAAC,KAAK,IAAI,EAAE;gBAC1E,OAAO,GAAG,IAAI,IAAI,CAAC,YAAsB,CAAC,CAAC;aAC5C;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;gBACnD,OAAO,GAAG,cAAc,CAAC,YAAsB,CAAC,CAAC;aAClD;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAGC,YAAmB,CAAC,YAAsB,CAAC,CAAC;aACvD;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,oBAAoB,CAAC,YAAsB,CAAC,CAAC;aACxD;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;gBACnD,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAAwB,EACxB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;aACH;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;gBACrD,OAAO,GAAG,yBAAyB,CACjC,IAAI,EACJ,MAA0B,EAC1B,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;aACH;SACF;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC;SAC/B;QAED,OAAO,OAAO,CAAC;KAChB;CACF;AAED,SAAS,OAAO,CAAC,GAAW,EAAE,EAAU;IACtC,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACrB,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QAC1C,EAAE,GAAG,CAAC;KACP;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAW;IACpC,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,EAAE,MAAM,YAAY,UAAU,CAAC,EAAE;QACnC,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;KAC5F;;IAED,MAAM,GAAG,GAAGC,eAAsB,CAAC,MAAM,CAAC,CAAC;;IAE3C,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;SACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,oBAAoB,CAAC,GAAW;IACvC,IAAI,CAAC,GAAG,EAAE;QACR,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;QAC5C,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;KACxF;;IAED,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;IAEhD,OAAOD,YAAmB,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAwB;IAClD,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,IAAI,EAAE;QACR,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEjC,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;YAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;gBACzC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;aACvD;iBAAM;gBACL,YAAY,IAAI,IAAI,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3B,YAAY,GAAG,EAAE,CAAC;aACnB;SACF;KACF;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,cAAc,CAAC,CAAgB;IACtC,IAAI,CAAC,CAAC,EAAE;QACN,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;QACnC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAW,CAAC,CAAC;KAC3B;IACD,OAAO,IAAI,CAAC,KAAK,CAAE,CAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,cAAc,CAAC,CAAS;IAC/B,IAAI,CAAC,CAAC,EAAE;QACN,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAgB,EAAE,UAAkB,EAAE,KAAU;IAC3E,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;QACzC,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YACxC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,eAAe,KAAK,0BAA0B,CAAC,CAAC;aAC9E;SACF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,IAAI,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;gBACvC,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,gBAAgB,KAAK,2BAA2B,CAAC,CAAC;aAChF;SACF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC7C,IAAI,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAIE,WAAiB,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtE,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,gBAAgB,KAAK,4CAA4C,CAC/E,CAAC;aACH;SACF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;YAChD,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;gBAC9B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,eAAe,KAAK,2BAA2B,CAAC,CAAC;aAC/E;SACF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,MAAM,UAAU,GAAG,OAAO,KAAK,CAAC;YAChC,IACE,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,UAAU;gBACzB,EAAE,KAAK,YAAY,WAAW,CAAC;gBAC/B,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC1B,EAAE,CAAC,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,EACpF;gBACA,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,uGAAuG,CACrH,CAAC;aACH;SACF;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAkB,EAAE,aAAyB,EAAE,KAAU;IAClF,IAAI,CAAC,aAAa,EAAE;QAClB,MAAM,IAAI,KAAK,CACb,qDAAqD,UAAU,mBAAmB,CACnF,CAAC;KACH;IACD,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI;QACxC,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACtC,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC;SACnD;QACD,OAAO,IAAI,KAAK,KAAK,CAAC;KACvB,CAAC,CAAC;IACH,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,KAAK,CACb,GAAG,KAAK,6BAA6B,UAAU,2BAA2B,IAAI,CAAC,SAAS,CACtF,aAAa,CACd,GAAG,CACL,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAiB;IACnE,IAAI,WAAW,GAAW,EAAE,CAAC;IAC7B,IAAI,KAAK,IAAI,SAAS,EAAE;QACtB,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,8BAA8B,CAAC,CAAC;SAC9D;QACD,WAAW,GAAGD,eAAsB,CAAC,KAAK,CAAC,CAAC;KAC7C;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAiB;IACnE,IAAI,WAAW,GAAW,EAAE,CAAC;IAC7B,IAAI,KAAK,IAAI,SAAS,EAAE;QACtB,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,8BAA8B,CAAC,CAAC;SAC9D;QACD,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;KAC9C;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB,EAAE,KAAU,EAAE,UAAkB;IAC1E,IAAI,KAAK,IAAI,SAAS,EAAE;QACtB,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YACtC,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;gBACA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,4DAA4D,CAAC,CAAC;aAC5F;YACD,KAAK;gBACH,KAAK,YAAY,IAAI;sBACjB,KAAK,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;sBACpC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;SACtD;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YACjD,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;gBACA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,4DAA4D,CAAC,CAAC;aAC5F;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;SACrF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;YACxD,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;gBACA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,6DAA6D,CAAC,CAAC;aAC7F;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;SACrF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YACjD,IACE,EACE,KAAK,YAAY,IAAI;iBACpB,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;gBACA,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,qEAAqE;oBAChF,mDAAmD,CACtD,CAAC;aACH;YACD,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;SAC/B;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YACjD,IAAI,CAACE,UAAgB,CAAC,KAAK,CAAC,EAAE;gBAC5B,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,sDAAsD,KAAK,IAAI,CAC7E,CAAC;aACH;SACF;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAsB,EACtB,MAAsB,EACtB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAoC;IAEpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,yBAAyB,CAAC,CAAC;KACzD;IACD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IACxC,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACnD,MAAM,IAAI,KAAK,CACb,wDAAwD;YACtD,0CAA0C,UAAU,GAAG,CAC1D,CAAC;KACH;IACD,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAE1F,IAAI,KAAK,IAAI,WAAW,CAAC,YAAY,EAAE;YACrC,MAAM,QAAQ,GAAG,WAAW,CAAC,kBAAkB;kBAC3C,SAAS,WAAW,CAAC,kBAAkB,EAAE;kBACzC,OAAO,CAAC;YACZ,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;gBACzC,SAAS,CAAC,CAAC,CAAC,qBAAQ,eAAe,CAAE,CAAC;gBACtC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;aACtE;iBAAM;gBACL,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBAClB,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;gBACnD,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;aACtE;SACF;aAAM;YACL,SAAS,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;SAChC;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAwB,EACxB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAoC;IAEpC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,0BAA0B,CAAC,CAAC;KAC1D;IACD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;IACpC,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QAC/C,MAAM,IAAI,KAAK,CACb,2DAA2D;YACzD,0CAA0C,UAAU,GAAG,CAC1D,CAAC;KACH;IACD,MAAM,cAAc,GAA2B,EAAE,CAAC;IAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACrC,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;;QAE1F,cAAc,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,SAAS,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;KACrF;;IAGD,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;QAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,GAAG,SAAS,MAAM,CAAC,kBAAkB,EAAE,GAAG,OAAO,CAAC;QAE5F,MAAM,MAAM,GAAG,cAAc,CAAC;QAC9B,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QAC1D,OAAO,MAAM,CAAC;KACf;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;;AAMA,SAAS,2BAA2B,CAClC,UAAsB,EACtB,MAAuB,EACvB,UAAkB;IAElB,MAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;IAE9D,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;QAClD,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,OAAO,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,CAAC,oBAAoB,CAAC;KAC/C;IAED,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED;;;;;;AAMA,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAuB,EACvB,UAAkB;IAElB,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;IACxC,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,KAAK,CACb,yBAAyB,UAAU,oCAAoC,IAAI,CAAC,SAAS,CACnF,MAAM,EACN,SAAS,EACT,CAAC,CACF,IAAI,CACN,CAAC;KACH;IAED,OAAO,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;AAKA,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,UAAkB;IAElB,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;IAC7C,IAAI,CAAC,UAAU,EAAE;QACf,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,mDAAmD,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC;SAC/F;QACD,UAAU,GAAG,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,KAAK,CACb,qDAAqD;gBACnD,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,cACpC,MAAM,CAAC,IAAI,CAAC,SACd,iBAAiB,UAAU,IAAI,CAClC,CAAC;SACH;KACF;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAoC;IAEpC,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;QAC9D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;KACzE;IAED,IAAI,MAAM,IAAI,SAAS,EAAE;QACvB,MAAM,OAAO,GAAQ,EAAE,CAAC;QACxB,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC1E,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACzC,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,cAAc,CAAC,QAAQ,EAAE;gBAC3B,SAAS;aACV;YAED,IAAI,QAA4B,CAAC;YACjC,IAAI,YAAY,GAAQ,OAAO,CAAC;YAChC,IAAI,UAAU,CAAC,KAAK,EAAE;gBACpB,IAAI,cAAc,CAAC,YAAY,EAAE;oBAC/B,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC;iBACnC;qBAAM;oBACL,QAAQ,GAAG,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC;iBACpE;aACF;iBAAM;gBACL,MAAM,KAAK,GAAG,kBAAkB,CAAC,cAAc,CAAC,cAAe,CAAC,CAAC;gBACjE,QAAQ,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;gBAEvB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;oBAC5B,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAC3C,IACE,WAAW,IAAI,SAAS;yBACvB,MAAM,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,cAAc,CAAC,YAAY,KAAK,SAAS,CAAC,EACvE;wBACA,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;qBAC7B;oBACD,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;iBACvC;aACF;YAED,IAAI,YAAY,IAAI,SAAS,EAAE;gBAC7B,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;oBAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB;0BACtC,SAAS,MAAM,CAAC,kBAAkB,EAAE;0BACpC,OAAO,CAAC;oBACZ,YAAY,CAAC,WAAW,CAAC,mCACpB,YAAY,CAAC,WAAW,CAAC,KAC5B,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,GAChC,CAAC;iBACH;gBACD,MAAM,kBAAkB,GACtB,cAAc,CAAC,cAAc,KAAK,EAAE;sBAChC,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC,cAAc;sBAChD,UAAU,CAAC;gBAEjB,IAAI,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC9B,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAC5F,IACE,wBAAwB;oBACxB,wBAAwB,CAAC,UAAU,KAAK,GAAG;oBAC3C,WAAW,IAAI,SAAS,EACxB;oBACA,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC;iBACrC;gBAED,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAC1C,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;gBAEF,IAAI,eAAe,KAAK,SAAS,IAAI,QAAQ,IAAI,SAAS,EAAE;oBAC1D,MAAM,KAAK,GAAG,iBAAiB,CAAC,cAAc,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;oBACjF,IAAI,KAAK,IAAI,cAAc,CAAC,cAAc,EAAE;;;;wBAI1C,YAAY,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;wBAC5D,YAAY,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC;qBACvD;yBAAM,IAAI,KAAK,IAAI,cAAc,CAAC,YAAY,EAAE;wBAC/C,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,cAAe,GAAG,KAAK,EAAE,CAAC;qBACtE;yBAAM;wBACL,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;qBAChC;iBACF;aACF;SACF;QAED,MAAM,0BAA0B,GAAG,2BAA2B,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC/F,IAAI,0BAA0B,EAAE;YAC9B,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,KAAK,MAAM,cAAc,IAAI,MAAM,EAAE;gBACnC,MAAM,oBAAoB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,cAAc,CAAC,CAAC;gBAC5E,IAAI,oBAAoB,EAAE;oBACxB,OAAO,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,SAAS,CAC5C,0BAA0B,EAC1B,MAAM,CAAC,cAAc,CAAC,EACtB,UAAU,GAAG,IAAI,GAAG,cAAc,GAAG,IAAI,EACzC,OAAO,CACR,CAAC;iBACH;aACF;SACF;QAED,OAAO,OAAO,CAAC;KAChB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,iBAAiB,CACxB,cAAsB,EACtB,eAAoB,EACpB,KAAc,EACd,OAAoC;IAEpC,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;QAC1C,OAAO,eAAe,CAAC;KACxB;IAED,MAAM,QAAQ,GAAG,cAAc,CAAC,kBAAkB;UAC9C,SAAS,cAAc,CAAC,kBAAkB,EAAE;UAC5C,OAAO,CAAC;IACZ,MAAM,YAAY,GAAG,EAAE,CAAC,QAAQ,GAAG,cAAc,CAAC,YAAY,EAAE,CAAC;IAEjE,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QACpD,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE;YAChC,OAAO,eAAe,CAAC;SACxB;aAAM;YACL,MAAM,MAAM,qBAAa,eAAe,CAAE,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;YACnC,OAAO,MAAM,CAAC;SACf;KACF;IACD,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;IAC7C,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;IACnC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,oBAAoB,CAAC,YAAoB,EAAE,OAAoC;IACtF,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAAsB,EACtB,MAAuB,EACvB,YAAiB,EACjB,UAAkB,EAClB,OAAoC;;IAEpC,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;QAC9D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;KACnF;IAED,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC1E,IAAI,QAAQ,GAA2B,EAAE,CAAC;IAC1C,MAAM,oBAAoB,GAAa,EAAE,CAAC;IAE1C,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;QACzC,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,CAAC;QAClE,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,cAAc,CAAC;QACnE,IAAI,kBAAkB,GAAG,UAAU,CAAC;QACpC,IAAI,cAAc,KAAK,EAAE,IAAI,cAAc,KAAK,SAAS,EAAE;YACzD,kBAAkB,GAAG,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC;SACxD;QAED,MAAM,sBAAsB,GAAI,cAAmC,CAAC,sBAAsB,CAAC;QAC3F,IAAI,sBAAsB,EAAE;YAC1B,MAAM,UAAU,GAAQ,EAAE,CAAC;YAC3B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBACjD,IAAI,SAAS,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE;oBAChD,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACpF,cAAmC,CAAC,IAAI,CAAC,KAAK,EAC/C,YAAY,CAAC,SAAS,CAAC,EACvB,kBAAkB,EAClB,OAAO,CACR,CAAC;iBACH;gBAED,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACtC;YACD,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;SAC5B;aAAM,IAAI,UAAU,CAAC,KAAK,EAAE;YAC3B,IAAI,cAAc,CAAC,cAAc,IAAI,YAAY,CAAC,WAAW,CAAC,EAAE;gBAC9D,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,YAAY,CAAC,WAAW,CAAC,CAAC,OAAQ,CAAC,EACnC,kBAAkB,EAClB,OAAO,CACR,CAAC;aACH;iBAAM;gBACL,MAAM,YAAY,GAAG,cAAc,IAAI,OAAO,IAAI,cAAc,CAAC;gBACjE,IAAI,cAAc,CAAC,YAAY,EAAE;;;;;;;;;;;;;;;oBAe/B,MAAM,OAAO,GAAG,YAAY,CAAC,OAAQ,CAAC,CAAC;oBACvC,MAAM,WAAW,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,cAAe,CAAC,mCAAI,EAAE,CAAC;oBACrD,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;iBACH;qBAAM;oBACL,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAa,CAAC,CAAC;oBAC7C,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,QAAQ,EACR,kBAAkB,EAClB,OAAO,CACR,CAAC;iBACH;aACF;SACF;aAAM;;YAEL,IAAI,gBAAgB,CAAC;YACrB,IAAI,GAAG,GAAG,YAAY,CAAC;;YAEvB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,IAAI,CAAC,GAAG;oBAAE,MAAM;gBAChB,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;aACjB;YACD,gBAAgB,GAAG,GAAG,CAAC;YACvB,MAAM,wBAAwB,GAAG,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC;;;;;;;;;;YAUtE,IACE,wBAAwB;gBACxB,GAAG,KAAK,wBAAwB,CAAC,UAAU;gBAC3C,gBAAgB,IAAI,SAAS,EAC7B;gBACA,gBAAgB,GAAG,MAAM,CAAC,cAAc,CAAC;aAC1C;YAED,IAAI,eAAe,CAAC;;YAEpB,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,cAAc,KAAK,EAAE,EAAE;gBAC7E,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBACrC,MAAM,aAAa,GAAG,UAAU,CAAC,WAAW,CAC1C,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;;;gBAGF,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC7C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE;wBAC3D,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;qBACtB;iBACF;gBACD,QAAQ,GAAG,aAAa,CAAC;aAC1B;iBAAM,IAAI,gBAAgB,KAAK,SAAS,IAAI,cAAc,CAAC,YAAY,KAAK,SAAS,EAAE;gBACtF,eAAe,GAAG,UAAU,CAAC,WAAW,CACtC,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;gBACF,QAAQ,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;aACjC;SACF;KACF;IAED,MAAM,0BAA0B,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;IACpE,IAAI,0BAA0B,EAAE;QAC9B,MAAM,oBAAoB,GAAG,CAAC,gBAAwB;YACpD,KAAK,MAAM,cAAc,IAAI,UAAU,EAAE;gBACvC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;gBAC5E,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE;oBACjC,OAAO,KAAK,CAAC;iBACd;aACF;YACD,OAAO,IAAI,CAAC;SACb,CAAC;QAEF,KAAK,MAAM,gBAAgB,IAAI,YAAY,EAAE;YAC3C,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;gBAC1C,QAAQ,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAC,WAAW,CACjD,0BAA0B,EAC1B,YAAY,CAAC,gBAAgB,CAAC,EAC9B,UAAU,GAAG,IAAI,GAAG,gBAAgB,GAAG,IAAI,EAC3C,OAAO,CACR,CAAC;aACH;SACF;KACF;SAAM,IAAI,YAAY,EAAE;QACvB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;YAC3C,IACE,QAAQ,CAAC,GAAG,CAAC,KAAK,SAAS;gBAC3B,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACnC,CAAC,oBAAoB,CAAC,GAAG,EAAE,OAAO,CAAC,EACnC;gBACA,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;aACnC;SACF;KACF;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,yBAAyB,CAChC,UAAsB,EACtB,MAAwB,EACxB,YAAiB,EACjB,UAAkB,EAClB,OAAoC;IAEpC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;IAChC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACvC,MAAM,IAAI,KAAK,CACb,2DAA2D;YACzD,0CAA0C,UAAU,EAAE,CACzD,CAAC;KACH;IACD,IAAI,YAAY,EAAE;QAChB,MAAM,cAAc,GAA2B,EAAE,CAAC;QAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;YAC3C,cAAc,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;SAC7F;QACD,OAAO,cAAc,CAAC;KACvB;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAsB,EACtB,YAAiB,EACjB,UAAkB,EAClB,OAAoC;IAEpC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IACpC,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC3C,MAAM,IAAI,KAAK,CACb,wDAAwD;YACtD,0CAA0C,UAAU,EAAE,CACzD,CAAC;KACH;IACD,IAAI,YAAY,EAAE;QAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;;YAEhC,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;SAC/B;QAED,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,SAAS,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACnC,OAAO,EACP,YAAY,CAAC,CAAC,CAAC,EACf,GAAG,UAAU,IAAI,CAAC,GAAG,EACrB,OAAO,CACR,CAAC;SACH;QACD,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,oBAAoB,CAC3B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,uBAAwD;IAExD,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAC5F,IAAI,wBAAwB,EAAE;QAC5B,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,uBAAuB,CAAC,CAAC;QAC5E,IAAI,iBAAiB,IAAI,SAAS,EAAE;YAClC,MAAM,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACrD,IAAI,kBAAkB,IAAI,SAAS,EAAE;gBACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACjE,MAAM,kBAAkB,GACtB,kBAAkB,KAAK,QAAQ;sBAC3B,kBAAkB;sBAClB,QAAQ,GAAG,GAAG,GAAG,kBAAkB,CAAC;gBAC1C,MAAM,iBAAiB,GAAG,UAAU,CAAC,YAAY,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;gBACrF,IAAI,iBAAiB,EAAE;oBACrB,MAAM,GAAG,iBAAiB,CAAC;iBAC5B;aACF;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,sCAAsC,CAC7C,UAAsB,EACtB,MAAuB;IAEvB,QACE,MAAM,CAAC,IAAI,CAAC,wBAAwB;QACpC,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACrE,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EACpE;AACJ,CAAC;AAED,SAAS,iCAAiC,CAAC,UAAsB,EAAE,QAAiB;IAClF,QACE,QAAQ;QACR,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC;QACjC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,wBAAwB,EAC/D;AACJ,CAAC;AAgKD;AACA,SAAgB,eAAe,CAAC,WAAoB;IAClD,MAAM,eAAe,GAAG,WAAsC,CAAC;IAC/D,IAAI,WAAW,IAAI,SAAS;QAAE,OAAO,SAAS,CAAC;IAC/C,IAAI,WAAW,YAAY,UAAU,EAAE;QACrC,WAAW,GAAGF,eAAsB,CAAC,WAAW,CAAC,CAAC;QAClD,OAAO,WAAW,CAAC;KACpB;SAAM,IAAI,WAAW,YAAY,IAAI,EAAE;QACtC,OAAO,WAAW,CAAC,WAAW,EAAE,CAAC;KAClC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;QACrC,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7C;QACD,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QAC1C,MAAM,UAAU,GAA2B,EAAE,CAAC;QAC9C,KAAK,MAAM,QAAQ,IAAI,WAAW,EAAE;YAClC,UAAU,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;SACnE;QACD,OAAO,UAAU,CAAC;KACnB;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;AAGA,SAAS,OAAO,CAAmB,CAAW;IAC5C,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE;QACnB,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KACnB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;AACA,MAAa,UAAU,GAAG,OAAO,CAAC;IAChC,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;IACX,MAAM;IACN,UAAU;IACV,iBAAiB;IACjB,YAAY;IACZ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,UAAU;CACX,CAAC;;ACryCF;AACA,SAqJgB,iBAAiB,CAAC,MAAe;IAC/C,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACxC,MAAM,UAAU,GAAG,MAOlB,CAAC;QACF,IACE,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ;YAClC,OAAO,UAAU,CAAC,MAAM,KAAK,QAAQ;YACrC,OAAO,UAAU,CAAC,OAAO,KAAK,QAAQ;YACtC,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC;YACrC,OAAO,UAAU,CAAC,yBAAyB,KAAK,UAAU;YAC1D,OAAO,UAAU,CAAC,OAAO,KAAK,UAAU;YACxC,OAAO,UAAU,CAAC,KAAK,KAAK,UAAU,EACtC;YACA,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;AAMA,MAAa,WAAW;IA2DtB,YACE,GAAY,EACZ,MAAoB,EACpB,IAAc,EACd,KAA8B,EAC9B,OAAkD,EAClD,kBAA4B,EAC5B,eAAyB,EACzB,WAA6B,EAC7B,OAAgB,EAChB,gBAA4D,EAC5D,kBAA8D,EAC9D,aAA6B,EAC7B,SAAmB,EACnB,kBAA4B,EAC5B,yBAAuC;QAEvC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;QAC3D,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,KAAK,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC/E,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,KAAK,CAAC;QAChD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,YAAY,EAAE,CAAC;KAC/E;;;;;;IAOD,yBAAyB;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACpD;QACD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACjD;KACF;;;;;;IAOD,OAAO,CAAC,OAA8B;QACpC,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SAC/C;QAED,IACE,OAAO,CAAC,MAAM,KAAK,SAAS;YAC5B,OAAO,CAAC,MAAM,KAAK,IAAI;YACvB,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,QAAQ,EAC5C;YACA,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACrD;QAED,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,YAAY,EAAE;YACvC,MAAM,IAAI,KAAK,CACb,kGAAkG,CACnG,CAAC;SACH;QAED,IACE,CAAC,OAAO,CAAC,YAAY,KAAK,SAAS;YACjC,OAAO,CAAC,YAAY,KAAK,IAAI;YAC7B,OAAO,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,QAAQ;aACnD,OAAO,CAAC,GAAG,KAAK,SAAS;gBACxB,OAAO,CAAC,GAAG,KAAK,IAAI;gBACpB,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,QAAQ,CAAC,EAC5C;YACA,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;SACvF;;QAGD,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,IAAI,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,EAAE;gBACnC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;aAC1D;YACD,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;SACxB;;QAGD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAC3F,IAAI,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC7D,MAAM,IAAI,KAAK,CACb,uBAAuB;oBACrB,OAAO,CAAC,MAAM;oBACd,4CAA4C;oBAC5C,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAC/B,CAAC;aACH;SACF;QACD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,EAAiB,CAAC;;QAG1D,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;YACjD,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;gBACpC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;aACnE;YACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACpB,OAAO,CAAC,OAAO,GAAG,8BAA8B,CAAC;aAClD;YACD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,IAAI,GAAG,GACL,OAAO;iBACN,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;iBACjC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YACpD,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;gBAC/B,IAAI,CAAC,cAAc,EAAE;oBACnB,MAAM,IAAI,KAAK,CACb,iBAAiB,YAAY,0EAA0E,CACxG,CAAC;iBACH;gBACD,QAAQ,CAAC,OAAO,CAAC,UAAS,IAAI;oBAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACxC,MAAM,SAAS,GAAI,cAAyC,CAAC,aAAa,CAAC,CAAC;oBAC5E,IACE,SAAS,KAAK,IAAI;wBAClB,SAAS,KAAK,SAAS;wBACvB,EAAE,OAAO,SAAS,KAAK,QAAQ,IAAI,OAAO,SAAS,KAAK,QAAQ,CAAC,EACjE;wBACA,MAAM,yBAAyB,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;wBAC/E,MAAM,IAAI,KAAK,CACb,iBAAiB,YAAY,gCAAgC,aAAa,EAAE;4BAC1E,8CAA8C,yBAAyB,GAAG;4BAC1E,0EAA0E,aAAa,6BAA6B;4BACpH,wCAAwC,aAAa,6DAA6D,CACrH,CAAC;qBACH;oBAED,IAAI,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;wBAC3C,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;qBACxD;oBAED,IAAI,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;wBAC3C,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;4BACpB,MAAM,IAAI,KAAK,CACb,0BAA0B,aAAa,mEAAmE,CAC3G,CAAC;yBACH;wBACD,IAAI,SAAS,CAAC,eAAe,EAAE;4BAC7B,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;yBAC1C;6BAAM;4BACL,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;yBAC9D;qBACF;iBACF,CAAC,CAAC;aACJ;YACD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;SAChB;;QAGD,IAAI,OAAO,CAAC,eAAe,EAAE;YAC3B,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YAChD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;gBACvC,MAAM,IAAI,KAAK,CACb,6EAA6E;oBAC3E,qFAAqF;oBACrF,2IAA2I,CAC9I,CAAC;aACH;;YAED,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC5C,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC;aACjB;;YAED,MAAM,WAAW,GAAG,EAAE,CAAC;;YAEvB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;YAChB,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE;gBAC5C,MAAM,UAAU,GAAQ,eAAe,CAAC,cAAc,CAAC,CAAC;gBACxD,IAAI,UAAU,EAAE;oBACd,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;wBAClC,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;wBACxE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;qBAC7D;yBAAM,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;wBACzC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;4BACrB,MAAM,IAAI,KAAK,CACb,2BAA2B,cAAc,mEAAmE,CAC7G,CAAC;yBACH;wBACD,IAAI,UAAU,CAAC,eAAe,EAAE;4BAC9B,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;4BAC1D,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;yBAC/C;6BAAM;4BACL,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;4BAC9E,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;yBACnE;qBACF;iBACF;aACF;;YAED,IAAI,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACnC;;QAGD,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;aACnD;SACF;;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;YACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;SAC9C;;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YAClF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5D;;QAGD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAC;SACrE;;QAGD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE;;YAEvD,IAAI,OAAO,CAAC,YAAY,EAAE;gBACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE;oBAC1C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;iBAClD;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,0BAA0B,EAAE;oBACnE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;iBAC9D;aACF;iBAAM;gBACL,IAAI,OAAO,CAAC,mBAAmB,EAAE;oBAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,SAAS,CACnD,OAAO,CAAC,mBAAmB,EAC3B,OAAO,CAAC,IAAI,EACZ,aAAa,CACd,CAAC;iBACH;gBACD,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;oBACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBAC1C;aACF;SACF;QAED,IAAI,OAAO,CAAC,WAAW,EAAE;YACvB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;SACxC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;YAC1B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;SAC9C;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QAEjD,OAAO,IAAI,CAAC;KACb;;;;;IAMD,KAAK;QACH,MAAM,MAAM,GAAG,IAAI,WAAW,CAC5B,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EACpC,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAC3C;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;SACnD;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,MAAM,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;SAC/D;QAED,OAAO,MAAM,CAAC;KACf;CACF;;ACxiBD;AACA,AAIO,MAAM,MAAM,GAAGG,YAAO,CAAC,MAAM,CAAC;;ACLrC;AACA,AAQA;;;AAGA,MAAa,QAAQ;IAArB;QACmB,cAAS,GAAwD,EAAE,CAAC;KAiItF;;;;IA5HQ,GAAG;QACR,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KAC/C;;;;IAKM,IAAI;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACpC;;;;;;IAOM,GAAG,CAAC,aAAqB,EAAE,cAAuB;QACvD,MAAM,kBAAkB,GAAG,cAE1B,CAAC;QACF,IAAI,aAAa,EAAE;YACjB,IAAI,kBAAkB,KAAK,SAAS,IAAI,kBAAkB,KAAK,IAAI,EAAE;gBACnE,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC;sBAC9C,kBAAkB;sBAClB,kBAAkB,CAAC,QAAQ,EAAE,CAAC;gBAClC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC;aAC1C;iBAAM;gBACL,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;aACtC;SACF;KACF;;;;;IAMM,GAAG,CAAC,aAAqB;QAC9B,OAAO,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;KAClE;;;;IAKM,QAAQ;QACb,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,SAAS,EAAE;YAC1C,IAAI,MAAM,EAAE;gBACV,MAAM,IAAI,GAAG,CAAC;aACf;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrD,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;gBACjC,MAAM,gBAAgB,GAAG,EAAE,CAAC;gBAC5B,KAAK,MAAM,qBAAqB,IAAI,cAAc,EAAE;oBAClD,gBAAgB,CAAC,IAAI,CAAC,GAAG,aAAa,IAAI,qBAAqB,EAAE,CAAC,CAAC;iBACpE;gBACD,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACtC;iBAAM;gBACL,MAAM,IAAI,GAAG,aAAa,IAAI,cAAc,EAAE,CAAC;aAChD;SACF;QACD,OAAO,MAAM,CAAC;KACf;;;;IAKM,OAAO,KAAK,CAAC,IAAY;QAC9B,MAAM,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,EAAE;YACR,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACxB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;aAC1B;YAED,IAAI,YAAY,GAAuB,eAAe,CAAC;YAEvD,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,cAAc,GAAG,EAAE,CAAC;YACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACpC,MAAM,gBAAgB,GAAW,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzC,QAAQ,YAAY;oBAClB,KAAK,eAAe;wBAClB,QAAQ,gBAAgB;4BACtB,KAAK,GAAG;gCACN,YAAY,GAAG,gBAAgB,CAAC;gCAChC,MAAM;4BAER,KAAK,GAAG;gCACN,aAAa,GAAG,EAAE,CAAC;gCACnB,cAAc,GAAG,EAAE,CAAC;gCACpB,MAAM;4BAER;gCACE,aAAa,IAAI,gBAAgB,CAAC;gCAClC,MAAM;yBACT;wBACD,MAAM;oBAER,KAAK,gBAAgB;wBACnB,QAAQ,gBAAgB;4BACtB,KAAK,GAAG;gCACN,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;gCAC1C,aAAa,GAAG,EAAE,CAAC;gCACnB,cAAc,GAAG,EAAE,CAAC;gCACpB,YAAY,GAAG,eAAe,CAAC;gCAC/B,MAAM;4BAER;gCACE,cAAc,IAAI,gBAAgB,CAAC;gCACnC,MAAM;yBACT;wBACD,MAAM;oBAER;wBACE,MAAM,IAAI,KAAK,CAAC,qCAAqC,GAAG,YAAY,CAAC,CAAC;iBACzE;aACF;YACD,IAAI,YAAY,KAAK,gBAAgB,EAAE;gBACrC,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;aAC3C;SACF;QAED,OAAO,MAAM,CAAC;KACf;CACF;AAED;;;AAGA,MAAa,UAAU;;;;;IAWd,SAAS,CAAC,MAA0B;QACzC,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;SAC1B;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC5B;KACF;;;;IAKM,SAAS;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;;;;;IAMM,OAAO,CAAC,IAAwB;QACrC,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SACxB;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;SAClC;KACF;;;;IAKM,OAAO;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;;;;IAMM,OAAO,CAAC,IAAiC;QAC9C,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE,EAAE;YACtD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SACxB;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;SACnC;KACF;;;;IAKM,OAAO;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;;;;IAMM,OAAO,CAAC,IAAwB;QACrC,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SACxB;aAAM;YACL,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;gBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;;;gBAGvD,IAAI,CAAC,GAAG,CAAC,WAAW,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;aAC9E;iBAAM;gBACL,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aACxB;SACF;KACF;;;;;IAMM,UAAU,CAAC,IAAwB;QACxC,IAAI,IAAI,EAAE;YACR,IAAI,WAAW,GAAuB,IAAI,CAAC,OAAO,EAAE,CAAC;YACrD,IAAI,WAAW,EAAE;gBACf,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAC9B,WAAW,IAAI,GAAG,CAAC;iBACpB;gBAED,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;oBACxB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;iBAC1B;gBAED,IAAI,GAAG,WAAW,GAAG,IAAI,CAAC;aAC3B;YACD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACxB;KACF;;;;IAKM,OAAO;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;;;IAKM,QAAQ,CAAC,KAAyB;QACvC,IAAI,CAAC,KAAK,EAAE;YACV,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACrC;KACF;;;;;;IAOM,iBAAiB,CAAC,kBAA0B,EAAE,mBAA4B;QAC/E,IAAI,kBAAkB,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,IAAI,CAAC,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;aAC9B;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;SAC1D;KACF;;;;;IAMM,sBAAsB,CAAC,kBAA0B;QACtD,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,SAAS,CAAC;KACtE;;;;IAKM,QAAQ;QACb,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC;KACzD;;;;IAKO,GAAG,CAAC,IAAY,EAAE,UAA6B;QACrD,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAErD,OAAO,SAAS,CAAC,IAAI,EAAE,EAAE;YACvB,MAAM,KAAK,GAAyB,SAAS,CAAC,OAAO,EAAE,CAAC;YACxD,IAAI,SAA6B,CAAC;YAClC,IAAI,KAAK,EAAE;gBACT,QAAQ,KAAK,CAAC,IAAI;oBAChB,KAAK,QAAQ;wBACX,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACvC,MAAM;oBAER,KAAK,MAAM;wBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACrC,MAAM;oBAER,KAAK,MAAM;wBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACrC,MAAM;oBAER,KAAK,MAAM;wBACT,SAAS,GAAG,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;wBACpC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,SAAS,KAAK,GAAG,EAAE;4BAC1D,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;yBACxB;wBACD,MAAM;oBAER,KAAK,OAAO;wBACV,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACzC,MAAM;oBAER;wBACE,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;iBAC/D;aACF;SACF;KACF;IAEM,QAAQ;QACb,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,CAAC;SAChC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;SACtB;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC/B,MAAM,IAAI,GAAG,CAAC;aACf;YACD,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;SACtB;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE;YACpC,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;SACxC;QAED,OAAO,MAAM,CAAC;KACf;;;;;IAMM,UAAU,CAAC,WAAmB,EAAE,YAAoB;QACzD,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;SACvE;KACF;IAEM,OAAO,KAAK,CAAC,IAAY;QAC9B,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QACnC,OAAO,MAAM,CAAC;KACf;CACF;AAMD,MAAa,QAAQ;IACnB,YAAmC,IAAY,EAAkB,IAAkB;QAAhD,SAAI,GAAJ,IAAI,CAAQ;QAAkB,SAAI,GAAJ,IAAI,CAAc;KAAI;IAEhF,OAAO,MAAM,CAAC,IAAY;QAC/B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACrC;IAEM,OAAO,IAAI,CAAC,IAAY;QAC7B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACnC;IAEM,OAAO,IAAI,CAAC,IAAY;QAC7B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACnC;IAEM,OAAO,IAAI,CAAC,IAAY;QAC7B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACnC;IAEM,OAAO,KAAK,CAAC,IAAY;QAC9B,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACpC;CACF;AAED;;;;AAIA,SAAgB,uBAAuB,CAAC,SAAiB;IACvD,MAAM,aAAa,GAAW,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACtD,QACE,CAAC,EAAE,cAAc,aAAa,IAAI,aAAa,IAAI,EAAE;SACpD,EAAE,cAAc,aAAa,IAAI,aAAa,IAAI,EAAE,CAAC;SACrD,EAAE,cAAc,aAAa,IAAI,aAAa,IAAI,GAAG,CAAC,YACvD;AACJ,CAAC;AAED;;;AAGA,MAAa,YAAY;IAMvB,YAA4B,KAAa,EAAE,KAAyB;QAAxC,UAAK,GAAL,KAAK,CAAQ;QACvC,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,GAAG,KAAK,GAAG,gBAAgB,CAAC;QACtF,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KACxB;;;;;IAMM,OAAO;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;;;;IAKM,IAAI;QACT,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;YAC9B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;SAChC;aAAM;YACL,QAAQ,IAAI,CAAC,aAAa;gBACxB,KAAK,QAAQ;oBACX,UAAU,CAAC,IAAI,CAAC,CAAC;oBACjB,MAAM;gBAER,KAAK,gBAAgB;oBACnB,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBACvB,MAAM;gBAER,KAAK,MAAM;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACf,MAAM;gBAER,KAAK,MAAM;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACf,MAAM;gBAER,KAAK,MAAM;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACf,MAAM;gBAER,KAAK,OAAO;oBACV,SAAS,CAAC,IAAI,CAAC,CAAC;oBAChB,MAAM;gBAER;oBACE,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;aAC5E;SACF;QACD,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;KAC7B;CACF;AAED;;;AAGA,SAAS,aAAa,CAAC,SAAuB;IAC5C,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,WAAW,EAAE;QACnD,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC5D,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;KACjD;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;AAGA,SAAS,mBAAmB,CAAC,SAAuB;IAClD,OAAO,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;AACzD,CAAC;AAED;;;AAGA,SAAS,mBAAmB,CAAC,SAAuB;IAClD,OAAO,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AAClD,CAAC;AAED;;;;AAIA,SAAS,aAAa,CAAC,SAAuB,EAAE,IAAa;IAC3D,IAAI,mBAAmB,CAAC,SAAS,CAAC,EAAE;QAClC,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,GAAG,CAAC,CAAC;SACV;QACD,SAAS,CAAC,aAAa,IAAI,IAAI,CAAC;KACjC;AACH,CAAC;AAED;;;;AAIA,SAAS,cAAc,CAAC,SAAuB,EAAE,gBAAwB;IACvE,IAAI,QAAQ,GAAW,SAAS,CAAC,aAAa,GAAG,gBAAgB,CAAC;IAClE,IAAI,SAAS,CAAC,WAAW,GAAG,QAAQ,EAAE;QACpC,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC;KAClC;IACD,OAAO,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;AACtE,CAAC;AAED;;;;AAIA,SAAS,SAAS,CAAC,SAAuB,EAAE,SAAyC;IACnF,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,OAAO,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACrC,MAAM,gBAAgB,GAAW,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAChE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE;YAChC,MAAM;SACP;aAAM;YACL,MAAM,IAAI,gBAAgB,CAAC;YAC3B,aAAa,CAAC,SAAS,CAAC,CAAC;SAC1B;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;AAIA,SAAS,sBAAsB,CAAC,SAAuB;IACrD,OAAO,SAAS,CAAC,SAAS,EAAE,CAAC,SAAiB,KAAK,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;AACzF,CAAC;AAED;;;;AAIA,SAAS,kBAAkB,CAAC,SAAuB,EAAE,GAAG,qBAA+B;IACrF,OAAO,SAAS,CACd,SAAS,EACT,CAAC,SAAiB,KAAK,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,SAAuB;IACzC,MAAM,MAAM,GAAW,sBAAsB,CAAC,SAAS,CAAC,CAAC;IACzD,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAClD,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,SAAuB;IAC/C,MAAM,YAAY,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC1E,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QACjD,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;YAC1C,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACxD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAClC;aAAM;YACL,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAClC;KACF;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;YAC1C,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAClC;aAAM;YACL,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;SACnC;KACF;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB;IACvC,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;QAC1C,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;KAC7B;IAED,MAAM,IAAI,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAClE,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QACjD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QACjD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;KACnC;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB;IACvC,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QAC1C,aAAa,CAAC,SAAS,CAAC,CAAC;KAC1B;IAED,MAAM,IAAI,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7D,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QACjD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;KACnC;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,SAAuB;IACvC,MAAM,IAAI,GAAW,kBAAkB,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IACxD,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;QACnC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;KAClC;SAAM;QACL,SAAS,CAAC,aAAa,GAAG,OAAO,CAAC;KACnC;AACH,CAAC;AAED,SAAS,SAAS,CAAC,SAAuB;IACxC,IAAI,mBAAmB,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QAC1C,aAAa,CAAC,SAAS,CAAC,CAAC;KAC1B;IAED,MAAM,KAAK,GAAW,aAAa,CAAC,SAAS,CAAC,CAAC;IAC/C,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAChD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;AACnC,CAAC;;ACjqBD;AACA,AAqBA,MAAM,cAAc,GAAG,UAAU,CAAC;AAElC,MAAM,yBAAyB,GAAG;IAChC,wBAAwB;IACxB,+BAA+B;IAC/B,gBAAgB;IAChB,6BAA6B;IAC7B,iBAAiB;IACjB,mBAAmB;IACnB,OAAO;IACP,0BAA0B;IAC1B,aAAa;IAEb,kCAAkC;IAClC,8BAA8B;IAC9B,8BAA8B;IAC9B,6BAA6B;IAC7B,+BAA+B;IAC/B,wBAAwB;IACxB,gCAAgC;IAChC,+BAA+B;IAC/B,QAAQ;IAER,QAAQ;IACR,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,MAAM;IACN,MAAM;IACN,SAAS;IACT,UAAU;IACV,mBAAmB;IACnB,eAAe;IACf,qBAAqB;IACrB,eAAe;IACf,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,mBAAmB;IACnB,YAAY;CACb,CAAC;AAEF,MAAM,6BAA6B,GAAa,CAAC,aAAa,CAAC,CAAC;AAEhE,MAAa,SAAS;IAIpB,YAAY,EAAE,kBAAkB,GAAG,EAAE,EAAE,sBAAsB,GAAG,EAAE,KAAuB,EAAE;QACzF,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC;cAClD,yBAAyB,CAAC,MAAM,CAAC,kBAAkB,CAAC;cACpD,yBAAyB,CAAC;QAE9B,sBAAsB,GAAG,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC;cAC1D,6BAA6B,CAAC,MAAM,CAAC,sBAAsB,CAAC;cAC5D,6BAA6B,CAAC;QAElC,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClF,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KAC3F;IAEM,QAAQ,CAAC,GAAY;QAC1B,MAAM,IAAI,GAAG,IAAI,GAAG,EAAW,CAAC;QAChC,OAAO,IAAI,CAAC,SAAS,CACnB,GAAG,EACH,CAAC,GAAW,EAAE,KAAc;;YAE1B,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,uCACK,KAAK,KACR,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,OAAO,EAAE,KAAK,CAAC,OAAO,IACtB;aACH;YAED,IAAI,GAAG,KAAK,aAAa,EAAE;gBACzB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAsB,CAAC,CAAC;aACrD;iBAAM,IAAI,GAAG,KAAK,KAAK,EAAE;gBACxB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;aAC1C;iBAAM,IAAI,GAAG,KAAK,OAAO,EAAE;gBAC1B,OAAO,IAAI,CAAC,aAAa,CAAC,KAAsB,CAAC,CAAC;aACnD;iBAAM,IAAI,GAAG,KAAK,MAAM,EAAE;;gBAEzB,OAAO,SAAS,CAAC;aAClB;iBAAM,IAAI,GAAG,KAAK,UAAU,EAAE;;gBAE7B,OAAO,SAAS,CAAC;aAClB;iBAAM,IAAI,GAAG,KAAK,eAAe,EAAE;;;gBAGlC,OAAO,SAAS,CAAC;aAClB;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAClD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACnB,OAAO,YAAY,CAAC;iBACrB;gBACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACjB;YAED,OAAO,KAAK,CAAC;SACd,EACD,CAAC,CACF,CAAC;KACH;IAEO,eAAe,CAAC,KAAoB;QAC1C,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KAClF;IAEO,aAAa,CAAC,KAAoB;QACxC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAChF;IAEO,cAAc,CACpB,KAAoB,EACpB,WAAwB,EACxB,QAA0C;QAE1C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;YAC/C,OAAO,KAAK,CAAC;SACd;QAED,MAAM,SAAS,GAAkB,EAAE,CAAC;QAEpC,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAClC,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;gBACpC,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACnC;iBAAM;gBACL,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;aAC/B;SACF;QAED,OAAO,SAAS,CAAC;KAClB;IAEO,WAAW,CAAC,KAAa;QAC/B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;YAC/C,OAAO,KAAK,CAAC;SACd;QAED,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC3C,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAE1C,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,KAAK,CAAC;SACd;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAC1C,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;gBACrD,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;aAC9B;SACF;QAED,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QACtC,OAAO,UAAU,CAAC,QAAQ,EAAE,CAAC;KAC9B;CACF;;ACrLD;AACA,AAOA,MAAM,cAAc,GAAG,IAAI,SAAS,EAAE,CAAC;AAEvC,MAAa,SAAU,SAAQ,KAAK;IASlC,YACE,OAAe,EACf,IAAa,EACb,UAAmB,EACnB,OAAyB,EACzB,QAAgC;QAEhC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;KAClD;;;;IAKD,CAAC,MAAM,CAAC;QACN,OAAO,cAAc,IAAI,CAAC,OAAO,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;KACzE;;AA9Be,4BAAkB,GAAW,oBAAoB,CAAC;AAClD,qBAAW,GAAW,aAAa,CAAC;;ACZtD;AACA,AAEO,MAAM,MAAM,GAAGC,2BAAkB,CAAC,WAAW,CAAC,CAAC;;ACHtD;AACA,MAiCa,eAAgB,SAAQC,gBAAS;IAS5C,YAAoB,gBAA2D;QAC7E,KAAK,EAAE,CAAC;QADU,qBAAgB,GAAhB,gBAAgB,CAA2C;QARvE,gBAAW,GAAW,CAAC,CAAC;KAU/B;IATD,UAAU,CAAC,KAAsB,EAAE,SAAiB,EAAE,QAA4B;QAChF,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,gBAAiB,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAC1D,QAAQ,CAAC,SAAS,CAAC,CAAC;KACrB;CAKF;AAED,MAAsB,eAAe;IACnC,MAAM,WAAW,CAAC,WAA4B;;QAC5C,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnD,MAAM,IAAI,KAAK,CACb,yFAAyF,CAC1F,CAAC;SACH;QAED,MAAMC,iBAAe,GAAG,IAAIC,+BAAe,EAAE,CAAC;QAC9C,IAAI,aAAiD,CAAC;QACtD,IAAI,WAAW,CAAC,WAAW,EAAE;YAC3B,IAAI,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE;gBACnC,MAAM,IAAIC,0BAAU,CAAC,4BAA4B,CAAC,CAAC;aACpD;YAED,aAAa,GAAG,CAAC,KAAY;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;oBAC1BF,iBAAe,CAAC,KAAK,EAAE,CAAC;iBACzB;aACF,CAAC;YACF,WAAW,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;SAClE;QAED,IAAI,WAAW,CAAC,OAAO,EAAE;YACvB,UAAU,CAAC;gBACTA,iBAAe,CAAC,KAAK,EAAE,CAAC;aACzB,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;SACzB;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE;YACxB,MAAM,QAAQ,GAAQ,WAAW,CAAC,QAAQ,CAAC;YAC3C,MAAM,WAAW,GAAG,IAAI,QAAQ,EAAE,CAAC;YACnC,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,KAAU;;gBAE9C,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;oBAC/B,KAAK,GAAG,KAAK,EAAE,CAAC;iBACjB;gBACD,IACE,KAAK;oBACL,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;oBACpD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,EACtD;oBACA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;iBACrD;qBAAM;oBACL,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBAChC;aACF,CAAC;YACF,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACpC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;oBAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACzC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;qBACxC;iBACF;qBAAM;oBACL,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;iBACrC;aACF;YAED,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC;YAC/B,WAAW,CAAC,QAAQ,GAAG,SAAS,CAAC;YACjC,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC5D,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpE,IAAI,OAAO,WAAW,CAAC,WAAW,KAAK,UAAU,EAAE;oBACjD,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,cAAc,EACd,iCAAiC,WAAW,CAAC,WAAW,EAAE,EAAE,CAC7D,CAAC;iBACH;qBAAM;;oBAEL,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;iBAC5C;aACF;SACF;QAED,IAAI,IAAI,GAAG,WAAW,CAAC,IAAI;cACvB,OAAO,WAAW,CAAC,IAAI,KAAK,UAAU;kBACpC,WAAW,CAAC,IAAI,EAAE;kBAClB,WAAW,CAAC,IAAI;cAClB,SAAS,CAAC;QACd,IAAI,WAAW,CAAC,gBAAgB,IAAI,WAAW,CAAC,IAAI,EAAE;YACpD,MAAM,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;YACtD,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAAC,gBAAgB,CAAC,CAAC;YACjE,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC/B;iBAAM;gBACL,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAC9B;YAED,IAAI,GAAG,kBAAkB,CAAC;SAC3B;QAED,MAAM,2BAA2B,GAAyB,MAAM,IAAI,CAAC,cAAc,CACjF,WAAW,CACZ,CAAC;QAEF,MAAM,WAAW,mBACf,IAAI,EAAE,IAAI,EACV,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EACzC,MAAM,EAAE,WAAW,CAAC,MAAM,EAC1B,MAAM,EAAEA,iBAAe,CAAC,MAAM,EAC9B,QAAQ,EAAE,QAAQ,IACf,2BAA2B,CAC/B,CAAC;QAEF,IAAI,iBAAoD,CAAC;QACzD,IAAI;YACF,MAAM,QAAQ,GAAmB,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YAEhF,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,SAAS,GACb,CAAA,MAAA,WAAW,CAAC,yBAAyB,0CAAE,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3D,WAAW,CAAC,kBAAkB,CAAC;YAEjC,iBAAiB,GAAG;gBAClB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,WAAW;gBACpB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,kBAAkB,EAAE,SAAS;sBACvB,QAAQ,CAAC,IAA0C;sBACrD,SAAS;gBACb,UAAU,EAAE,CAAC,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,GAAG,SAAS;aAC3D,CAAC;YAEF,MAAM,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;YAC1D,IAAI,kBAAkB,EAAE;gBACtB,MAAM,YAAY,GAA2C,QAAQ,CAAC,IAAI,IAAI,SAAS,CAAC;gBAExF,IAAI,gBAAgB,CAAC,YAAY,CAAC,EAAE;oBAClC,MAAM,oBAAoB,GAAG,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAAC;oBACrE,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACxC,iBAAiB,CAAC,kBAAkB,GAAG,oBAAoB,CAAC;iBAC7D;qBAAM;oBACL,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAE,CAAC,IAAI,SAAS,CAAC;oBACrE,IAAI,MAAM,EAAE;;wBAEV,kBAAkB,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;qBAC7C;iBACF;aACF;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAE7C,OAAO,iBAAiB,CAAC;SAC1B;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,UAAU,GAAe,KAAK,CAAC;YACrC,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,EAAE;gBACnC,MAAM,IAAI,SAAS,CACjB,UAAU,CAAC,OAAO,EAClB,SAAS,CAAC,kBAAkB,EAC5B,SAAS,EACT,WAAW,CACZ,CAAC;aACH;iBAAM,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE;gBACxC,MAAM,IAAIE,0BAAU,CAAC,4BAA4B,CAAC,CAAC;aACpD;YAED,MAAM,UAAU,CAAC;SAClB;gBAAS;;YAER,IAAI,WAAW,CAAC,WAAW,IAAI,aAAa,EAAE;gBAC5C,IAAI,gBAAgB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzC,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;oBAC1B,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;iBAC3C;gBACD,IAAI,kBAAkB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC3C,IAAI,gBAAgB,CAAC,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,kBAAkB,CAAC,EAAE;oBAC3D,kBAAkB,GAAG,gBAAgB,CACnC,iBAAkB,CAAC,kBAAkB,EACrCF,iBAAe,CAChB,CAAC;iBACH;gBAED,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;qBAChD,IAAI,CAAC;;oBACJ,MAAA,WAAW,CAAC,WAAW,0CAAE,mBAAmB,CAAC,OAAO,EAAE,aAAc,CAAC,CAAC;oBACtE,OAAO;iBACR,CAAC;qBACD,KAAK,CAAC,CAAC,CAAC;oBACP,MAAM,CAAC,OAAO,CAAC,qDAAqD,EAAE,CAAC,CAAC,CAAC;iBAC1E,CAAC,CAAC;aACN;SACF;KACF;CAKF;AAED,SAAS,gBAAgB,CAAC,IAAS;IACjC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;AACjD,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAgB,EAAE,OAAyB;IACnE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO;QACzB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;YACnB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,EAAE,CAAC;YACjB,OAAO,EAAE,CAAC;SACX,CAAC,CAAC;QACH,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KAC/B,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,YAAY,CAAC,OAAgB;IAC3C,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IAEtC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG;QACzB,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC7B,CAAC,CAAC;IAEH,OAAO,WAAW,CAAC;AACrB,CAAC;;ACrQD;AACA,SAWgB,gBAAgB,CAC9B,UAAkB,EAClB,aAA4B,EAC5B,OAAyB;IAEzB,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,EAAY,CAAC;IACtE,IAAI,CAAC,IAAI,EAAE;QACT,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;KAClE;IACD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;QACpC,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;KAChG;IACD,MAAM,aAAa,GAAiC;QAClD,KAAK,EAAE;YACL,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,OAAO,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE;SACjD;KACF,CAAC;IAEF,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,EAAE;QACpD,aAAa,CAAC,KAAM,CAAC,SAAS,GAAG,GAAG,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;KACxF;SAAM,IAAI,aAAa,CAAC,QAAQ,EAAE;QACjC,aAAa,CAAC,KAAM,CAAC,SAAS,GAAG,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;KAC9D;IAED,MAAM,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;IAC9C,MAAM,YAAY,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAEpD,MAAM,UAAU,GAAG;QACjB,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,YAAY,CAAC,cAAc,EAAE,YAAY,EAAE,aAAa,CAAC;KACjE,CAAC;IAEF,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAgB,UAAU,CAAC,GAAW;IACpC,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC;IAC1D,OAAO,SAAS,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC;AAC7C,CAAC;AAED,SAAgB,YAAY,CAC1B,cAAuB,EACvB,YAAqB,EACrB,aAA2C;IAE3C,IAAI,cAAc,IAAI,YAAY,EAAE;QAClC,OAAOG,qBAAqB,CAAC,aAAa,CAAC,CAAC;KAC7C;SAAM,IAAI,cAAc,IAAI,CAAC,YAAY,EAAE;QAC1C,OAAOC,oBAAoB,CAAC,aAAa,CAAC,CAAC;KAC5C;SAAM,IAAI,CAAC,cAAc,IAAI,YAAY,EAAE;QAC1C,OAAOC,oBAAoB,CAAC,aAAa,CAAC,CAAC;KAC5C;SAAM;QACL,OAAOC,mBAAmB,CAAC,aAAa,CAAC,CAAC;KAC3C;AACH,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;;;IAG/B,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC;AACpC,CAAC;;AC1ED;AACA,AAsBA,SAAS,cAAc,CACrB,OAAgB,EAChB,UAAsB;IAEtB,OAAO,OAAO,GAAG,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC;AAChE,CAAC;AAED,MAAa,mBAAoB,SAAQ,eAAe;IAAxD;;;QAEU,kBAAa,GAA4B,IAAI,GAAG,EAAE,CAAC;QACnD,oBAAe,GAAe,EAAE,CAAC;QAExB,cAAS,GAAG,IAAIC,eAAe,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;KA0GlF;IAxGS,gBAAgB,CAAC,WAA4B;;QACnD,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;;;;QAK5C,IAAI,WAAW,CAAC,aAAa,EAAE;YAC7B,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,aAAa,CAAC;YACrE,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;YACtD,MAAM,WAAW,GAAG,MAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,mCAAI,EAAE,CAAC;YAEtD,IAAI,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YACjD,IAAI,KAAK,EAAE;gBACT,OAAO,KAAK,CAAC;aACd;YAED,MAAM,MAAM,GAAe,gBAAgB,CACzC,WAAW,CAAC,GAAG,EACf,WAAW,CAAC,aAAa,EACzB,WAAW,CAAC,OAAO,CACpB,CAAC;YAEF,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YACrB,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,WAAW,CAAC,UAAU,GAAG,MAAM,CAAC,KAAoB,CAAC;aACtD;iBAAM;gBACL,WAAW,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;aACtC;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YAEzC,OAAO,KAAK,CAAC;SACd;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE;YAChC,IAAI,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC1D,IAAI,KAAK,EAAE;gBACT,OAAO,KAAK,CAAC;aACd;YAED,MAAM,YAAY,GAA2C;gBAC3D,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC,CAAC;YAEF,IAAI,OAAO,EAAE;gBACX,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,IAAIC,WAAW,CAAC,YAAY,CAAC,CAAC;aACzE;iBAAM;gBACL,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,IAAIC,UAAU,CAAC,YAAY,CAAC,CAAC;aACvE;YAED,OAAO,KAAK,CAAC;SACd;aAAM;YACL,OAAO,OAAO,GAAGC,iBAAiB,GAAGC,gBAAgB,CAAC;SACvD;KACF;;IAGD,MAAM,KAAK,CAAC,KAAwB,EAAE,IAAwB;QAC5D,OAAQ,UAAU,CAAC,KAAK,EAAE,IAAI,CAAwC,CAAC;KACxE;IAED,MAAM,cAAc,CAAC,WAA4B;QAC/C,MAAM,WAAW,GAA+D,EAAE,CAAC;QAEnF,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACxD,MAAM,YAAY,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM;gBAC7D,IAAI,CAAC,SAAU,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM;oBAC3D,IAAI,GAAG,EAAE;wBACP,MAAM,CAAC,GAAG,CAAC,CAAC;qBACb;yBAAM;wBACL,OAAO,CAAC,MAAM,CAAC,CAAC;qBACjB;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;YAEH,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;SACjD;;QAGD,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAEvD,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,kBAAkB,CAAC;QAEtD,OAAO,WAAW,CAAC;KACpB;IAED,MAAM,cAAc,CAAC,iBAAwC;QAC3D,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,eAAe,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACpE,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM;oBACtC,IAAI,CAAC,SAAU,CAAC,SAAS,CACvB,eAAe,EACf,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAC7B,EAAE,WAAW,EAAE,IAAI,EAAE,EACrB,CAAC,GAAG;wBACF,IAAI,GAAG,EAAE;4BACP,MAAM,CAAC,GAAG,CAAC,CAAC;yBACb;6BAAM;4BACL,OAAO,EAAE,CAAC;yBACX;qBACF,CACF,CAAC;iBACH,CAAC,CAAC;aACJ;SACF;KACF;CACF;;AC7ID;AACA,AAKA,WAAY,oBAAoB;;;;IAI9B,6DAAG,CAAA;;;;IAKH,iEAAK,CAAA;;;;IAKL,qEAAO,CAAA;;;;IAKP,+DAAI,CAAA;AACN,CAAC,EApBWC,4BAAoB,KAApBA,4BAAoB,QAoB/B;;AC0BD;;;;;AAKA,SAAgB,oCAAoC,CAClD,IAAO;;IAEP,MAAM,EAAE,cAAc,EAAE,cAAc,KAA2B,IAAI,EAA1B,iBAAiB,gBAAK,IAAI,EAA/D,oCAAwD,CAAO,CAAC;IAEtE,IAAI,MAAM,GAAuB,iBAAiB,CAAC;IAEnD,IAAI,cAAc,EAAE;QAClB,MAAM,mCAAQ,MAAM,GAAK,cAAc,CAAE,CAAC;KAC3C;IAED,IAAI,cAAc,EAAE;QAClB,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;;QAEtD,MAAM,CAAC,WAAW,GAAG,MAAC,cAAsB,0CAAE,WAAW,CAAC;KAC3D;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;;AC3ED;AACA,MAkBsB,iBAAiB;IACrC,YACW,WAA0B,EAC1B,QAAkC;QADlC,gBAAW,GAAX,WAAW,CAAe;QAC1B,aAAQ,GAAR,QAAQ,CAA0B;KACzC;;;;;;IASG,SAAS,CAAC,QAA8B;QAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;KAC1C;;;;;;;IAQM,GAAG,CAAC,QAA8B,EAAE,OAAe;QACxD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;KACtC;CACF;AAsBD;;;AAGA,MAAa,oBAAoB;IAC/B,YAAoB,OAA4B;QAA5B,YAAO,GAAP,OAAO,CAAqB;KAAI;;;;;;IAO7C,SAAS,CAAC,QAA8B;QAC7C,QACE,CAAC,CAAC,IAAI,CAAC,OAAO;YACd,QAAQ,KAAKA,4BAAoB,CAAC,GAAG;YACrC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EACxC;KACH;;;;;;;IAQM,GAAG,CAAC,QAA8B,EAAE,OAAe;QACxD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SACrC;KACF;CACF;;ACjGD;AACA,SAmCgB,SAAS,CAAC,iBAAmC,EAAE;IAC7D,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;SAC3D;KACF,CAAC;AACJ,CAAC;AAED,MAAa,SAAU,SAAQ,iBAAiB;IA4C9C,YACE,UAAyB,EACzB,OAA6B,EAC7B,UACEC,QAAM,GAAGC,MAAU,CAAC,IAAI,EACxB,kBAAkB,GAAG,EAAE,EACvB,sBAAsB,GAAG,EAAE,KACP,EAAE;QAExB,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAGD,QAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,CAAC,CAAC;KAChF;;;;;;;;IA7CD,IAAW,kBAAkB;QAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;KAC1C;;;;;;;;IASD,IAAW,kBAAkB,CAAC,kBAA+B;QAC3D,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;KACxD;;;;;;IAOD,IAAW,sBAAsB;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;KAC9C;;;;;;IAOD,IAAW,sBAAsB,CAAC,sBAAmC;QACnE,IAAI,CAAC,SAAS,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;KAChE;IAgBM,WAAW,CAAC,OAAwB;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEvE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC7F;IAEO,UAAU,CAAC,OAAwB;QACzC,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;KAC7D;IAEO,WAAW,CAAC,QAA+B;QACjD,IAAI,CAAC,MAAM,CAAC,yBAAyB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACrE,OAAO,QAAQ,CAAC;KACjB;CACF;;ACtHD;AACA;AAkDA;;;;;AAKA,SAAgB,0BAA0B,CAAC,SAA6B;IACtE,OAAO,8BAA8B,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACnF,CAAC;AAED,SAAgB,8BAA8B,CAC5C,aAA4B,EAC5B,MAAc;IAEd,IAAI,MAAc,CAAC;IACnB,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,MAAM,GAAG,aAAa,CAAC;KACxB;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QACvC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAClC;SAAM;QACL,MAAM,GAAG,MAAM,CAAC,cAAe,CAAC;KACjC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;;ACzED;AACA,AAgGA;;;;AAIA,SAAgB,4BAA4B,CAAC,aAA4B;IACvE,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;IACjC,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,SAAS,EAAE;QAChD,MAAM,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC9D,IACE,iBAAiB,CAAC,UAAU;YAC5B,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM,EAC5D;YACA,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;SAChC;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;;ACjHD;AACA,AAKA;AACA;AACA;AACA;AACA,MAAM,sBAAsB,GAAqB;IAC/C,eAAe,EAAE,KAAK;IACtB,IAAI,EAAE,KAAK;IACX,SAAS,EAAE,KAAK;IAChB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,WAAW;IACpB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,KAAK;IAClB,UAAU,EAAE,KAAK;IACjB,YAAY,EAAE,IAAI;IAClB,SAAS,EAAE,SAAS;IACpB,KAAK,EAAE,KAAK;IACZ,gBAAgB,EAAE,KAAK;IACvB,qBAAqB,EAAE,KAAK;IAC5B,QAAQ,EAAE,IAAI;IACd,eAAe,EAAE,KAAK;IACtB,iBAAiB,EAAE,KAAK;IACxB,KAAK,EAAE,KAAK;IACZ,MAAM,EAAE,IAAI;IACZ,kBAAkB,EAAE,SAAS;IAC7B,mBAAmB,EAAE,SAAS;IAC9B,iBAAiB,EAAE,SAAS;IAC5B,eAAe,EAAE,SAAS;IAC1B,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE;QACN,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,OAAO;QACjB,UAAU,EAAE,IAAI;KACjB;IACD,OAAO,EAAE,SAAS;IAClB,UAAU,EAAE;QACV,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,IAAI;KACd;IACD,QAAQ,EAAE,KAAK;IACf,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,EAAE;IACZ,KAAK,EAAE,KAAK;CACb,CAAC;AAEF;AACA,MAAM,oBAAoB,GAAQ,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,sBAAsB,CAAC,CAAC;AAC5E,oBAAoB,CAAC,aAAa,GAAG,KAAK,CAAC;AAE3C;AACA,MAAM,qBAAqB,GAAQ,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,sBAAsB,CAAC,CAAC;AAC7E,qBAAqB,CAAC,aAAa,GAAG,KAAK,CAAC;AAC5C,qBAAqB,CAAC,UAAU,GAAG;IACjC,MAAM,EAAE,KAAK;CACd,CAAC;AAEF;;;;;AAKA,SAAgB,YAAY,CAAC,GAAY,EAAE,OAA0B,EAAE;;IACrE,qBAAqB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/C,qBAAqB,CAAC,OAAO,GAAG,MAAA,IAAI,CAAC,UAAU,mCAAI,WAAW,CAAC;IAC/D,MAAM,OAAO,GAAG,IAAIE,cAAc,CAAC,qBAAqB,CAAC,CAAC;IAC1D,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED;;;;;AAKA,SAAgB,QAAQ,CAAC,GAAW,EAAE,OAA0B,EAAE;;IAChE,oBAAoB,CAAC,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;IACvD,oBAAoB,CAAC,OAAO,GAAG,MAAA,IAAI,CAAC,UAAU,mCAAI,WAAW,CAAC;IAC9D,MAAM,SAAS,GAAG,IAAIC,aAAa,CAAC,oBAAoB,CAAC,CAAC;IAC1D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;SACxC;aAAM;YACL,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAW,EAAE,GAAS;gBAChD,IAAI,GAAG,EAAE;oBACP,MAAM,CAAC,GAAG,CAAC,CAAC;iBACb;qBAAM;oBACL,OAAO,CAAC,GAAG,CAAC,CAAC;iBACd;aACF,CAAC,CAAC;SACJ;KACF,CAAC,CAAC;AACL,CAAC;;AChGD;AACA,AA8CA;;;;AAIA,SAAgB,qBAAqB,CACnC,2BAAyD,EACzD,cAAkC;IAElC,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,qBAAqB,CAC9B,UAAU,EACV,OAAO,EACP,2BAA2B,EAC3B,cAAc,CACf,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAED,AAAO,MAAM,uBAAuB,GAAG,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;AACzE,AAAO,MAAM,sBAAsB,GAAG,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;AAElF,AAAO,MAAM,6BAA6B,GAA2B;IACnE,oBAAoB,EAAE;QACpB,IAAI,EAAE,uBAAuB;QAC7B,GAAG,EAAE,sBAAsB;KAC5B;CACF,CAAC;AAEF;;;;AAIA,MAAa,qBAAsB,SAAQ,iBAAiB;IAK1D,YACE,UAAyB,EACzB,oBAA0C,EAC1C,2BAAyD,EACzD,iBAAoC,EAAE;;QAEtC,KAAK,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB;YACnB,CAAC,2BAA2B,IAAI,2BAA2B,CAAC,IAAI,KAAK,uBAAuB,CAAC;QAC/F,IAAI,CAAC,eAAe;YAClB,CAAC,2BAA2B,IAAI,2BAA2B,CAAC,GAAG,KAAK,sBAAsB,CAAC;QAC7F,IAAI,CAAC,UAAU,GAAG,MAAA,cAAc,CAAC,UAAU,mCAAI,WAAW,CAAC;KAC5D;IAEM,MAAM,WAAW,CAAC,OAAwB;QAC/C,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,QAA+B,KAChF,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE;YAC7E,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC,CACH,CAAC;KACH;CACF;AAED,SAAS,oBAAoB,CAC3B,cAAqC;IAErC,IAAI,MAAqC,CAAC;IAC1C,MAAM,OAAO,GAAoB,cAAc,CAAC,OAAO,CAAC;IACxD,MAAM,aAAa,GAA8B,OAAO,CAAC,aAAa,CAAC;IACvE,IAAI,aAAa,EAAE;QACjB,MAAM,uBAAuB,GAKa,OAAO,CAAC,uBAAuB,CAAC;QAC1E,IAAI,CAAC,uBAAuB,EAAE;YAC5B,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACzD;aAAM;YACL,MAAM,GAAG,uBAAuB,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;SACjE;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,yBAAyB,CAAC,cAAqC;IACtE,MAAM,iBAAiB,GACrB,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC;IAC3C,IAAI,MAAe,CAAC;IACpB,IAAI,iBAAiB,KAAK,SAAS,EAAE;QACnC,MAAM,GAAG,IAAI,CAAC;KACf;SAAM,IAAI,OAAO,iBAAiB,KAAK,SAAS,EAAE;QACjD,MAAM,GAAG,iBAAiB,CAAC;KAC5B;SAAM;QACL,MAAM,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAC;KAC5C;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,uBAAuB,CACrC,gBAA0B,EAC1B,eAAyB,EACzB,QAA+B,EAC/B,UAA6B,EAAE;;IAE/B,MAAM,cAAc,GAAgC;QAClD,QAAQ,EAAE,MAAA,OAAO,CAAC,QAAQ,mCAAI,EAAE;QAChC,WAAW,EAAE,MAAA,OAAO,CAAC,WAAW,mCAAI,KAAK;QACzC,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,WAAW;KAC9C,CAAC;IACF,OAAO,KAAK,CAAC,gBAAgB,EAAE,eAAe,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,IAAI,CAC5E,CAAC,cAAc;QACb,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,EAAE;YAC9C,OAAO,cAAc,CAAC;SACvB;QAED,MAAM,aAAa,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC;QAC3D,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YAC9C,OAAO,cAAc,CAAC;SACvB;QAED,MAAM,YAAY,GAAG,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAE1D,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAE,GAAG,mBAAmB,CACzD,cAAc,EACd,aAAa,EACb,YAAY,CACb,CAAC;QACF,IAAI,KAAK,EAAE;YACT,MAAM,KAAK,CAAC;SACb;aAAM,IAAI,oBAAoB,EAAE;YAC/B,OAAO,cAAc,CAAC;SACvB;;;QAID,IAAI,YAAY,EAAE;YAChB,IAAI,YAAY,CAAC,UAAU,EAAE;gBAC3B,IAAI,kBAAkB,GAAQ,cAAc,CAAC,UAAU,CAAC;gBACxD,IAAI,aAAa,CAAC,KAAK,IAAI,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,QAAQ,EAAE;oBACpF,kBAAkB;wBAChB,OAAO,kBAAkB,KAAK,QAAQ;8BAClC,kBAAkB,CAAC,YAAY,CAAC,UAAU,CAAC,cAAe,CAAC;8BAC3D,EAAE,CAAC;iBACV;gBACD,IAAI;oBACF,cAAc,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAC9D,YAAY,CAAC,UAAU,EACvB,kBAAkB,EAClB,yBAAyB,EACzB,OAAO,CACR,CAAC;iBACH;gBAAC,OAAO,UAAU,EAAE;oBACnB,MAAM,SAAS,GAAG,IAAI,SAAS,CAC7B,SAAS,UAAU,iDAAiD,cAAc,CAAC,UAAU,EAAE,EAC/F,SAAS,EACT,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,OAAO,EACtB,cAAc,CACf,CAAC;oBACF,MAAM,SAAS,CAAC;iBACjB;aACF;iBAAM,IAAI,aAAa,CAAC,UAAU,KAAK,MAAM,EAAE;;gBAE9C,cAAc,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;aAC7E;YAED,IAAI,YAAY,CAAC,aAAa,EAAE;gBAC9B,cAAc,CAAC,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CACjE,YAAY,CAAC,aAAa,EAC1B,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,EACnC,4BAA4B,EAC5B,OAAO,CACR,CAAC;aACH;SACF;QAED,OAAO,cAAc,CAAC;KACvB,CACF,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,aAA4B;IACxD,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IACjE,QACE,mBAAmB,CAAC,MAAM,KAAK,CAAC;SAC/B,mBAAmB,CAAC,MAAM,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,EAC1E;AACJ,CAAC;AAED,SAAS,mBAAmB,CAC1B,cAAqC,EACrC,aAA4B,EAC5B,YAA2C;;IAE3C,MAAM,iBAAiB,GAAG,GAAG,IAAI,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC;IACtF,MAAM,oBAAoB,GAAY,oBAAoB,CAAC,aAAa,CAAC;UACrE,iBAAiB;UACjB,CAAC,CAAC,YAAY,CAAC;IAEnB,IAAI,oBAAoB,EAAE;QACxB,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;gBACzB,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;aACrD;SACF;aAAM;YACL,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;SACrD;KACF;IAED,MAAM,iBAAiB,GAAG,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC;IAC1E,MAAM,SAAS,GACb,CAAA,MAAA,cAAc,CAAC,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC;QAC5E,cAAc,CAAC,OAAO,CAAC,kBAAkB,CAAC;IAC5C,MAAM,mBAAmB,GAAG,SAAS;UACjC,2BAA2B,cAAc,CAAC,MAAM,EAAE;UACjD,cAAc,CAAC,UAAqB,CAAC;IAE1C,MAAM,KAAK,GAAG,IAAI,SAAS,CACzB,mBAAmB,EACnB,SAAS,EACT,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,OAAO,EACtB,cAAc,CACf,CAAC;;;IAIF,IAAI,CAAC,iBAAiB,EAAE;QACtB,MAAM,KAAK,CAAC;KACb;IAED,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,UAAU,CAAC;IACvD,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,aAAa,CAAC;IAE7D,IAAI;;;QAGF,IAAI,cAAc,CAAC,UAAU,EAAE;YAC7B,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;YAC7C,IAAI,WAAW,CAAC;YAChB,IAAI,iBAAiB,EAAE;gBACrB,IAAI,kBAAkB,GAAQ,UAAU,CAAC;gBACzC,IAAI,aAAa,CAAC,KAAK,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,QAAQ,EAAE;oBAC9E,kBAAkB;wBAChB,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,CAAC,iBAAiB,CAAC,cAAe,CAAC,GAAG,EAAE,CAAC;iBACvF;gBACD,WAAW,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAChD,iBAAiB,EACjB,kBAAkB,EAClB,2BAA2B,CAC5B,CAAC;aACH;YAED,MAAM,aAAa,GAAQ,UAAU,CAAC,KAAK,IAAI,WAAW,IAAI,UAAU,CAAC;YACzE,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;YAChC,IAAI,aAAa,CAAC,OAAO,EAAE;gBACzB,KAAK,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;aACvC;YAED,IAAI,iBAAiB,EAAE;gBACrB,KAAK,CAAC,QAAS,CAAC,UAAU,GAAG,WAAW,CAAC;aAC1C;SACF;;QAGD,IAAI,cAAc,CAAC,OAAO,IAAI,oBAAoB,EAAE;YAClD,KAAK,CAAC,QAAS,CAAC,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAClE,oBAAoB,EACpB,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,EACnC,4BAA4B,CAC7B,CAAC;SACH;KACF;IAAC,OAAO,YAAY,EAAE;QACrB,KAAK,CAAC,OAAO,GAAG,UAAU,YAAY,CAAC,OAAO,mDAAmD,cAAc,CAAC,UAAU,6BAA6B,CAAC;KACzJ;IAED,OAAO,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AAChD,CAAC;AAED,SAAS,KAAK,CACZ,gBAA0B,EAC1B,eAAyB,EACzB,iBAAwC,EACxC,IAAiC;;IAEjC,MAAM,YAAY,GAAG,CAAC,GAA6B;QACjD,MAAM,GAAG,GAAG,UAAU,GAAG,gDAAgD,iBAAiB,CAAC,UAAU,GAAG,CAAC;QACzG,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,WAAW,CAAC;QAClD,MAAM,CAAC,GAAG,IAAI,SAAS,CACrB,GAAG,EACH,OAAO,EACP,iBAAiB,CAAC,MAAM,EACxB,iBAAiB,CAAC,OAAO,EACzB,iBAAiB,CAClB,CAAC;QACF,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KAC1B,CAAC;IAEF,MAAM,SAAS,GACb,CAAA,MAAA,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,iBAAiB,CAAC,MAAM,CAAC;QAClF,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,CAAC;IAC/C,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,UAAU,EAAE;QAC9C,MAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;QAC1C,MAAM,WAAW,GAAW,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAChF,MAAM,iBAAiB,GAAa,CAAC,WAAW;cAC5C,EAAE;cACF,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;QACvE,IACE,iBAAiB,CAAC,MAAM,KAAK,CAAC;YAC9B,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EACjF;YACA,OAAO,IAAI,OAAO,CAAwB,CAAC,OAAO;gBAChD,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAChD,OAAO,CAAC,iBAAiB,CAAC,CAAC;aAC5B,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;SACxB;aAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAC3F,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;iBACxB,IAAI,CAAC,CAAC,IAAI;gBACT,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC;gBACpC,OAAO,iBAAiB,CAAC;aAC1B,CAAC;iBACD,KAAK,CAAC,YAAY,CAAC,CAAC;SACxB;KACF;IAED,OAAO,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC5C,CAAC;;ACtXD;AACA;AAIA,AAAO,MAAM,0BAA0B,GAAG,CAAC,CAAC;AAC5C;AACA,AAAO,MAAM,6BAA6B,GAAG,IAAI,GAAG,EAAE,CAAC;AACvD,AAAO,MAAM,iCAAiC,GAAG,IAAI,GAAG,EAAE,CAAC;AAC3D,AAAO,MAAM,iCAAiC,GAAG,IAAI,GAAG,CAAC,CAAC;AAE1D,SAAgB,QAAQ,CAAC,CAAU;IACjC,OAAO,OAAO,CAAC,KAAK,QAAQ,CAAC;AAC/B,CAAC;AAaD;;;;;;;;;AASA,SAAgB,WAAW,CACzB,UAAkB,EAClB,SAA4E,EAC5E,SAAoB,EACpB,QAAgC,EAChC,KAAkB;IAElB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;QAC/B,OAAO,KAAK,CAAC;KACd;IAED,OAAO,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;AAC3C,CAAC;AAED;;;;;;;;AAQA,SAAgB,eAAe,CAC7B,YAA2F,EAC3F,YAAuB,EAAE,UAAU,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,EAC1D,GAAgB;IAEhB,IAAI,GAAG,EAAE;QACP,IAAI,SAAS,CAAC,KAAK,EAAE;YACnB,GAAG,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC;SAClC;QAED,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC;KACvB;;IAGD,SAAS,CAAC,UAAU,EAAE,CAAC;;IAGvB,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/D,MAAM,gBAAgB,GACpB,YAAY,CAAC,aAAa,GAAG,GAAG;QAChC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC;IACjE,cAAc,IAAI,gBAAgB,CAAC;IAEnC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAChC,YAAY,CAAC,gBAAgB,GAAG,cAAc,EAC9C,YAAY,CAAC,gBAAgB,CAC9B,CAAC;IAEF,OAAO,SAAS,CAAC;AACnB,CAAC;;ACtFD;AACA;AAEA;;;;;AAKA,SAAgB,SAAS,CAAI,KAA2B;IACtD,OAAO,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC;AACxD,CAAC;;ACVD;AACA,AAIA,MAAM,oBAAoB,GAAG,4BAA4B,CAAC;AAE1D;;;;;;;;;AASA,SAAgB,KAAK,CACnB,SAAiB,EACjB,KAAS,EACT,OAGC;IAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,KAAK,GAA8C,SAAS,CAAC;QACjE,IAAI,SAAS,GAA6B,SAAS,CAAC;QAEpD,MAAM,aAAa,GAAG;YACpB,OAAO,MAAM,CACX,IAAId,0BAAU,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,IAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,GAAG,oBAAoB,CAAC,CACvF,CAAC;SACH,CAAC;QAEF,MAAM,eAAe,GAAG;YACtB,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,KAAI,SAAS,EAAE;gBACrC,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;aAC7D;SACF,CAAC;QAEF,SAAS,GAAG;YACV,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;gBACpB,YAAY,CAAC,KAAK,CAAC,CAAC;aACrB;YACD,eAAe,EAAE,CAAC;YAClB,OAAO,aAAa,EAAE,CAAC;SACxB,CAAC;QAEF,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,KAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;YACvD,OAAO,aAAa,EAAE,CAAC;SACxB;QAED,KAAK,GAAG,UAAU,CAAC;YACjB,eAAe,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,CAAC;SAChB,EAAE,SAAS,CAAC,CAAC;QAEd,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE;YACxB,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;SAC1D;KACF,CAAC,CAAC;AACL,CAAC;;AC7DD;AACA,SAyBgB,sBAAsB,CACpC,UAAmB,EACnB,aAAsB,EACtB,gBAAyB;IAEzB,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,sBAAsB,CAC/B,UAAU,EACV,OAAO,EACP,UAAU,EACV,aAAa,EACb,gBAAgB,CACjB,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAED,AAGA,WAAY,SAAS;IACnB,uDAAW,CAAA;AACb,CAAC,EAFWe,iBAAS,KAATA,iBAAS,QAEpB;AA8BD,AAAO,MAAM,mBAAmB,GAAiB;IAC/C,UAAU,EAAE,0BAA0B;IACtC,cAAc,EAAE,6BAA6B;IAC7C,iBAAiB,EAAE,iCAAiC;CACrD,CAAC;AAEF;;;AAGA,MAAa,sBAAuB,SAAQ,iBAAiB;;;;;;;;;IAsB3D,YACE,UAAyB,EACzB,OAA6B,EAC7B,UAAmB,EACnB,aAAsB,EACtB,gBAAyB;QAEzB,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,0BAA0B,CAAC;QACjF,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,6BAA6B,CAAC;QAC7F,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;cAC9C,gBAAgB;cAChB,iCAAiC,CAAC;KACvC;IAEM,WAAW,CAAC,OAAwB;QACzC,OAAO,IAAI,CAAC,WAAW;aACpB,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;aAC5B,IAAI,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;aAClD,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;KAC7E;CACF;AAED,eAAe,KAAK,CAClB,MAA8B,EAC9B,OAAwB,EACxB,QAAgC,EAChC,SAAqB,EACrB,YAAyB;IAEzB,SAAS,iBAAiB,CAAC,aAAqC;QAC9D,MAAM,UAAU,GAAG,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,CAAC;QACzC,IAAI,UAAU,KAAK,GAAG,KAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA,EAAE;YACtF,OAAO,KAAK,CAAC;SACd;QAED,IACE,UAAU,KAAK,SAAS;aACvB,UAAU,GAAG,GAAG,IAAI,UAAU,KAAK,GAAG,CAAC;YACxC,UAAU,KAAK,GAAG;YAClB,UAAU,KAAK,GAAG,EAClB;YACA,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC;KACb;IAED,SAAS,GAAG,eAAe,CACzB;QACE,aAAa,EAAE,MAAM,CAAC,aAAa;QACnC,gBAAgB,EAAE,CAAC;QACnB,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;KAC1C,EACD,SAAS,EACT,YAAY,CACb,CAAC;IAEF,MAAM,SAAS,GAAwB,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC;IAC1F,IAAI,CAAC,SAAS,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE;QACxF,MAAM,CAAC,IAAI,CAAC,uBAAuB,SAAS,CAAC,aAAa,EAAE,CAAC,CAAC;QAC9D,IAAI;YACF,MAAM,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;SAC/C;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;SACzD;KACF;SAAM,IAAI,SAAS,IAAI,YAAY,IAAI,CAAC,QAAQ,EAAE;;QAEjD,MAAM,GAAG,GACP,SAAS,CAAC,KAAK;YACf,IAAI,SAAS,CACX,6BAA6B,EAC7B,SAAS,CAAC,kBAAkB,EAC5B,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAC3B,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAC5B,QAAQ,CACT,CAAC;QACJ,MAAM,GAAG,CAAC;KACX;SAAM;QACL,OAAO,QAAQ,CAAC;KACjB;AACH,CAAC;;AChMD;AACA,SAWgB,6BAA6B,CAC3C,mBAAmB,GAAG,wBAAwB;IAE9C,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,6BAA6B,CAAC,UAAU,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;SACpF;KACF,CAAC;AACJ,CAAC;AAED,MAAa,6BAA8B,SAAQ,iBAAiB;IAClE,YACE,UAAyB,EACzB,OAA6B,EACrB,oBAA4B;QAEpC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFnB,yBAAoB,GAApB,oBAAoB,CAAQ;KAGrC;IAEM,WAAW,CAAC,OAAwB;QACzC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;YACxD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;SACnE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;CACF;;ACrCD;AACA,SAMgB,sBAAsB;IACpC,OAAO,SAAS,CAAC,eAAe,CAAC,UAAU,CAAC;AAC9C,CAAC;AAED,SAAgB,uBAAuB;IACrC,MAAM,WAAW,GAAG;QAClB,GAAG,EAAE,MAAM;QACX,KAAK,EAAE,OAAO,CAAC,OAAO;KACvB,CAAC;IAEF,MAAM,MAAM,GAAG;QACb,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,IAAIC,OAAO,EAAE,IAAIC,OAAO,EAAE,IAAIC,UAAU,EAAE,GAAG;KACrD,CAAC;IAEF,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAC/B,CAAC;;ACvBD;AACA,AA2BA,SAAS,cAAc;IACrB,MAAM,aAAa,GAAG;QACpB,GAAG,EAAE,WAAW;QAChB,KAAK,EAAE,SAAS,CAAC,eAAe;KACjC,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,kBAAkB,CACzB,aAA8B,EAC9B,YAAY,GAAG,GAAG,EAClB,cAAc,GAAG,GAAG;IAEpB,OAAO,aAAa;SACjB,GAAG,CAAC,CAAC,IAAI;QACR,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;QACjE,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;KAC9B,CAAC;SACD,IAAI,CAAC,YAAY,CAAC,CAAC;AACxB,CAAC;AAED,AAAO,MAAM,6BAA6B,GAAG,sBAAsB,CAAC;AAEpE,SAAgB,wBAAwB;IACtC,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IACrC,MAAM,oBAAoB,GAAG,uBAAuB,EAAE,CAAC;IACvD,MAAM,SAAS,GAAG,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAC/E,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAgB,eAAe,CAAC,aAA6B;IAC3D,MAAM,GAAG,GACP,CAAC,aAAa,IAAI,aAAa,CAAC,GAAG,KAAK,SAAS,IAAI,aAAa,CAAC,GAAG,KAAK,IAAI;UAC3E,sBAAsB,EAAE;UACxB,aAAa,CAAC,GAAG,CAAC;IACxB,MAAM,KAAK,GACT,CAAC,aAAa,IAAI,aAAa,CAAC,KAAK,KAAK,SAAS,IAAI,aAAa,CAAC,KAAK,KAAK,IAAI;UAC/E,wBAAwB,EAAE;UAC1B,aAAa,CAAC,KAAK,CAAC;IAE1B,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;SAC7D;KACF,CAAC;AACJ,CAAC;AAED,MAAa,eAAgB,SAAQ,iBAAiB;IACpD,YACW,WAA0B,EAC1B,QAA8B,EAC7B,SAAiB,EACjB,WAAmB;QAE7B,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QALpB,gBAAW,GAAX,WAAW,CAAe;QAC1B,aAAQ,GAAR,QAAQ,CAAsB;QAC7B,cAAS,GAAT,SAAS,CAAQ;QACjB,gBAAW,GAAX,WAAW,CAAQ;KAG9B;IAED,WAAW,CAAC,OAAwB;QAClC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;IAED,kBAAkB,CAAC,OAAwB;QACzC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACpB,OAAO,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;SACrC;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;YAC5D,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SACvD;KACF;CACF;;ACpGD;AACA,AAYA;;;AAGA,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAkBxC,AAAO,MAAM,sBAAsB,GAAoB;IACrD,eAAe,EAAE,IAAI;IACrB,UAAU,EAAE,EAAE;CACf,CAAC;AAEF,SAAgB,cAAc,CAAC,cAAc,GAAG,EAAE;IAChD,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,cAAc,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;SAChE;KACF,CAAC;AACJ,CAAC;AAED,MAAa,cAAe,SAAQ,iBAAiB;IACnD,YAAY,UAAyB,EAAE,OAA6B,EAAW,aAAa,EAAE;QAC5F,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QADkD,eAAU,GAAV,UAAU,CAAK;KAE7F;IAEM,WAAW,CAAC,OAAwB;QACzC,OAAO,IAAI,CAAC,WAAW;aACpB,WAAW,CAAC,OAAO,CAAC;aACpB,IAAI,CAAC,CAAC,QAAQ,KAAK,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;KAC1D;CACF;AAED,SAAS,cAAc,CACrB,MAAsB,EACtB,QAA+B,EAC/B,cAAsB;IAEtB,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IACrC,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACxD,IACE,cAAc;SACb,MAAM,KAAK,GAAG;aACZ,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC3D,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC3D,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC;YAC7C,MAAM,KAAK,GAAG,CAAC;SAChB,CAAC,MAAM,CAAC,UAAU,IAAI,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,EAC1D;QACA,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC9C,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;;;QAIjC,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;YACvB,OAAO,OAAO,CAAC,IAAI,CAAC;SACrB;QAED,OAAO,MAAM,CAAC,WAAW;aACtB,WAAW,CAAC,OAAO,CAAC;aACpB,IAAI,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;KACnE;IAED,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,CAAC;;AC5FD;AACA,SAagB,oBAAoB,CAAC,YAAY,GAAG,EAAE;IACpD,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,oBAAoB,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;SACpE;KACF,CAAC;AACJ,CAAC;AAED,MAAa,oBAAqB,SAAQ,iBAAiB;IACzD,YACE,UAAyB,EACzB,OAA6B,EACpB,gBAAgB,EAAE;QAE3B,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFlB,kBAAa,GAAb,aAAa,CAAK;KAG5B;IAEM,WAAW,CAAC,OAAwB;QACzC,OAAO,IAAI,CAAC,WAAW;aACpB,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;aAC5B,IAAI,CAAC,CAAC,QAAQ,KAAK,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;KAClE;CACF;AAED,SAAS,gBAAgB,CACvB,MAA4B,EAC5B,OAAwB,EACxB,QAA+B;IAE/B,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;QAC3B,MAAM,MAAM,GAAG,yBAAyB,CAAC,QAAQ,CAAC,UAAoB,CAAC,CAAC;QACxE,IAAI,MAAM,EAAE;YACV,MAAM,SAAS,GAAG,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACtD,QACE,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;;;;iBAI3C,KAAK,CAAC,MAAM,KAAK,CAAC;iBAClB,IAAI,CAAC,CAAC,kBAAkB;gBACvB,IAAI,kBAAkB,EAAE;;;oBAGtB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAEC,YAAkB,EAAE,CAAC,CAAC;oBACpE,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;iBACxD;gBACD,OAAO,QAAQ,CAAC;aACjB,CAAC,EACJ;SACH;KACF;IAED,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,CAAC;AAED;;;;;;AAMA,SAAS,oBAAoB,CAC3B,eAAgC,EAChC,WAAW,GAAG,KAAK;IAEnB,MAAM,UAAU,GAAoB,eAAe,CAAC,KAAK,EAAE,CAAC;IAC5D,IAAI,WAAW,EAAE;QACf,UAAU,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC;KACtC;;;IAID,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAEA,YAAkB,EAAE,CAAC,CAAC;;IAGvE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAC;IAE1E,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;;;AAMA,SAAS,yBAAyB,CAAC,IAAY;IAC7C,IAAI,MAAM,EAAE,YAAY,CAAC;IACzB,IAAI,IAAI,EAAE;QACR,IAAI;YACF,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACjC;QAAC,OAAO,GAAG,EAAE;;SAEb;QACD,IACE,YAAY;YACZ,YAAY,CAAC,KAAK;YAClB,YAAY,CAAC,KAAK,CAAC,OAAO;YAC1B,YAAY,CAAC,KAAK,CAAC,IAAI;YACvB,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,iCAAiC,EAC7D;YACA,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC/D,IAAI,QAAQ,EAAE;gBACZ,MAAM,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;aACzB;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;AAMA,SAAS,sBAAsB,CAAC,GAAW;IACzC,IAAI,MAAM,CAAC;IACX,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;IAChE,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;QAC3B,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;KACtB;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,yDAAyD,GAAG,GAAG,CAAC,CAAC;KAClF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;AAQA,eAAe,UAAU,CACvB,MAA4B,EAC5B,SAAiB,EACjB,QAAgB,EAChB,eAAgC;IAEhC,MAAM,OAAO,GAAG,GAAG,SAAS,aAAa,QAAQ,kCAAkC,CAAC;IACpF,MAAM,MAAM,GAAG,GAAG,SAAS,aAAa,QAAQ,yBAAyB,CAAC;IAC1E,MAAM,UAAU,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;IACzD,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;IAC3B,UAAU,CAAC,GAAG,GAAG,OAAO,CAAC;IAEzB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAClE,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,2CAA2C,CAAC,CAAC;KAC7F;IACD,OAAO,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;AAChE,CAAC;AAED;;;;;;;;;AASA,eAAe,qBAAqB,CAClC,MAA4B,EAC5B,GAAW,EACX,eAAgC;IAEhC,MAAM,UAAU,GAAQ,oBAAoB,CAAC,eAAe,CAAC,CAAC;IAC9D,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IACrB,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;IAE1B,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAC7D,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC;IAC3B,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,iBAAiB,KAAK,YAAY,EAAE;QACrF,OAAO,IAAI,CAAC;KACb;SAAM;QACL,MAAM,KAAK,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;QACzC,OAAO,qBAAqB,CAAC,MAAM,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;KAC5D;AACH,CAAC;;AClMD;AACA,AA6CA;AACA,AAAO,MAAM,sBAAsB,GAAuB;IACxD,uBAAuB,EAAE,IAAI;IAC7B,iBAAiB,EAAE,IAAI;IACvB,iBAAiB,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC;CACjC,CAAC;AAEF;;;;;;;;;;;;;AAaA,eAAe,YAAY,CACzB,cAAiD,EACjD,iBAAyB,EACzB,WAAmB;;;IAInB,eAAe,iBAAiB;QAC9B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,EAAE;YAC5B,IAAI;gBACF,OAAO,MAAM,cAAc,EAAE,CAAC;aAC/B;YAAC,WAAM;gBACN,OAAO,IAAI,CAAC;aACb;SACF;aAAM;YACL,MAAM,UAAU,GAAG,MAAM,cAAc,EAAE,CAAC;;YAG1C,IAAI,UAAU,KAAK,IAAI,EAAE;gBACvB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;aACpD;YAED,OAAO,UAAU,CAAC;SACnB;KACF;IAED,IAAI,KAAK,GAAuB,MAAM,iBAAiB,EAAE,CAAC;IAE1D,OAAO,KAAK,KAAK,IAAI,EAAE;QACrB,MAAM,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAE/B,KAAK,GAAG,MAAM,iBAAiB,EAAE,CAAC;KACnC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;;;;;;;AAeA,SAAS,iBAAiB,CACxB,UAA2B,EAC3B,MAAyB,EACzB,kBAAgD;IAEhD,IAAI,aAAa,GAAgC,IAAI,CAAC;IACtD,IAAI,KAAK,GAAuB,IAAI,CAAC;IAErC,MAAM,OAAO,mCACR,sBAAsB,GACtB,kBAAkB,CACtB,CAAC;;;;;IAMF,MAAM,MAAM,GAAG;;;;QAIb,IAAI,YAAY;YACd,OAAO,aAAa,KAAK,IAAI,CAAC;SAC/B;;;;;QAKD,IAAI,aAAa;;YACf,QACE,CAAC,MAAM,CAAC,YAAY;gBACpB,CAAC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,kBAAkB,mCAAI,CAAC,IAAI,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,EACzE;SACH;;;;;QAKD,IAAI,WAAW;YACb,QACE,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC,GAAG,EAAE,EACzF;SACH;KACF,CAAC;;;;;IAMF,SAAS,OAAO,CAAC,eAAgC;;QAC/C,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;;YAExB,MAAM,iBAAiB,GAAG,MACxB,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;;;YAI/C,aAAa,GAAG,YAAY,CAC1B,iBAAiB,EACjB,OAAO,CAAC,iBAAiB;;YAEzB,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,kBAAkB,mCAAI,IAAI,CAAC,GAAG,EAAE,CACxC;iBACE,IAAI,CAAC,CAAC,MAAM;gBACX,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,MAAM,CAAC;gBACf,OAAO,KAAK,CAAC;aACd,CAAC;iBACD,KAAK,CAAC,CAAC,MAAM;;;;gBAIZ,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,IAAI,CAAC;gBACb,MAAM,MAAM,CAAC;aACd,CAAC,CAAC;SACN;QAED,OAAO,aAAqC,CAAC;KAC9C;IAED,OAAO,OAAO,YAA6B;;;;;;;;;;QAWzC,IAAI,MAAM,CAAC,WAAW;YAAE,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC;QAErD,IAAI,MAAM,CAAC,aAAa,EAAE;YACxB,OAAO,CAAC,YAAY,CAAC,CAAC;SACvB;QAED,OAAO,KAAoB,CAAC;KAC7B,CAAC;AACJ,CAAC;AAED;AAEA;;;;;;;AAOA,SAAgB,+BAA+B,CAC7C,UAA2B,EAC3B,MAAyB;;IAGzB,MAAM,QAAQ,GAAG,iBAAiB,CAAC,UAAU,EAAE,MAAM,iBAAiB,CAAC;IAEvE,MAAM,+BAAgC,SAAQ,iBAAiB;QAC7D,YAAmB,UAAyB,EAAE,OAA6B;YACzE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SAC5B;QAEM,MAAM,WAAW,CAAC,WAA4B;YACnD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBACzD,MAAM,IAAI,KAAK,CACb,sFAAsF,CACvF,CAAC;aACH;YAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC;gBAC/B,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,cAAc,EAAE;oBACd,cAAc,EAAE,WAAW,CAAC,cAAc;iBAC3C;aACF,CAAC,CAAC;YACH,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;YACpF,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;SAClD;KACF;IAED,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,+BAA+B,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SACjE;KACF,CAAC;AACJ,CAAC;;ACxQD;AACA,SAuBgB,sBAAsB,CACpC,UAAmB,EACnB,aAAsB,EACtB,gBAAyB,EACzB,gBAAyB;IAEzB,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,sBAAsB,CAC/B,UAAU,EACV,OAAO,EACP,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,gBAAgB,CACjB,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAED;;;;;;AAMA,MAAa,sBAAuB,SAAQ,iBAAiB;IAM3D,YACE,UAAyB,EACzB,OAA6B,EAC7B,UAAmB,EACnB,aAAsB,EACtB,gBAAyB,EACzB,gBAAyB;QAEzB,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,0BAA0B,CAAC;QACjF,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,6BAA6B,CAAC;QAC7F,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;cAC9C,gBAAgB;cAChB,iCAAiC,CAAC;QACtC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;cAC9C,gBAAgB;cAChB,iCAAiC,CAAC;KACvC;IAEM,WAAW,CAAC,OAAwB;QACzC,OAAO,IAAI,CAAC,WAAW;aACpB,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;aAC5B,KAAK,CAAC,CAAC,KAAK,KAAKC,OAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;KAClE;CACF;AAED,eAAeA,OAAK,CAClB,MAA8B,EAC9B,OAAwB,EACxB,iBAAwC,EACxC,GAAgB,EAChB,SAAqB;IAErB,SAAS,GAAG,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;IAEpD,SAAS,iBAAiB,CAAC,SAAiC,EAAE,KAAkB;QAC9E,IACE,KAAK;YACL,KAAK,CAAC,IAAI;aACT,KAAK,CAAC,IAAI,KAAK,WAAW;gBACzB,KAAK,CAAC,IAAI,KAAK,iBAAiB;gBAChC,KAAK,CAAC,IAAI,KAAK,cAAc;gBAC7B,KAAK,CAAC,IAAI,KAAK,YAAY;gBAC3B,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,EAC1B;YACA,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;KACd;IAED,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,iBAAiB,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG,CAAC,EAAE;;QAExF,IAAI;YACF,MAAM,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;SACxD;QAAC,OAAO,SAAS,EAAE;YAClB,OAAOA,OAAK,CAAC,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;SACxE;KACF;SAAM;QACL,IAAI,GAAG,EAAE;;YAEP,OAAO,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACxC;QACD,OAAO,iBAAiB,CAAC;KAC1B;AACH,CAAC;;ACzHD;AACA,AAKA,WAAY,qBAAqB;IAC/B,kCAAS,CAAA;IACT,kCAAS,CAAA;IACT,mCAAU,CAAA;IACV,oCAAW,CAAA;IACX,wCAAe,CAAA;AACjB,CAAC,EANWC,6BAAqB,KAArBA,6BAAqB,QAMhC;;ACZD;AACA,AAeA;;;;AAIA,AAAO,MAAM,iBAAiB,GAAa,EAAE,CAAC;AAC9C,IAAI,iBAAiB,GAAY,KAAK,CAAC;AAEvC;AACA,MAAM,iBAAiB,GAAyB,IAAI,GAAG,EAAE,CAAC;AAE1D,SAAS,yBAAyB;IAChC,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,UAAU,GAAG,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9D,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1D,MAAM,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAE5D,OAAO,UAAU,IAAI,QAAQ,IAAI,SAAS,CAAC;AAC7C,CAAC;AAED;;;;;AAKA,SAAS,UAAU,CACjB,GAAW,EACX,WAAqB,EACrB,WAAkC;IAElC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,OAAO,KAAK,CAAC;KACd;IACD,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAG,CAAC;IAC9C,IAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,IAAI,CAAC,EAAE;QAC1B,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC9B;IACD,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;QACjC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;;YAGtB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC1B,cAAc,GAAG,IAAI,CAAC;aACvB;iBAAM;gBACL,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;oBACnE,cAAc,GAAG,IAAI,CAAC;iBACvB;aACF;SACF;aAAM;YACL,IAAI,IAAI,KAAK,OAAO,EAAE;gBACpB,cAAc,GAAG,IAAI,CAAC;aACvB;SACF;KACF;IACD,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACvC,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;AAGA,SAAgB,WAAW;IACzB,MAAM,OAAO,GAAG,mBAAmB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACxD,iBAAiB,GAAG,IAAI,CAAC;IACzB,IAAI,OAAO,EAAE;QACX,OAAO,OAAO;aACX,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;aAC1B,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;KAClC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAgB,uBAAuB,CAAC,QAAiB;IACvD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,yBAAyB,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,SAAS,CAAC;SAClB;KACF;IAED,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC5E,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACnD,MAAM,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,GAAG,SAAS,CAAC,SAAS,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;IAC1E,OAAO;QACL,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC,OAAO,EAAE;QAClC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC;QAClD,QAAQ;QACR,QAAQ;KACT,CAAC;AACJ,CAAC;AAED;;;;;;;AAOA,SAAgB,WAAW,CACzB,aAA6B,EAC7B,OAGC;IAED,IAAI,CAAC,aAAa,EAAE;QAClB,aAAa,GAAG,uBAAuB,EAAE,CAAC;KAC3C;IACD,IAAI,CAAC,iBAAiB,EAAE;QACtB,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC;KAC1C;IACD,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,oBAA0C;YAC5E,OAAO,IAAI,WAAW,CACpB,UAAU,EACV,oBAAoB,EACpB,aAAc,EACd,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,CAC3B,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,GAAW;IAEX,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE;QAClB,OAAO,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC;KAChC;IAED,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACvC,MAAM,SAAS,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3D,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACrC,MAAM,WAAW,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC;IACtC,MAAM,QAAQ,GAAG,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC;IACpE,MAAM,QAAQ,GAAG,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;IAC1E,MAAM,cAAc,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;IAChF,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,cAAc;KACf,CAAC;AACJ,CAAC;AAED,MAAa,WAAY,SAAQ,iBAAiB;IAChD,YACE,UAAyB,EACzB,OAA6B,EACtB,aAA4B,EAC3B,iBAA4B;QAEpC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAHpB,kBAAa,GAAb,aAAa,CAAe;QAC3B,sBAAiB,GAAjB,iBAAiB,CAAW;KAGrC;IAEM,WAAW,CAAC,OAAwB;;QACzC,IACE,CAAC,OAAO,CAAC,aAAa;YACtB,CAAC,UAAU,CACT,OAAO,CAAC,GAAG,EACX,MAAA,IAAI,CAAC,iBAAiB,mCAAI,iBAAiB,EAC3C,IAAI,CAAC,iBAAiB,GAAG,SAAS,GAAG,iBAAiB,CACvD,EACD;YACA,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAC5C;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;CACF;;AC9LD;AACA;AAEA;;;AAGA,AAAO,MAAM,8BAA8B,GAAG,CAAC,CAAC;;ACNhD;AACA,AAmBA,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC;AAExD,SAAgB,qBAAqB;IACnC,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,qBAAqB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SACvD;KACF,CAAC;AACJ,CAAC;AAED,MAAMC,sBAAoB,GAAG,4BAA4B,CAAC;AAE1D;;;;;;AAMA,MAAa,qBAAsB,SAAQ,iBAAiB;IAI1D,YACE,UAAyB,EACzB,OAA6B,EAC7B,eAAiC;QAEjC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAPrB,oBAAe,GAAG,CAAC,CAAC;QAQ1B,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,CAAC,uBAAuB,CAAC;KACxE;IAEM,MAAM,WAAW,CAAC,WAA4B;QACnD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;QACzE,IACE,QAAQ,CAAC,MAAM,KAAK,WAAW,CAAC,eAAe;YAC/C,QAAQ,CAAC,MAAM,KAAK,WAAW,CAAC,kBAAkB,EAClD;YACA,OAAO,QAAQ,CAAC;SACjB;aAAM;YACL,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;SACpD;KACF;IAEO,MAAM,uBAAuB,CACnC,WAA4B,EAC5B,YAAmC;;QAEnC,MAAM,gBAAgB,GAAuB,YAAY,CAAC,OAAO,CAAC,GAAG,CACnE,SAAS,CAAC,eAAe,CAAC,WAAW,CACtC,CAAC;QAEF,IAAI,gBAAgB,EAAE;YACpB,MAAM,SAAS,GAAuB,qBAAqB,CAAC,qBAAqB,CAC/E,gBAAgB,CACjB,CAAC;YACF,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;gBAE1B,MAAM,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE;oBAChC,WAAW,EAAE,WAAW,CAAC,WAAW;oBACpC,aAAa,EAAEA,sBAAoB;iBACpC,CAAC,CAAC;gBAEH,IAAI,MAAA,WAAW,CAAC,WAAW,0CAAE,OAAO,EAAE;oBACpC,MAAM,IAAItB,0BAAU,CAACsB,sBAAoB,CAAC,CAAC;iBAC5C;gBAED,IAAI,IAAI,CAAC,eAAe,GAAG,8BAA8B,EAAE;oBACzD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;iBACtC;qBAAM;oBACL,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;iBAClD;aACF;SACF;QAED,OAAO,YAAY,CAAC;KACrB;IAEM,OAAO,qBAAqB,CAAC,WAAmB;QACrD,MAAM,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE;YACrC,OAAO,qBAAqB,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;SACrE;aAAM;YACL,OAAO,mBAAmB,GAAG,IAAI,CAAC;SACnC;KACF;IAEM,OAAO,yBAAyB,CAAC,WAAmB;QACzD,IAAI;YACF,MAAM,GAAG,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAW,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC7C,MAAM,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;YAExB,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;SAC9C;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,SAAS,CAAC;SAClB;KACF;CACF;;ACtHD;AACA,SAYgB,aAAa,CAC3B,sBAAgD;IAEhD,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;SACvE;KACF,CAAC;AACJ,CAAC;AAED,MAAa,aAAc,SAAQ,iBAAiB;IAClD,YACE,UAAyB,EACzB,OAA6B,EACtB,sBAAgD;QAEvD,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFpB,2BAAsB,GAAtB,sBAAsB,CAA0B;KAGxD;IAED,WAAW,CAAC,OAAwB;QAClC,OAAO,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KACzD;IAEM,WAAW,CAAC,OAAwB;QACzC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAChD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAC1C,CAAC;KACH;CACF;;ACzCD;AACA,AAuBO,MAAM,uBAAuB,GAAqB;IACvD,MAAM,EAAE,IAAI;CACb,CAAC;AAEF,SAAgB,eAAe,CAAC,gBAAmC;IACjE,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,gBAAgB,IAAI,uBAAuB,CAAC,CAAC;SAC9F;KACF,CAAC;AACJ,CAAC;AAED;;;AAGA,MAAa,eAAgB,SAAQ,iBAAiB;;;;;;;;IAQpD,YACE,UAAyB,EACzB,OAA6B,EACZ,gBAAkC;QAEnD,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFV,qBAAgB,GAAhB,gBAAgB,CAAkB;KAGpD;;;;;;;IAQM,MAAM,WAAW,CAAC,OAAwB;QAC/C,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QACjD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;CACF;;ACjED;AACA,AAqBA,MAAM,UAAU,GAAGC,8BAAkB,CAAC;IACpC,aAAa,EAAE,EAAE;IACjB,SAAS,EAAE,EAAE;CACd,CAAC,CAAC;AAMH,SAAgB,aAAa,CAAC,iBAAuC,EAAE;IACrE,OAAO;QACL,MAAM,CAAC,UAAyB,EAAE,OAA6B;YAC7D,OAAO,IAAI,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;SAC/D;KACF,CAAC;AACJ,CAAC;AAED,MAAa,aAAc,SAAQ,iBAAiB;IAGlD,YACE,UAAyB,EACzB,OAA6B,EAC7B,cAAoC;QAEpC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;KAC3C;IAEM,MAAM,WAAW,CAAC,OAAwB;QAC/C,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC9C;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC9C;QAED,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC7D,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACxC,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAChC,MAAM,GAAG,CAAC;SACX;KACF;IAED,aAAa,CAAC,OAAwB;;QACpC,IAAI;YACF,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC;;;YAI5D,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE;gBAChC,cAAc,EAAE;oBACd,WAAW,kCACL,OAAe,CAAC,WAAW,KAC/B,IAAI,EAAEC,oBAAQ,CAAC,MAAM,GACtB;oBACD,cAAc,EAAE,OAAO,CAAC,cAAc;iBACvC;aACF,CAAC,CAAC;;YAGH,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;gBACvB,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,OAAO,SAAS,CAAC;aAClB;YAED,MAAM,oBAAoB,GAAG,MAAA,OAAO,CAAC,cAAc,0CAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;YAE1F,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;gBAC5C,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;aACzD;YAED,IAAI,CAAC,aAAa,CAAC;gBACjB,aAAa,EAAE,OAAO,CAAC,MAAM;gBAC7B,UAAU,EAAE,OAAO,CAAC,GAAG;gBACvB,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aACtD;;YAGD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,iBAAiB,GAAGC,gCAAoB,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,iBAAiB,IAAIC,8BAAkB,CAAC,WAAW,CAAC,EAAE;gBACxD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;gBACtD,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;;gBAEhF,IAAI,UAAU,EAAE;oBACd,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;iBAC/C;aACF;YACD,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,OAAO,CAAC,qDAAqD,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACrF,OAAO,SAAS,CAAC;SAClB;KACF;IAEO,eAAe,CAAC,IAAU,EAAE,GAAQ;QAC1C,IAAI;YACF,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAEC,0BAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC;YAEH,IAAI,GAAG,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;aACvD;YACD,IAAI,CAAC,GAAG,EAAE,CAAC;SACZ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,OAAO,CAAC,qDAAqD,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtF;KACF;IAEO,kBAAkB,CAAC,IAAU,EAAE,QAA+B;QACpE,IAAI;YACF,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACjE,IAAI,gBAAgB,EAAE;gBACpB,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACzD;YACD,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAEA,0BAAc,CAAC,EAAE;aACxB,CAAC,CAAC;YACH,IAAI,CAAC,GAAG,EAAE,CAAC;SACZ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,OAAO,CAAC,qDAAqD,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtF;KACF;CACF;;AC/JD;AACA,AAWA;;;;AAIA,SAAgB,kCAAkC;IAChD,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,kCAAkC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SACpE;KACF,CAAC;AACJ,CAAC;AAED;;;;AAIA,MAAa,kCAAmC,SAAQ,iBAAiB;;;;;;;;;IASvE,YAAY,UAAyB,EAAE,OAA6B;QAClE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;KAC5B;;;;;;;IAQM,MAAM,WAAW,CAAC,OAAoB;QAC3C,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;CACF;;ACnDD;AACA,SAcgB,YAAY;IAC1B,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B;YAC/D,OAAO,IAAI,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;SAC9C;KACF,CAAC;AACJ,CAAC;AAED;;;AAGA,MAAM,YAAa,SAAQ,iBAAiB;;;;IAI1C,YAAY,UAAyB,EAAE,OAA6B;QAClE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;KAC5B;;;;IAKM,MAAM,WAAW,CAAC,OAAwB;;QAE/C,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACpE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACvB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACzE;SACF;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC9C;CACF;;AC/CD;AACA,AAKA,IAAI,gBAAwC,CAAC;AAE7C,SAAgB,0BAA0B;IACxC,IAAI,CAAC,gBAAgB,EAAE;QACrB,gBAAgB,GAAG,IAAIC,mBAAiB,EAAE,CAAC;KAC5C;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;;ACdD;AACA,AA8JA;;;AAGA,MAAa,aAAa;;;;;;IA2BxB,YACE,WAAwD;;IAExD,OAA8B;QAE9B,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,EAAE,CAAC;SACd;QAED,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,IAAI,KAAK,CAAC;QACzD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,0BAA0B,EAAE,CAAC;QACtE,IAAI,CAAC,qBAAqB,GAAG,IAAI,oBAAoB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAElF,IAAI,sBAA8C,CAAC;QACnD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE;YACjD,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC5D,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;SACzD;aAAM;YACL,IAAI,iBAAiB,GAAqC,SAAS,CAAC;YACpE,IAAIC,0BAAiB,CAAC,WAAW,CAAC,EAAE;gBAClC,MAAM,CAAC,IAAI,CACT,sFAAsF,CACvF,CAAC;;;;;;;gBAOF,MAAM,oBAAoB,GAA+B;oBACvD,IAAI,wBAAwB,GAAqC,SAAS,CAAC;;oBAE3E,MAAM,aAAa,GAAG,IAAI,CAAC;oBAC3B,MAAM,oBAAoB,GAAG,OAAO,CAAC;oBACrC,OAAO;wBACL,MAAM,CAAC,UAAyB,EAAE,aAAmC;4BACnE,MAAM,gBAAgB,GAAG,mBAAmB,CAC1C,oBAAoB,EACpB,aAAa,CAAC,OAAO,CACtB,CAAC;4BAEF,IAAI,CAAC,gBAAgB,EAAE;gCACrB,MAAM,IAAI,KAAK,CACb,mKAAmK,CACpK,CAAC;6BACH;4BAED,IAAI,wBAAwB,KAAK,SAAS,IAAI,wBAAwB,KAAK,IAAI,EAAE;gCAC/E,wBAAwB,GAAG,+BAA+B,CACxD,WAAW,EACX,gBAAgB,CACjB,CAAC;6BACH;4BAED,OAAO,wBAAwB,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;yBACnE;qBACF,CAAC;iBACH,CAAC;gBAEF,iBAAiB,GAAG,oBAAoB,EAAE,CAAC;aAC5C;iBAAM,IAAI,WAAW,IAAI,OAAO,WAAW,CAAC,WAAW,KAAK,UAAU,EAAE;gBACvE,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;gBAChF,iBAAiB,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;aAChD;iBAAM,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;gBAC5D,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;aAC1F;YAED,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAC7D,sBAAsB,GAAG,mCAAmC,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YACzF,IAAI,OAAO,CAAC,sBAAsB,EAAE;;;gBAGlC,MAAM,yBAAyB,GAEF,OAAO,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;gBACpF,IAAI,yBAAyB,EAAE;oBAC7B,sBAAsB,GAAG,yBAAyB,CAAC;iBACpD;aACF;SACF;QACD,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;KACvD;;;;IAKD,WAAW,CAAC,OAAgD;QAC1D,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC5E,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;SACvF;QAED,IAAI,WAA4B,CAAC;QACjC,IAAI;YACF,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE;gBAC9B,OAAO,CAAC,yBAAyB,EAAE,CAAC;gBACpC,WAAW,GAAG,OAAO,CAAC;aACvB;iBAAM;gBACL,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;gBAChC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAC5C;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,IAAI,YAAY,GAAkB,IAAI,CAAC,WAAW,CAAC;QACnD,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3E,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;gBACjE,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,MAAM,CACnD,YAAY,EACZ,IAAI,CAAC,qBAAqB,CAC3B,CAAC;aACH;SACF;QACD,OAAO,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;KAC9C;;;;;;;IAQD,MAAM,oBAAoB,CACxB,kBAAsC,EACtC,aAA4B,EAC5B,QAA+B;;QAE/B,IAAI,OAAO,kBAAkB,CAAC,OAAO,KAAK,UAAU,EAAE;YACpD,QAAQ,GAAG,kBAAkB,CAAC,OAAO,CAAC;YACtC,kBAAkB,CAAC,OAAO,GAAG,SAAS,CAAC;SACxC;QAED,MAAM,iBAAiB,GAAG,MAAA,kBAAkB,CAAC,OAAO,0CAAE,iBAAiB,CAAC;QACxE,MAAM,WAAW,GAAoB,IAAI,WAAW,EAAE,CAAC;QAEvD,IAAI,MAA6B,CAAC;QAClC,IAAI;YACF,MAAM,OAAO,GAAuB,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;YAC1E,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CACb,0IAA0I,CAC3I,CAAC;aACH;YAED,WAAW,CAAC,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC;YAC9C,WAAW,CAAC,aAAa,GAAG,aAAa,CAAC;YAE1C,MAAM,UAAU,GAAe,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzD,IAAI,aAAa,CAAC,IAAI,EAAE;gBACtB,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAC3C;YACD,IAAI,aAAa,CAAC,aAAa,IAAI,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzE,KAAK,MAAM,YAAY,IAAI,aAAa,CAAC,aAAa,EAAE;oBACtD,IAAI,iBAAiB,GAAW,sCAAsC,CACpE,IAAI,EACJ,kBAAkB,EAClB,YAAY,EACZ,aAAa,CAAC,UAAU,CACzB,CAAC;oBACF,iBAAiB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACpD,YAAY,CAAC,MAAM,EACnB,iBAAiB,EACjB,0BAA0B,CAAC,YAAY,CAAC,EACxC,iBAAiB,CAClB,CAAC;oBACF,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;wBAC9B,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;qBAC3D;oBACD,UAAU,CAAC,UAAU,CACnB,IAAI,YAAY,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,YAAY,CAAC,GAAG,EACrF,iBAAiB,CAClB,CAAC;iBACH;aACF;YACD,IAAI,aAAa,CAAC,eAAe,IAAI,aAAa,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7E,KAAK,MAAM,cAAc,IAAI,aAAa,CAAC,eAAe,EAAE;oBAC1D,IAAI,mBAAmB,GAAQ,sCAAsC,CACnE,IAAI,EACJ,kBAAkB,EAClB,cAAc,EACd,aAAa,CAAC,UAAU,CACzB,CAAC;oBACF,IAAI,mBAAmB,KAAK,SAAS,IAAI,mBAAmB,KAAK,IAAI,EAAE;wBACrE,mBAAmB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACtD,cAAc,CAAC,MAAM,EACrB,mBAAmB,EACnB,0BAA0B,CAAC,cAAc,CAAC,EAC1C,iBAAiB,CAClB,CAAC;wBACF,IACE,cAAc,CAAC,gBAAgB,KAAK,SAAS;4BAC7C,cAAc,CAAC,gBAAgB,KAAK,IAAI,EACxC;4BACA,IAAI,cAAc,CAAC,gBAAgB,KAAKR,6BAAqB,CAAC,KAAK,EAAE;gCACnE,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;;oCAEpC,SAAS;iCACV;qCAAM;oCACL,KAAK,MAAM,KAAK,IAAI,mBAAmB,EAAE;wCACvC,MAAM,IAAI,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;wCACxC,mBAAmB,CAAC,KAAK,CAAC;4CACxB,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;qCAC9D;iCACF;6BACF;iCAAM,IACL,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,GAAG;gCAC7D,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,GAAG,EAC7D;gCACA,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;6BACjF;yBACF;wBACD,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;4BAChC,IAAI,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;gCACtC,KAAK,MAAM,KAAK,IAAI,mBAAmB,EAAE;oCACvC,IACE,mBAAmB,CAAC,KAAK,CAAC,KAAK,SAAS;wCACxC,mBAAmB,CAAC,KAAK,CAAC,KAAK,IAAI,EACnC;wCACA,mBAAmB,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;qCAC7E;iCACF;6BACF;iCAAM;gCACL,mBAAmB,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;6BAC/D;yBACF;wBACD,IACE,cAAc,CAAC,gBAAgB,KAAK,SAAS;4BAC7C,cAAc,CAAC,gBAAgB,KAAK,IAAI;4BACxC,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,KAAK;4BAC/D,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,GAAG;4BAC7D,cAAc,CAAC,gBAAgB,KAAKA,6BAAqB,CAAC,GAAG,EAC7D;4BACA,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;yBACjF;wBACD,UAAU,CAAC,iBAAiB,CAC1B,cAAc,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,cAAc,CAAC,EAClF,mBAAmB,CACpB,CAAC;qBACH;iBACF;aACF;YACD,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;YAExC,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,kBAAkB,CAAC;YACzE,IAAI,WAAW,IAAI,aAAa,CAAC,WAAW,EAAE;gBAC5C,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;aACtD;YAED,IAAI,aAAa,CAAC,gBAAgB,EAAE;gBAClC,KAAK,MAAM,eAAe,IAAI,aAAa,CAAC,gBAAgB,EAAE;oBAC5D,IAAI,WAAW,GAAQ,sCAAsC,CAC3D,IAAI,EACJ,kBAAkB,EAClB,eAAe,EACf,aAAa,CAAC,UAAU,CACzB,CAAC;oBACF,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;wBACrD,WAAW,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CAC9C,eAAe,CAAC,MAAM,EACtB,WAAW,EACX,0BAA0B,CAAC,eAAe,CAAC,EAC3C,iBAAiB,CAClB,CAAC;wBACF,MAAM,sBAAsB,GAAI,eAAe,CAAC,MAA2B;6BACxE,sBAAsB,CAAC;wBAC1B,IAAI,sBAAsB,EAAE;4BAC1B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gCAC1C,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;6BACzE;yBACF;6BAAM;4BACL,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,eAAe,CAAC,MAAM,CAAC,cAAc;gCACnC,0BAA0B,CAAC,eAAe,CAAC,EAC7C,WAAW,CACZ,CAAC;yBACH;qBACF;iBACF;aACF;YAED,MAAM,OAAO,GAAmC,kBAAkB,CAAC,OAAO,CAAC;YAC3E,IAAI,OAAO,EAAE;gBACX,IAAI,OAAO,CAAC,aAAa,EAAE;oBACzB,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,aAAa,EAAE;wBACpD,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;qBACpF;iBACF;gBAED,IAAI,OAAO,CAAC,WAAW,EAAE;oBACvB,WAAW,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;iBAC/C;gBAED,IAAI,OAAO,CAAC,OAAO,EAAE;oBACnB,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;iBACvC;gBAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;oBAC5B,WAAW,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;iBACzD;gBAED,IAAI,OAAO,CAAC,kBAAkB,EAAE;oBAC9B,WAAW,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;iBAC7D;gBAED,IAAI,OAAO,CAAC,WAAW,EAAE;;oBAEtB,WAAmB,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;iBACxD;gBAED,IAAI,OAAO,CAAC,cAAc,EAAE;oBAC1B,WAAW,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;iBACrD;gBAED,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,IAAI,OAAO,CAAC,iBAAiB,KAAK,IAAI,EAAE;oBACjF,WAAW,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;iBAC3D;aACF;YAED,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAEpD,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;YAE3E,IAAI,WAAW,CAAC,yBAAyB,KAAK,SAAS,EAAE;gBACvD,WAAW,CAAC,yBAAyB,GAAG,4BAA4B,CAAC,aAAa,CAAC,CAAC;aACrF;YAED,IAAI,WAAkC,CAAC;YACvC,IAAI,gBAAgB,CAAC;YACrB,IAAI;gBACF,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;aACnD;YAAC,OAAO,KAAK,EAAE;gBACd,gBAAgB,GAAG,KAAK,CAAC;aAC1B;YACD,IAAI,gBAAgB,EAAE;gBACpB,IAAI,gBAAgB,CAAC,QAAQ,EAAE;oBAC7B,gBAAgB,CAAC,OAAO,GAAG,eAAe,CACxC,gBAAgB,CAAC,QAAQ,EACzB,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC;wBAClD,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CACrC,CAAC;iBACH;gBACD,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;aAC3C;iBAAM;gBACL,MAAM,GAAG,OAAO,CAAC,OAAO,CACtB,eAAe,CAAC,WAAY,EAAE,aAAa,CAAC,SAAS,CAAC,WAAY,CAAC,MAAM,CAAC,CAAC,CAC5E,CAAC;aACH;SACF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAChC;QAED,MAAM,EAAE,GAAG,QAAQ,CAAC;QACpB,IAAI,EAAE,EAAE;YACN,MAAM;iBACH,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;iBACvF,KAAK,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;SAC5B;QAED,OAAO,MAAM,CAAC;KACf;CACF;AAED,SAAgB,oBAAoB,CAClC,aAA4B,EAC5B,WAA4B,EAC5B,kBAAsC,EACtC,aAA4B;;IAE5B,MAAM,iBAAiB,GAAG,MAAA,MAAA,kBAAkB,CAAC,OAAO,0CAAE,iBAAiB,mCAAI,EAAE,CAAC;IAC9E,MAAM,cAAc,GAAgC;QAClD,QAAQ,EAAE,MAAA,iBAAiB,CAAC,QAAQ,mCAAI,EAAE;QAC1C,WAAW,EAAE,MAAA,iBAAiB,CAAC,WAAW,mCAAI,KAAK;QACnD,UAAU,EAAE,MAAA,iBAAiB,CAAC,UAAU,mCAAI,WAAW;KACxD,CAAC;IAEF,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;IAChD,IAAI,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE;QACjE,WAAW,CAAC,IAAI,GAAG,sCAAsC,CACvD,aAAa,EACb,kBAAkB,EAClB,aAAa,CAAC,WAAW,EACzB,aAAa,CAAC,UAAU,CACzB,CAAC;QAEF,MAAM,UAAU,GAAG,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC;QACpD,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,cAAc,EACd,cAAc,EACd,YAAY,EACZ,kBAAkB,EACnB,GAAG,UAAU,CAAC;QACf,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QAEtC,IAAI;YACF,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,KAAK,QAAQ,EAAE;gBAC7E,MAAM,8BAA8B,GAAW,0BAA0B,CACvE,aAAa,CAAC,WAAW,CAC1B,CAAC;gBACF,WAAW,CAAC,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACnD,UAAU,EACV,WAAW,CAAC,IAAI,EAChB,8BAA8B,EAC9B,cAAc,CACf,CAAC;gBAEF,MAAM,QAAQ,GAAG,QAAQ,KAAK,UAAU,CAAC,MAAM,CAAC;gBAEhD,IAAI,aAAa,CAAC,KAAK,EAAE;oBACvB,MAAM,QAAQ,GAAG,kBAAkB,GAAG,SAAS,kBAAkB,EAAE,GAAG,OAAO,CAAC;oBAC9E,MAAM,KAAK,GAAG,wBAAwB,CACpC,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,WAAW,CAAC,IAAI,EAChB,cAAc,CACf,CAAC;oBACF,IAAI,QAAQ,KAAK,UAAU,CAAC,QAAQ,EAAE;wBACpC,WAAW,CAAC,IAAI,GAAG,YAAY,CAC7BS,kBAAwB,CACtB,KAAK,EACL,cAAc,IAAI,OAAO,IAAI,cAAe,EAC5C,QAAQ,EACR,YAAY,CACb,EACD;4BACE,QAAQ,EAAE,OAAO,IAAI,cAAc;4BACnC,UAAU;yBACX,CACF,CAAC;qBACH;yBAAM,IAAI,CAAC,QAAQ,EAAE;wBACpB,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE;4BACrC,QAAQ,EAAE,OAAO,IAAI,cAAc;4BACnC,UAAU;yBACX,CAAC,CAAC;qBACJ;iBACF;qBAAM,IACL,QAAQ,KAAK,UAAU,CAAC,MAAM;qBAC7B,CAAA,MAAA,aAAa,CAAC,WAAW,0CAAE,KAAK,CAAC,YAAY,CAAC,KAAI,aAAa,CAAC,SAAS,KAAK,MAAM,CAAC,EACtF;;;oBAGA,OAAO;iBACR;qBAAM,IAAI,CAAC,QAAQ,EAAE;oBACpB,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBACrD;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,IAAI,KAAK,CACb,UAAU,KAAK,CAAC,OAAO,2CAA2C,IAAI,CAAC,SAAS,CAC9E,cAAc,EACd,SAAS,EACT,IAAI,CACL,GAAG,CACL,CAAC;SACH;KACF;SAAM,IAAI,aAAa,CAAC,kBAAkB,IAAI,aAAa,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC1F,WAAW,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC1B,KAAK,MAAM,iBAAiB,IAAI,aAAa,CAAC,kBAAkB,EAAE;YAChE,MAAM,sBAAsB,GAAQ,sCAAsC,CACxE,aAAa,EACb,kBAAkB,EAClB,iBAAiB,EACjB,aAAa,CAAC,UAAU,CACzB,CAAC;YACF,IAAI,sBAAsB,KAAK,SAAS,IAAI,sBAAsB,KAAK,IAAI,EAAE;gBAC3E,MAAM,6BAA6B,GACjC,iBAAiB,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;gBAC3F,WAAW,CAAC,QAAQ,CAAC,6BAA6B,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACtF,iBAAiB,CAAC,MAAM,EACxB,sBAAsB,EACtB,0BAA0B,CAAC,iBAAiB,CAAC,EAC7C,cAAc,CACf,CAAC;aACH;SACF;KACF;AACH,CAAC;AAED;;;AAGA,SAAS,wBAAwB,CAC/B,YAAgC,EAChC,QAAgB,EAChB,QAAgB,EAChB,eAAoB,EACpB,OAAoC;;;IAIpC,IAAI,YAAY,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC/E,MAAM,MAAM,GAAQ,EAAE,CAAC;QACvB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;QAC7C,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,GAAG,YAAY,EAAE,CAAC;QACnD,OAAO,MAAM,CAAC;KACf;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,wBAAwB,CAC/B,KAA8D,EAC9D,mBAAiC;IAEjC,IAAI,MAAc,CAAC;IACnB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,GAAG,KAAK,CAAC;KAChB;SAAM;QACL,MAAM,GAAG,mBAAmB,EAAE,CAAC;QAC/B,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC/B,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;SACxB;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,mCAAmC,CAC1C,iBAAmD,EACnD,OAA6B;IAE7B,MAAM,SAAS,GAA2B,EAAE,CAAC;IAE7C,IAAI,OAAO,CAAC,6BAA6B,EAAE;QACzC,SAAS,CAAC,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,CAAC;KAClF;IAED,IAAI,iBAAiB,EAAE;QACrB,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;KACnC;IAED,MAAM,mBAAmB,GAAW,wBAAwB,CAC1D,OAAO,CAAC,mBAAmB,EAC3B,6BAA6B,CAC9B,CAAC;IACF,MAAM,oBAAoB,GAAW,wBAAwB,CAC3D,OAAO,CAAC,SAAS,EACjB,wBAAwB,CACzB,CAAC;IACF,IAAI,mBAAmB,IAAI,oBAAoB,EAAE;QAC/C,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,EAAE,mBAAmB,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC;KAC5F;IACD,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACjC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;IAEzE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;QAC1B,SAAS,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;QACzC,SAAS,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;QACzC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;KACzC;IAED,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC,CAAC;IAE3E,IAAI,MAAM,EAAE;QACV,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;KACpD;IAED,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAEnD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAgB,yBAAyB,CACvC,eAAwC,EACxC,iBAAwC;IAExC,MAAM,sBAAsB,GAA2B,EAAE,CAAC;IAE1D,IAAI,eAAe,CAAC,iBAAiB,EAAE;QACrC,sBAAsB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;KAC7C;IAED,IAAI,cAAc,GAAG,SAAS,CAAC;IAC/B,IAAI,eAAe,CAAC,gBAAgB,IAAI,eAAe,CAAC,gBAAgB,CAAC,eAAe,EAAE;QACxF,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;;;QAIrE,MAAM,oBAAoB,GAAG,wBAAwB,EAAE,CAAC;QACxD,IAAI,aAAa,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE;YACtD,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC1C;QAED,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC1C;IAED,MAAM,gBAAgB,mCACjB,uBAAuB,GACvB,eAAe,CAAC,gBAAgB,CACpC,CAAC;IAEF,MAAM,YAAY,mCACb,mBAAmB,GACnB,eAAe,CAAC,YAAY,CAChC,CAAC;IAEF,MAAM,eAAe,mCAChB,sBAAsB,GACtB,eAAe,CAAC,eAAe,CACnC,CAAC;IAEF,IAAI,MAAM,EAAE;QACV,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC;KACxE;IAED,MAAM,sBAAsB,mCACvB,6BAA6B,GAC7B,eAAe,CAAC,sBAAsB,CAC1C,CAAC;IAEF,MAAM,cAAc,qBACf,eAAe,CAAC,cAAc,CAClC,CAAC;IAEF,sBAAsB,CAAC,IAAI,CACzB,aAAa,CAAC,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,EAC5C,eAAe,CAAC,gBAAgB,CAAC,EACjC,eAAe,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,EAC1C,6BAA6B,EAAE,EAC/B,qBAAqB,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,EAClE,qBAAqB,EAAE,EACvB,sBAAsB,EAAE,EACxB,sBAAsB,CACpB,YAAY,CAAC,UAAU,EACvB,YAAY,CAAC,cAAc,EAC3B,YAAY,CAAC,iBAAiB,CAC/B,CACF,CAAC;IAEF,IAAI,eAAe,CAAC,eAAe,EAAE;QACnC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;KACzE;IAED,IAAI,iBAAiB,EAAE;QACrB,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAChD;IAED,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC;IAEvD,IAAI,MAAM,IAAI,eAAe,CAAC,kBAAkB,KAAK,KAAK,EAAE;QAC1D,sBAAsB,CAAC,IAAI,CAAC,kCAAkC,EAAE,CAAC,CAAC;KACnE;IAED,OAAO;QACL,UAAU,EAAE,eAAe,CAAC,UAAU;QACtC,sBAAsB;KACvB,CAAC;AACJ,CAAC;AAID,AAkBA,SAAS,sCAAsC,CAC7C,aAA4B,EAC5B,kBAAsC,EACtC,SAA6B,EAC7B,UAAsB;IAEtB,OAAO,0CAA0C,CAC/C,aAAa,EACb,kBAAkB,EAClB,SAAS,CAAC,aAAa,EACvB,SAAS,CAAC,MAAM,EAChB,UAAU,CACX,CAAC;AACJ,CAAC;AAED,SAAgB,0CAA0C,CACxD,aAA4B,EAC5B,kBAAsC,EACtC,aAA4B,EAC5B,eAAuB,EACvB,UAAsB;;IAEtB,IAAI,KAAU,CAAC;IACf,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,aAAa,GAAG,CAAC,aAAa,CAAC,CAAC;KACjC;IACD,MAAM,iBAAiB,GAAG,MAAA,kBAAkB,CAAC,OAAO,0CAAE,iBAAiB,CAAC;IACxE,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QAChC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAI,eAAe,CAAC,UAAU,EAAE;gBAC9B,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC;aACtC;iBAAM;gBACL,IAAI,oBAAoB,GAAyB,4BAA4B,CAC3E,kBAAkB,EAClB,aAAa,CACd,CAAC;gBACF,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE;oBACvC,oBAAoB,GAAG,4BAA4B,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;iBACnF;gBAED,IAAI,eAAe,GAAG,KAAK,CAAC;gBAC5B,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE;oBACvC,eAAe;wBACb,eAAe,CAAC,QAAQ;6BACvB,aAAa,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;iBAClE;gBACD,KAAK,GAAG,eAAe,GAAG,eAAe,CAAC,YAAY,GAAG,oBAAoB,CAAC,aAAa,CAAC;aAC7F;;YAGD,MAAM,mBAAmB,GAAW,8BAA8B,CAChE,aAAa,EACb,eAAe,CAChB,CAAC;YACF,UAAU,CAAC,SAAS,CAAC,eAAe,EAAE,KAAK,EAAE,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;SACtF;KACF;SAAM;QACL,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC5B,KAAK,GAAG,EAAE,CAAC;SACZ;QAED,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,MAAM,cAAc,GAAY,eAAmC,CAAC,IAAI,CAAC,eAAgB,CACvF,YAAY,CACb,CAAC;YACF,MAAM,YAAY,GAAkB,aAAa,CAAC,YAAY,CAAC,CAAC;YAChE,MAAM,aAAa,GAAQ,0CAA0C,CACnE,aAAa,EACb,kBAAkB,EAClB,YAAY,EACZ,cAAc,EACd,UAAU,CACX,CAAC;;YAEF,MAAM,kBAAkB,GAAW,8BAA8B,CAC/D,YAAY,EACZ,cAAc,CACf,CAAC;YACF,UAAU,CAAC,SAAS,CAAC,cAAc,EAAE,aAAa,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YAC3F,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;gBACzD,IAAI,CAAC,KAAK,EAAE;oBACV,KAAK,GAAG,EAAE,CAAC;iBACZ;gBACD,KAAK,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;aACrC;SACF;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAOD,SAAS,4BAA4B,CACnC,MAAwC,EACxC,aAAuB;IAEvB,MAAM,MAAM,GAAyB,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;IAC9D,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACpC,MAAM,iBAAiB,GAAW,aAAa,CAAC,CAAC,CAAC,CAAC;;QAEnD,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,IAAI,iBAAiB,IAAI,MAAM,EAAE;YAC1E,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;SACpC;aAAM;YACL,MAAM;SACP;KACF;IACD,IAAI,CAAC,KAAK,aAAa,CAAC,MAAM,EAAE;QAC9B,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC;QAC9B,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;KAC7B;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,eAAe,CAC7B,SAAgC,EAChC,YAA2C;IAE3C,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;IAC9C,MAAM,UAAU,GAAG,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC;IAE3D,MAAM,oBAAoB,GAAG,CAC3B,GAA4B;QAI5B,OAAO,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,WAAW,EAAE;YAC7C,KAAK,EAAE,SAAS;SACjB,CAAC,CAAC;KACJ,CAAC;IAEF,IAAI,UAAU,EAAE;QACd,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACzB,OAAO,oBAAoB,iCACtB,aAAa,KAChB,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAC5B,kBAAkB,EAAE,SAAS,CAAC,kBAAkB,IAChD,CAAC;SACJ;QAED,MAAM,eAAe,GACnB,CAAC,QAAQ,KAAK,WAAW,IAAK,UAA8B,CAAC,IAAI,CAAC,eAAe,KAAK,EAAE,CAAC;QAC3F,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAC1D,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,CAAC,CAAC,cAAc,KAAK,EAAE,CAChD,CAAC;QACF,IAAI,QAAQ,KAAK,UAAU,IAAI,kBAAkB,EAAE;YACjD,MAAM,aAAa,GAAG,CAAC,IAAI,SAAS,CAAC,UAAU,IAAI,EAAE,CAAC,CAAyB,CAAC;YAEhF,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;gBAC9C,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE;oBACvC,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;iBAChD;aACF;YAED,IAAI,aAAa,EAAE;gBACjB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;oBAC5C,aAAa,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;iBACzC;aACF;YACD,oBAAoB,CAAC,aAAa,CAAC,CAAC;YACpC,OAAO,aAAa,CAAC;SACtB;QAED,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,YAAY,EAAE;YACzD,OAAO,oBAAoB,iCACtB,aAAa,GACb,SAAS,CAAC,UAAU,EACvB,CAAC;SACJ;KACF;IAED,IACE,UAAU;QACV,SAAS,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM;QACnCC,eAAqB,CAAC,SAAS,CAAC,UAAU,CAAC,EAC3C;;QAEA,OAAO,oBAAoB,iCACtB,aAAa,KAChB,IAAI,EAAE,SAAS,CAAC,UAAU,IAC1B,CAAC;KACJ;IAED,OAAO,oBAAoB,iCACtB,aAAa,GACb,SAAS,CAAC,UAAU,EACvB,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAA8B,EAC9B,OAAgB;IAEhB,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,EAAE;QAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACxC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;cACxB,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,IAAIC,OAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;cAChD,IAAIA,OAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;KAChC;IAED,IAAI,OAAO,EAAE;QACX,OAAO,GAAG,OAAO,WAAW,CAAC;KAC9B;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;;AC5iCD;AACA,AA0BA;;;;;;;;;AASA,SAAgB,kBAAkB,CAChC,IAAgB;IAKhB,OAAOC,8BAA6B,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC;;AC3CD;AACA;AAIA;;;AAGA,AAAO,MAAM,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAqBlD;;;;;;;AAOA,MAAa,wBAAwB;;;;;IAQnC,YAAY,uBAA+B,oBAAoB;QANvD,gBAAW,GAAiB,SAAS,CAAC;QAO5C,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;KAClD;IAED,cAAc,CAAC,WAAoC;QACjD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;KAChC;IAED,cAAc;QACZ,IACE,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAC7E;YACA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;SAC9B;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;CACF;;AC9DD;AACA;AAIA;;;;;AAKA,MAAa,oBAAoB;IAI/B,YACU,UAA2B,EAC3B,MAAyB,EACzB,uCAA+C,KAAK;QAFpD,eAAU,GAAV,UAAU,CAAiB;QAC3B,WAAM,GAAN,MAAM,CAAmB;QACzB,yCAAoC,GAApC,oCAAoC,CAAgB;QALtD,eAAU,GAAG,CAAC,CAAC;KAMnB;;;;;IAMG,OAAO;;QAEZ,QACE,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oCAAoC,EAC5F;KACH;;;;;;;IAQO,MAAM,QAAQ,CAAC,OAAwB;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACnE,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,OAAO,KAAK,IAAI,SAAS,CAAC;KAC3B;;;;;IAMM,OAAO,CAAC,OAAwB;QACrC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;SACvC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;CACF;;ACvDD;AACA,AAOA,MAAM,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC;AAClD,MAAM,4BAA4B,GAAG,OAAO,CAAC;AAE7C,MAAa,8BAA8B;;;;;;;;IAYzC,YACE,QAAgB,EAChB,QAAgB,EAChB,sBAA8B,4BAA4B;QAZ5D,wBAAmB,GAAW,4BAA4B,CAAC;QAczD,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACzF,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;SACrF;QACD,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACzF,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;SACrF;QACD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;KAChD;;;;;;;IAQD,WAAW,CAAC,WAA4B;QACtC,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxD,MAAM,kBAAkB,GAAG,GAAG,IAAI,CAAC,mBAAmB,IAAIC,YAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;QAC7F,IAAI,CAAC,WAAW,CAAC,OAAO;YAAE,WAAW,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;QAC3E,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;KACrC;CACF;;ACpDD;AACA,AAoBA;;;AAGA,MAAa,iBAAiB;;;;IAa5B,YAAY,OAAgC;QAC1C,IAAI,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClE,MAAM,IAAI,KAAK,CACb,0HAA0H,CAC3H,CAAC;SACH;QACD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;KAChC;;;;;;;IAQD,WAAW,CAAC,WAA4B;QACtC,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CAAC,uEAAuE,CAAC,CACnF,CAAC;SACH;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;gBACxB,WAAW,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;aACzC;YACD,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACtC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;aAChE;SACF;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACpB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC,CAAC;aAC/E;YACD,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACpC,WAAW,CAAC,GAAG,IAAI,GAAG,CAAC;aACxB;YACD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC9B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAClC,WAAW,CAAC,GAAG,IAAI,GAAG,CAAC;iBACxB;gBACD,WAAW,CAAC,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;aAClD;SACF;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;KACrC;CACF;;ACtFD;AACA,MAIa,gBAAiB,SAAQ,iBAAiB;;;;;;IAMrD,YAAY,QAAgB;QAC1B,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,CAAC,EAAE;YAC3D,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;SACrF;QACD,MAAM,OAAO,GAA4B;YACvC,QAAQ,EAAE;gBACR,aAAa,EAAE,QAAQ;aACxB;SACF,CAAC;QACF,KAAK,CAAC,OAAO,CAAC,CAAC;KAChB;CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}