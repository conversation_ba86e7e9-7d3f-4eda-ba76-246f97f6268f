{"name": "@azure/core-http", "sdk-type": "client", "author": "Microsoft Corporation", "version": "2.2.2", "description": "Isomorphic client Runtime for Typescript/node.js/browser javascript client libraries generated using AutoRest", "tags": ["isomorphic", "browser", "javascript", "node", "microsoft", "autorest", "clientruntime"], "engines": {"node": ">=12.0.0"}, "keywords": ["isomorphic", "browser", "javascript", "node", "microsoft", "autorest", "clientruntime", "azure", "cloud"], "main": "dist/index.js", "module": "./dist-esm/src/coreHttp.js", "types": "./types/latest/src/coreHttp.d.ts", "typesVersions": {"<3.6": {"types/latest/src/*": ["types/3.1/src/*"]}}, "files": ["dist/", "dist-esm/src/", "dom-shim.d.ts", "types/*/src/**/*.d.ts", "types/*/src/**/*.d.ts.map", "README.md", "LICENSE"], "browser": {"./dist-esm/src/policies/msRestUserAgentPolicy.js": "./dist-esm/src/policies/msRestUserAgentPolicy.browser.js", "./dist-esm/src/policies/disableResponseDecompressionPolicy.js": "./dist-esm/src/policies/disableResponseDecompressionPolicy.browser.js", "./dist-esm/src/policies/proxyPolicy.js": "./dist-esm/src/policies/proxyPolicy.browser.js", "./dist-esm/src/util/base64.js": "./dist-esm/src/util/base64.browser.js", "./dist-esm/src/util/xml.js": "./dist-esm/src/util/xml.browser.js", "./dist-esm/src/defaultHttpClient.js": "./dist-esm/src/defaultHttpClient.browser.js", "./dist-esm/src/util/inspect.js": "./dist-esm/src/util/inspect.browser.js", "./dist-esm/src/util/url.js": "./dist-esm/src/util/url.browser.js"}, "license": "MIT", "homepage": "https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/core/core-http/README.md", "repository": "github:Azure/azure-sdk-for-js", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "scripts": {"audit": "node ../../../common/scripts/rush-audit.js && rimraf node_modules package-lock.json && npm i --package-lock-only 2>&1 && npm audit", "build:samples": "echo Obsolete", "build:test": "tsc -p tsconfig.es.json && rollup -c 2>&1", "build:types": "downlevel-dts types/latest/ types/3.1/", "build": "npm run clean && tsc -p tsconfig.es.json && rollup -c 2>&1 && api-extractor run --local && npm run build:types", "check-format": "prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"*.{js,json}\"", "clean": "rimraf dist dist-* temp types *.tgz *.log", "execute:samples": "echo skipped", "extract-api": "tsc -p tsconfig.es.json && api-extractor run --local", "format": "prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "integration-test:browser": "echo skipped", "integration-test:node": "echo skipped", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "lint:fix": "eslint package.json api-extractor.json src test --ext .ts --fix --fix-type [problem,suggestion]", "lint": "eslint package.json api-extractor.json src test --ext .ts", "pack": "npm pack 2>&1", "test:browser": "npm run clean && npm run build:test && npm run unit-test:browser && npm run integration-test:browser", "test:node": "npm run clean && tsc -p . && npm run unit-test:node && npm run integration-test:node", "test": "npm run clean && tsc -p . && npm run unit-test:node && rollup -c 2>&1 && npm run unit-test:browser && npm run integration-test", "unit-test": "npm run unit-test:node && npm run unit-test:browser", "unit-test:browser": "karma start --single-run", "unit-test:node": "cross-env TS_NODE_FILES=true mocha -r esm -r ts-node/register --reporter ../../../common/tools/mocha-multi-reporter.js --timeout 500000 --full-trace --exclude \"test/**/*.browser.ts\" \"test/**/*.ts\"", "docs": "typedoc --excludePrivate --excludeNotExported --excludeExternals --stripInternal --mode file --out ./dist/docs ./src"}, "sideEffects": false, "nyc": {"extension": [".ts"], "exclude": ["coverage/**/*", "**/*.d.ts", "**/*.js"], "reporter": ["text", "html", "cobertura"], "all": true}, "//metadata": {"constantPaths": [{"path": "src/util/constants.ts", "prefix": "coreHttpVersion"}]}, "dependencies": {"@azure/abort-controller": "^1.0.0", "@azure/core-asynciterator-polyfill": "^1.0.0", "@azure/core-auth": "^1.3.0", "@azure/core-tracing": "1.0.0-preview.13", "@azure/logger": "^1.0.0", "@types/node-fetch": "^2.5.0", "@types/tunnel": "^0.0.3", "form-data": "^4.0.0", "node-fetch": "^2.6.0", "process": "^0.11.10", "tough-cookie": "^4.0.0", "tslib": "^2.2.0", "tunnel": "^0.0.6", "uuid": "^8.3.0", "xml2js": "^0.4.19"}, "devDependencies": {"@azure/eslint-plugin-azure-sdk": "^3.0.0", "@azure/dev-tool": "^1.0.0", "@azure/logger-js": "^1.0.2", "@microsoft/api-extractor": "^7.18.11", "@opentelemetry/api": "^1.0.1", "@types/chai": "^4.1.6", "@types/express": "^4.16.0", "@types/glob": "^7.1.1", "@types/mocha": "^7.0.2", "@types/node": "^12.0.0", "@types/sinon": "^9.0.4", "@types/tough-cookie": "^4.0.0", "@types/uuid": "^8.0.0", "@types/xml2js": "^0.4.3", "babel-runtime": "^6.26.0", "chai": "^4.2.0", "cross-env": "^7.0.2", "downlevel-dts": "~0.4.0", "eslint": "^7.15.0", "express": "^4.16.3", "fetch-mock": "^9.10.1", "glob": "^7.1.2", "karma": "^6.2.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.0.0", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^2.0.1", "karma-rollup-preprocessor": "^7.0.0", "karma-sourcemap-loader": "^0.3.8", "mocha": "^7.1.1", "mocha-junit-reporter": "^1.18.0", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "prettier": "^1.16.4", "puppeteer": "^10.2.0", "regenerator-runtime": "^0.13.3", "rimraf": "^3.0.0", "rollup": "^1.16.3", "shx": "^0.3.2", "sinon": "^9.0.2", "ts-node": "^10.0.0", "typescript": "~4.2.0", "uglify-js": "^3.4.9", "xhr-mock": "^2.4.1", "typedoc": "0.15.2"}}