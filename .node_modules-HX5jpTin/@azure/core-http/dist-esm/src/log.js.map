{"version": 3, "file": "log.js", "sourceRoot": "", "sources": ["../../src/log.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,OAAO,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAC;AACnD,MAAM,CAAC,MAAM,MAAM,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\nimport { createClientLogger } from \"@azure/logger\";\nexport const logger = createClientLogger(\"core-http\");\n"]}