{"version": 3, "file": "browserFetchHttpClient.js", "sourceRoot": "", "sources": ["../../src/browserFetchHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,eAAe,EAIhB,MAAM,mBAAmB,CAAC;AAI3B,MAAM,OAAO,sBAAuB,SAAQ,eAAe;IACzD,cAAc,CAAC,YAA6B;QAC1C,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED,cAAc,CAAC,kBAAyC;QACtD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,6EAA6E;IAC7E,KAAK,CAAC,KAAwB,EAAE,IAAwB;QACtD,OAAO,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC5B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  FetchHttpClient,\n  CommonRequestInfo,\n  CommonResponse,\n  CommonRequestInit\n} from \"./fetchHttpClient\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { WebResourceLike } from \"./webResource\";\n\nexport class BrowserFetchHttpClient extends FetchHttpClient {\n  prepareRequest(_httpRequest: WebResourceLike): Promise<Partial<RequestInit>> {\n    return Promise.resolve({});\n  }\n\n  processRequest(_operationResponse: HttpOperationResponse): Promise<void> {\n    return Promise.resolve();\n  }\n\n  // eslint-disable-next-line @azure/azure-sdk/ts-apisurface-standardized-verbs\n  fetch(input: CommonRequestInfo, init?: CommonRequestInit): Promise<CommonResponse> {\n    return fetch(input, init);\n  }\n}\n"]}