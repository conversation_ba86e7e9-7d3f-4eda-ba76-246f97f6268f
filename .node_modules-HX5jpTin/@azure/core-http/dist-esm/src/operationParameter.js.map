{"version": 3, "file": "operationParameter.js", "sourceRoot": "", "sources": ["../../src/operationParameter.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAkDlC;;;;GAIG;AACH,MAAM,UAAU,0BAA0B,CAAC,SAA6B;IACtE,OAAO,8BAA8B,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AACnF,CAAC;AAED,MAAM,UAAU,8BAA8B,CAC5C,aAA4B,EAC5B,MAAc;IAEd,IAAI,MAAc,CAAC;IACnB,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,MAAM,GAAG,aAAa,CAAC;KACxB;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QACvC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAClC;SAAM;QACL,MAAM,GAAG,MAAM,CAAC,cAAe,CAAC;KACjC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { QueryCollectionFormat } from \"./queryCollectionFormat\";\nimport { Mapper } from \"./serializer\";\n\nexport type ParameterPath = string | string[] | { [propertyName: string]: ParameterPath };\n\n/**\n * A common interface that all Operation parameter's extend.\n */\nexport interface OperationParameter {\n  /**\n   * The path to this parameter's value in OperationArguments or the object that contains paths for\n   * each property's value in OperationArguments.\n   */\n  parameterPath: ParameterPath;\n\n  /**\n   * The mapper that defines how to validate and serialize this parameter's value.\n   */\n  mapper: Mapper;\n}\n\n/**\n * A parameter for an operation that will be substituted into the operation's request URL.\n */\nexport interface OperationURLParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the URL parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n}\n\n/**\n * A parameter for an operation that will be added as a query parameter to the operation's HTTP\n * request.\n */\nexport interface OperationQueryParameter extends OperationParameter {\n  /**\n   * Whether or not to skip encoding the query parameter's value before adding it to the URL.\n   */\n  skipEncoding?: boolean;\n\n  /**\n   * If this query parameter's value is a collection, what type of format should the value be\n   * converted to.\n   */\n  collectionFormat?: QueryCollectionFormat;\n}\n\n/**\n * Get the path to this parameter's value as a dotted string (a.b.c).\n * @param parameter - The parameter to get the path string for.\n * @returns The path to this parameter's value as a dotted string.\n */\nexport function getPathStringFromParameter(parameter: OperationParameter): string {\n  return getPathStringFromParameterPath(parameter.parameterPath, parameter.mapper);\n}\n\nexport function getPathStringFromParameterPath(\n  parameterPath: ParameterPath,\n  mapper: Mapper\n): string {\n  let result: string;\n  if (typeof parameterPath === \"string\") {\n    result = parameterPath;\n  } else if (Array.isArray(parameterPath)) {\n    result = parameterPath.join(\".\");\n  } else {\n    result = mapper.serializedName!;\n  }\n  return result;\n}\n"]}