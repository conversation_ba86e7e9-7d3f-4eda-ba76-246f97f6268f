{"version": 3, "file": "signingPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/signingPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AAEzB,MAAM,UAAU,aAAa,CAC3B,sBAAgD;IAEhD,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;QACxE,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,aAAc,SAAQ,iBAAiB;IAClD,YACE,UAAyB,EACzB,OAA6B,EACtB,sBAAgD;QAEvD,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFpB,2BAAsB,GAAtB,sBAAsB,CAA0B;IAGzD,CAAC;IAED,WAAW,CAAC,OAAwB;QAClC,OAAO,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;IAEM,WAAW,CAAC,OAAwB;QACzC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE,CACpD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAC1C,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { ServiceClientCredentials } from \"../credentials/serviceClientCredentials\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\n\nexport function signingPolicy(\n  authenticationProvider: ServiceClientCredentials\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new SigningPolicy(nextPolicy, options, authenticationProvider);\n    }\n  };\n}\n\nexport class SigningPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    public authenticationProvider: ServiceClientCredentials\n  ) {\n    super(nextPolicy, options);\n  }\n\n  signRequest(request: WebResourceLike): Promise<WebResourceLike> {\n    return this.authenticationProvider.signRequest(request);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this.signRequest(request).then((nextRequest) =>\n      this._nextPolicy.sendRequest(nextRequest)\n    );\n  }\n}\n"]}