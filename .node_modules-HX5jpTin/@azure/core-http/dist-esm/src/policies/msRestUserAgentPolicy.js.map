{"version": 3, "file": "msRestUserAgentPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/msRestUserAgentPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAEzB,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,MAAM,UAAU,sBAAsB;IACpC,OAAO,SAAS,CAAC,eAAe,CAAC,UAAU,CAAC;AAC9C,CAAC;AAED,MAAM,UAAU,uBAAuB;IACrC,MAAM,WAAW,GAAG;QAClB,GAAG,EAAE,MAAM;QACX,KAAK,EAAE,OAAO,CAAC,OAAO;KACvB,CAAC;IAEF,MAAM,MAAM,GAAG;QACb,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG;KACrD,CAAC;IAEF,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as os from \"os\";\nimport { TelemetryInfo } from \"./userAgentPolicy\";\nimport { Constants } from \"../util/constants\";\n\nexport function getDefaultUserAgentKey(): string {\n  return Constants.HeaderConstants.USER_AGENT;\n}\n\nexport function getPlatformSpecificData(): TelemetryInfo[] {\n  const runtimeInfo = {\n    key: \"Node\",\n    value: process.version\n  };\n\n  const osInfo = {\n    key: \"OS\",\n    value: `(${os.arch()}-${os.type()}-${os.release()})`\n  };\n\n  return [runtimeInfo, osInfo];\n}\n"]}