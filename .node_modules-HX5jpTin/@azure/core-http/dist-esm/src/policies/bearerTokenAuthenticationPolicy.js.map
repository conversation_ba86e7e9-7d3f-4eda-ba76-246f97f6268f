{"version": 3, "file": "bearerTokenAuthenticationPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/bearerTokenAuthenticationPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,iBAAiB,EAIlB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAG9C,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAiCtC,sDAAsD;AACtD,MAAM,CAAC,MAAM,sBAAsB,GAAuB;IACxD,uBAAuB,EAAE,IAAI;IAC7B,iBAAiB,EAAE,IAAI;IACvB,iBAAiB,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,oCAAoC;CACtE,CAAC;AAEF;;;;;;;;;;;;GAYG;AACH,KAAK,UAAU,YAAY,CACzB,cAAiD,EACjD,iBAAyB,EACzB,WAAmB;IAEnB,4EAA4E;IAC5E,eAAe;IACf,KAAK,UAAU,iBAAiB;QAC9B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,EAAE;YAC5B,IAAI;gBACF,OAAO,MAAM,cAAc,EAAE,CAAC;aAC/B;YAAC,WAAM;gBACN,OAAO,IAAI,CAAC;aACb;SACF;aAAM;YACL,MAAM,UAAU,GAAG,MAAM,cAAc,EAAE,CAAC;YAE1C,6CAA6C;YAC7C,IAAI,UAAU,KAAK,IAAI,EAAE;gBACvB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;aACpD;YAED,OAAO,UAAU,CAAC;SACnB;IACH,CAAC;IAED,IAAI,KAAK,GAAuB,MAAM,iBAAiB,EAAE,CAAC;IAE1D,OAAO,KAAK,KAAK,IAAI,EAAE;QACrB,MAAM,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAE/B,KAAK,GAAG,MAAM,iBAAiB,EAAE,CAAC;KACnC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,SAAS,iBAAiB,CACxB,UAA2B,EAC3B,MAAyB,EACzB,kBAAgD;IAEhD,IAAI,aAAa,GAAgC,IAAI,CAAC;IACtD,IAAI,KAAK,GAAuB,IAAI,CAAC;IAErC,MAAM,OAAO,mCACR,sBAAsB,GACtB,kBAAkB,CACtB,CAAC;IAEF;;;OAGG;IACH,MAAM,MAAM,GAAG;QACb;;WAEG;QACH,IAAI,YAAY;YACd,OAAO,aAAa,KAAK,IAAI,CAAC;QAChC,CAAC;QACD;;;WAGG;QACH,IAAI,aAAa;;YACf,OAAO,CACL,CAAC,MAAM,CAAC,YAAY;gBACpB,CAAC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,kBAAkB,mCAAI,CAAC,CAAC,GAAG,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAC1E,CAAC;QACJ,CAAC;QACD;;;WAGG;QACH,IAAI,WAAW;YACb,OAAO,CACL,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC,GAAG,EAAE,CAC1F,CAAC;QACJ,CAAC;KACF,CAAC;IAEF;;;OAGG;IACH,SAAS,OAAO,CAAC,eAAgC;;QAC/C,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YACxB,yDAAyD;YACzD,MAAM,iBAAiB,GAAG,GAAgC,EAAE,CAC1D,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YAE/C,wEAAwE;YACxE,6CAA6C;YAC7C,aAAa,GAAG,YAAY,CAC1B,iBAAiB,EACjB,OAAO,CAAC,iBAAiB;YACzB,+DAA+D;YAC/D,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,kBAAkB,mCAAI,IAAI,CAAC,GAAG,EAAE,CACxC;iBACE,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,MAAM,CAAC;gBACf,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;gBAChB,sEAAsE;gBACtE,qEAAqE;gBACrE,mBAAmB;gBACnB,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,IAAI,CAAC;gBACb,MAAM,MAAM,CAAC;YACf,CAAC,CAAC,CAAC;SACN;QAED,OAAO,aAAqC,CAAC;IAC/C,CAAC;IAED,OAAO,KAAK,EAAE,YAA6B,EAAwB,EAAE;QACnE,EAAE;QACF,gBAAgB;QAChB,+DAA+D;QAC/D,6CAA6C;QAC7C,+DAA+D;QAC/D,yCAAyC;QACzC,6DAA6D;QAC7D,YAAY;QACZ,EAAE;QAEF,IAAI,MAAM,CAAC,WAAW;YAAE,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC;QAErD,IAAI,MAAM,CAAC,aAAa,EAAE;YACxB,OAAO,CAAC,YAAY,CAAC,CAAC;SACvB;QAED,OAAO,KAAoB,CAAC;IAC9B,CAAC,CAAC;AACJ,CAAC;AAED,aAAa;AAEb;;;;;;GAMG;AACH,MAAM,UAAU,+BAA+B,CAC7C,UAA2B,EAC3B,MAAyB;IAEzB,wFAAwF;IACxF,MAAM,QAAQ,GAAG,iBAAiB,CAAC,UAAU,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;IAEvE,MAAM,+BAAgC,SAAQ,iBAAiB;QAC7D,YAAmB,UAAyB,EAAE,OAA6B;YACzE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC7B,CAAC;QAEM,KAAK,CAAC,WAAW,CAAC,WAA4B;YACnD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBACzD,MAAM,IAAI,KAAK,CACb,sFAAsF,CACvF,CAAC;aACH;YAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC;gBAC/B,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,cAAc,EAAE;oBACd,cAAc,EAAE,WAAW,CAAC,cAAc;iBAC3C;aACF,CAAC,CAAC;YACH,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;YACpF,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QACnD,CAAC;KACF;IAED,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,+BAA+B,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAClE,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { TokenCredential, GetTokenOptions, AccessToken } from \"@azure/core-auth\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyOptions,\n  RequestPolicyFactory\n} from \"../policies/requestPolicy\";\nimport { Constants } from \"../util/constants\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport { delay } from \"../util/delay\";\n\n// #region Access Token Cycler\n\n/**\n * A function that gets a promise of an access token and allows providing\n * options.\n *\n * @param options - the options to pass to the underlying token provider\n */\ntype AccessTokenGetter = (options: GetTokenOptions) => Promise<AccessToken>;\n\ninterface TokenCyclerOptions {\n  /**\n   * The window of time before token expiration during which the token will be\n   * considered unusable due to risk of the token expiring before sending the\n   * request.\n   *\n   * This will only become meaningful if the refresh fails for over\n   * (refreshWindow - forcedRefreshWindow) milliseconds.\n   */\n  forcedRefreshWindowInMs: number;\n  /**\n   * Interval in milliseconds to retry failed token refreshes.\n   */\n  retryIntervalInMs: number;\n  /**\n   * The window of time before token expiration during which\n   * we will attempt to refresh the token.\n   */\n  refreshWindowInMs: number;\n}\n\n// Default options for the cycler if none are provided\nexport const DEFAULT_CYCLER_OPTIONS: TokenCyclerOptions = {\n  forcedRefreshWindowInMs: 1000, // Force waiting for a refresh 1s before the token expires\n  retryIntervalInMs: 3000, // Allow refresh attempts every 3s\n  refreshWindowInMs: 1000 * 60 * 2 // Start refreshing 2m before expiry\n};\n\n/**\n * Converts an an unreliable access token getter (which may resolve with null)\n * into an AccessTokenGetter by retrying the unreliable getter in a regular\n * interval.\n *\n * @param getAccessToken - a function that produces a promise of an access\n * token that may fail by returning null\n * @param retryIntervalInMs - the time (in milliseconds) to wait between retry\n * attempts\n * @param timeoutInMs - the timestamp after which the refresh attempt will fail,\n * throwing an exception\n * @returns - a promise that, if it resolves, will resolve with an access token\n */\nasync function beginRefresh(\n  getAccessToken: () => Promise<AccessToken | null>,\n  retryIntervalInMs: number,\n  timeoutInMs: number\n): Promise<AccessToken> {\n  // This wrapper handles exceptions gracefully as long as we haven't exceeded\n  // the timeout.\n  async function tryGetAccessToken(): Promise<AccessToken | null> {\n    if (Date.now() < timeoutInMs) {\n      try {\n        return await getAccessToken();\n      } catch {\n        return null;\n      }\n    } else {\n      const finalToken = await getAccessToken();\n\n      // Timeout is up, so throw if it's still null\n      if (finalToken === null) {\n        throw new Error(\"Failed to refresh access token.\");\n      }\n\n      return finalToken;\n    }\n  }\n\n  let token: AccessToken | null = await tryGetAccessToken();\n\n  while (token === null) {\n    await delay(retryIntervalInMs);\n\n    token = await tryGetAccessToken();\n  }\n\n  return token;\n}\n\n/**\n * Creates a token cycler from a credential, scopes, and optional settings.\n *\n * A token cycler represents a way to reliably retrieve a valid access token\n * from a TokenCredential. It will handle initializing the token, refreshing it\n * when it nears expiration, and synchronizes refresh attempts to avoid\n * concurrency hazards.\n *\n * @param credential - the underlying TokenCredential that provides the access\n * token\n * @param scopes - the scopes to request authorization for\n * @param tokenCyclerOptions - optionally override default settings for the cycler\n *\n * @returns - a function that reliably produces a valid access token\n */\nfunction createTokenCycler(\n  credential: TokenCredential,\n  scopes: string | string[],\n  tokenCyclerOptions?: Partial<TokenCyclerOptions>\n): AccessTokenGetter {\n  let refreshWorker: Promise<AccessToken> | null = null;\n  let token: AccessToken | null = null;\n\n  const options = {\n    ...DEFAULT_CYCLER_OPTIONS,\n    ...tokenCyclerOptions\n  };\n\n  /**\n   * This little holder defines several predicates that we use to construct\n   * the rules of refreshing the token.\n   */\n  const cycler = {\n    /**\n     * Produces true if a refresh job is currently in progress.\n     */\n    get isRefreshing(): boolean {\n      return refreshWorker !== null;\n    },\n    /**\n     * Produces true if the cycler SHOULD refresh (we are within the refresh\n     * window and not already refreshing)\n     */\n    get shouldRefresh(): boolean {\n      return (\n        !cycler.isRefreshing &&\n        (token?.expiresOnTimestamp ?? 0) - options.refreshWindowInMs < Date.now()\n      );\n    },\n    /**\n     * Produces true if the cycler MUST refresh (null or nearly-expired\n     * token).\n     */\n    get mustRefresh(): boolean {\n      return (\n        token === null || token.expiresOnTimestamp - options.forcedRefreshWindowInMs < Date.now()\n      );\n    }\n  };\n\n  /**\n   * Starts a refresh job or returns the existing job if one is already\n   * running.\n   */\n  function refresh(getTokenOptions: GetTokenOptions): Promise<AccessToken> {\n    if (!cycler.isRefreshing) {\n      // We bind `scopes` here to avoid passing it around a lot\n      const tryGetAccessToken = (): Promise<AccessToken | null> =>\n        credential.getToken(scopes, getTokenOptions);\n\n      // Take advantage of promise chaining to insert an assignment to `token`\n      // before the refresh can be considered done.\n      refreshWorker = beginRefresh(\n        tryGetAccessToken,\n        options.retryIntervalInMs,\n        // If we don't have a token, then we should timeout immediately\n        token?.expiresOnTimestamp ?? Date.now()\n      )\n        .then((_token) => {\n          refreshWorker = null;\n          token = _token;\n          return token;\n        })\n        .catch((reason) => {\n          // We also should reset the refresher if we enter a failed state.  All\n          // existing awaiters will throw, but subsequent requests will start a\n          // new retry chain.\n          refreshWorker = null;\n          token = null;\n          throw reason;\n        });\n    }\n\n    return refreshWorker as Promise<AccessToken>;\n  }\n\n  return async (tokenOptions: GetTokenOptions): Promise<AccessToken> => {\n    //\n    // Simple rules:\n    // - If we MUST refresh, then return the refresh task, blocking\n    //   the pipeline until a token is available.\n    // - If we SHOULD refresh, then run refresh but don't return it\n    //   (we can still use the cached token).\n    // - Return the token, since it's fine if we didn't return in\n    //   step 1.\n    //\n\n    if (cycler.mustRefresh) return refresh(tokenOptions);\n\n    if (cycler.shouldRefresh) {\n      refresh(tokenOptions);\n    }\n\n    return token as AccessToken;\n  };\n}\n\n// #endregion\n\n/**\n * Creates a new factory for a RequestPolicy that applies a bearer token to\n * the requests' `Authorization` headers.\n *\n * @param credential - The TokenCredential implementation that can supply the bearer token.\n * @param scopes - The scopes for which the bearer token applies.\n */\nexport function bearerTokenAuthenticationPolicy(\n  credential: TokenCredential,\n  scopes: string | string[]\n): RequestPolicyFactory {\n  // This simple function encapsulates the entire process of reliably retrieving the token\n  const getToken = createTokenCycler(credential, scopes /* , options */);\n\n  class BearerTokenAuthenticationPolicy extends BaseRequestPolicy {\n    public constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n      super(nextPolicy, options);\n    }\n\n    public async sendRequest(webResource: WebResourceLike): Promise<HttpOperationResponse> {\n      if (!webResource.url.toLowerCase().startsWith(\"https://\")) {\n        throw new Error(\n          \"Bearer token authentication is not permitted for non-TLS protected (non-https) URLs.\"\n        );\n      }\n\n      const { token } = await getToken({\n        abortSignal: webResource.abortSignal,\n        tracingOptions: {\n          tracingContext: webResource.tracingContext\n        }\n      });\n      webResource.headers.set(Constants.HeaderConstants.AUTHORIZATION, `Bearer ${token}`);\n      return this._nextPolicy.sendRequest(webResource);\n    }\n  }\n\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new BearerTokenAuthenticationPolicy(nextPolicy, options);\n    }\n  };\n}\n"]}