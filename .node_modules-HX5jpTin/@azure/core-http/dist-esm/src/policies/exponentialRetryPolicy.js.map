{"version": 3, "file": "exponentialRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/exponentialRetryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAGL,iCAAiC,EACjC,0BAA0B,EAC1B,6BAA6B,EAC7B,QAAQ,EACR,eAAe,EACf,WAAW,EACZ,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAChC,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,MAAM,UAAU,sBAAsB,CACpC,UAAmB,EACnB,aAAsB,EACtB,gBAAyB;IAEzB,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,sBAAsB,CAC/B,UAAU,EACV,OAAO,EACP,UAAU,EACV,aAAa,EACb,gBAAgB,CACjB,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,SAEX;AAFD,WAAY,SAAS;IACnB,uDAAW,CAAA;AACb,CAAC,EAFW,SAAS,KAAT,SAAS,QAEpB;AA8BD,MAAM,CAAC,MAAM,mBAAmB,GAAiB;IAC/C,UAAU,EAAE,0BAA0B;IACtC,cAAc,EAAE,6BAA6B;IAC7C,iBAAiB,EAAE,iCAAiC;CACrD,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,iBAAiB;IAc3D;;;;;;;OAOG;IACH,YACE,UAAyB,EACzB,OAA6B,EAC7B,UAAmB,EACnB,aAAsB,EACtB,gBAAyB;QAEzB,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,0BAA0B,CAAC;QACjF,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,6BAA6B,CAAC;QAC7F,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;YAChD,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,iCAAiC,CAAC;IACxC,CAAC;IAEM,WAAW,CAAC,OAAwB;QACzC,OAAO,IAAI,CAAC,WAAW;aACpB,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;aAC5B,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;aAClD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IAC9E,CAAC;CACF;AAED,KAAK,UAAU,KAAK,CAClB,MAA8B,EAC9B,OAAwB,EACxB,QAAgC,EAChC,SAAqB,EACrB,YAAyB;IAEzB,SAAS,iBAAiB,CAAC,aAAqC;QAC9D,MAAM,UAAU,GAAG,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,CAAC;QACzC,IAAI,UAAU,KAAK,GAAG,KAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA,EAAE;YACtF,OAAO,KAAK,CAAC;SACd;QAED,IACE,UAAU,KAAK,SAAS;YACxB,CAAC,UAAU,GAAG,GAAG,IAAI,UAAU,KAAK,GAAG,CAAC;YACxC,UAAU,KAAK,GAAG;YAClB,UAAU,KAAK,GAAG,EAClB;YACA,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,GAAG,eAAe,CACzB;QACE,aAAa,EAAE,MAAM,CAAC,aAAa;QACnC,gBAAgB,EAAE,CAAC;QACnB,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;KAC1C,EACD,SAAS,EACT,YAAY,CACb,CAAC;IAEF,MAAM,SAAS,GAAwB,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC;IAC1F,IAAI,CAAC,SAAS,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE;QACxF,MAAM,CAAC,IAAI,CAAC,uBAAuB,SAAS,CAAC,aAAa,EAAE,CAAC,CAAC;QAC9D,IAAI;YACF,MAAM,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;SAC/C;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;SACzD;KACF;SAAM,IAAI,SAAS,IAAI,YAAY,IAAI,CAAC,QAAQ,EAAE;QACjD,qFAAqF;QACrF,MAAM,GAAG,GACP,SAAS,CAAC,KAAK;YACf,IAAI,SAAS,CACX,6BAA6B,EAC7B,SAAS,CAAC,kBAAkB,EAC5B,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAC3B,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAC5B,QAAQ,CACT,CAAC;QACJ,MAAM,GAAG,CAAC;KACX;SAAM;QACL,OAAO,QAAQ,CAAC;KACjB;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\nimport {\n  RetryData,\n  RetryError,\n  DEFAULT_CLIENT_MAX_RETRY_INTERVAL,\n  DEFAULT_CLIENT_RETRY_COUNT,\n  DEFAULT_CLIENT_RETRY_INTERVAL,\n  isNumber,\n  updateRetryData,\n  shouldRetry\n} from \"../util/exponentialBackoffStrategy\";\nimport { RestError } from \"../restError\";\nimport { logger } from \"../log\";\nimport { Constants } from \"../util/constants\";\nimport { delay } from \"../util/delay\";\n\nexport function exponentialRetryPolicy(\n  retryCount?: number,\n  retryInterval?: number,\n  maxRetryInterval?: number\n): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new ExponentialRetryPolicy(\n        nextPolicy,\n        options,\n        retryCount,\n        retryInterval,\n        maxRetryInterval\n      );\n    }\n  };\n}\n\n/**\n * Describes the Retry Mode type. Currently supporting only Exponential.\n */\nexport enum RetryMode {\n  Exponential\n}\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface RetryOptions {\n  /**\n   * The maximum number of retry attempts.  Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 30000\n   * (30 seconds). The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 90000 (90 seconds).\n   */\n  maxRetryDelayInMs?: number;\n\n  /**\n   * Currently supporting only Exponential mode.\n   */\n  mode?: RetryMode;\n}\n\nexport const DefaultRetryOptions: RetryOptions = {\n  maxRetries: DEFAULT_CLIENT_RETRY_COUNT,\n  retryDelayInMs: DEFAULT_CLIENT_RETRY_INTERVAL,\n  maxRetryDelayInMs: DEFAULT_CLIENT_MAX_RETRY_INTERVAL\n};\n\n/**\n * Instantiates a new \"ExponentialRetryPolicyFilter\" instance.\n */\nexport class ExponentialRetryPolicy extends BaseRequestPolicy {\n  /**\n   * The client retry count.\n   */\n  retryCount: number;\n  /**\n   * The client retry interval in milliseconds.\n   */\n  retryInterval: number;\n  /**\n   * The maximum retry interval in milliseconds.\n   */\n  maxRetryInterval: number;\n\n  /**\n   * @param nextPolicy - The next RequestPolicy in the pipeline chain.\n   * @param options - The options for this RequestPolicy.\n   * @param retryCount - The client retry count.\n   * @param retryInterval - The client retry interval, in milliseconds.\n   * @param minRetryInterval - The minimum retry interval, in milliseconds.\n   * @param maxRetryInterval - The maximum retry interval, in milliseconds.\n   */\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    retryCount?: number,\n    retryInterval?: number,\n    maxRetryInterval?: number\n  ) {\n    super(nextPolicy, options);\n    this.retryCount = isNumber(retryCount) ? retryCount : DEFAULT_CLIENT_RETRY_COUNT;\n    this.retryInterval = isNumber(retryInterval) ? retryInterval : DEFAULT_CLIENT_RETRY_INTERVAL;\n    this.maxRetryInterval = isNumber(maxRetryInterval)\n      ? maxRetryInterval\n      : DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request.clone())\n      .then((response) => retry(this, request, response))\n      .catch((error) => retry(this, request, error.response, undefined, error));\n  }\n}\n\nasync function retry(\n  policy: ExponentialRetryPolicy,\n  request: WebResourceLike,\n  response?: HttpOperationResponse,\n  retryData?: RetryData,\n  requestError?: RetryError\n): Promise<HttpOperationResponse> {\n  function shouldPolicyRetry(responseParam?: HttpOperationResponse): boolean {\n    const statusCode = responseParam?.status;\n    if (statusCode === 503 && response?.headers.get(Constants.HeaderConstants.RETRY_AFTER)) {\n      return false;\n    }\n\n    if (\n      statusCode === undefined ||\n      (statusCode < 500 && statusCode !== 408) ||\n      statusCode === 501 ||\n      statusCode === 505\n    ) {\n      return false;\n    }\n    return true;\n  }\n\n  retryData = updateRetryData(\n    {\n      retryInterval: policy.retryInterval,\n      minRetryInterval: 0,\n      maxRetryInterval: policy.maxRetryInterval\n    },\n    retryData,\n    requestError\n  );\n\n  const isAborted: boolean | undefined = request.abortSignal && request.abortSignal.aborted;\n  if (!isAborted && shouldRetry(policy.retryCount, shouldPolicyRetry, retryData, response)) {\n    logger.info(`Retrying request in ${retryData.retryInterval}`);\n    try {\n      await delay(retryData.retryInterval);\n      const res = await policy._nextPolicy.sendRequest(request.clone());\n      return retry(policy, request, res, retryData);\n    } catch (err) {\n      return retry(policy, request, response, retryData, err);\n    }\n  } else if (isAborted || requestError || !response) {\n    // If the operation failed in the end, return all errors instead of just the last one\n    const err =\n      retryData.error ||\n      new RestError(\n        \"Failed to send the request.\",\n        RestError.REQUEST_SEND_ERROR,\n        response && response.status,\n        response && response.request,\n        response\n      );\n    throw err;\n  } else {\n    return response;\n  }\n}\n"]}