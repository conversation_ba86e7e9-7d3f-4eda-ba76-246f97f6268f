{"version": 3, "file": "rpRegistrationPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/rpRegistrationPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,OAAO,KAAK,KAAK,MAAM,eAAe,CAAC;AAEvC,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AAEzB,MAAM,UAAU,oBAAoB,CAAC,YAAY,GAAG,EAAE;IACpD,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,oBAAoB,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QACrE,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IACzD,YACE,UAAyB,EACzB,OAA6B,EACpB,gBAAgB,EAAE;QAE3B,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFlB,kBAAa,GAAb,aAAa,CAAK;IAG7B,CAAC;IAEM,WAAW,CAAC,OAAwB;QACzC,OAAO,IAAI,CAAC,WAAW;aACpB,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;aAC5B,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;IACnE,CAAC;CACF;AAED,SAAS,gBAAgB,CACvB,MAA4B,EAC5B,OAAwB,EACxB,QAA+B;IAE/B,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;QAC3B,MAAM,MAAM,GAAG,yBAAyB,CAAC,QAAQ,CAAC,UAAoB,CAAC,CAAC;QACxE,IAAI,MAAM,EAAE;YACV,MAAM,SAAS,GAAG,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACtD,OAAO,CACL,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;gBAC5C,wFAAwF;gBACxF,kFAAkF;gBAClF,uFAAuF;iBACtF,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;iBAClB,IAAI,CAAC,CAAC,kBAAkB,EAAE,EAAE;gBAC3B,IAAI,kBAAkB,EAAE;oBACtB,2EAA2E;oBAC3E,0EAA0E;oBAC1E,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;oBACpE,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;iBACxD;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC,CACL,CAAC;SACH;KACF;IAED,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,CAAC;AAED;;;;;GAKG;AACH,SAAS,oBAAoB,CAC3B,eAAgC,EAChC,WAAW,GAAG,KAAK;IAEnB,MAAM,UAAU,GAAoB,eAAe,CAAC,KAAK,EAAE,CAAC;IAC5D,IAAI,WAAW,EAAE;QACf,UAAU,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC;KACtC;IAED,wEAAwE;IACxE,iDAAiD;IACjD,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;IAEvE,uCAAuC;IACvC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAC;IAE1E,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;;GAKG;AACH,SAAS,yBAAyB,CAAC,IAAY;IAC7C,IAAI,MAAM,EAAE,YAAY,CAAC;IACzB,IAAI,IAAI,EAAE;QACR,IAAI;YACF,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACjC;QAAC,OAAO,GAAG,EAAE;YACZ,cAAc;SACf;QACD,IACE,YAAY;YACZ,YAAY,CAAC,KAAK;YAClB,YAAY,CAAC,KAAK,CAAC,OAAO;YAC1B,YAAY,CAAC,KAAK,CAAC,IAAI;YACvB,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,iCAAiC,EAC7D;YACA,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC/D,IAAI,QAAQ,EAAE;gBACZ,MAAM,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;aACzB;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,SAAS,sBAAsB,CAAC,GAAW;IACzC,IAAI,MAAM,CAAC;IACX,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;IAChE,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;QAC3B,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;KACtB;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,yDAAyD,GAAG,GAAG,CAAC,CAAC;KAClF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,KAAK,UAAU,UAAU,CACvB,MAA4B,EAC5B,SAAiB,EACjB,QAAgB,EAChB,eAAgC;IAEhC,MAAM,OAAO,GAAG,GAAG,SAAS,aAAa,QAAQ,kCAAkC,CAAC;IACpF,MAAM,MAAM,GAAG,GAAG,SAAS,aAAa,QAAQ,yBAAyB,CAAC;IAC1E,MAAM,UAAU,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;IACzD,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;IAC3B,UAAU,CAAC,GAAG,GAAG,OAAO,CAAC;IAEzB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAClE,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,2CAA2C,CAAC,CAAC;KAC7F;IACD,OAAO,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;AAChE,CAAC;AAED;;;;;;;;GAQG;AACH,KAAK,UAAU,qBAAqB,CAClC,MAA4B,EAC5B,GAAW,EACX,eAAgC;IAEhC,MAAM,UAAU,GAAQ,oBAAoB,CAAC,eAAe,CAAC,CAAC;IAC9D,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IACrB,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;IAE1B,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAC7D,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC;IAC3B,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,iBAAiB,KAAK,YAAY,EAAE;QACrF,OAAO,IAAI,CAAC;KACb;SAAM;QACL,MAAM,KAAK,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;QACzC,OAAO,qBAAqB,CAAC,MAAM,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;KAC5D;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { delay } from \"../util/delay\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport * as utils from \"../util/utils\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\n\nexport function rpRegistrationPolicy(retryTimeout = 30): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new RPRegistrationPolicy(nextPolicy, options, retryTimeout);\n    }\n  };\n}\n\nexport class RPRegistrationPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    readonly _retryTimeout = 30\n  ) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    return this._nextPolicy\n      .sendRequest(request.clone())\n      .then((response) => registerIfNeeded(this, request, response));\n  }\n}\n\nfunction registerIfNeeded(\n  policy: RPRegistrationPolicy,\n  request: WebResourceLike,\n  response: HttpOperationResponse\n): Promise<HttpOperationResponse> {\n  if (response.status === 409) {\n    const rpName = checkRPNotRegisteredError(response.bodyAsText as string);\n    if (rpName) {\n      const urlPrefix = extractSubscriptionUrl(request.url);\n      return (\n        registerRP(policy, urlPrefix, rpName, request)\n          // Autoregistration of ${provider} failed for some reason. We will not return this error\n          // instead will return the initial response with 409 status code back to the user.\n          // do nothing here as we are returning the original response at the end of this method.\n          .catch(() => false)\n          .then((registrationStatus) => {\n            if (registrationStatus) {\n              // Retry the original request. We have to change the x-ms-client-request-id\n              // otherwise Azure endpoint will return the initial 409 (cached) response.\n              request.headers.set(\"x-ms-client-request-id\", utils.generateUuid());\n              return policy._nextPolicy.sendRequest(request.clone());\n            }\n            return response;\n          })\n      );\n    }\n  }\n\n  return Promise.resolve(response);\n}\n\n/**\n * Reuses the headers of the original request and url (if specified).\n * @param originalRequest - The original request\n * @param reuseUrlToo - Should the url from the original request be reused as well. Default false.\n * @returns A new request object with desired headers.\n */\nfunction getRequestEssentials(\n  originalRequest: WebResourceLike,\n  reuseUrlToo = false\n): WebResourceLike {\n  const reqOptions: WebResourceLike = originalRequest.clone();\n  if (reuseUrlToo) {\n    reqOptions.url = originalRequest.url;\n  }\n\n  // We have to change the x-ms-client-request-id otherwise Azure endpoint\n  // will return the initial 409 (cached) response.\n  reqOptions.headers.set(\"x-ms-client-request-id\", utils.generateUuid());\n\n  // Set content-type to application/json\n  reqOptions.headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n\n  return reqOptions;\n}\n\n/**\n * Validates the error code and message associated with 409 response status code. If it matches to that of\n * RP not registered then it returns the name of the RP else returns undefined.\n * @param body - The response body received after making the original request.\n * @returns The name of the RP if condition is satisfied else undefined.\n */\nfunction checkRPNotRegisteredError(body: string): string {\n  let result, responseBody;\n  if (body) {\n    try {\n      responseBody = JSON.parse(body);\n    } catch (err) {\n      // do nothing;\n    }\n    if (\n      responseBody &&\n      responseBody.error &&\n      responseBody.error.message &&\n      responseBody.error.code &&\n      responseBody.error.code === \"MissingSubscriptionRegistration\"\n    ) {\n      const matchRes = responseBody.error.message.match(/.*'(.*)'/i);\n      if (matchRes) {\n        result = matchRes.pop();\n      }\n    }\n  }\n  return result;\n}\n\n/**\n * Extracts the first part of the URL, just after subscription:\n * https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/\n * @param url - The original request url\n * @returns The url prefix as explained above.\n */\nfunction extractSubscriptionUrl(url: string): string {\n  let result;\n  const matchRes = url.match(/.*\\/subscriptions\\/[a-f0-9-]+\\//gi);\n  if (matchRes && matchRes[0]) {\n    result = matchRes[0];\n  } else {\n    throw new Error(`Unable to extract subscriptionId from the given url - ${url}.`);\n  }\n  return result;\n}\n\n/**\n * Registers the given provider.\n * @param policy - The RPRegistrationPolicy this function is being called against.\n * @param urlPrefix - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/\n * @param provider - The provider name to be registered.\n * @param originalRequest - The original request sent by the user that returned a 409 response\n * with a message that the provider is not registered.\n */\nasync function registerRP(\n  policy: RPRegistrationPolicy,\n  urlPrefix: string,\n  provider: string,\n  originalRequest: WebResourceLike\n): Promise<boolean> {\n  const postUrl = `${urlPrefix}providers/${provider}/register?api-version=2016-02-01`;\n  const getUrl = `${urlPrefix}providers/${provider}?api-version=2016-02-01`;\n  const reqOptions = getRequestEssentials(originalRequest);\n  reqOptions.method = \"POST\";\n  reqOptions.url = postUrl;\n\n  const response = await policy._nextPolicy.sendRequest(reqOptions);\n  if (response.status !== 200) {\n    throw new Error(`Autoregistration of ${provider} failed. Please try registering manually.`);\n  }\n  return getRegistrationStatus(policy, getUrl, originalRequest);\n}\n\n/**\n * Polls the registration status of the provider that was registered. Polling happens at an interval of 30 seconds.\n * Polling will happen till the registrationState property of the response body is \"Registered\".\n * @param policy - The RPRegistrationPolicy this function is being called against.\n * @param url - The request url for polling\n * @param originalRequest - The original request sent by the user that returned a 409 response\n * with a message that the provider is not registered.\n * @returns True if RP Registration is successful.\n */\nasync function getRegistrationStatus(\n  policy: RPRegistrationPolicy,\n  url: string,\n  originalRequest: WebResourceLike\n): Promise<boolean> {\n  const reqOptions: any = getRequestEssentials(originalRequest);\n  reqOptions.url = url;\n  reqOptions.method = \"GET\";\n\n  const res = await policy._nextPolicy.sendRequest(reqOptions);\n  const obj = res.parsedBody;\n  if (res.parsedBody && obj.registrationState && obj.registrationState === \"Registered\") {\n    return true;\n  } else {\n    await delay(policy._retryTimeout * 1000);\n    return getRegistrationStatus(policy, url, originalRequest);\n  }\n}\n"]}