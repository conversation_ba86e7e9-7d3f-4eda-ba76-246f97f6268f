{"version": 3, "file": "keepAlivePolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/keepAlivePolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AAgBzB,MAAM,CAAC,MAAM,uBAAuB,GAAqB;IACvD,MAAM,EAAE,IAAI;CACb,CAAC;AAEF,MAAM,UAAU,eAAe,CAAC,gBAAmC;IACjE,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,gBAAgB,IAAI,uBAAuB,CAAC,CAAC;QAC/F,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IACpD;;;;;;OAMG;IACH,YACE,UAAyB,EACzB,OAA6B,EACZ,gBAAkC;QAEnD,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAFV,qBAAgB,GAAhB,gBAAgB,CAAkB;IAGrD,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,WAAW,CAAC,OAAwB;QAC/C,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QACjD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyOptions,\n  RequestPolicyFactory\n} from \"./requestPolicy\";\nimport { WebResourceLike } from \"../webResource\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\n\n/**\n * Options for how HTTP connections should be maintained for future\n * requests.\n */\nexport interface KeepAliveOptions {\n  /*\n   * When true, connections will be kept alive for multiple requests.\n   * Defaults to true.\n   */\n  enable: boolean;\n}\n\nexport const DefaultKeepAliveOptions: KeepAliveOptions = {\n  enable: true\n};\n\nexport function keepAlivePolicy(keepAliveOptions?: KeepAliveOptions): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new KeepAlivePolicy(nextPolicy, options, keepAliveOptions || DefaultKeepAliveOptions);\n    }\n  };\n}\n\n/**\n * KeepAlivePolicy is a policy used to control keep alive settings for every request.\n */\nexport class KeepAlivePolicy extends BaseRequestPolicy {\n  /**\n   * Creates an instance of KeepAlivePolicy.\n   *\n   * @param nextPolicy -\n   * @param options -\n   * @param keepAliveOptions -\n   */\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    private readonly keepAliveOptions: KeepAliveOptions\n  ) {\n    super(nextPolicy, options);\n  }\n\n  /**\n   * Sends out request.\n   *\n   * @param request -\n   * @returns\n   */\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    request.keepAlive = this.keepAliveOptions.enable;\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n"]}