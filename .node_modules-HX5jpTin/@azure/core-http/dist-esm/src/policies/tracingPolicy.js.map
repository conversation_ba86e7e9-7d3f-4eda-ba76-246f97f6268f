{"version": 3, "file": "tracingPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/tracingPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,oBAAoB,EACpB,kBAAkB,EAClB,QAAQ,EACR,cAAc,EACd,kBAAkB,EAEnB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAIL,iBAAiB,EAClB,MAAM,iBAAiB,CAAC;AAGzB,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEhC,MAAM,UAAU,GAAG,kBAAkB,CAAC;IACpC,aAAa,EAAE,EAAE;IACjB,SAAS,EAAE,EAAE;CACd,CAAC,CAAC;AAMH,MAAM,UAAU,aAAa,CAAC,iBAAuC,EAAE;IACrE,OAAO;QACL,MAAM,CAAC,UAAyB,EAAE,OAA6B;YAC7D,OAAO,IAAI,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QAChE,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,aAAc,SAAQ,iBAAiB;IAGlD,YACE,UAAyB,EACzB,OAA6B,EAC7B,cAAoC;QAEpC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAAwB;QAC/C,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC9C;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC9C;QAED,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC7D,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACxC,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAChC,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAED,aAAa,CAAC,OAAwB;;QACpC,IAAI;YACF,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC;YAE5D,sHAAsH;YACtH,uFAAuF;YACvF,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE;gBAChC,cAAc,EAAE;oBACd,WAAW,kCACL,OAAe,CAAC,WAAW,KAC/B,IAAI,EAAE,QAAQ,CAAC,MAAM,GACtB;oBACD,cAAc,EAAE,OAAO,CAAC,cAAc;iBACvC;aACF,CAAC,CAAC;YAEH,wDAAwD;YACxD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;gBACvB,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,OAAO,SAAS,CAAC;aAClB;YAED,MAAM,oBAAoB,GAAG,MAAA,OAAO,CAAC,cAAc,0CAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;YAE1F,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;gBAC5C,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;aACzD;YAED,IAAI,CAAC,aAAa,CAAC;gBACjB,aAAa,EAAE,OAAO,CAAC,MAAM;gBAC7B,UAAU,EAAE,OAAO,CAAC,GAAG;gBACvB,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aACtD;YAED,cAAc;YACd,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,iBAAiB,IAAI,kBAAkB,CAAC,WAAW,CAAC,EAAE;gBACxD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;gBACtD,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;gBAChF,0FAA0F;gBAC1F,IAAI,UAAU,EAAE;oBACd,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;iBAC/C;aACF;YACD,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,OAAO,CAAC,qDAAqD,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACrF,OAAO,SAAS,CAAC;SAClB;IACH,CAAC;IAEO,eAAe,CAAC,IAAU,EAAE,GAAQ;QAC1C,IAAI;YACF,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,cAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC;YAEH,IAAI,GAAG,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;aACvD;YACD,IAAI,CAAC,GAAG,EAAE,CAAC;SACZ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,OAAO,CAAC,qDAAqD,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtF;IACH,CAAC;IAEO,kBAAkB,CAAC,IAAU,EAAE,QAA+B;QACpE,IAAI;YACF,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACjE,IAAI,gBAAgB,EAAE;gBACpB,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACzD;YACD,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,cAAc,CAAC,EAAE;aACxB,CAAC,CAAC;YACH,IAAI,CAAC,GAAG,EAAE,CAAC;SACZ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,OAAO,CAAC,qDAAqD,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtF;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  getTraceParentHeader,\n  createSpanFunction,\n  SpanKind,\n  SpanStatusCode,\n  isSpanContextValid,\n  Span\n} from \"@azure/core-tracing\";\nimport {\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptions,\n  BaseRequestPolicy\n} from \"./requestPolicy\";\nimport { WebResourceLike } from \"../webResource\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { URLBuilder } from \"../url\";\nimport { logger } from \"../log\";\n\nconst createSpan = createSpanFunction({\n  packagePrefix: \"\",\n  namespace: \"\"\n});\n\nexport interface TracingPolicyOptions {\n  userAgent?: string;\n}\n\nexport function tracingPolicy(tracingOptions: TracingPolicyOptions = {}): RequestPolicyFactory {\n  return {\n    create(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n      return new TracingPolicy(nextPolicy, options, tracingOptions);\n    }\n  };\n}\n\nexport class TracingPolicy extends BaseRequestPolicy {\n  private userAgent?: string;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    tracingOptions: TracingPolicyOptions\n  ) {\n    super(nextPolicy, options);\n    this.userAgent = tracingOptions.userAgent;\n  }\n\n  public async sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!request.tracingContext) {\n      return this._nextPolicy.sendRequest(request);\n    }\n\n    const span = this.tryCreateSpan(request);\n\n    if (!span) {\n      return this._nextPolicy.sendRequest(request);\n    }\n\n    try {\n      const response = await this._nextPolicy.sendRequest(request);\n      this.tryProcessResponse(span, response);\n      return response;\n    } catch (err) {\n      this.tryProcessError(span, err);\n      throw err;\n    }\n  }\n\n  tryCreateSpan(request: WebResourceLike): Span | undefined {\n    try {\n      const path = URLBuilder.parse(request.url).getPath() || \"/\";\n\n      // Passing spanOptions as part of tracingOptions to maintain compatibility @azure/core-tracing@preview.13 and earlier.\n      // We can pass this as a separate parameter once we upgrade to the latest core-tracing.\n      const { span } = createSpan(path, {\n        tracingOptions: {\n          spanOptions: {\n            ...(request as any).spanOptions,\n            kind: SpanKind.CLIENT\n          },\n          tracingContext: request.tracingContext\n        }\n      });\n\n      // If the span is not recording, don't do any more work.\n      if (!span.isRecording()) {\n        span.end();\n        return undefined;\n      }\n\n      const namespaceFromContext = request.tracingContext?.getValue(Symbol.for(\"az.namespace\"));\n\n      if (typeof namespaceFromContext === \"string\") {\n        span.setAttribute(\"az.namespace\", namespaceFromContext);\n      }\n\n      span.setAttributes({\n        \"http.method\": request.method,\n        \"http.url\": request.url,\n        requestId: request.requestId\n      });\n\n      if (this.userAgent) {\n        span.setAttribute(\"http.user_agent\", this.userAgent);\n      }\n\n      // set headers\n      const spanContext = span.spanContext();\n      const traceParentHeader = getTraceParentHeader(spanContext);\n      if (traceParentHeader && isSpanContextValid(spanContext)) {\n        request.headers.set(\"traceparent\", traceParentHeader);\n        const traceState = spanContext.traceState && spanContext.traceState.serialize();\n        // if tracestate is set, traceparent MUST be set, so only set tracestate after traceparent\n        if (traceState) {\n          request.headers.set(\"tracestate\", traceState);\n        }\n      }\n      return span;\n    } catch (error) {\n      logger.warning(`Skipping creating a tracing span due to an error: ${error.message}`);\n      return undefined;\n    }\n  }\n\n  private tryProcessError(span: Span, err: any): void {\n    try {\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message\n      });\n\n      if (err.statusCode) {\n        span.setAttribute(\"http.status_code\", err.statusCode);\n      }\n      span.end();\n    } catch (error) {\n      logger.warning(`Skipping tracing span processing due to an error: ${error.message}`);\n    }\n  }\n\n  private tryProcessResponse(span: Span, response: HttpOperationResponse): void {\n    try {\n      span.setAttribute(\"http.status_code\", response.status);\n      const serviceRequestId = response.headers.get(\"x-ms-request-id\");\n      if (serviceRequestId) {\n        span.setAttribute(\"serviceRequestId\", serviceRequestId);\n      }\n      span.setStatus({\n        code: SpanStatusCode.OK\n      });\n      span.end();\n    } catch (error) {\n      logger.warning(`Skipping tracing span processing due to an error: ${error.message}`);\n    }\n  }\n}\n"]}