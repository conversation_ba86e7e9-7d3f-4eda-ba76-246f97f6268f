{"version": 3, "file": "disableResponseDecompressionPolicy.browser.js", "sourceRoot": "", "sources": ["../../../src/policies/disableResponseDecompressionPolicy.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AAIzB,MAAM,gDAAgD,GAAG,IAAI,KAAK,CAChE,4EAA4E,CAC7E,CAAC;AAEF;;;GAGG;AACH,MAAM,UAAU,kCAAkC;IAChD,OAAO;QACL,MAAM,EAAE,CAAC,WAA0B,EAAE,QAA8B,EAAE,EAAE;YACrE,MAAM,gDAAgD,CAAC;QACzD,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,kCAAmC,SAAQ,iBAAiB;IACvE,YAAY,UAAyB,EAAE,OAA6B;QAClE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,MAAM,gDAAgD,CAAC;IACzD,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,QAAqB;QAC5C,MAAM,gDAAgD,CAAC;IACzD,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/*\n * NOTE: When moving this file, please update \"browser\" section in package.json\n */\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyOptions,\n  RequestPolicyFactory\n} from \"./requestPolicy\";\nimport { WebResource } from \"../webResource\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\n\nconst DisbleResponseDecompressionNotSupportedInBrowser = new Error(\n  \"DisableResponseDecompressionPolicy is not supported in browser environment\"\n);\n\n/**\n * {@link DisableResponseDecompressionPolicy} is not supported in browser and attempting\n * to use it will results in error being thrown.\n */\nexport function disableResponseDecompressionPolicy(): RequestPolicyFactory {\n  return {\n    create: (_nextPolicy: RequestPolicy, _options: RequestPolicyOptions) => {\n      throw DisbleResponseDecompressionNotSupportedInBrowser;\n    }\n  };\n}\n\nexport class DisableResponseDecompressionPolicy extends BaseRequestPolicy {\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n    super(nextPolicy, options);\n    throw DisbleResponseDecompressionNotSupportedInBrowser;\n  }\n\n  public async sendRequest(_request: WebResource): Promise<HttpOperationResponse> {\n    throw DisbleResponseDecompressionNotSupportedInBrowser;\n  }\n}\n"]}