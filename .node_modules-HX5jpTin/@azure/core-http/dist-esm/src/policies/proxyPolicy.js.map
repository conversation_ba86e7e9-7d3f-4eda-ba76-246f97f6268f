{"version": 3, "file": "proxyPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/proxyPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AAIzB,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AACpC,OAAO,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAC;AAEpD;;;GAGG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAa,EAAE,CAAC;AAC9C,IAAI,iBAAiB,GAAY,KAAK,CAAC;AAEvC,yDAAyD;AACzD,MAAM,iBAAiB,GAAyB,IAAI,GAAG,EAAE,CAAC;AAE1D,SAAS,yBAAyB;IAChC,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,UAAU,GAAG,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9D,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1D,MAAM,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAE5D,OAAO,UAAU,IAAI,QAAQ,IAAI,SAAS,CAAC;AAC7C,CAAC;AAED;;;;GAIG;AACH,SAAS,UAAU,CACjB,GAAW,EACX,WAAqB,EACrB,WAAkC;IAElC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,OAAO,KAAK,CAAC;KACd;IACD,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAG,CAAC;IAC9C,IAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,IAAI,CAAC,EAAE;QAC1B,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC9B;IACD,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;QACjC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACtB,mEAAmE;YACnE,mDAAmD;YACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC1B,cAAc,GAAG,IAAI,CAAC;aACvB;iBAAM;gBACL,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;oBACnE,cAAc,GAAG,IAAI,CAAC;iBACvB;aACF;SACF;aAAM;YACL,IAAI,IAAI,KAAK,OAAO,EAAE;gBACpB,cAAc,GAAG,IAAI,CAAC;aACvB;SACF;KACF;IACD,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACvC,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW;IACzB,MAAM,OAAO,GAAG,mBAAmB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACxD,iBAAiB,GAAG,IAAI,CAAC;IACzB,IAAI,OAAO,EAAE;QACX,OAAO,OAAO;aACX,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;aAC1B,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAClC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,QAAiB;IACvD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,yBAAyB,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,SAAS,CAAC;SAClB;KACF;IAED,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC5E,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACnD,MAAM,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1E,OAAO;QACL,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC,OAAO,EAAE;QAClC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC;QAClD,QAAQ;QACR,QAAQ;KACT,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CACzB,aAA6B,EAC7B,OAGC;IAED,IAAI,CAAC,aAAa,EAAE;QAClB,aAAa,GAAG,uBAAuB,EAAE,CAAC;KAC3C;IACD,IAAI,CAAC,iBAAiB,EAAE;QACtB,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC;KAC1C;IACD,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,oBAA0C,EAAE,EAAE;YAChF,OAAO,IAAI,WAAW,CACpB,UAAU,EACV,oBAAoB,EACpB,aAAc,EACd,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,CAC3B,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,GAAW;IAEX,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE;QAClB,OAAO,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC;KAChC;IAED,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACvC,MAAM,SAAS,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACrC,MAAM,WAAW,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC;IACtC,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACpE,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC1E,MAAM,cAAc,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;IAChF,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,cAAc;KACf,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,WAAY,SAAQ,iBAAiB;IAChD,YACE,UAAyB,EACzB,OAA6B,EACtB,aAA4B,EAC3B,iBAA4B;QAEpC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAHpB,kBAAa,GAAb,aAAa,CAAe;QAC3B,sBAAiB,GAAjB,iBAAiB,CAAW;IAGtC,CAAC;IAEM,WAAW,CAAC,OAAwB;;QACzC,IACE,CAAC,OAAO,CAAC,aAAa;YACtB,CAAC,UAAU,CACT,OAAO,CAAC,GAAG,EACX,MAAA,IAAI,CAAC,iBAAiB,mCAAI,iBAAiB,EAC3C,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,CACvD,EACD;YACA,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAC5C;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { ProxySettings } from \"../serviceClient\";\nimport { WebResourceLike } from \"../webResource\";\nimport { Constants } from \"../util/constants\";\nimport { URLBuilder } from \"../url\";\nimport { getEnvironmentValue } from \"../util/utils\";\n\n/**\n * Stores the patterns specified in NO_PROXY environment variable.\n * @internal\n */\nexport const globalNoProxyList: string[] = [];\nlet noProxyListLoaded: boolean = false;\n\n/** A cache of whether a host should bypass the proxy. */\nconst globalBypassedMap: Map<string, boolean> = new Map();\n\nfunction loadEnvironmentProxyValue(): string | undefined {\n  if (!process) {\n    return undefined;\n  }\n\n  const httpsProxy = getEnvironmentValue(Constants.HTTPS_PROXY);\n  const allProxy = getEnvironmentValue(Constants.ALL_PROXY);\n  const httpProxy = getEnvironmentValue(Constants.HTTP_PROXY);\n\n  return httpsProxy || allProxy || httpProxy;\n}\n\n/**\n * Check whether the host of a given `uri` matches any pattern in the no proxy list.\n * If there's a match, any request sent to the same host shouldn't have the proxy settings set.\n * This implementation is a port of https://github.com/Azure/azure-sdk-for-net/blob/8cca811371159e527159c7eb65602477898683e2/sdk/core/Azure.Core/src/Pipeline/Internal/HttpEnvironmentProxy.cs#L210\n */\nfunction isBypassed(\n  uri: string,\n  noProxyList: string[],\n  bypassedMap?: Map<string, boolean>\n): boolean | undefined {\n  if (noProxyList.length === 0) {\n    return false;\n  }\n  const host = URLBuilder.parse(uri).getHost()!;\n  if (bypassedMap?.has(host)) {\n    return bypassedMap.get(host);\n  }\n  let isBypassedFlag = false;\n  for (const pattern of noProxyList) {\n    if (pattern[0] === \".\") {\n      // This should match either domain it self or any subdomain or host\n      // .foo.com will match foo.com it self or *.foo.com\n      if (host.endsWith(pattern)) {\n        isBypassedFlag = true;\n      } else {\n        if (host.length === pattern.length - 1 && host === pattern.slice(1)) {\n          isBypassedFlag = true;\n        }\n      }\n    } else {\n      if (host === pattern) {\n        isBypassedFlag = true;\n      }\n    }\n  }\n  bypassedMap?.set(host, isBypassedFlag);\n  return isBypassedFlag;\n}\n\n/**\n * @internal\n */\nexport function loadNoProxy(): string[] {\n  const noProxy = getEnvironmentValue(Constants.NO_PROXY);\n  noProxyListLoaded = true;\n  if (noProxy) {\n    return noProxy\n      .split(\",\")\n      .map((item) => item.trim())\n      .filter((item) => item.length);\n  }\n\n  return [];\n}\n\nexport function getDefaultProxySettings(proxyUrl?: string): ProxySettings | undefined {\n  if (!proxyUrl) {\n    proxyUrl = loadEnvironmentProxyValue();\n    if (!proxyUrl) {\n      return undefined;\n    }\n  }\n\n  const { username, password, urlWithoutAuth } = extractAuthFromUrl(proxyUrl);\n  const parsedUrl = URLBuilder.parse(urlWithoutAuth);\n  const schema = parsedUrl.getScheme() ? parsedUrl.getScheme() + \"://\" : \"\";\n  return {\n    host: schema + parsedUrl.getHost(),\n    port: Number.parseInt(parsedUrl.getPort() || \"80\"),\n    username,\n    password\n  };\n}\n\n/**\n * A policy that allows one to apply proxy settings to all requests.\n * If not passed static settings, they will be retrieved from the HTTPS_PROXY\n * or HTTP_PROXY environment variables.\n * @param proxySettings - ProxySettings to use on each request.\n * @param options - additional settings, for example, custom NO_PROXY patterns\n */\nexport function proxyPolicy(\n  proxySettings?: ProxySettings,\n  options?: {\n    /** a list of patterns to override those loaded from NO_PROXY environment variable. */\n    customNoProxyList?: string[];\n  }\n): RequestPolicyFactory {\n  if (!proxySettings) {\n    proxySettings = getDefaultProxySettings();\n  }\n  if (!noProxyListLoaded) {\n    globalNoProxyList.push(...loadNoProxy());\n  }\n  return {\n    create: (nextPolicy: RequestPolicy, requestPolicyOptions: RequestPolicyOptions) => {\n      return new ProxyPolicy(\n        nextPolicy,\n        requestPolicyOptions,\n        proxySettings!,\n        options?.customNoProxyList\n      );\n    }\n  };\n}\n\nfunction extractAuthFromUrl(\n  url: string\n): { username?: string; password?: string; urlWithoutAuth: string } {\n  const atIndex = url.indexOf(\"@\");\n  if (atIndex === -1) {\n    return { urlWithoutAuth: url };\n  }\n\n  const schemeIndex = url.indexOf(\"://\");\n  const authStart = schemeIndex !== -1 ? schemeIndex + 3 : 0;\n  const auth = url.substring(authStart, atIndex);\n  const colonIndex = auth.indexOf(\":\");\n  const hasPassword = colonIndex !== -1;\n  const username = hasPassword ? auth.substring(0, colonIndex) : auth;\n  const password = hasPassword ? auth.substring(colonIndex + 1) : undefined;\n  const urlWithoutAuth = url.substring(0, authStart) + url.substring(atIndex + 1);\n  return {\n    username,\n    password,\n    urlWithoutAuth\n  };\n}\n\nexport class ProxyPolicy extends BaseRequestPolicy {\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    public proxySettings: ProxySettings,\n    private customNoProxyList?: string[]\n  ) {\n    super(nextPolicy, options);\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (\n      !request.proxySettings &&\n      !isBypassed(\n        request.url,\n        this.customNoProxyList ?? globalNoProxyList,\n        this.customNoProxyList ? undefined : globalBypassedMap\n      )\n    ) {\n      request.proxySettings = this.proxySettings;\n    }\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n"]}