{"version": 3, "file": "proxyPolicy.browser.js", "sourceRoot": "", "sources": ["../../../src/policies/proxyPolicy.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AAIzB,MAAM,0BAA0B,GAAG,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;AAEpG,MAAM,UAAU,uBAAuB,CAAC,SAAkB;IACxD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,cAA8B;IACxD,OAAO;QACL,MAAM,EAAE,CAAC,WAA0B,EAAE,QAA8B,EAAE,EAAE;YACrE,MAAM,0BAA0B,CAAC;QACnC,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,WAAY,SAAQ,iBAAiB;IAChD,YAAY,UAAyB,EAAE,OAA6B;QAClE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,MAAM,0BAA0B,CAAC;IACnC,CAAC;IAEM,WAAW,CAAC,QAAyB;QAC1C,MAAM,0BAA0B,CAAC;IACnC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { ProxySettings } from \"../serviceClient\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\n\nconst proxyNotSupportedInBrowser = new Error(\"ProxyPolicy is not supported in browser environment\");\n\nexport function getDefaultProxySettings(_proxyUrl?: string): ProxySettings | undefined {\n  return undefined;\n}\n\nexport function proxyPolicy(_proxySettings?: ProxySettings): RequestPolicyFactory {\n  return {\n    create: (_nextPolicy: RequestPolicy, _options: RequestPolicyOptions) => {\n      throw proxyNotSupportedInBrowser;\n    }\n  };\n}\n\nexport class ProxyPolicy extends BaseRequestPolicy {\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n    super(nextPolicy, options);\n    throw proxyNotSupportedInBrowser;\n  }\n\n  public sendRequest(_request: WebResourceLike): Promise<HttpOperationResponse> {\n    throw proxyNotSupportedInBrowser;\n  }\n}\n"]}