{"version": 3, "file": "requestPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/requestPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAc/D,MAAM,OAAgB,iBAAiB;IACrC,YACW,WAA0B,EAC1B,QAAkC;QADlC,gBAAW,GAAX,WAAW,CAAe;QAC1B,aAAQ,GAAR,QAAQ,CAA0B;IAC1C,CAAC;IAIJ;;;;OAIG;IACI,SAAS,CAAC,QAA8B;QAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,QAA8B,EAAE,OAAe;QACxD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;CACF;AAsBD;;GAEG;AACH,MAAM,OAAO,oBAAoB;IAC/B,YAAoB,OAA4B;QAA5B,YAAO,GAAP,OAAO,CAAqB;IAAG,CAAC;IAEpD;;;;OAIG;IACI,SAAS,CAAC,QAA8B;QAC7C,OAAO,CACL,CAAC,CAAC,IAAI,CAAC,OAAO;YACd,QAAQ,KAAK,oBAAoB,CAAC,GAAG;YACrC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CACzC,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,QAA8B,EAAE,OAAe;QACxD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SACrC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { HttpPipelineLogger } from \"../httpPipelineLogger\";\nimport { HttpPipelineLogLevel } from \"../httpPipelineLogLevel\";\nimport { WebResourceLike } from \"../webResource\";\n\n/**\n * Creates a new RequestPolicy per-request that uses the provided nextPolicy.\n */\nexport type RequestPolicyFactory = {\n  create(nextPolicy: RequestPolicy, options: RequestPolicyOptionsLike): RequestPolicy;\n};\n\nexport interface RequestPolicy {\n  sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse>;\n}\n\nexport abstract class BaseRequestPolicy implements RequestPolicy {\n  protected constructor(\n    readonly _nextPolicy: RequestPolicy,\n    readonly _options: RequestPolicyOptionsLike\n  ) {}\n\n  public abstract sendRequest(webResource: WebResourceLike): Promise<HttpOperationResponse>;\n\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  public shouldLog(logLevel: HttpPipelineLogLevel): boolean {\n    return this._options.shouldLog(logLevel);\n  }\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meat the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  public log(logLevel: HttpPipelineLogLevel, message: string): void {\n    this._options.log(logLevel, message);\n  }\n}\n\n/**\n * Optional properties that can be used when creating a RequestPolicy.\n */\nexport interface RequestPolicyOptionsLike {\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  shouldLog(logLevel: HttpPipelineLogLevel): boolean;\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meet the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  log(logLevel: HttpPipelineLogLevel, message: string): void;\n}\n\n/**\n * Optional properties that can be used when creating a RequestPolicy.\n */\nexport class RequestPolicyOptions {\n  constructor(private _logger?: HttpPipelineLogger) {}\n\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  public shouldLog(logLevel: HttpPipelineLogLevel): boolean {\n    return (\n      !!this._logger &&\n      logLevel !== HttpPipelineLogLevel.OFF &&\n      logLevel <= this._logger.minimumLogLevel\n    );\n  }\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meet the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  public log(logLevel: HttpPipelineLogLevel, message: string): void {\n    if (this._logger && this.shouldLog(logLevel)) {\n      this._logger.log(logLevel, message);\n    }\n  }\n}\n"]}