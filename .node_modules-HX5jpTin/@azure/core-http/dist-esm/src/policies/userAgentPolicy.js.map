{"version": 3, "file": "userAgentPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/userAgentPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAE7C,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EAAE,sBAAsB,EAAE,uBAAuB,EAAE,MAAM,yBAAyB,CAAC;AAC1F,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AAezB,SAAS,cAAc;IACrB,MAAM,aAAa,GAAG;QACpB,GAAG,EAAE,WAAW;QAChB,KAAK,EAAE,SAAS,CAAC,eAAe;KACjC,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,kBAAkB,CACzB,aAA8B,EAC9B,YAAY,GAAG,GAAG,EAClB,cAAc,GAAG,GAAG;IAEpB,OAAO,aAAa;SACjB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACjE,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;IAC/B,CAAC,CAAC;SACD,IAAI,CAAC,YAAY,CAAC,CAAC;AACxB,CAAC;AAED,MAAM,CAAC,MAAM,6BAA6B,GAAG,sBAAsB,CAAC;AAEpE,MAAM,UAAU,wBAAwB;IACtC,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IACrC,MAAM,oBAAoB,GAAG,uBAAuB,EAAE,CAAC;IACvD,MAAM,SAAS,GAAG,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAC/E,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,aAA6B;IAC3D,MAAM,GAAG,GACP,CAAC,aAAa,IAAI,aAAa,CAAC,GAAG,KAAK,SAAS,IAAI,aAAa,CAAC,GAAG,KAAK,IAAI;QAC7E,CAAC,CAAC,sBAAsB,EAAE;QAC1B,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC;IACxB,MAAM,KAAK,GACT,CAAC,aAAa,IAAI,aAAa,CAAC,KAAK,KAAK,SAAS,IAAI,aAAa,CAAC,KAAK,KAAK,IAAI;QACjF,CAAC,CAAC,wBAAwB,EAAE;QAC5B,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC;IAE1B,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IACpD,YACW,WAA0B,EAC1B,QAA8B,EAC7B,SAAiB,EACjB,WAAmB;QAE7B,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QALpB,gBAAW,GAAX,WAAW,CAAe;QAC1B,aAAQ,GAAR,QAAQ,CAAsB;QAC7B,cAAS,GAAT,SAAS,CAAQ;QACjB,gBAAW,GAAX,WAAW,CAAQ;IAG/B,CAAC;IAED,WAAW,CAAC,OAAwB;QAClC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,kBAAkB,CAAC,OAAwB;QACzC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACpB,OAAO,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;SACrC;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;YAC5D,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SACvD;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpHeaders } from \"../httpHeaders\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { Constants } from \"../util/constants\";\nimport { WebResourceLike } from \"../webResource\";\nimport { getDefaultUserAgentKey, getPlatformSpecificData } from \"./msRestUserAgentPolicy\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\n\nexport type TelemetryInfo = { key?: string; value?: string };\n\n/**\n * Options for adding user agent details to outgoing requests.\n */\nexport interface UserAgentOptions {\n  /*\n   * String prefix to add to the user agent for outgoing requests.\n   * Defaults to an empty string.\n   */\n  userAgentPrefix?: string;\n}\n\nfunction getRuntimeInfo(): TelemetryInfo[] {\n  const msRestRuntime = {\n    key: \"core-http\",\n    value: Constants.coreHttpVersion\n  };\n\n  return [msRestRuntime];\n}\n\nfunction getUserAgentString(\n  telemetryInfo: TelemetryInfo[],\n  keySeparator = \" \",\n  valueSeparator = \"/\"\n): string {\n  return telemetryInfo\n    .map((info) => {\n      const value = info.value ? `${valueSeparator}${info.value}` : \"\";\n      return `${info.key}${value}`;\n    })\n    .join(keySeparator);\n}\n\nexport const getDefaultUserAgentHeaderName = getDefaultUserAgentKey;\n\nexport function getDefaultUserAgentValue(): string {\n  const runtimeInfo = getRuntimeInfo();\n  const platformSpecificData = getPlatformSpecificData();\n  const userAgent = getUserAgentString(runtimeInfo.concat(platformSpecificData));\n  return userAgent;\n}\n\nexport function userAgentPolicy(userAgentData?: TelemetryInfo): RequestPolicyFactory {\n  const key: string =\n    !userAgentData || userAgentData.key === undefined || userAgentData.key === null\n      ? getDefaultUserAgentKey()\n      : userAgentData.key;\n  const value: string =\n    !userAgentData || userAgentData.value === undefined || userAgentData.value === null\n      ? getDefaultUserAgentValue()\n      : userAgentData.value;\n\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new UserAgentPolicy(nextPolicy, options, key, value);\n    }\n  };\n}\n\nexport class UserAgentPolicy extends BaseRequestPolicy {\n  constructor(\n    readonly _nextPolicy: RequestPolicy,\n    readonly _options: RequestPolicyOptions,\n    protected headerKey: string,\n    protected headerValue: string\n  ) {\n    super(_nextPolicy, _options);\n  }\n\n  sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    this.addUserAgentHeader(request);\n    return this._nextPolicy.sendRequest(request);\n  }\n\n  addUserAgentHeader(request: WebResourceLike): void {\n    if (!request.headers) {\n      request.headers = new HttpHeaders();\n    }\n\n    if (!request.headers.get(this.headerKey) && this.headerValue) {\n      request.headers.set(this.headerKey, this.headerValue);\n    }\n  }\n}\n"]}