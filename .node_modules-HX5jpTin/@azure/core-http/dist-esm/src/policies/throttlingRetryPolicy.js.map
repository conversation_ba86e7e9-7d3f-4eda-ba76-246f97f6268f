{"version": 3, "file": "throttlingRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/throttlingRetryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AAGzB,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,8BAA8B,EAAE,MAAM,iCAAiC,CAAC;AACjF,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAMtC,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC;AAExD,MAAM,UAAU,qBAAqB;IACnC,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,qBAAqB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACxD,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,oBAAoB,GAAG,4BAA4B,CAAC;AAE1D;;;;;GAKG;AACH,MAAM,OAAO,qBAAsB,SAAQ,iBAAiB;IAI1D,YACE,UAAyB,EACzB,OAA6B,EAC7B,eAAiC;QAEjC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAPrB,oBAAe,GAAG,CAAC,CAAC;QAQ1B,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,CAAC,uBAAuB,CAAC;IACzE,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,WAA4B;QACnD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;QACzE,IACE,QAAQ,CAAC,MAAM,KAAK,WAAW,CAAC,eAAe;YAC/C,QAAQ,CAAC,MAAM,KAAK,WAAW,CAAC,kBAAkB,EAClD;YACA,OAAO,QAAQ,CAAC;SACjB;aAAM;YACL,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;SACpD;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,WAA4B,EAC5B,YAAmC;;QAEnC,MAAM,gBAAgB,GAAuB,YAAY,CAAC,OAAO,CAAC,GAAG,CACnE,SAAS,CAAC,eAAe,CAAC,WAAW,CACtC,CAAC;QAEF,IAAI,gBAAgB,EAAE;YACpB,MAAM,SAAS,GAAuB,qBAAqB,CAAC,qBAAqB,CAC/E,gBAAgB,CACjB,CAAC;YACF,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;gBAE1B,MAAM,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE;oBAChC,WAAW,EAAE,WAAW,CAAC,WAAW;oBACpC,aAAa,EAAE,oBAAoB;iBACpC,CAAC,CAAC;gBAEH,IAAI,MAAA,WAAW,CAAC,WAAW,0CAAE,OAAO,EAAE;oBACpC,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC,CAAC;iBAC5C;gBAED,IAAI,IAAI,CAAC,eAAe,GAAG,8BAA8B,EAAE;oBACzD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;iBACtC;qBAAM;oBACL,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;iBAClD;aACF;SACF;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEM,MAAM,CAAC,qBAAqB,CAAC,WAAmB;QACrD,MAAM,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE;YACrC,OAAO,qBAAqB,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;SACrE;aAAM;YACL,OAAO,mBAAmB,GAAG,IAAI,CAAC;SACnC;IACH,CAAC;IAEM,MAAM,CAAC,yBAAyB,CAAC,WAAmB;QACzD,IAAI;YACF,MAAM,GAAG,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAW,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC7C,MAAM,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;YAExB,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;SAC9C;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,SAAS,CAAC;SAClB;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortError } from \"@azure/abort-controller\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyOptions,\n  RequestPolicyFactory\n} from \"./requestPolicy\";\nimport { WebResourceLike } from \"../webResource\";\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { Constants } from \"../util/constants\";\nimport { DEFAULT_CLIENT_MAX_RETRY_COUNT } from \"../util/throttlingRetryStrategy\";\nimport { delay } from \"../util/delay\";\n\ntype ResponseHandler = (\n  httpRequest: WebResourceLike,\n  response: HttpOperationResponse\n) => Promise<HttpOperationResponse>;\nconst StatusCodes = Constants.HttpConstants.StatusCodes;\n\nexport function throttlingRetryPolicy(): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new ThrottlingRetryPolicy(nextPolicy, options);\n    }\n  };\n}\n\nconst StandardAbortMessage = \"The operation was aborted.\";\n\n/**\n * To learn more, please refer to\n * https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-manager-request-limits,\n * https://docs.microsoft.com/en-us/azure/azure-subscription-service-limits and\n * https://docs.microsoft.com/en-us/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors\n */\nexport class ThrottlingRetryPolicy extends BaseRequestPolicy {\n  private _handleResponse: ResponseHandler;\n  private numberOfRetries = 0;\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    _handleResponse?: ResponseHandler\n  ) {\n    super(nextPolicy, options);\n    this._handleResponse = _handleResponse || this._defaultResponseHandler;\n  }\n\n  public async sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse> {\n    const response = await this._nextPolicy.sendRequest(httpRequest.clone());\n    if (\n      response.status !== StatusCodes.TooManyRequests &&\n      response.status !== StatusCodes.ServiceUnavailable\n    ) {\n      return response;\n    } else {\n      return this._handleResponse(httpRequest, response);\n    }\n  }\n\n  private async _defaultResponseHandler(\n    httpRequest: WebResourceLike,\n    httpResponse: HttpOperationResponse\n  ): Promise<HttpOperationResponse> {\n    const retryAfterHeader: string | undefined = httpResponse.headers.get(\n      Constants.HeaderConstants.RETRY_AFTER\n    );\n\n    if (retryAfterHeader) {\n      const delayInMs: number | undefined = ThrottlingRetryPolicy.parseRetryAfterHeader(\n        retryAfterHeader\n      );\n      if (delayInMs) {\n        this.numberOfRetries += 1;\n\n        await delay(delayInMs, undefined, {\n          abortSignal: httpRequest.abortSignal,\n          abortErrorMsg: StandardAbortMessage\n        });\n\n        if (httpRequest.abortSignal?.aborted) {\n          throw new AbortError(StandardAbortMessage);\n        }\n\n        if (this.numberOfRetries < DEFAULT_CLIENT_MAX_RETRY_COUNT) {\n          return this.sendRequest(httpRequest);\n        } else {\n          return this._nextPolicy.sendRequest(httpRequest);\n        }\n      }\n    }\n\n    return httpResponse;\n  }\n\n  public static parseRetryAfterHeader(headerValue: string): number | undefined {\n    const retryAfterInSeconds = Number(headerValue);\n    if (Number.isNaN(retryAfterInSeconds)) {\n      return ThrottlingRetryPolicy.parseDateRetryAfterHeader(headerValue);\n    } else {\n      return retryAfterInSeconds * 1000;\n    }\n  }\n\n  public static parseDateRetryAfterHeader(headerValue: string): number | undefined {\n    try {\n      const now: number = Date.now();\n      const date: number = Date.parse(headerValue);\n      const diff = date - now;\n\n      return Number.isNaN(diff) ? undefined : diff;\n    } catch (error) {\n      return undefined;\n    }\n  }\n}\n"]}