{"version": 3, "file": "logPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/logPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EACL,iBAAiB,EAIlB,MAAM,iBAAiB,CAAC;AAEzB,OAAO,EAAE,MAAM,IAAI,UAAU,EAAE,MAAM,QAAQ,CAAC;AAC9C,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAuB9C,MAAM,UAAU,SAAS,CAAC,iBAAmC,EAAE;IAC7D,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAE,EAAE;YACnE,OAAO,IAAI,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QAC5D,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,SAAU,SAAQ,iBAAiB;IA4C9C,YACE,UAAyB,EACzB,OAA6B,EAC7B,EACE,MAAM,GAAG,UAAU,CAAC,IAAI,EACxB,kBAAkB,GAAG,EAAE,EACvB,sBAAsB,GAAG,EAAE,KACP,EAAE;QAExB,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,CAAC,CAAC;IACjF,CAAC;IApDD;;;;;;OAMG;IACH,IAAW,kBAAkB;QAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;;;;;OAMG;IACH,IAAW,kBAAkB,CAAC,kBAA+B;QAC3D,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACH,IAAW,sBAAsB;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACH,IAAW,sBAAsB,CAAC,sBAAmC;QACnE,IAAI,CAAC,SAAS,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IACjE,CAAC;IAgBM,WAAW,CAAC,OAAwB;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEvE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC9F,CAAC;IAEO,UAAU,CAAC,OAAwB;QACzC,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEO,WAAW,CAAC,QAA+B;QACjD,IAAI,CAAC,MAAM,CAAC,yBAAyB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACrE,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../httpOperationResponse\";\nimport { WebResourceLike } from \"../webResource\";\nimport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./requestPolicy\";\nimport { Debugger } from \"@azure/logger\";\nimport { logger as coreLogger } from \"../log\";\nimport { Sanitizer } from \"../util/sanitizer\";\n\nexport interface LogPolicyOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  allowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  allowedQueryParameters?: string[];\n\n  /**\n   * The Debugger (logger) instance to use for writing pipeline logs.\n   */\n  logger?: Debugger;\n}\n\nexport function logPolicy(loggingOptions: LogPolicyOptions = {}): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions) => {\n      return new LogPolicy(nextPolicy, options, loggingOptions);\n    }\n  };\n}\n\nexport class LogPolicy extends BaseRequestPolicy {\n  logger: Debugger;\n  sanitizer: Sanitizer;\n\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   * @deprecated Pass these into the constructor instead.\n   */\n  public get allowedHeaderNames(): Set<string> {\n    return this.sanitizer.allowedHeaderNames;\n  }\n\n  /**\n   * Header names whose values will be logged when logging is enabled. Defaults to\n   * Date, traceparent, x-ms-client-request-id, and x-ms-request id.  Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   * @deprecated Pass these into the constructor instead.\n   */\n  public set allowedHeaderNames(allowedHeaderNames: Set<string>) {\n    this.sanitizer.allowedHeaderNames = allowedHeaderNames;\n  }\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   * @deprecated Pass these into the constructor instead.\n   */\n  public get allowedQueryParameters(): Set<string> {\n    return this.sanitizer.allowedQueryParameters;\n  }\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   * @deprecated Pass these into the constructor instead.\n   */\n  public set allowedQueryParameters(allowedQueryParameters: Set<string>) {\n    this.sanitizer.allowedQueryParameters = allowedQueryParameters;\n  }\n\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    {\n      logger = coreLogger.info,\n      allowedHeaderNames = [],\n      allowedQueryParameters = []\n    }: LogPolicyOptions = {}\n  ) {\n    super(nextPolicy, options);\n    this.logger = logger;\n    this.sanitizer = new Sanitizer({ allowedHeaderNames, allowedQueryParameters });\n  }\n\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!this.logger.enabled) return this._nextPolicy.sendRequest(request);\n\n    this.logRequest(request);\n    return this._nextPolicy.sendRequest(request).then((response) => this.logResponse(response));\n  }\n\n  private logRequest(request: WebResourceLike): void {\n    this.logger(`Request: ${this.sanitizer.sanitize(request)}`);\n  }\n\n  private logResponse(response: HttpOperationResponse): HttpOperationResponse {\n    this.logger(`Response status code: ${response.status}`);\n    this.logger(`Headers: ${this.sanitizer.sanitize(response.headers)}`);\n    return response;\n  }\n}\n"]}