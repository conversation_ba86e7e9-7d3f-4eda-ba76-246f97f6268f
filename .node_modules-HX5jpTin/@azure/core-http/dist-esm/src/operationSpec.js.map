{"version": 3, "file": "operationSpec.js", "sourceRoot": "", "sources": ["../../src/operationSpec.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC,OAAO,EAAE,UAAU,EAAc,MAAM,cAAc,CAAC;AAwFtD;;;GAGG;AACH,MAAM,UAAU,4BAA4B,CAAC,aAA4B;IACvE,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;IACjC,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,SAAS,EAAE;QAChD,MAAM,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC9D,IACE,iBAAiB,CAAC,UAAU;YAC5B,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM,EAC5D;YACA,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;SAChC;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  OperationParameter,\n  OperationQueryParameter,\n  OperationURLParameter\n} from \"./operationParameter\";\nimport { OperationResponse } from \"./operationResponse\";\nimport { MapperType, Serializer } from \"./serializer\";\nimport { HttpMethods } from \"./webResource\";\n\n/**\n * A specification that defines an operation.\n */\nexport interface OperationSpec {\n  /**\n   * The serializer to use in this operation.\n   */\n  readonly serializer: Serializer;\n\n  /**\n   * The HTTP method that should be used by requests for this operation.\n   */\n  readonly httpMethod: HttpMethods;\n\n  /**\n   * The URL that was provided in the service's specification. This will still have all of the URL\n   * template variables in it. If this is not provided when the OperationSpec is created, then it\n   * will be populated by a \"baseUri\" property on the ServiceClient.\n   */\n  readonly baseUrl?: string;\n\n  /**\n   * The fixed path for this operation's URL. This will still have all of the URL template variables\n   * in it.\n   */\n  readonly path?: string;\n\n  /**\n   * The content type of the request body. This value will be used as the \"Content-Type\" header if\n   * it is provided.\n   */\n  readonly contentType?: string;\n\n  /**\n   * The media type of the request body.\n   * This value can be used to aide in serialization if it is provided.\n   */\n  readonly mediaType?:\n    | \"json\"\n    | \"xml\"\n    | \"form\"\n    | \"binary\"\n    | \"multipart\"\n    | \"text\"\n    | \"unknown\"\n    | string;\n  /**\n   * The parameter that will be used to construct the HTTP request's body.\n   */\n  readonly requestBody?: OperationParameter;\n\n  /**\n   * Whether or not this operation uses XML request and response bodies.\n   */\n  readonly isXML?: boolean;\n\n  /**\n   * The parameters to the operation method that will be substituted into the constructed URL.\n   */\n  readonly urlParameters?: ReadonlyArray<OperationURLParameter>;\n\n  /**\n   * The parameters to the operation method that will be added to the constructed URL's query.\n   */\n  readonly queryParameters?: ReadonlyArray<OperationQueryParameter>;\n\n  /**\n   * The parameters to the operation method that will be converted to headers on the operation's\n   * HTTP request.\n   */\n  readonly headerParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The parameters to the operation method that will be used to create a formdata body for the\n   * operation's HTTP request.\n   */\n  readonly formDataParameters?: ReadonlyArray<OperationParameter>;\n\n  /**\n   * The different types of responses that this operation can return based on what status code is\n   * returned.\n   */\n  readonly responses: { [responseCode: string]: OperationResponse };\n}\n\n/**\n * Gets the list of status codes for streaming responses.\n * @internal\n */\nexport function getStreamResponseStatusCodes(operationSpec: OperationSpec): Set<number> {\n  const result = new Set<number>();\n  for (const statusCode in operationSpec.responses) {\n    const operationResponse = operationSpec.responses[statusCode];\n    if (\n      operationResponse.bodyMapper &&\n      operationResponse.bodyMapper.type.name === MapperType.Stream\n    ) {\n      result.add(Number(statusCode));\n    }\n  }\n  return result;\n}\n"]}