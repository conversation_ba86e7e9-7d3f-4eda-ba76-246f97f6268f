{"version": 3, "file": "fetchHttpClient.js", "sourceRoot": "", "sources": ["../../src/fetchHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,QAAQ,MAAM,WAAW,CAAC;AAKjC,OAAO,EAAE,WAAW,EAAmB,MAAM,eAAe,CAAC;AAC7D,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAY,SAAS,EAAE,MAAM,QAAQ,CAAC;AAC7C,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAsB/B,MAAM,OAAO,eAAgB,SAAQ,SAAS;IAS5C,YAAoB,gBAA2D;QAC7E,KAAK,EAAE,CAAC;QADU,qBAAgB,GAAhB,gBAAgB,CAA2C;QARvE,gBAAW,GAAW,CAAC,CAAC;IAUhC,CAAC;IATD,UAAU,CAAC,KAAsB,EAAE,SAAiB,EAAE,QAA4B;QAChF,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,gBAAiB,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAC1D,QAAQ,CAAC,SAAS,CAAC,CAAC;IACtB,CAAC;CAKF;AAED,MAAM,OAAgB,eAAe;IACnC,KAAK,CAAC,WAAW,CAAC,WAA4B;;QAC5C,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnD,MAAM,IAAI,KAAK,CACb,yFAAyF,CAC1F,CAAC;SACH;QAED,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,IAAI,aAAiD,CAAC;QACtD,IAAI,WAAW,CAAC,WAAW,EAAE;YAC3B,IAAI,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE;gBACnC,MAAM,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC;aACpD;YAED,aAAa,GAAG,CAAC,KAAY,EAAE,EAAE;gBAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;oBAC1B,eAAe,CAAC,KAAK,EAAE,CAAC;iBACzB;YACH,CAAC,CAAC;YACF,WAAW,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;SAClE;QAED,IAAI,WAAW,CAAC,OAAO,EAAE;YACvB,UAAU,CAAC,GAAG,EAAE;gBACd,eAAe,CAAC,KAAK,EAAE,CAAC;YAC1B,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;SACzB;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE;YACxB,MAAM,QAAQ,GAAQ,WAAW,CAAC,QAAQ,CAAC;YAC3C,MAAM,WAAW,GAAG,IAAI,QAAQ,EAAE,CAAC;YACnC,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,KAAU,EAAQ,EAAE;gBACxD,0FAA0F;gBAC1F,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;oBAC/B,KAAK,GAAG,KAAK,EAAE,CAAC;iBACjB;gBACD,IACE,KAAK;oBACL,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;oBACpD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,EACtD;oBACA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;iBACrD;qBAAM;oBACL,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBAChC;YACH,CAAC,CAAC;YACF,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACpC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;oBAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACzC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;qBACxC;iBACF;qBAAM;oBACL,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;iBACrC;aACF;YAED,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC;YAC/B,WAAW,CAAC,QAAQ,GAAG,SAAS,CAAC;YACjC,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC5D,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpE,IAAI,OAAO,WAAW,CAAC,WAAW,KAAK,UAAU,EAAE;oBACjD,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,cAAc,EACd,iCAAiC,WAAW,CAAC,WAAW,EAAE,EAAE,CAC7D,CAAC;iBACH;qBAAM;oBACL,kEAAkE;oBAClE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;iBAC5C;aACF;SACF;QAED,IAAI,IAAI,GAAG,WAAW,CAAC,IAAI;YACzB,CAAC,CAAC,OAAO,WAAW,CAAC,IAAI,KAAK,UAAU;gBACtC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE;gBACpB,CAAC,CAAC,WAAW,CAAC,IAAI;YACpB,CAAC,CAAC,SAAS,CAAC;QACd,IAAI,WAAW,CAAC,gBAAgB,IAAI,WAAW,CAAC,IAAI,EAAE;YACpD,MAAM,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;YACtD,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAAC,gBAAgB,CAAC,CAAC;YACjE,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC/B;iBAAM;gBACL,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAC9B;YAED,IAAI,GAAG,kBAAkB,CAAC;SAC3B;QAED,MAAM,2BAA2B,GAAyB,MAAM,IAAI,CAAC,cAAc,CACjF,WAAW,CACZ,CAAC;QAEF,MAAM,WAAW,mBACf,IAAI,EAAE,IAAI,EACV,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EACzC,MAAM,EAAE,WAAW,CAAC,MAAM,EAC1B,MAAM,EAAE,eAAe,CAAC,MAAM,EAC9B,QAAQ,EAAE,QAAQ,IACf,2BAA2B,CAC/B,CAAC;QAEF,IAAI,iBAAoD,CAAC;QACzD,IAAI;YACF,MAAM,QAAQ,GAAmB,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YAEhF,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,SAAS,GACb,CAAA,MAAA,WAAW,CAAC,yBAAyB,0CAAE,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3D,WAAW,CAAC,kBAAkB,CAAC;YAEjC,iBAAiB,GAAG;gBAClB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,WAAW;gBACpB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,kBAAkB,EAAE,SAAS;oBAC3B,CAAC,CAAG,QAAQ,CAAC,IAA0C;oBACvD,CAAC,CAAC,SAAS;gBACb,UAAU,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;aAC3D,CAAC;YAEF,MAAM,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;YAC1D,IAAI,kBAAkB,EAAE;gBACtB,MAAM,YAAY,GAA2C,QAAQ,CAAC,IAAI,IAAI,SAAS,CAAC;gBAExF,IAAI,gBAAgB,CAAC,YAAY,CAAC,EAAE;oBAClC,MAAM,oBAAoB,GAAG,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAAC;oBACrE,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACxC,iBAAiB,CAAC,kBAAkB,GAAG,oBAAoB,CAAC;iBAC7D;qBAAM;oBACL,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAE,CAAC,IAAI,SAAS,CAAC;oBACrE,IAAI,MAAM,EAAE;wBACV,wEAAwE;wBACxE,kBAAkB,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;qBAC7C;iBACF;aACF;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAE7C,OAAO,iBAAiB,CAAC;SAC1B;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,UAAU,GAAe,KAAK,CAAC;YACrC,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,EAAE;gBACnC,MAAM,IAAI,SAAS,CACjB,UAAU,CAAC,OAAO,EAClB,SAAS,CAAC,kBAAkB,EAC5B,SAAS,EACT,WAAW,CACZ,CAAC;aACH;iBAAM,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE;gBACxC,MAAM,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC;aACpD;YAED,MAAM,UAAU,CAAC;SAClB;gBAAS;YACR,0BAA0B;YAC1B,IAAI,WAAW,CAAC,WAAW,IAAI,aAAa,EAAE;gBAC5C,IAAI,gBAAgB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzC,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;oBAC1B,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;iBAC3C;gBACD,IAAI,kBAAkB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC3C,IAAI,gBAAgB,CAAC,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,kBAAkB,CAAC,EAAE;oBAC3D,kBAAkB,GAAG,gBAAgB,CACnC,iBAAkB,CAAC,kBAAkB,EACrC,eAAe,CAChB,CAAC;iBACH;gBAED,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;qBAChD,IAAI,CAAC,GAAG,EAAE;;oBACT,MAAA,WAAW,CAAC,WAAW,0CAAE,mBAAmB,CAAC,OAAO,EAAE,aAAc,CAAC,CAAC;oBACtE,OAAO;gBACT,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;oBACX,MAAM,CAAC,OAAO,CAAC,qDAAqD,EAAE,CAAC,CAAC,CAAC;gBAC3E,CAAC,CAAC,CAAC;aACN;SACF;IACH,CAAC;CAKF;AAED,SAAS,gBAAgB,CAAC,IAAS;IACjC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;AACjD,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAgB,EAAE,OAAyB;IACnE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;YACxB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,EAAE,CAAC;YACjB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,OAAgB;IAC3C,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IAEtC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QAC7B,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,OAAO,WAAW,CAAC;AACrB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Abort<PERSON>ontroller, AbortError } from \"@azure/abort-controller\";\nimport FormData from \"form-data\";\n\nimport { HttpClient } from \"./httpClient\";\nimport { TransferProgressEvent, WebResourceLike } from \"./webResource\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { HttpHeaders, HttpHeadersLike } from \"./httpHeaders\";\nimport { RestError } from \"./restError\";\nimport { Readable, Transform } from \"stream\";\nimport { logger } from \"./log\";\n\ninterface FetchError extends Error {\n  code?: string;\n  errno?: string;\n  type?: string;\n}\n\nexport type CommonRequestInfo = string; // We only ever call fetch() on string urls.\n\nexport type CommonRequestInit = Omit<RequestInit, \"body\" | \"headers\" | \"signal\"> & {\n  body?: any;\n  headers?: any;\n  signal?: any;\n};\n\nexport type CommonResponse = Omit<Response, \"body\" | \"trailer\" | \"formData\"> & {\n  body: any;\n  trailer: any;\n  formData: any;\n};\n\nexport class ReportTransform extends Transform {\n  private loadedBytes: number = 0;\n  _transform(chunk: string | Buffer, _encoding: string, callback: (arg: any) => void): void {\n    this.push(chunk);\n    this.loadedBytes += chunk.length;\n    this.progressCallback!({ loadedBytes: this.loadedBytes });\n    callback(undefined);\n  }\n\n  constructor(private progressCallback: (progress: TransferProgressEvent) => void) {\n    super();\n  }\n}\n\nexport abstract class FetchHttpClient implements HttpClient {\n  async sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse> {\n    if (!httpRequest && typeof httpRequest !== \"object\") {\n      throw new Error(\n        \"'httpRequest' (WebResourceLike) cannot be null or undefined and must be of type object.\"\n      );\n    }\n\n    const abortController = new AbortController();\n    let abortListener: ((event: any) => void) | undefined;\n    if (httpRequest.abortSignal) {\n      if (httpRequest.abortSignal.aborted) {\n        throw new AbortError(\"The operation was aborted.\");\n      }\n\n      abortListener = (event: Event) => {\n        if (event.type === \"abort\") {\n          abortController.abort();\n        }\n      };\n      httpRequest.abortSignal.addEventListener(\"abort\", abortListener);\n    }\n\n    if (httpRequest.timeout) {\n      setTimeout(() => {\n        abortController.abort();\n      }, httpRequest.timeout);\n    }\n\n    if (httpRequest.formData) {\n      const formData: any = httpRequest.formData;\n      const requestForm = new FormData();\n      const appendFormValue = (key: string, value: any): void => {\n        // value function probably returns a stream so we can provide a fresh stream on each retry\n        if (typeof value === \"function\") {\n          value = value();\n        }\n        if (\n          value &&\n          Object.prototype.hasOwnProperty.call(value, \"value\") &&\n          Object.prototype.hasOwnProperty.call(value, \"options\")\n        ) {\n          requestForm.append(key, value.value, value.options);\n        } else {\n          requestForm.append(key, value);\n        }\n      };\n      for (const formKey of Object.keys(formData)) {\n        const formValue = formData[formKey];\n        if (Array.isArray(formValue)) {\n          for (let j = 0; j < formValue.length; j++) {\n            appendFormValue(formKey, formValue[j]);\n          }\n        } else {\n          appendFormValue(formKey, formValue);\n        }\n      }\n\n      httpRequest.body = requestForm;\n      httpRequest.formData = undefined;\n      const contentType = httpRequest.headers.get(\"Content-Type\");\n      if (contentType && contentType.indexOf(\"multipart/form-data\") !== -1) {\n        if (typeof requestForm.getBoundary === \"function\") {\n          httpRequest.headers.set(\n            \"Content-Type\",\n            `multipart/form-data; boundary=${requestForm.getBoundary()}`\n          );\n        } else {\n          // browser will automatically apply a suitable content-type header\n          httpRequest.headers.remove(\"Content-Type\");\n        }\n      }\n    }\n\n    let body = httpRequest.body\n      ? typeof httpRequest.body === \"function\"\n        ? httpRequest.body()\n        : httpRequest.body\n      : undefined;\n    if (httpRequest.onUploadProgress && httpRequest.body) {\n      const onUploadProgress = httpRequest.onUploadProgress;\n      const uploadReportStream = new ReportTransform(onUploadProgress);\n      if (isReadableStream(body)) {\n        body.pipe(uploadReportStream);\n      } else {\n        uploadReportStream.end(body);\n      }\n\n      body = uploadReportStream;\n    }\n\n    const platformSpecificRequestInit: Partial<RequestInit> = await this.prepareRequest(\n      httpRequest\n    );\n\n    const requestInit: RequestInit = {\n      body: body,\n      headers: httpRequest.headers.rawHeaders(),\n      method: httpRequest.method,\n      signal: abortController.signal,\n      redirect: \"manual\",\n      ...platformSpecificRequestInit\n    };\n\n    let operationResponse: HttpOperationResponse | undefined;\n    try {\n      const response: CommonResponse = await this.fetch(httpRequest.url, requestInit);\n\n      const headers = parseHeaders(response.headers);\n\n      const streaming =\n        httpRequest.streamResponseStatusCodes?.has(response.status) ||\n        httpRequest.streamResponseBody;\n\n      operationResponse = {\n        headers: headers,\n        request: httpRequest,\n        status: response.status,\n        readableStreamBody: streaming\n          ? ((response.body as unknown) as NodeJS.ReadableStream)\n          : undefined,\n        bodyAsText: !streaming ? await response.text() : undefined\n      };\n\n      const onDownloadProgress = httpRequest.onDownloadProgress;\n      if (onDownloadProgress) {\n        const responseBody: ReadableStream<Uint8Array> | undefined = response.body || undefined;\n\n        if (isReadableStream(responseBody)) {\n          const downloadReportStream = new ReportTransform(onDownloadProgress);\n          responseBody.pipe(downloadReportStream);\n          operationResponse.readableStreamBody = downloadReportStream;\n        } else {\n          const length = parseInt(headers.get(\"Content-Length\")!) || undefined;\n          if (length) {\n            // Calling callback for non-stream response for consistency with browser\n            onDownloadProgress({ loadedBytes: length });\n          }\n        }\n      }\n\n      await this.processRequest(operationResponse);\n\n      return operationResponse;\n    } catch (error) {\n      const fetchError: FetchError = error;\n      if (fetchError.code === \"ENOTFOUND\") {\n        throw new RestError(\n          fetchError.message,\n          RestError.REQUEST_SEND_ERROR,\n          undefined,\n          httpRequest\n        );\n      } else if (fetchError.type === \"aborted\") {\n        throw new AbortError(\"The operation was aborted.\");\n      }\n\n      throw fetchError;\n    } finally {\n      // clean up event listener\n      if (httpRequest.abortSignal && abortListener) {\n        let uploadStreamDone = Promise.resolve();\n        if (isReadableStream(body)) {\n          uploadStreamDone = isStreamComplete(body);\n        }\n        let downloadStreamDone = Promise.resolve();\n        if (isReadableStream(operationResponse?.readableStreamBody)) {\n          downloadStreamDone = isStreamComplete(\n            operationResponse!.readableStreamBody,\n            abortController\n          );\n        }\n\n        Promise.all([uploadStreamDone, downloadStreamDone])\n          .then(() => {\n            httpRequest.abortSignal?.removeEventListener(\"abort\", abortListener!);\n            return;\n          })\n          .catch((e) => {\n            logger.warning(\"Error when cleaning up abortListener on httpRequest\", e);\n          });\n      }\n    }\n  }\n\n  abstract prepareRequest(httpRequest: WebResourceLike): Promise<Partial<RequestInit>>;\n  abstract processRequest(operationResponse: HttpOperationResponse): Promise<void>;\n  abstract fetch(input: CommonRequestInfo, init?: CommonRequestInit): Promise<CommonResponse>;\n}\n\nfunction isReadableStream(body: any): body is Readable {\n  return body && typeof body.pipe === \"function\";\n}\n\nfunction isStreamComplete(stream: Readable, aborter?: AbortController): Promise<void> {\n  return new Promise((resolve) => {\n    stream.once(\"close\", () => {\n      aborter?.abort();\n      resolve();\n    });\n    stream.once(\"end\", resolve);\n    stream.once(\"error\", resolve);\n  });\n}\n\nexport function parseHeaders(headers: Headers): HttpHeadersLike {\n  const httpHeaders = new HttpHeaders();\n\n  headers.forEach((value, key) => {\n    httpHeaders.set(key, value);\n  });\n\n  return httpHeaders;\n}\n"]}