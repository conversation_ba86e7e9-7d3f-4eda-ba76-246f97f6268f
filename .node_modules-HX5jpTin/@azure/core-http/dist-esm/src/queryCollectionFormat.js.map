{"version": 3, "file": "queryCollectionFormat.js", "sourceRoot": "", "sources": ["../../src/queryCollectionFormat.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,MAAM,CAAN,IAAY,qBAMX;AAND,WAAY,qBAAqB;IAC/B,kCAAS,CAAA;IACT,kCAAS,CAAA;IACT,mCAAU,CAAA;IACV,oCAAW,CAAA;IACX,wCAAe,CAAA;AACjB,CAAC,EANW,qBAAqB,KAArB,qBAAqB,QAMhC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The format that will be used to join an array of values together for a query parameter value.\n */\nexport enum QueryCollectionFormat {\n  Csv = \",\",\n  Ssv = \" \",\n  Tsv = \"\\t\",\n  Pipes = \"|\",\n  Multi = \"Multi\"\n}\n"]}