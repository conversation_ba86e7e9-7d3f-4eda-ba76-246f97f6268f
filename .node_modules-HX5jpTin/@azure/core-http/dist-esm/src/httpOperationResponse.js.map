{"version": 3, "file": "httpOperationResponse.js", "sourceRoot": "", "sources": ["../../src/httpOperationResponse.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { WebResourceLike } from \"./webResource\";\nimport { HttpHeadersLike } from \"./httpHeaders\";\n\n/**\n * The properties on an HTTP response which will always be present.\n */\nexport interface HttpResponse {\n  /**\n   * The raw request\n   */\n  request: WebResourceLike;\n\n  /**\n   * The HTTP response status (e.g. 200)\n   */\n  status: number;\n\n  /**\n   * The HTTP response headers.\n   */\n  headers: HttpHeadersLike;\n}\n\n// eslint-disable-next-line @azure/azure-sdk/ts-no-namespaces\ndeclare global {\n  /**\n   * Stub declaration of the browser-only Blob type.\n   * Full type information can be obtained by including \"lib\": [\"dom\"] in tsconfig.json.\n   */\n  interface Blob {}\n}\n\n/**\n * Wrapper object for http request and response. Deserialized object is stored in\n * the `parsedBody` property when the response body is received in JSON or XML.\n */\nexport interface HttpOperationResponse extends HttpResponse {\n  /**\n   * The parsed HTTP response headers.\n   */\n  parsedHeaders?: { [key: string]: any };\n\n  /**\n   * The response body as text (string format)\n   */\n  bodyAsText?: string | null;\n\n  /**\n   * The response body as parsed JSON or XML\n   */\n  parsedBody?: any;\n\n  /**\n   * BROWSER ONLY\n   *\n   * The response body as a browser Blob.\n   * Always undefined in node.js.\n   */\n  blobBody?: Promise<Blob>;\n\n  /**\n   * NODEJS ONLY\n   *\n   * The response body as a node.js Readable stream.\n   * Always undefined in the browser.\n   */\n  readableStreamBody?: NodeJS.ReadableStream;\n}\n\n/**\n * The flattened response to a REST call.\n * Contains the underlying HttpOperationResponse as well as\n * the merged properties of the parsedBody, parsedHeaders, etc.\n */\nexport interface RestResponse {\n  /**\n   * The underlying HTTP response containing both raw and deserialized response data.\n   */\n  _response: HttpOperationResponse;\n\n  [key: string]: any;\n}\n"]}