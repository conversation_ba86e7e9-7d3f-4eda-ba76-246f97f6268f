{"version": 3, "file": "defaultHttpClient.browser.js", "sourceRoot": "", "sources": ["../../src/defaultHttpClient.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,aAAa,IAAI,iBAAiB,EAAE,MAAM,iBAAiB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport { XhrHttpClient as DefaultHttpClient } from \"./xhrHttpClient\";\n"]}