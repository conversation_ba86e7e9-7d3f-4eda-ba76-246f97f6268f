{"version": 3, "file": "xhrHttpClient.js", "sourceRoot": "", "sources": ["../../src/xhrHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD,OAAO,EAAE,WAAW,EAAmB,MAAM,eAAe,CAAC;AAG7D,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC;;GAEG;AACH,MAAM,OAAO,aAAa;IACjB,WAAW,CAAC,OAAwB;;QACzC,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QAEjC,IAAI,OAAO,CAAC,aAAa,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACvE;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,IAAI,WAAW,EAAE;YACf,IAAI,WAAW,CAAC,OAAO,EAAE;gBACvB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC,CAAC;aACrE;YAED,MAAM,QAAQ,GAAG,GAAS,EAAE;gBAC1B,GAAG,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,CAAC;YACF,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAChD,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC5C,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,IAAI,EAAE;oBAC1C,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;iBACpD;YACH,CAAC,CAAC,CAAC;SACJ;QAED,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC1D,mBAAmB,CAAC,GAAG,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAErD,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAClC,MAAM,WAAW,GAAG,IAAI,QAAQ,EAAE,CAAC;YACnC,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,KAAU,EAAQ,EAAE;gBACxD,IACE,KAAK;oBACL,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;oBACpD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,EACtD;oBACA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;iBACrD;qBAAM;oBACL,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBAChC;YACH,CAAC,CAAC;YACF,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACpC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;oBAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACzC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;qBACxC;iBACF;qBAAM;oBACL,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;iBACrC;aACF;YAED,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;YAC3B,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC;YAC7B,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YACxD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpE,kEAAkE;gBAClE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;aACxC;SACF;QAED,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC9B,GAAG,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC9C,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE;YACnD,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;SACjD;QAED,GAAG,CAAC,YAAY;YACd,CAAA,MAAA,OAAO,CAAC,yBAAyB,0CAAE,IAAI,KAAI,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QAE1F,2CAA2C;QAC3C,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE3D,IAAI,GAAG,CAAC,YAAY,KAAK,MAAM,EAAE;YAC/B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,kBAAkB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;gBAClD,qBAAqB,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,OAAO,IAAI,OAAO,CAAC,UAAS,OAAO,EAAE,MAAM;gBACzC,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE,CAChC,OAAO,CAAC;oBACN,OAAO;oBACP,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC;oBAC1B,UAAU,EAAE,GAAG,CAAC,YAAY;iBAC7B,CAAC,CACH,CAAC;gBACF,qBAAqB,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;CACF;AAED,SAAS,kBAAkB,CACzB,GAAmB,EACnB,OAAwB,EACxB,GAAgF,EAChF,GAA2B;IAE3B,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;;QAC5C,wCAAwC;QACxC,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,gBAAgB,EAAE;YACtD,IAAI,OAAO,CAAC,kBAAkB,KAAI,MAAA,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA,EAAE;gBACpF,MAAM,QAAQ,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrD,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;wBAChC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACxB,CAAC,CAAC,CAAC;oBACH,qBAAqB,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;gBACH,GAAG,CAAC;oBACF,OAAO;oBACP,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC;oBAC1B,QAAQ;iBACT,CAAC,CAAC;aACJ;iBAAM;gBACL,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;oBAChC,uFAAuF;oBACvF,sEAAsE;oBACtE,oDAAoD;oBACpD,IAAI,GAAG,CAAC,QAAQ,EAAE;wBAChB,iEAAiE;wBACjE,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;wBAChC,MAAM,CAAC,MAAM,GAAG,UAAS,CAAC;;4BACxB,MAAM,IAAI,GAAG,MAAA,CAAC,CAAC,MAAM,0CAAE,MAAgB,CAAC;4BACxC,GAAG,CAAC;gCACF,OAAO;gCACP,MAAM,EAAE,GAAG,CAAC,MAAM;gCAClB,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC;gCAC1B,UAAU,EAAE,IAAI;6BACjB,CAAC,CAAC;wBACL,CAAC,CAAC;wBACF,MAAM,CAAC,OAAO,GAAG,UAAS,EAAE;4BAC1B,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACpB,CAAC,CAAC;wBACF,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;qBAC1C;yBAAM;wBACL,GAAG,CAAC;4BACF,OAAO;4BACP,MAAM,EAAE,GAAG,CAAC,MAAM;4BAClB,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC;yBAC3B,CAAC,CAAC;qBACJ;gBACH,CAAC,CAAC,CAAC;aACJ;SACF;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAC1B,GAA8B,EAC9B,QAAoD;IAEpD,IAAI,QAAQ,EAAE;QACZ,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,EAAE,CAC5C,QAAQ,CAAC;YACP,WAAW,EAAE,QAAQ,CAAC,MAAM;SAC7B,CAAC,CACH,CAAC;KACH;AACH,CAAC;AAED,+BAA+B;AAC/B,MAAM,UAAU,YAAY,CAAC,GAAmB;IAC9C,MAAM,eAAe,GAAG,IAAI,WAAW,EAAE,CAAC;IAC1C,MAAM,WAAW,GAAG,GAAG;SACpB,qBAAqB,EAAE;SACvB,IAAI,EAAE;SACN,KAAK,CAAC,SAAS,CAAC,CAAC;IACpB,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACxC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC1C,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;KAC9C;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,qBAAqB,CAC5B,OAAwB,EACxB,GAAmB,EACnB,MAA0B;IAE1B,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CACjC,MAAM,CACJ,IAAI,SAAS,CACX,6BAA6B,OAAO,CAAC,GAAG,EAAE,EAC1C,SAAS,CAAC,kBAAkB,EAC5B,SAAS,EACT,OAAO,CACR,CACF,CACF,CAAC;IACF,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC;IAChE,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;IACxD,GAAG,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AAC5D,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortError } from \"@azure/abort-controller\";\nimport { HttpClient } from \"./httpClient\";\nimport { HttpHeaders, HttpHeadersLike } from \"./httpHeaders\";\nimport { WebResourceLike, TransferProgressEvent } from \"./webResource\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { RestError } from \"./restError\";\n\n/**\n * A HttpClient implementation that uses XMLHttpRequest to send HTTP requests.\n */\nexport class XhrHttpClient implements HttpClient {\n  public sendRequest(request: WebResourceLike): Promise<HttpOperationResponse> {\n    const xhr = new XMLHttpRequest();\n\n    if (request.proxySettings) {\n      throw new Error(\"HTTP proxy is not supported in browser environment\");\n    }\n\n    const abortSignal = request.abortSignal;\n    if (abortSignal) {\n      if (abortSignal.aborted) {\n        return Promise.reject(new AbortError(\"The operation was aborted.\"));\n      }\n\n      const listener = (): void => {\n        xhr.abort();\n      };\n      abortSignal.addEventListener(\"abort\", listener);\n      xhr.addEventListener(\"readystatechange\", () => {\n        if (xhr.readyState === XMLHttpRequest.DONE) {\n          abortSignal.removeEventListener(\"abort\", listener);\n        }\n      });\n    }\n\n    addProgressListener(xhr.upload, request.onUploadProgress);\n    addProgressListener(xhr, request.onDownloadProgress);\n\n    if (request.formData) {\n      const formData = request.formData;\n      const requestForm = new FormData();\n      const appendFormValue = (key: string, value: any): void => {\n        if (\n          value &&\n          Object.prototype.hasOwnProperty.call(value, \"value\") &&\n          Object.prototype.hasOwnProperty.call(value, \"options\")\n        ) {\n          requestForm.append(key, value.value, value.options);\n        } else {\n          requestForm.append(key, value);\n        }\n      };\n      for (const formKey of Object.keys(formData)) {\n        const formValue = formData[formKey];\n        if (Array.isArray(formValue)) {\n          for (let j = 0; j < formValue.length; j++) {\n            appendFormValue(formKey, formValue[j]);\n          }\n        } else {\n          appendFormValue(formKey, formValue);\n        }\n      }\n\n      request.body = requestForm;\n      request.formData = undefined;\n      const contentType = request.headers.get(\"Content-Type\");\n      if (contentType && contentType.indexOf(\"multipart/form-data\") !== -1) {\n        // browser will automatically apply a suitable content-type header\n        request.headers.remove(\"Content-Type\");\n      }\n    }\n\n    xhr.open(request.method, request.url);\n    xhr.timeout = request.timeout;\n    xhr.withCredentials = request.withCredentials;\n    for (const header of request.headers.headersArray()) {\n      xhr.setRequestHeader(header.name, header.value);\n    }\n\n    xhr.responseType =\n      request.streamResponseStatusCodes?.size || request.streamResponseBody ? \"blob\" : \"text\";\n\n    // tslint:disable-next-line:no-null-keyword\n    xhr.send(request.body === undefined ? null : request.body);\n\n    if (xhr.responseType === \"blob\") {\n      return new Promise((resolve, reject) => {\n        handleBlobResponse(xhr, request, resolve, reject);\n        rejectOnTerminalEvent(request, xhr, reject);\n      });\n    } else {\n      return new Promise(function(resolve, reject) {\n        xhr.addEventListener(\"load\", () =>\n          resolve({\n            request,\n            status: xhr.status,\n            headers: parseHeaders(xhr),\n            bodyAsText: xhr.responseText\n          })\n        );\n        rejectOnTerminalEvent(request, xhr, reject);\n      });\n    }\n  }\n}\n\nfunction handleBlobResponse(\n  xhr: XMLHttpRequest,\n  request: WebResourceLike,\n  res: (value: HttpOperationResponse | PromiseLike<HttpOperationResponse>) => void,\n  rej: (reason?: any) => void\n): void {\n  xhr.addEventListener(\"readystatechange\", () => {\n    // Resolve as soon as headers are loaded\n    if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {\n      if (request.streamResponseBody || request.streamResponseStatusCodes?.has(xhr.status)) {\n        const blobBody = new Promise<Blob>((resolve, reject) => {\n          xhr.addEventListener(\"load\", () => {\n            resolve(xhr.response);\n          });\n          rejectOnTerminalEvent(request, xhr, reject);\n        });\n        res({\n          request,\n          status: xhr.status,\n          headers: parseHeaders(xhr),\n          blobBody\n        });\n      } else {\n        xhr.addEventListener(\"load\", () => {\n          // xhr.response is of Blob type if the request is sent with xhr.responseType === \"blob\"\n          // but the status code is not one of the stream response status codes,\n          // so treat it as text and convert from Blob to text\n          if (xhr.response) {\n            // Blob.text() is not supported in IE so using FileReader instead\n            const reader = new FileReader();\n            reader.onload = function(e) {\n              const text = e.target?.result as string;\n              res({\n                request,\n                status: xhr.status,\n                headers: parseHeaders(xhr),\n                bodyAsText: text\n              });\n            };\n            reader.onerror = function(_e) {\n              rej(reader.error);\n            };\n            reader.readAsText(xhr.response, \"UTF-8\");\n          } else {\n            res({\n              request,\n              status: xhr.status,\n              headers: parseHeaders(xhr)\n            });\n          }\n        });\n      }\n    }\n  });\n}\n\nfunction addProgressListener(\n  xhr: XMLHttpRequestEventTarget,\n  listener?: (progress: TransferProgressEvent) => void\n): void {\n  if (listener) {\n    xhr.addEventListener(\"progress\", (rawEvent) =>\n      listener({\n        loadedBytes: rawEvent.loaded\n      })\n    );\n  }\n}\n\n// exported locally for testing\nexport function parseHeaders(xhr: XMLHttpRequest): HttpHeadersLike {\n  const responseHeaders = new HttpHeaders();\n  const headerLines = xhr\n    .getAllResponseHeaders()\n    .trim()\n    .split(/[\\r\\n]+/);\n  for (const line of headerLines) {\n    const index = line.indexOf(\":\");\n    const headerName = line.slice(0, index);\n    const headerValue = line.slice(index + 2);\n    responseHeaders.set(headerName, headerValue);\n  }\n  return responseHeaders;\n}\n\nfunction rejectOnTerminalEvent(\n  request: WebResourceLike,\n  xhr: XMLHttpRequest,\n  reject: (err: any) => void\n): void {\n  xhr.addEventListener(\"error\", () =>\n    reject(\n      new RestError(\n        `Failed to send request to ${request.url}`,\n        RestError.REQUEST_SEND_ERROR,\n        undefined,\n        request\n      )\n    )\n  );\n  const abortError = new AbortError(\"The operation was aborted.\");\n  xhr.addEventListener(\"abort\", () => reject(abortError));\n  xhr.addEventListener(\"timeout\", () => reject(abortError));\n}\n"]}