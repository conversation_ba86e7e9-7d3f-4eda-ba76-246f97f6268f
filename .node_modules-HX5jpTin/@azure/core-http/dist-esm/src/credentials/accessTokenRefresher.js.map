{"version": 3, "file": "accessTokenRefresher.js", "sourceRoot": "", "sources": ["../../../src/credentials/accessTokenRefresher.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC;;;;GAIG;AACH,MAAM,OAAO,oBAAoB;IAI/B,YACU,UAA2B,EAC3B,MAAyB,EACzB,uCAA+C,KAAK;QAFpD,eAAU,GAAV,UAAU,CAAiB;QAC3B,WAAM,GAAN,MAAM,CAAmB;QACzB,yCAAoC,GAApC,oCAAoC,CAAgB;QALtD,eAAU,GAAG,CAAC,CAAC;IAMpB,CAAC;IAEJ;;;OAGG;IACI,OAAO;QACZ,+EAA+E;QAC/E,OAAO,CACL,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oCAAoC,CAC7F,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,QAAQ,CAAC,OAAwB;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACnE,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,OAAO,KAAK,IAAI,SAAS,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,OAAwB;QACrC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;SACvC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, TokenCredential, GetTokenOptions } from \"@azure/core-auth\";\n\n/**\n * Helps the core-http token authentication policies with requesting a new token if we're not currently waiting for a new token.\n *\n * @deprecated No longer used in the bearer authorization policy.\n */\nexport class AccessTokenRefresher {\n  private promise: Promise<AccessToken | undefined> | undefined;\n  private lastCalled = 0;\n\n  constructor(\n    private credential: TokenCredential,\n    private scopes: string | string[],\n    private requiredMillisecondsBeforeNewRefresh: number = 30000\n  ) {}\n\n  /**\n   * Returns true if the required milliseconds(defaulted to 30000) have been passed signifying\n   * that we are ready for a new refresh.\n   */\n  public isReady(): boolean {\n    // We're only ready for a new refresh if the required milliseconds have passed.\n    return (\n      !this.lastCalled || Date.now() - this.lastCalled > this.requiredMillisecondsBeforeNewRefresh\n    );\n  }\n\n  /**\n   * Stores the time in which it is called,\n   * then requests a new token,\n   * then sets this.promise to undefined,\n   * then returns the token.\n   */\n  private async getToken(options: GetTokenOptions): Promise<AccessToken | undefined> {\n    this.lastCalled = Date.now();\n    const token = await this.credential.getToken(this.scopes, options);\n    this.promise = undefined;\n    return token || undefined;\n  }\n\n  /**\n   * Requests a new token if we're not currently waiting for a new token.\n   * Returns null if the required time between each call hasn't been reached.\n   */\n  public refresh(options: GetTokenOptions): Promise<AccessToken | undefined> {\n    if (!this.promise) {\n      this.promise = this.getToken(options);\n    }\n\n    return this.promise;\n  }\n}\n"]}