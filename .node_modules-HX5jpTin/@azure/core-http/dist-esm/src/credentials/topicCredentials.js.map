{"version": 3, "file": "topicCredentials.js", "sourceRoot": "", "sources": ["../../../src/credentials/topicCredentials.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,iBAAiB,EAA2B,MAAM,qBAAqB,CAAC;AAEjF,MAAM,OAAO,gBAAiB,SAAQ,iBAAiB;IACrD;;;;OAIG;IACH,YAAY,QAAgB;QAC1B,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,CAAC,EAAE;YAC3D,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;SACrF;QACD,MAAM,OAAO,GAA4B;YACvC,QAAQ,EAAE;gBACR,aAAa,EAAE,QAAQ;aACxB;SACF,CAAC;QACF,KAAK,CAAC,OAAO,CAAC,CAAC;IACjB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { ApiKeyCredentials, ApiKeyCredentialOptions } from \"./apiKeyCredentials\";\n\nexport class TopicCredentials extends ApiKeyCredentials {\n  /**\n   * Creates a new EventGrid TopicCredentials object.\n   *\n   * @param topicKey - The EventGrid topic key\n   */\n  constructor(topicKey: string) {\n    if (!topicKey || (topicKey && typeof topicKey !== \"string\")) {\n      throw new Error(\"topicKey cannot be null or undefined and must be of type string.\");\n    }\n    const options: ApiKeyCredentialOptions = {\n      inHeader: {\n        \"aeg-sas-key\": topicKey\n      }\n    };\n    super(options);\n  }\n}\n"]}