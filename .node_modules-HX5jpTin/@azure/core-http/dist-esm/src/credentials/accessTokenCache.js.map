{"version": 3, "file": "accessTokenCache.js", "sourceRoot": "", "sources": ["../../../src/credentials/accessTokenCache.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;AAqB/D;;;;;;GAMG;AACH,MAAM,OAAO,wBAAwB;IAInC;;;OAGG;IACH,YAAY,uBAA+B,oBAAoB;QANvD,gBAAW,GAAiB,SAAS,CAAC;QAO5C,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;IACnD,CAAC;IAED,cAAc,CAAC,WAAoC;QACjD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,cAAc;QACZ,IACE,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAC7E;YACA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;SAC9B;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken } from \"@azure/core-auth\";\n\n/**\n * Defines the default token refresh buffer duration.\n */\nexport const TokenRefreshBufferMs = 2 * 60 * 1000; // 2 Minutes\n\n/**\n * Provides a cache for an AccessToken that was that\n * was returned from a TokenCredential.\n */\nexport interface AccessTokenCache {\n  /**\n   * Sets the cached token.\n   *\n   * @param accessToken - The {@link AccessToken} to be cached or null to\n   *        clear the cached token.\n   */\n  setCachedToken(accessToken: AccessToken | undefined): void;\n\n  /**\n   * Returns the cached {@link AccessToken} or undefined if nothing is cached.\n   */\n  getCachedToken(): AccessToken | undefined;\n}\n\n/**\n * Provides an {@link AccessTokenCache} implementation which clears\n * the cached {@link AccessToken}'s after the expiresOnTimestamp has\n * passed.\n *\n * @deprecated No longer used in the bearer authorization policy.\n */\nexport class ExpiringAccessTokenCache implements AccessTokenCache {\n  private tokenRefreshBufferMs: number;\n  private cachedToken?: AccessToken = undefined;\n\n  /**\n   * Constructs an instance of {@link ExpiringAccessTokenCache} with\n   * an optional expiration buffer time.\n   */\n  constructor(tokenRefreshBufferMs: number = TokenRefreshBufferMs) {\n    this.tokenRefreshBufferMs = tokenRefreshBufferMs;\n  }\n\n  setCachedToken(accessToken: AccessToken | undefined): void {\n    this.cachedToken = accessToken;\n  }\n\n  getCachedToken(): AccessToken | undefined {\n    if (\n      this.cachedToken &&\n      Date.now() + this.tokenRefreshBufferMs >= this.cachedToken.expiresOnTimestamp\n    ) {\n      this.cachedToken = undefined;\n    }\n\n    return this.cachedToken;\n  }\n}\n"]}