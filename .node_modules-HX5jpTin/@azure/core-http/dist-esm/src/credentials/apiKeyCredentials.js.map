{"version": 3, "file": "apiKeyCredentials.js", "sourceRoot": "", "sources": ["../../../src/credentials/apiKeyCredentials.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAkB7C;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAU5B;;OAEG;IACH,YAAY,OAAgC;QAC1C,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClE,MAAM,IAAI,KAAK,CACb,0HAA0H,CAC3H,CAAC;SACH;QACD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACH,WAAW,CAAC,WAA4B;QACtC,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CAAC,uEAAuE,CAAC,CACnF,CAAC;SACH;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;gBACxB,WAAW,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;aACzC;YACD,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACtC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;aAChE;SACF;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;gBACpB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC,CAAC;aAC/E;YACD,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACpC,WAAW,CAAC,GAAG,IAAI,GAAG,CAAC;aACxB;YACD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC9B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAClC,WAAW,CAAC,GAAG,IAAI,GAAG,CAAC;iBACxB;gBACD,WAAW,CAAC,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;aAClD;SACF;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpHeaders } from \"../httpHeaders\";\nimport { WebResourceLike } from \"../webResource\";\nimport { ServiceClientCredentials } from \"./serviceClientCredentials\";\n\n/**\n * Describes the options to be provided while creating an instance of ApiKeyCredentials\n */\nexport interface ApiKeyCredentialOptions {\n  /**\n   * A key value pair of the header parameters that need to be applied to the request.\n   */\n  inHeader?: { [x: string]: any };\n  /**\n   * A key value pair of the query parameters that need to be applied to the request.\n   */\n  inQuery?: { [x: string]: any };\n}\n\n/**\n * Authenticates to a service using an API key.\n */\nexport class ApiKeyCredentials implements ServiceClientCredentials {\n  /**\n   * A key value pair of the header parameters that need to be applied to the request.\n   */\n  private readonly inHeader?: { [x: string]: any };\n  /**\n   * A key value pair of the query parameters that need to be applied to the request.\n   */\n  private readonly inQuery?: { [x: string]: any };\n\n  /**\n   * @param options - Specifies the options to be provided for auth. Either header or query needs to be provided.\n   */\n  constructor(options: ApiKeyCredentialOptions) {\n    if (!options || (options && !options.inHeader && !options.inQuery)) {\n      throw new Error(\n        `options cannot be null or undefined. Either \"inHeader\" or \"inQuery\" property of the options object needs to be provided.`\n      );\n    }\n    this.inHeader = options.inHeader;\n    this.inQuery = options.inQuery;\n  }\n\n  /**\n   * Signs a request with the values provided in the inHeader and inQuery parameter.\n   *\n   * @param webResource - The WebResourceLike to be signed.\n   * @returns The signed request object.\n   */\n  signRequest(webResource: WebResourceLike): Promise<WebResourceLike> {\n    if (!webResource) {\n      return Promise.reject(\n        new Error(`webResource cannot be null or undefined and must be of type \"object\".`)\n      );\n    }\n\n    if (this.inHeader) {\n      if (!webResource.headers) {\n        webResource.headers = new HttpHeaders();\n      }\n      for (const headerName in this.inHeader) {\n        webResource.headers.set(headerName, this.inHeader[headerName]);\n      }\n    }\n\n    if (this.inQuery) {\n      if (!webResource.url) {\n        return Promise.reject(new Error(`url cannot be null in the request object.`));\n      }\n      if (webResource.url.indexOf(\"?\") < 0) {\n        webResource.url += \"?\";\n      }\n      for (const key in this.inQuery) {\n        if (!webResource.url.endsWith(\"?\")) {\n          webResource.url += \"&\";\n        }\n        webResource.url += `${key}=${this.inQuery[key]}`;\n      }\n    }\n\n    return Promise.resolve(webResource);\n  }\n}\n"]}