{"version": 3, "file": "proxyAgent.js", "sourceRoot": "", "sources": ["../../src/proxyAgent.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAC;AAGjC,OAAO,EAAE,UAAU,EAAE,MAAM,OAAO,CAAC;AAInC,MAAM,UAAU,gBAAgB,CAC9B,UAAkB,EAClB,aAA4B,EAC5B,OAAyB;IAEzB,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,EAAY,CAAC;IACtE,IAAI,CAAC,IAAI,EAAE;QACT,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;KAClE;IACD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;QACpC,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;KAChG;IACD,MAAM,aAAa,GAAiC;QAClD,KAAK,EAAE;YACL,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,OAAO,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE;SACjD;KACF,CAAC;IAEF,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,EAAE;QACpD,aAAa,CAAC,KAAM,CAAC,SAAS,GAAG,GAAG,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;KACxF;SAAM,IAAI,aAAa,CAAC,QAAQ,EAAE;QACjC,aAAa,CAAC,KAAM,CAAC,SAAS,GAAG,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;KAC9D;IAED,MAAM,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;IAC9C,MAAM,YAAY,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAEpD,MAAM,UAAU,GAAG;QACjB,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,YAAY,CAAC,cAAc,EAAE,YAAY,EAAE,aAAa,CAAC;KACjE,CAAC;IAEF,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,GAAW;IACpC,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC;IAC1D,OAAO,SAAS,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC;AAC7C,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,cAAuB,EACvB,YAAqB,EACrB,aAA2C;IAE3C,IAAI,cAAc,IAAI,YAAY,EAAE;QAClC,OAAO,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;KAC7C;SAAM,IAAI,cAAc,IAAI,CAAC,YAAY,EAAE;QAC1C,OAAO,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;KAC5C;SAAM,IAAI,CAAC,cAAc,IAAI,YAAY,EAAE;QAC1C,OAAO,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;KAC5C;SAAM;QACL,OAAO,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;KAC3C;AACH,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAC/B,sFAAsF;IACtF,oFAAoF;IACpF,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC;AACpC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport * as tunnel from \"tunnel\";\n\nimport { ProxySettings } from \"./serviceClient\";\nimport { URLBuilder } from \"./url\";\nimport { HttpHeadersLike } from \"./httpHeaders\";\n\nexport type ProxyAgent = { isHttps: boolean; agent: http.Agent | https.Agent };\nexport function createProxyAgent(\n  requestUrl: string,\n  proxySettings: ProxySettings,\n  headers?: HttpHeadersLike\n): ProxyAgent {\n  const host = URLBuilder.parse(proxySettings.host).getHost() as string;\n  if (!host) {\n    throw new Error(\"Expecting a non-empty host in proxy settings.\");\n  }\n  if (!isValidPort(proxySettings.port)) {\n    throw new Error(\"Expecting a valid port number in the range of [0, 65535] in proxy settings.\");\n  }\n  const tunnelOptions: tunnel.HttpsOverHttpsOptions = {\n    proxy: {\n      host: host,\n      port: proxySettings.port,\n      headers: (headers && headers.rawHeaders()) || {}\n    }\n  };\n\n  if (proxySettings.username && proxySettings.password) {\n    tunnelOptions.proxy!.proxyAuth = `${proxySettings.username}:${proxySettings.password}`;\n  } else if (proxySettings.username) {\n    tunnelOptions.proxy!.proxyAuth = `${proxySettings.username}`;\n  }\n\n  const isRequestHttps = isUrlHttps(requestUrl);\n  const isProxyHttps = isUrlHttps(proxySettings.host);\n\n  const proxyAgent = {\n    isHttps: isRequestHttps,\n    agent: createTunnel(isRequestHttps, isProxyHttps, tunnelOptions)\n  };\n\n  return proxyAgent;\n}\n\nexport function isUrlHttps(url: string): boolean {\n  const urlScheme = URLBuilder.parse(url).getScheme() || \"\";\n  return urlScheme.toLowerCase() === \"https\";\n}\n\nexport function createTunnel(\n  isRequestHttps: boolean,\n  isProxyHttps: boolean,\n  tunnelOptions: tunnel.HttpsOverHttpsOptions\n): http.Agent | https.Agent {\n  if (isRequestHttps && isProxyHttps) {\n    return tunnel.httpsOverHttps(tunnelOptions);\n  } else if (isRequestHttps && !isProxyHttps) {\n    return tunnel.httpsOverHttp(tunnelOptions);\n  } else if (!isRequestHttps && isProxyHttps) {\n    return tunnel.httpOverHttps(tunnelOptions);\n  } else {\n    return tunnel.httpOverHttp(tunnelOptions);\n  }\n}\n\nfunction isValidPort(port: number): boolean {\n  // any port in 0-65535 range is valid (RFC 793) even though almost all implementations\n  // will reserve 0 for a specific purpose, and a range of numbers for ephemeral ports\n  return 0 <= port && port <= 65535;\n}\n"]}