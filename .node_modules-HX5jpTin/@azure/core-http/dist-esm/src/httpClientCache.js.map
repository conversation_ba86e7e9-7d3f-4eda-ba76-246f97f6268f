{"version": 3, "file": "httpClientCache.js", "sourceRoot": "", "sources": ["../../src/httpClientCache.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,IAAI,gBAAwC,CAAC;AAE7C,MAAM,UAAU,0BAA0B;IACxC,IAAI,CAAC,gBAAgB,EAAE;QACrB,gBAAgB,GAAG,IAAI,iBAAiB,EAAE,CAAC;KAC5C;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpClient } from \"./httpClient\";\nimport { DefaultHttpClient } from \"./defaultHttpClient\";\n\nlet cachedHttpClient: HttpClient | undefined;\n\nexport function getCachedDefaultHttpClient(): HttpClient {\n  if (!cachedHttpClient) {\n    cachedHttpClient = new DefaultHttpClient();\n  }\n\n  return cachedHttpClient;\n}\n"]}