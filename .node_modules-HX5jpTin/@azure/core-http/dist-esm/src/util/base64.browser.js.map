{"version": 3, "file": "base64.browser.js", "sourceRoot": "", "sources": ["../../../src/util/base64.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa;IACxC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,KAAiB;IAC/C,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;IACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa;IACxC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC1C,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KACnC;IACD,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Encodes a string in base64 format.\n * @param value - The string to encode\n */\nexport function encodeString(value: string): string {\n  return btoa(value);\n}\n\n/**\n * Encodes a byte array in base64 format.\n * @param value - The Uint8Aray to encode\n */\nexport function encodeByteArray(value: Uint8Array): string {\n  let str = \"\";\n  for (let i = 0; i < value.length; i++) {\n    str += String.fromCharCode(value[i]);\n  }\n  return btoa(str);\n}\n\n/**\n * Decodes a base64 string into a byte array.\n * @param value - The base64 string to decode\n */\nexport function decodeString(value: string): Uint8Array {\n  const byteString = atob(value);\n  const arr = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    arr[i] = byteString.charCodeAt(i);\n  }\n  return arr;\n}\n"]}