{"version": 3, "file": "exponentialBackoffStrategy.js", "sourceRoot": "", "sources": ["../../../src/util/exponentialBackoffStrategy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,MAAM,CAAC,MAAM,0BAA0B,GAAG,CAAC,CAAC;AAC5C,sBAAsB;AACtB,MAAM,CAAC,MAAM,6BAA6B,GAAG,IAAI,GAAG,EAAE,CAAC;AACvD,MAAM,CAAC,MAAM,iCAAiC,GAAG,IAAI,GAAG,EAAE,CAAC;AAC3D,MAAM,CAAC,MAAM,iCAAiC,GAAG,IAAI,GAAG,CAAC,CAAC;AAE1D,MAAM,UAAU,QAAQ,CAAC,CAAU;IACjC,OAAO,OAAO,CAAC,KAAK,QAAQ,CAAC;AAC/B,CAAC;AAaD;;;;;;;;GAQG;AACH,MAAM,UAAU,WAAW,CACzB,UAAkB,EAClB,SAA4E,EAC5E,SAAoB,EACpB,QAAgC,EAChC,KAAkB;IAElB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;QAC/B,OAAO,KAAK,CAAC;KACd;IAED,OAAO,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;AAC3C,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,eAAe,CAC7B,YAA2F,EAC3F,YAAuB,EAAE,UAAU,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,EAC1D,GAAgB;IAEhB,IAAI,GAAG,EAAE;QACP,IAAI,SAAS,CAAC,KAAK,EAAE;YACnB,GAAG,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC;SAClC;QAED,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC;KACvB;IAED,qBAAqB;IACrB,SAAS,CAAC,UAAU,EAAE,CAAC;IAEvB,wBAAwB;IACxB,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/D,MAAM,gBAAgB,GACpB,YAAY,CAAC,aAAa,GAAG,GAAG;QAChC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,YAAY,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC;IACjE,cAAc,IAAI,gBAAgB,CAAC;IAEnC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAChC,YAAY,CAAC,gBAAgB,GAAG,cAAc,EAC9C,YAAY,CAAC,gBAAgB,CAC9B,CAAC;IAEF,OAAO,SAAS,CAAC;AACnB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { HttpOperationResponse } from \"../coreHttp\";\n\nexport const DEFAULT_CLIENT_RETRY_COUNT = 3;\n// intervals are in ms\nexport const DEFAULT_CLIENT_RETRY_INTERVAL = 1000 * 30;\nexport const DEFAULT_CLIENT_MAX_RETRY_INTERVAL = 1000 * 90;\nexport const DEFAULT_CLIENT_MIN_RETRY_INTERVAL = 1000 * 3;\n\nexport function isNumber(n: unknown): n is number {\n  return typeof n === \"number\";\n}\nexport interface RetryData {\n  retryCount: number;\n  retryInterval: number;\n  error?: RetryError;\n}\n\nexport interface RetryError extends Error {\n  message: string;\n  code?: string;\n  innerError?: RetryError;\n}\n\n/**\n * @internal\n * Determines if the operation should be retried.\n *\n * @param retryLimit - Specifies the max number of retries.\n * @param predicate - Initial chekck on whether to retry based on given responses or errors\n * @param retryData -  The retry data.\n * @returns True if the operation qualifies for a retry; false otherwise.\n */\nexport function shouldRetry(\n  retryLimit: number,\n  predicate: (response?: HttpOperationResponse, error?: RetryError) => boolean,\n  retryData: RetryData,\n  response?: HttpOperationResponse,\n  error?: RetryError\n): boolean {\n  if (!predicate(response, error)) {\n    return false;\n  }\n\n  return retryData.retryCount < retryLimit;\n}\n\n/**\n * @internal\n * Updates the retry data for the next attempt.\n *\n * @param retryOptions - specifies retry interval, and its lower bound and upper bound.\n * @param retryData -  The retry data.\n * @param err - The operation\"s error, if any.\n */\nexport function updateRetryData(\n  retryOptions: { retryInterval: number; minRetryInterval: number; maxRetryInterval: number },\n  retryData: RetryData = { retryCount: 0, retryInterval: 0 },\n  err?: RetryError\n): RetryData {\n  if (err) {\n    if (retryData.error) {\n      err.innerError = retryData.error;\n    }\n\n    retryData.error = err;\n  }\n\n  // Adjust retry count\n  retryData.retryCount++;\n\n  // Adjust retry interval\n  let incrementDelta = Math.pow(2, retryData.retryCount - 1) - 1;\n  const boundedRandDelta =\n    retryOptions.retryInterval * 0.8 +\n    Math.floor(Math.random() * (retryOptions.retryInterval * 0.4));\n  incrementDelta *= boundedRandDelta;\n\n  retryData.retryInterval = Math.min(\n    retryOptions.minRetryInterval + incrementDelta,\n    retryOptions.maxRetryInterval\n  );\n\n  return retryData;\n}\n"]}