{"version": 3, "file": "base64.js", "sourceRoot": "", "sources": ["../../../src/util/base64.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC/C,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,KAAiB;IAC/C,kGAAkG;IAClG,mGAAmG;IACnG,MAAM,WAAW,GAAG,KAAK,YAAY,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;IAC/F,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACtC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Encodes a string in base64 format.\n * @param value - The string to encode\n */\nexport function encodeString(value: string): string {\n  return Buffer.from(value).toString(\"base64\");\n}\n\n/**\n * Encodes a byte array in base64 format.\n * @param value - The Uint8Aray to encode\n */\nexport function encodeByteArray(value: Uint8Array): string {\n  // Buffer.from accepts <ArrayBuffer> | <SharedArrayBuffer>-- the TypeScript definition is off here\n  // https://nodejs.org/api/buffer.html#buffer_class_method_buffer_from_arraybuffer_byteoffset_length\n  const bufferValue = value instanceof Buffer ? value : Buffer.from(value.buffer as ArrayBuffer);\n  return bufferValue.toString(\"base64\");\n}\n\n/**\n * Decodes a base64 string into a byte array.\n * @param value - The base64 string to decode\n */\nexport function decodeString(value: string): Uint8Array {\n  return Buffer.from(value, \"base64\");\n}\n"]}