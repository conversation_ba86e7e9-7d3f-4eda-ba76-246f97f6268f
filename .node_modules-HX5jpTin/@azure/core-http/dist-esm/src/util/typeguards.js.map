{"version": 3, "file": "typeguards.js", "sourceRoot": "", "sources": ["../../../src/util/typeguards.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;GAIG;AACH,MAAM,UAAU,SAAS,CAAI,KAA2B;IACtD,OAAO,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC;AACxD,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Helper TypeGuard that checks if the value is not null or undefined.\n * @param thing - Anything\n * @internal\n */\nexport function isDefined<T>(thing: T | undefined | null): thing is T {\n  return typeof thing !== \"undefined\" && thing !== null;\n}\n"]}