{"version": 3, "file": "coreHttp.js", "sourceRoot": "", "sources": ["../../src/coreHttp.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,wEAAwE;AACxE,yCAAyC;AAEzC,OAAO,EACL,WAAW,EAQZ,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,OAAO,EAAc,WAAW,EAAmC,MAAM,eAAe,CAAC;AAGzF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,OAAO,EAGL,oCAAoC,EACrC,MAAM,oBAAoB,CAAC;AAS5B,OAAO,EACL,aAAa,EAEb,eAAe,EACf,yBAAyB,EAG1B,MAAM,iBAAiB,CAAC;AAEzB,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,+BAA+B,EAAE,MAAM,4CAA4C,CAAC;AAC7F,OAAO,EAAoB,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EACL,iBAAiB,EAGjB,oBAAoB,EAErB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,6BAA6B,EAAE,MAAM,0CAA0C,CAAC;AACzF,OAAO,EAAE,sBAAsB,EAAgB,SAAS,EAAE,MAAM,mCAAmC,CAAC;AACpG,OAAO,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAC;AAC3E,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AACzE,OAAO,EAAE,uBAAuB,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAC9E,OAAO,EAAE,cAAc,EAAmB,MAAM,2BAA2B,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAoB,MAAM,4BAA4B,CAAC;AAC/E,OAAO,EAAE,kCAAkC,EAAE,MAAM,+CAA+C,CAAC;AACnG,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EACL,eAAe,EACf,wBAAwB,EAGzB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EACL,qBAAqB,EAErB,uBAAuB,EAExB,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EAAE,aAAa,EAAwB,MAAM,0BAA0B,CAAC;AAC/E,OAAO,EACL,UAAU,EAcV,UAAU,EAEV,eAAe,EAChB,MAAM,cAAc,CAAC;AACtB,OAAO,EACL,YAAY,EACZ,aAAa,EACb,2BAA2B,EAC3B,YAAY,EACZ,SAAS,EAET,iBAAiB,EACjB,wBAAwB,EACxB,WAAW,EACX,WAAW,EACX,MAAM,EACN,UAAU,EACX,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAE7C,OAAO,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AACrC,mGAAmG;AACnG,OAAO,EAAE,kBAAkB,EAAc,MAAM,oBAAoB,CAAC;AAEpE,cAAc;AACd,OAAO,EAAiD,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACpG,OAAO,EAAoB,wBAAwB,EAAE,MAAM,gCAAgC,CAAC;AAC5F,OAAO,EAAE,oBAAoB,EAAE,MAAM,oCAAoC,CAAC;AAC1E,OAAO,EAAE,8BAA8B,EAAE,MAAM,8CAA8C,CAAC;AAC9F,OAAO,EAAE,iBAAiB,EAA2B,MAAM,iCAAiC,CAAC;AAE7F,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAGlE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,YAAY,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,WAAW,EAAqB,MAAM,0BAA0B,CAAC;AACvF,OAAO,oCAAoC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/* eslint-disable-next-line @typescript-eslint/triple-slash-reference */\n/// <reference path=\"../dom-shim.d.ts\" />\n\nexport {\n  WebResource,\n  WebResourceLike,\n  HttpRequestBody,\n  RequestPrepareOptions,\n  HttpMethods,\n  ParameterValue,\n  RequestOptionsBase,\n  TransferProgressEvent\n} from \"./webResource\";\nexport { DefaultHttpClient } from \"./defaultHttpClient\";\nexport { HttpClient } from \"./httpClient\";\nexport { HttpHeader, HttpHeaders, HttpHeadersLike, RawHttpHeaders } from \"./httpHeaders\";\nexport { HttpOperationResponse, HttpResponse, RestResponse } from \"./httpOperationResponse\";\nexport { HttpPipelineLogger } from \"./httpPipelineLogger\";\nexport { HttpPipelineLogLevel } from \"./httpPipelineLogLevel\";\nexport { RestError } from \"./restError\";\nexport { OperationArguments } from \"./operationArguments\";\nexport {\n  OperationOptions,\n  OperationRequestOptions,\n  operationOptionsToRequestOptionsBase\n} from \"./operationOptions\";\nexport {\n  OperationParameter,\n  OperationQueryParameter,\n  OperationURLParameter,\n  ParameterPath\n} from \"./operationParameter\";\nexport { OperationResponse } from \"./operationResponse\";\nexport { OperationSpec } from \"./operationSpec\";\nexport {\n  ServiceClient,\n  ServiceClientOptions,\n  flattenResponse,\n  createPipelineFromOptions,\n  ProxySettings,\n  ProxyOptions\n} from \"./serviceClient\";\nexport { PipelineOptions, InternalPipelineOptions } from \"./pipelineOptions\";\nexport { QueryCollectionFormat } from \"./queryCollectionFormat\";\nexport { Constants } from \"./util/constants\";\nexport { bearerTokenAuthenticationPolicy } from \"./policies/bearerTokenAuthenticationPolicy\";\nexport { LogPolicyOptions, logPolicy } from \"./policies/logPolicy\";\nexport {\n  BaseRequestPolicy,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions,\n  RequestPolicyOptionsLike\n} from \"./policies/requestPolicy\";\nexport { generateClientRequestIdPolicy } from \"./policies/generateClientRequestIdPolicy\";\nexport { exponentialRetryPolicy, RetryOptions, RetryMode } from \"./policies/exponentialRetryPolicy\";\nexport { systemErrorRetryPolicy } from \"./policies/systemErrorRetryPolicy\";\nexport { throttlingRetryPolicy } from \"./policies/throttlingRetryPolicy\";\nexport { getDefaultProxySettings, proxyPolicy } from \"./policies/proxyPolicy\";\nexport { redirectPolicy, RedirectOptions } from \"./policies/redirectPolicy\";\nexport { keepAlivePolicy, KeepAliveOptions } from \"./policies/keepAlivePolicy\";\nexport { disableResponseDecompressionPolicy } from \"./policies/disableResponseDecompressionPolicy\";\nexport { signingPolicy } from \"./policies/signingPolicy\";\nexport {\n  userAgentPolicy,\n  getDefaultUserAgentValue,\n  UserAgentOptions,\n  TelemetryInfo\n} from \"./policies/userAgentPolicy\";\nexport {\n  deserializationPolicy,\n  DeserializationOptions,\n  deserializeResponseBody,\n  DeserializationContentTypes\n} from \"./policies/deserializationPolicy\";\nexport { tracingPolicy, TracingPolicyOptions } from \"./policies/tracingPolicy\";\nexport {\n  MapperType,\n  SimpleMapperType,\n  CompositeMapperType,\n  DictionaryMapperType,\n  SequenceMapperType,\n  EnumMapperType,\n  Mapper,\n  BaseMapper,\n  CompositeMapper,\n  SequenceMapper,\n  DictionaryMapper,\n  EnumMapper,\n  MapperConstraints,\n  PolymorphicDiscriminator,\n  Serializer,\n  UrlParameterValue,\n  serializeObject\n} from \"./serializer\";\nexport {\n  stripRequest,\n  stripResponse,\n  executePromisesSequentially,\n  generateUuid,\n  encodeUri,\n  ServiceCallback,\n  promiseToCallback,\n  promiseToServiceCallback,\n  isValidUuid,\n  applyMixins,\n  isNode,\n  isDuration\n} from \"./util/utils\";\nexport { URLBuilder, URLQuery } from \"./url\";\nexport { AbortSignalLike } from \"@azure/abort-controller\";\nexport { delay } from \"./util/delay\";\n// legacy exports. Use core-tracing instead (and remove on next major version update of core-http).\nexport { createSpanFunction, SpanConfig } from \"./createSpanLegacy\";\n\n// Credentials\nexport { TokenCredential, GetTokenOptions, AccessToken, isTokenCredential } from \"@azure/core-auth\";\nexport { AccessTokenCache, ExpiringAccessTokenCache } from \"./credentials/accessTokenCache\";\nexport { AccessTokenRefresher } from \"./credentials/accessTokenRefresher\";\nexport { BasicAuthenticationCredentials } from \"./credentials/basicAuthenticationCredentials\";\nexport { ApiKeyCredentials, ApiKeyCredentialOptions } from \"./credentials/apiKeyCredentials\";\nexport { ServiceClientCredentials } from \"./credentials/serviceClientCredentials\";\nexport { TopicCredentials } from \"./credentials/topicCredentials\";\nexport { Authenticator } from \"./credentials/credentials\";\n\nexport { parseXML, stringifyXML } from \"./util/xml\";\nexport { XML_ATTRKEY, XML_CHARKEY, SerializerOptions } from \"./util/serializer.common\";\nimport \"@azure/core-asynciterator-polyfill\";\n"]}