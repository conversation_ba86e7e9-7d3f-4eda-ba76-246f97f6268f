{"version": 3, "file": "serializer.js", "sourceRoot": "", "sources": ["../../src/serializer.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,2BAA2B;AAE3B,OAAO,KAAK,MAAM,MAAM,eAAe,CAAC;AACxC,OAAO,KAAK,KAAK,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAqB,MAAM,0BAA0B,CAAC;AAEvF,MAAM,OAAO,UAAU;IACrB,YACkB,eAAuC,EAAE,EACzC,KAAe;QADf,iBAAY,GAAZ,YAAY,CAA6B;QACzC,UAAK,GAAL,KAAK,CAAU;IAC9B,CAAC;IAEJ,mBAAmB,CAAC,MAAc,EAAE,KAAc,EAAE,UAAkB;QACpE,MAAM,cAAc,GAAG,CACrB,cAAuC,EACvC,eAAoB,EACb,EAAE;YACT,MAAM,IAAI,KAAK,CACb,IAAI,UAAU,iBAAiB,KAAK,oCAAoC,cAAc,MAAM,eAAe,GAAG,CAC/G,CAAC;QACJ,CAAC,CAAC;QACF,IAAI,MAAM,CAAC,WAAW,IAAI,KAAK,IAAI,SAAS,EAAE;YAC5C,MAAM,aAAa,GAAG,KAAe,CAAC;YACtC,MAAM,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,OAAO,EACP,WAAW,EACZ,GAAG,MAAM,CAAC,WAAW,CAAC;YACvB,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,IAAI,gBAAgB,EAAE;gBACtE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACtD;YACD,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,IAAI,gBAAgB,EAAE;gBACtE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACtD;YACD,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,GAAG,gBAAgB,EAAE;gBACrE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACtD;YACD,IAAI,gBAAgB,IAAI,SAAS,IAAI,aAAa,GAAG,gBAAgB,EAAE;gBACrE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;aACtD;YACD,MAAM,YAAY,GAAG,KAAc,CAAC;YACpC,IAAI,QAAQ,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,QAAQ,EAAE;gBAC3D,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aACtC;YACD,IAAI,SAAS,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,SAAS,EAAE;gBAC7D,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;aACxC;YACD,IAAI,QAAQ,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,QAAQ,EAAE;gBAC3D,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aACtC;YACD,IAAI,SAAS,IAAI,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,SAAS,EAAE;gBAC7D,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;aACxC;YACD,IAAI,UAAU,IAAI,SAAS,IAAI,aAAa,GAAG,UAAU,KAAK,CAAC,EAAE;gBAC/D,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;aAC1C;YACD,IAAI,OAAO,EAAE;gBACX,MAAM,OAAO,GAAW,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACpF,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;oBAC9D,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;iBACpC;aACF;YACD,IACE,WAAW;gBACX,YAAY,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,CAAS,EAAE,EAAc,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EACnF;gBACA,cAAc,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;aAC5C;SACF;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,SAAS,CACP,MAAc,EACd,MAAe,EACf,UAAmB,EACnB,UAA6B,EAAE;;QAE/B,MAAM,cAAc,GAAgC;YAClD,QAAQ,EAAE,MAAA,OAAO,CAAC,QAAQ,mCAAI,EAAE;YAChC,WAAW,EAAE,MAAA,OAAO,CAAC,WAAW,mCAAI,KAAK;YACzC,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,WAAW;SAC9C,CAAC;QACF,IAAI,OAAO,GAAQ,EAAE,CAAC;QACtB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAc,CAAC;QAC9C,IAAI,CAAC,UAAU,EAAE;YACf,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;SACrC;QACD,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YAC5C,OAAO,GAAG,EAAE,CAAC;SACd;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;SAC9B;QAED,mDAAmD;QACnD,sDAAsD;QACtD,mDAAmD;QACnD,wBAAwB;QACxB,iCAAiC;QACjC,0CAA0C;QAC1C,0CAA0C;QAC1C,qCAAqC;QACrC,0CAA0C;QAE1C,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;QAEtC,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,uBAAuB,CAAC,CAAC;SACvD;QACD,IAAI,QAAQ,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,SAAS,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,+BAA+B,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,EAAE;YACtD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,kBAAkB,CAAC,CAAC;SAClD;QAED,IAAI,MAAM,IAAI,SAAS,EAAE;YACvB,OAAO,GAAG,MAAM,CAAC;SAClB;aAAM;YACL,8BAA8B;YAC9B,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;YACrD,IAAI,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;gBACvC,OAAO,GAAG,MAAM,CAAC;aAClB;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,+CAA+C,CAAC,KAAK,IAAI,EAAE;gBACrF,OAAO,GAAG,mBAAmB,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;aAC/D;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;gBAC/C,MAAM,UAAU,GAAe,MAAoB,CAAC;gBACpD,OAAO,GAAG,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;aAChF;iBAAM,IACL,UAAU,CAAC,KAAK,CAAC,sDAAsD,CAAC,KAAK,IAAI,EACjF;gBACA,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;aAC9D;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAoB,CAAC,CAAC;aACpE;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAoB,CAAC,CAAC;aACpE;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;gBACnD,OAAO,GAAG,qBAAqB,CAC7B,IAAI,EACJ,MAAwB,EACxB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;aACH;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;gBACrD,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAA0B,EAC1B,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;aACH;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,sBAAsB,CAC9B,IAAI,EACJ,MAAyB,EACzB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;aACH;SACF;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACH,WAAW,CACT,MAAc,EACd,YAAqB,EACrB,UAAkB,EAClB,UAA6B,EAAE;;QAE/B,MAAM,cAAc,GAAgC;YAClD,QAAQ,EAAE,MAAA,OAAO,CAAC,QAAQ,mCAAI,EAAE;YAChC,WAAW,EAAE,MAAA,OAAO,CAAC,WAAW,mCAAI,KAAK;YACzC,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,WAAW;SAC9C,CAAC;QACF,IAAI,YAAY,IAAI,SAAS,EAAE;YAC7B,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACzE,sEAAsE;gBACtE,qDAAqD;gBACrD,qEAAqE;gBACrE,YAAY,GAAG,EAAE,CAAC;aACnB;YACD,+FAA+F;YAC/F,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE;gBACrC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;aACpC;YACD,OAAO,YAAY,CAAC;SACrB;QAED,IAAI,OAAY,CAAC;QACjB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,UAAU,EAAE;YACf,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;SACrC;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;YAC7C,OAAO,GAAG,wBAAwB,CAChC,IAAI,EACJ,MAAyB,EACzB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;SACH;aAAM;YACL,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;gBAC7C,MAAM,gBAAgB,GAAG,YAAuC,CAAC;gBACjE;;;;mBAIG;gBACH,IACE,gBAAgB,CAAC,WAAW,CAAC,IAAI,SAAS;oBAC1C,gBAAgB,CAAC,UAAU,CAAC,IAAI,SAAS,EACzC;oBACA,YAAY,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;iBAC7C;aACF;YAED,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;gBAC1C,OAAO,GAAG,UAAU,CAAC,YAAsB,CAAC,CAAC;gBAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;oBAClB,OAAO,GAAG,YAAY,CAAC;iBACxB;aACF;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;gBAClD,IAAI,YAAY,KAAK,MAAM,EAAE;oBAC3B,OAAO,GAAG,IAAI,CAAC;iBAChB;qBAAM,IAAI,YAAY,KAAK,OAAO,EAAE;oBACnC,OAAO,GAAG,KAAK,CAAC;iBACjB;qBAAM;oBACL,OAAO,GAAG,YAAY,CAAC;iBACxB;aACF;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,kDAAkD,CAAC,KAAK,IAAI,EAAE;gBACxF,OAAO,GAAG,YAAY,CAAC;aACxB;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,oCAAoC,CAAC,KAAK,IAAI,EAAE;gBAC1E,OAAO,GAAG,IAAI,IAAI,CAAC,YAAsB,CAAC,CAAC;aAC5C;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;gBACnD,OAAO,GAAG,cAAc,CAAC,YAAsB,CAAC,CAAC;aAClD;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC,YAAsB,CAAC,CAAC;aACvD;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO,GAAG,oBAAoB,CAAC,YAAsB,CAAC,CAAC;aACxD;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;gBACnD,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAAwB,EACxB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;aACH;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;gBACrD,OAAO,GAAG,yBAAyB,CACjC,IAAI,EACJ,MAA0B,EAC1B,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;aACH;SACF;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC;SAC/B;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AAED,SAAS,OAAO,CAAC,GAAW,EAAE,EAAU;IACtC,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACrB,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QAC1C,EAAE,GAAG,CAAC;KACP;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAW;IACpC,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,CAAC,CAAC,MAAM,YAAY,UAAU,CAAC,EAAE;QACnC,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;KAC5F;IACD,wBAAwB;IACxB,MAAM,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC3C,uBAAuB;IACvB,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;SACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,oBAAoB,CAAC,GAAW;IACvC,IAAI,CAAC,GAAG,EAAE;QACR,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;QAC5C,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;KACxF;IACD,uBAAuB;IACvB,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAChD,wBAAwB;IACxB,OAAO,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAwB;IAClD,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,IAAI,EAAE;QACR,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEjC,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;YAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;gBACzC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;aACvD;iBAAM;gBACL,YAAY,IAAI,IAAI,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3B,YAAY,GAAG,EAAE,CAAC;aACnB;SACF;KACF;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,cAAc,CAAC,CAAgB;IACtC,IAAI,CAAC,CAAC,EAAE;QACN,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;QACnC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAW,CAAC,CAAC;KAC3B;IACD,OAAO,IAAI,CAAC,KAAK,CAAE,CAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,cAAc,CAAC,CAAS;IAC/B,IAAI,CAAC,CAAC,EAAE;QACN,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAgB,EAAE,UAAkB,EAAE,KAAU;IAC3E,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;QACzC,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YACxC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,eAAe,KAAK,0BAA0B,CAAC,CAAC;aAC9E;SACF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,IAAI,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;gBACvC,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,gBAAgB,KAAK,2BAA2B,CAAC,CAAC;aAChF;SACF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC7C,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtE,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,gBAAgB,KAAK,4CAA4C,CAC/E,CAAC;aACH;SACF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;YAChD,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;gBAC9B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,eAAe,KAAK,2BAA2B,CAAC,CAAC;aAC/E;SACF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,MAAM,UAAU,GAAG,OAAO,KAAK,CAAC;YAChC,IACE,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,UAAU;gBACzB,CAAC,CAAC,KAAK,YAAY,WAAW,CAAC;gBAC/B,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC1B,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,EACpF;gBACA,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,uGAAuG,CACrH,CAAC;aACH;SACF;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAkB,EAAE,aAAyB,EAAE,KAAU;IAClF,IAAI,CAAC,aAAa,EAAE;QAClB,MAAM,IAAI,KAAK,CACb,qDAAqD,UAAU,mBAAmB,CACnF,CAAC;KACH;IACD,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;QAC5C,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACtC,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC;SACnD;QACD,OAAO,IAAI,KAAK,KAAK,CAAC;IACxB,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,KAAK,CACb,GAAG,KAAK,6BAA6B,UAAU,2BAA2B,IAAI,CAAC,SAAS,CACtF,aAAa,CACd,GAAG,CACL,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAiB;IACnE,IAAI,WAAW,GAAW,EAAE,CAAC;IAC7B,IAAI,KAAK,IAAI,SAAS,EAAE;QACtB,IAAI,CAAC,CAAC,KAAK,YAAY,UAAU,CAAC,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,8BAA8B,CAAC,CAAC;SAC9D;QACD,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;KAC7C;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAiB;IACnE,IAAI,WAAW,GAAW,EAAE,CAAC;IAC7B,IAAI,KAAK,IAAI,SAAS,EAAE;QACtB,IAAI,CAAC,CAAC,KAAK,YAAY,UAAU,CAAC,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,8BAA8B,CAAC,CAAC;SAC9D;QACD,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;KAC9C;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB,EAAE,KAAU,EAAE,UAAkB;IAC1E,IAAI,KAAK,IAAI,SAAS,EAAE;QACtB,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YACtC,IACE,CAAC,CACC,KAAK,YAAY,IAAI;gBACrB,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;gBACA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,4DAA4D,CAAC,CAAC;aAC5F;YACD,KAAK;gBACH,KAAK,YAAY,IAAI;oBACnB,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;oBACtC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;SACtD;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YACjD,IACE,CAAC,CACC,KAAK,YAAY,IAAI;gBACrB,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;gBACA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,4DAA4D,CAAC,CAAC;aAC5F;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;SACrF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE;YACxD,IACE,CAAC,CACC,KAAK,YAAY,IAAI;gBACrB,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;gBACA,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,6DAA6D,CAAC,CAAC;aAC7F;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;SACrF;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YACjD,IACE,CAAC,CACC,KAAK,YAAY,IAAI;gBACrB,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD;gBACA,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,qEAAqE;oBAChF,mDAAmD,CACtD,CAAC;aACH;YACD,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;SAC/B;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;YACjD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;gBAC5B,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,sDAAsD,KAAK,IAAI,CAC7E,CAAC;aACH;SACF;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAsB,EACtB,MAAsB,EACtB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAoC;IAEpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,yBAAyB,CAAC,CAAC;KACzD;IACD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IACxC,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACnD,MAAM,IAAI,KAAK,CACb,wDAAwD;YACtD,0CAA0C,UAAU,GAAG,CAC1D,CAAC;KACH;IACD,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAE1F,IAAI,KAAK,IAAI,WAAW,CAAC,YAAY,EAAE;YACrC,MAAM,QAAQ,GAAG,WAAW,CAAC,kBAAkB;gBAC7C,CAAC,CAAC,SAAS,WAAW,CAAC,kBAAkB,EAAE;gBAC3C,CAAC,CAAC,OAAO,CAAC;YACZ,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;gBACzC,SAAS,CAAC,CAAC,CAAC,qBAAQ,eAAe,CAAE,CAAC;gBACtC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,YAAY,EAAE,CAAC;aACtE;iBAAM;gBACL,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBAClB,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;gBACnD,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,YAAY,EAAE,CAAC;aACtE;SACF;aAAM;YACL,SAAS,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;SAChC;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAwB,EACxB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAoC;IAEpC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,0BAA0B,CAAC,CAAC;KAC1D;IACD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;IACpC,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QAC/C,MAAM,IAAI,KAAK,CACb,2DAA2D;YACzD,0CAA0C,UAAU,GAAG,CAC1D,CAAC;KACH;IACD,MAAM,cAAc,GAA2B,EAAE,CAAC;IAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACrC,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC1F,gFAAgF;QAChF,cAAc,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,SAAS,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;KACrF;IAED,kDAAkD;IAClD,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;QAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;QAE5F,MAAM,MAAM,GAAG,cAAc,CAAC;QAC9B,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC;QAC1D,OAAO,MAAM,CAAC;KACf;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;GAKG;AACH,SAAS,2BAA2B,CAClC,UAAsB,EACtB,MAAuB,EACvB,UAAkB;IAElB,MAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;IAE9D,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;QAClD,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,OAAO,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,CAAC,oBAAoB,CAAC;KAC/C;IAED,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED;;;;;GAKG;AACH,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAuB,EACvB,UAAkB;IAElB,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;IACxC,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,KAAK,CACb,yBAAyB,UAAU,oCAAoC,IAAI,CAAC,SAAS,CACnF,MAAM,EACN,SAAS,EACT,CAAC,CACF,IAAI,CACN,CAAC;KACH;IAED,OAAO,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC;AAED;;;;GAIG;AACH,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,UAAkB;IAElB,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;IAC7C,IAAI,CAAC,UAAU,EAAE;QACf,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,mDAAmD,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC;SAC/F;QACD,UAAU,GAAG,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,KAAK,CACb,qDAAqD;gBACnD,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,cACpC,MAAM,CAAC,IAAI,CAAC,SACd,iBAAiB,UAAU,IAAI,CAClC,CAAC;SACH;KACF;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAoC;IAEpC,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;QAC9D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;KACzE;IAED,IAAI,MAAM,IAAI,SAAS,EAAE;QACvB,MAAM,OAAO,GAAQ,EAAE,CAAC;QACxB,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC1E,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACzC,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,cAAc,CAAC,QAAQ,EAAE;gBAC3B,SAAS;aACV;YAED,IAAI,QAA4B,CAAC;YACjC,IAAI,YAAY,GAAQ,OAAO,CAAC;YAChC,IAAI,UAAU,CAAC,KAAK,EAAE;gBACpB,IAAI,cAAc,CAAC,YAAY,EAAE;oBAC/B,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC;iBACnC;qBAAM;oBACL,QAAQ,GAAG,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC;iBACpE;aACF;iBAAM;gBACL,MAAM,KAAK,GAAG,kBAAkB,CAAC,cAAc,CAAC,cAAe,CAAC,CAAC;gBACjE,QAAQ,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;gBAEvB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;oBAC5B,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAC3C,IACE,WAAW,IAAI,SAAS;wBACxB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,SAAS,IAAI,cAAc,CAAC,YAAY,KAAK,SAAS,CAAC,EACvE;wBACA,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;qBAC7B;oBACD,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;iBACvC;aACF;YAED,IAAI,YAAY,IAAI,SAAS,EAAE;gBAC7B,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;oBAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB;wBACxC,CAAC,CAAC,SAAS,MAAM,CAAC,kBAAkB,EAAE;wBACtC,CAAC,CAAC,OAAO,CAAC;oBACZ,YAAY,CAAC,WAAW,CAAC,mCACpB,YAAY,CAAC,WAAW,CAAC,KAC5B,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,YAAY,GAChC,CAAC;iBACH;gBACD,MAAM,kBAAkB,GACtB,cAAc,CAAC,cAAc,KAAK,EAAE;oBAClC,CAAC,CAAC,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC,cAAc;oBAClD,CAAC,CAAC,UAAU,CAAC;gBAEjB,IAAI,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC9B,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAC5F,IACE,wBAAwB;oBACxB,wBAAwB,CAAC,UAAU,KAAK,GAAG;oBAC3C,WAAW,IAAI,SAAS,EACxB;oBACA,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC;iBACrC;gBAED,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAC1C,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;gBAEF,IAAI,eAAe,KAAK,SAAS,IAAI,QAAQ,IAAI,SAAS,EAAE;oBAC1D,MAAM,KAAK,GAAG,iBAAiB,CAAC,cAAc,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;oBACjF,IAAI,KAAK,IAAI,cAAc,CAAC,cAAc,EAAE;wBAC1C,uEAAuE;wBACvE,2DAA2D;wBAC3D,gCAAgC;wBAChC,YAAY,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;wBAC5D,YAAY,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC;qBACvD;yBAAM,IAAI,KAAK,IAAI,cAAc,CAAC,YAAY,EAAE;wBAC/C,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,cAAe,CAAC,EAAE,KAAK,EAAE,CAAC;qBACtE;yBAAM;wBACL,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;qBAChC;iBACF;aACF;SACF;QAED,MAAM,0BAA0B,GAAG,2BAA2B,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC/F,IAAI,0BAA0B,EAAE;YAC9B,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,KAAK,MAAM,cAAc,IAAI,MAAM,EAAE;gBACnC,MAAM,oBAAoB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC;gBAC5E,IAAI,oBAAoB,EAAE;oBACxB,OAAO,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,SAAS,CAC5C,0BAA0B,EAC1B,MAAM,CAAC,cAAc,CAAC,EACtB,UAAU,GAAG,IAAI,GAAG,cAAc,GAAG,IAAI,EACzC,OAAO,CACR,CAAC;iBACH;aACF;SACF;QAED,OAAO,OAAO,CAAC;KAChB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,iBAAiB,CACxB,cAAsB,EACtB,eAAoB,EACpB,KAAc,EACd,OAAoC;IAEpC,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;QAC1C,OAAO,eAAe,CAAC;KACxB;IAED,MAAM,QAAQ,GAAG,cAAc,CAAC,kBAAkB;QAChD,CAAC,CAAC,SAAS,cAAc,CAAC,kBAAkB,EAAE;QAC9C,CAAC,CAAC,OAAO,CAAC;IACZ,MAAM,YAAY,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,cAAc,CAAC,YAAY,EAAE,CAAC;IAEjE,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QACpD,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE;YAChC,OAAO,eAAe,CAAC;SACxB;aAAM;YACL,MAAM,MAAM,qBAAa,eAAe,CAAE,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;YACnC,OAAO,MAAM,CAAC;SACf;KACF;IACD,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;IAC7C,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;IACnC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,oBAAoB,CAAC,YAAoB,EAAE,OAAoC;IACtF,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAAsB,EACtB,MAAuB,EACvB,YAAiB,EACjB,UAAkB,EAClB,OAAoC;;IAEpC,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;QAC9D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;KACnF;IAED,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC1E,IAAI,QAAQ,GAA2B,EAAE,CAAC;IAC1C,MAAM,oBAAoB,GAAa,EAAE,CAAC;IAE1C,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;QACzC,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,CAAC;QAClE,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,cAAc,CAAC;QACnE,IAAI,kBAAkB,GAAG,UAAU,CAAC;QACpC,IAAI,cAAc,KAAK,EAAE,IAAI,cAAc,KAAK,SAAS,EAAE;YACzD,kBAAkB,GAAG,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC;SACxD;QAED,MAAM,sBAAsB,GAAI,cAAmC,CAAC,sBAAsB,CAAC;QAC3F,IAAI,sBAAsB,EAAE;YAC1B,MAAM,UAAU,GAAQ,EAAE,CAAC;YAC3B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBACjD,IAAI,SAAS,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE;oBAChD,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACpF,cAAmC,CAAC,IAAI,CAAC,KAAK,EAC/C,YAAY,CAAC,SAAS,CAAC,EACvB,kBAAkB,EAClB,OAAO,CACR,CAAC;iBACH;gBAED,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACtC;YACD,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;SAC5B;aAAM,IAAI,UAAU,CAAC,KAAK,EAAE;YAC3B,IAAI,cAAc,CAAC,cAAc,IAAI,YAAY,CAAC,WAAW,CAAC,EAAE;gBAC9D,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,YAAY,CAAC,WAAW,CAAC,CAAC,OAAQ,CAAC,EACnC,kBAAkB,EAClB,OAAO,CACR,CAAC;aACH;iBAAM;gBACL,MAAM,YAAY,GAAG,cAAc,IAAI,OAAO,IAAI,cAAc,CAAC;gBACjE,IAAI,cAAc,CAAC,YAAY,EAAE;oBAC/B;;;;;;;;;;;;;sBAaE;oBACF,MAAM,OAAO,GAAG,YAAY,CAAC,OAAQ,CAAC,CAAC;oBACvC,MAAM,WAAW,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,cAAe,CAAC,mCAAI,EAAE,CAAC;oBACrD,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;iBACH;qBAAM;oBACL,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAa,CAAC,CAAC;oBAC7C,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,QAAQ,EACR,kBAAkB,EAClB,OAAO,CACR,CAAC;iBACH;aACF;SACF;aAAM;YACL,kFAAkF;YAClF,IAAI,gBAAgB,CAAC;YACrB,IAAI,GAAG,GAAG,YAAY,CAAC;YACvB,sCAAsC;YACtC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,IAAI,CAAC,GAAG;oBAAE,MAAM;gBAChB,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;aACjB;YACD,gBAAgB,GAAG,GAAG,CAAC;YACvB,MAAM,wBAAwB,GAAG,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC;YACtE,sEAAsE;YACtE,yEAAyE;YACzE,kFAAkF;YAClF,kFAAkF;YAClF,gGAAgG;YAChG,8FAA8F;YAC9F,qFAAqF;YACrF,mFAAmF;YACnF,sFAAsF;YACtF,IACE,wBAAwB;gBACxB,GAAG,KAAK,wBAAwB,CAAC,UAAU;gBAC3C,gBAAgB,IAAI,SAAS,EAC7B;gBACA,gBAAgB,GAAG,MAAM,CAAC,cAAc,CAAC;aAC1C;YAED,IAAI,eAAe,CAAC;YACpB,SAAS;YACT,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,cAAc,KAAK,EAAE,EAAE;gBAC7E,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBACrC,MAAM,aAAa,GAAG,UAAU,CAAC,WAAW,CAC1C,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;gBACF,yFAAyF;gBACzF,6CAA6C;gBAC7C,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC7C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE;wBAC3D,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;qBACtB;iBACF;gBACD,QAAQ,GAAG,aAAa,CAAC;aAC1B;iBAAM,IAAI,gBAAgB,KAAK,SAAS,IAAI,cAAc,CAAC,YAAY,KAAK,SAAS,EAAE;gBACtF,eAAe,GAAG,UAAU,CAAC,WAAW,CACtC,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;gBACF,QAAQ,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;aACjC;SACF;KACF;IAED,MAAM,0BAA0B,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;IACpE,IAAI,0BAA0B,EAAE;QAC9B,MAAM,oBAAoB,GAAG,CAAC,gBAAwB,EAAW,EAAE;YACjE,KAAK,MAAM,cAAc,IAAI,UAAU,EAAE;gBACvC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;gBAC5E,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE;oBACjC,OAAO,KAAK,CAAC;iBACd;aACF;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,KAAK,MAAM,gBAAgB,IAAI,YAAY,EAAE;YAC3C,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;gBAC1C,QAAQ,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAC,WAAW,CACjD,0BAA0B,EAC1B,YAAY,CAAC,gBAAgB,CAAC,EAC9B,UAAU,GAAG,IAAI,GAAG,gBAAgB,GAAG,IAAI,EAC3C,OAAO,CACR,CAAC;aACH;SACF;KACF;SAAM,IAAI,YAAY,EAAE;QACvB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;YAC3C,IACE,QAAQ,CAAC,GAAG,CAAC,KAAK,SAAS;gBAC3B,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACnC,CAAC,oBAAoB,CAAC,GAAG,EAAE,OAAO,CAAC,EACnC;gBACA,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;aACnC;SACF;KACF;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,yBAAyB,CAChC,UAAsB,EACtB,MAAwB,EACxB,YAAiB,EACjB,UAAkB,EAClB,OAAoC;IAEpC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;IAChC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACvC,MAAM,IAAI,KAAK,CACb,2DAA2D;YACzD,0CAA0C,UAAU,EAAE,CACzD,CAAC;KACH;IACD,IAAI,YAAY,EAAE;QAChB,MAAM,cAAc,GAA2B,EAAE,CAAC;QAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;YAC3C,cAAc,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;SAC7F;QACD,OAAO,cAAc,CAAC;KACvB;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAsB,EACtB,YAAiB,EACjB,UAAkB,EAClB,OAAoC;IAEpC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IACpC,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC3C,MAAM,IAAI,KAAK,CACb,wDAAwD;YACtD,0CAA0C,UAAU,EAAE,CACzD,CAAC;KACH;IACD,IAAI,YAAY,EAAE;QAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAChC,+FAA+F;YAC/F,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;SAC/B;QAED,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,SAAS,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACnC,OAAO,EACP,YAAY,CAAC,CAAC,CAAC,EACf,GAAG,UAAU,IAAI,CAAC,GAAG,EACrB,OAAO,CACR,CAAC;SACH;QACD,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,oBAAoB,CAC3B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,uBAAwD;IAExD,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAC5F,IAAI,wBAAwB,EAAE;QAC5B,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,uBAAuB,CAAC,CAAC;QAC5E,IAAI,iBAAiB,IAAI,SAAS,EAAE;YAClC,MAAM,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACrD,IAAI,kBAAkB,IAAI,SAAS,EAAE;gBACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACjE,MAAM,kBAAkB,GACtB,kBAAkB,KAAK,QAAQ;oBAC7B,CAAC,CAAC,kBAAkB;oBACpB,CAAC,CAAC,QAAQ,GAAG,GAAG,GAAG,kBAAkB,CAAC;gBAC1C,MAAM,iBAAiB,GAAG,UAAU,CAAC,YAAY,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;gBACrF,IAAI,iBAAiB,EAAE;oBACrB,MAAM,GAAG,iBAAiB,CAAC;iBAC5B;aACF;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,sCAAsC,CAC7C,UAAsB,EACtB,MAAuB;IAEvB,OAAO,CACL,MAAM,CAAC,IAAI,CAAC,wBAAwB;QACpC,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACrE,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CACrE,CAAC;AACJ,CAAC;AAED,SAAS,iCAAiC,CAAC,UAAsB,EAAE,QAAiB;IAClF,OAAO,CACL,QAAQ;QACR,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC;QACjC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAChE,CAAC;AACJ,CAAC;AAgKD,0BAA0B;AAC1B,MAAM,UAAU,eAAe,CAAC,WAAoB;IAClD,MAAM,eAAe,GAAG,WAAsC,CAAC;IAC/D,IAAI,WAAW,IAAI,SAAS;QAAE,OAAO,SAAS,CAAC;IAC/C,IAAI,WAAW,YAAY,UAAU,EAAE;QACrC,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAClD,OAAO,WAAW,CAAC;KACpB;SAAM,IAAI,WAAW,YAAY,IAAI,EAAE;QACtC,OAAO,WAAW,CAAC,WAAW,EAAE,CAAC;KAClC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;QACrC,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7C;QACD,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QAC1C,MAAM,UAAU,GAA2B,EAAE,CAAC;QAC9C,KAAK,MAAM,QAAQ,IAAI,WAAW,EAAE;YAClC,UAAU,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;SACnE;QACD,OAAO,UAAU,CAAC;KACnB;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAS,OAAO,CAAmB,CAAW;IAC5C,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE;QACnB,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KACnB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,2DAA2D;AAC3D,MAAM,CAAC,MAAM,UAAU,GAAG,OAAO,CAAC;IAChC,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;IACX,MAAM;IACN,UAAU;IACV,iBAAiB;IACjB,YAAY;IACZ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,UAAU;CACX,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/* eslint-disable eqeqeq */\n\nimport * as base64 from \"./util/base64\";\nimport * as utils from \"./util/utils\";\nimport { XML_ATTRKEY, XML_CHARKEY, SerializerOptions } from \"./util/serializer.common\";\n\nexport class Serializer {\n  constructor(\n    public readonly modelMappers: { [key: string]: any } = {},\n    public readonly isXML?: boolean\n  ) {}\n\n  validateConstraints(mapper: Mapper, value: unknown, objectName: string): void {\n    const failValidation = (\n      constraintName: keyof MapperConstraints,\n      constraintValue: any\n    ): Error => {\n      throw new Error(\n        `\"${objectName}\" with value \"${value}\" should satisfy the constraint \"${constraintName}\": ${constraintValue}.`\n      );\n    };\n    if (mapper.constraints && value != undefined) {\n      const valueAsNumber = value as number;\n      const {\n        ExclusiveMaximum,\n        ExclusiveMinimum,\n        InclusiveMaximum,\n        InclusiveMinimum,\n        MaxItems,\n        <PERSON>Length,\n        <PERSON><PERSON>tems,\n        <PERSON><PERSON>eng<PERSON>,\n        MultipleOf,\n        Pattern,\n        UniqueItems\n      } = mapper.constraints;\n      if (ExclusiveMaximum != undefined && valueAsNumber >= ExclusiveMaximum) {\n        failValidation(\"ExclusiveMaximum\", ExclusiveMaximum);\n      }\n      if (ExclusiveMinimum != undefined && valueAsNumber <= ExclusiveMinimum) {\n        failValidation(\"ExclusiveMinimum\", ExclusiveMinimum);\n      }\n      if (InclusiveMaximum != undefined && valueAsNumber > InclusiveMaximum) {\n        failValidation(\"InclusiveMaximum\", InclusiveMaximum);\n      }\n      if (InclusiveMinimum != undefined && valueAsNumber < InclusiveMinimum) {\n        failValidation(\"InclusiveMinimum\", InclusiveMinimum);\n      }\n      const valueAsArray = value as any[];\n      if (MaxItems != undefined && valueAsArray.length > MaxItems) {\n        failValidation(\"MaxItems\", MaxItems);\n      }\n      if (MaxLength != undefined && valueAsArray.length > MaxLength) {\n        failValidation(\"MaxLength\", MaxLength);\n      }\n      if (MinItems != undefined && valueAsArray.length < MinItems) {\n        failValidation(\"MinItems\", MinItems);\n      }\n      if (MinLength != undefined && valueAsArray.length < MinLength) {\n        failValidation(\"MinLength\", MinLength);\n      }\n      if (MultipleOf != undefined && valueAsNumber % MultipleOf !== 0) {\n        failValidation(\"MultipleOf\", MultipleOf);\n      }\n      if (Pattern) {\n        const pattern: RegExp = typeof Pattern === \"string\" ? new RegExp(Pattern) : Pattern;\n        if (typeof value !== \"string\" || value.match(pattern) === null) {\n          failValidation(\"Pattern\", Pattern);\n        }\n      }\n      if (\n        UniqueItems &&\n        valueAsArray.some((item: any, i: number, ar: Array<any>) => ar.indexOf(item) !== i)\n      ) {\n        failValidation(\"UniqueItems\", UniqueItems);\n      }\n    }\n  }\n\n  /**\n   * Serialize the given object based on its metadata defined in the mapper\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object\n   * @param object - A valid Javascript object to be serialized\n   * @param objectName - Name of the serialized object\n   * @param options - additional options to deserialization\n   * @returns A valid serialized Javascript object\n   */\n  serialize(\n    mapper: Mapper,\n    object: unknown,\n    objectName?: string,\n    options: SerializerOptions = {}\n  ): any {\n    const updatedOptions: Required<SerializerOptions> = {\n      rootName: options.rootName ?? \"\",\n      includeRoot: options.includeRoot ?? false,\n      xmlCharKey: options.xmlCharKey ?? XML_CHARKEY\n    };\n    let payload: any = {};\n    const mapperType = mapper.type.name as string;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n    if (mapperType.match(/^Sequence$/i) !== null) {\n      payload = [];\n    }\n\n    if (mapper.isConstant) {\n      object = mapper.defaultValue;\n    }\n\n    // This table of allowed values should help explain\n    // the mapper.required and mapper.nullable properties.\n    // X means \"neither undefined or null are allowed\".\n    //           || required\n    //           || true      | false\n    //  nullable || ==========================\n    //      true || null      | undefined/null\n    //     false || X         | undefined\n    // undefined || X         | undefined/null\n\n    const { required, nullable } = mapper;\n\n    if (required && nullable && object === undefined) {\n      throw new Error(`${objectName} cannot be undefined.`);\n    }\n    if (required && !nullable && object == undefined) {\n      throw new Error(`${objectName} cannot be null or undefined.`);\n    }\n    if (!required && nullable === false && object === null) {\n      throw new Error(`${objectName} cannot be null.`);\n    }\n\n    if (object == undefined) {\n      payload = object;\n    } else {\n      // Validate Constraints if any\n      this.validateConstraints(mapper, object, objectName);\n      if (mapperType.match(/^any$/i) !== null) {\n        payload = object;\n      } else if (mapperType.match(/^(Number|String|Boolean|Object|Stream|Uuid)$/i) !== null) {\n        payload = serializeBasicTypes(mapperType, objectName, object);\n      } else if (mapperType.match(/^Enum$/i) !== null) {\n        const enumMapper: EnumMapper = mapper as EnumMapper;\n        payload = serializeEnumType(objectName, enumMapper.type.allowedValues, object);\n      } else if (\n        mapperType.match(/^(Date|DateTime|TimeSpan|DateTimeRfc1123|UnixTime)$/i) !== null\n      ) {\n        payload = serializeDateTypes(mapperType, object, objectName);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = serializeByteArrayType(objectName, object as Uint8Array);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = serializeBase64UrlType(objectName, object as Uint8Array);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = serializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = serializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Composite$/i) !== null) {\n        payload = serializeCompositeType(\n          this,\n          mapper as CompositeMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions\n        );\n      }\n    }\n    return payload;\n  }\n\n  /**\n   * Deserialize the given object based on its metadata defined in the mapper\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object\n   * @param responseBody - A valid Javascript entity to be deserialized\n   * @param objectName - Name of the deserialized object\n   * @param options - Controls behavior of XML parser and builder.\n   * @returns A valid deserialized Javascript object\n   */\n  deserialize(\n    mapper: Mapper,\n    responseBody: unknown,\n    objectName: string,\n    options: SerializerOptions = {}\n  ): any {\n    const updatedOptions: Required<SerializerOptions> = {\n      rootName: options.rootName ?? \"\",\n      includeRoot: options.includeRoot ?? false,\n      xmlCharKey: options.xmlCharKey ?? XML_CHARKEY\n    };\n    if (responseBody == undefined) {\n      if (this.isXML && mapper.type.name === \"Sequence\" && !mapper.xmlIsWrapped) {\n        // Edge case for empty XML non-wrapped lists. xml2js can't distinguish\n        // between the list being empty versus being missing,\n        // so let's do the more user-friendly thing and return an empty list.\n        responseBody = [];\n      }\n      // specifically check for undefined as default value can be a falsey value `0, \"\", false, null`\n      if (mapper.defaultValue !== undefined) {\n        responseBody = mapper.defaultValue;\n      }\n      return responseBody;\n    }\n\n    let payload: any;\n    const mapperType = mapper.type.name;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n\n    if (mapperType.match(/^Composite$/i) !== null) {\n      payload = deserializeCompositeType(\n        this,\n        mapper as CompositeMapper,\n        responseBody,\n        objectName,\n        updatedOptions\n      );\n    } else {\n      if (this.isXML) {\n        const xmlCharKey = updatedOptions.xmlCharKey;\n        const castResponseBody = responseBody as Record<string, unknown>;\n        /**\n         * If the mapper specifies this as a non-composite type value but the responseBody contains\n         * both header (\"$\" i.e., XML_ATTRKEY) and body (\"#\" i.e., XML_CHARKEY) properties,\n         * then just reduce the responseBody value to the body (\"#\" i.e., XML_CHARKEY) property.\n         */\n        if (\n          castResponseBody[XML_ATTRKEY] != undefined &&\n          castResponseBody[xmlCharKey] != undefined\n        ) {\n          responseBody = castResponseBody[xmlCharKey];\n        }\n      }\n\n      if (mapperType.match(/^Number$/i) !== null) {\n        payload = parseFloat(responseBody as string);\n        if (isNaN(payload)) {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^Boolean$/i) !== null) {\n        if (responseBody === \"true\") {\n          payload = true;\n        } else if (responseBody === \"false\") {\n          payload = false;\n        } else {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^(String|Enum|Object|Stream|Uuid|TimeSpan|any)$/i) !== null) {\n        payload = responseBody;\n      } else if (mapperType.match(/^(Date|DateTime|DateTimeRfc1123)$/i) !== null) {\n        payload = new Date(responseBody as string);\n      } else if (mapperType.match(/^UnixTime$/i) !== null) {\n        payload = unixTimeToDate(responseBody as number);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = base64.decodeString(responseBody as string);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = base64UrlToByteArray(responseBody as string);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = deserializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          responseBody,\n          objectName,\n          updatedOptions\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = deserializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          responseBody,\n          objectName,\n          updatedOptions\n        );\n      }\n    }\n\n    if (mapper.isConstant) {\n      payload = mapper.defaultValue;\n    }\n\n    return payload;\n  }\n}\n\nfunction trimEnd(str: string, ch: string): string {\n  let len = str.length;\n  while (len - 1 >= 0 && str[len - 1] === ch) {\n    --len;\n  }\n  return str.substr(0, len);\n}\n\nfunction bufferToBase64Url(buffer: any): string | undefined {\n  if (!buffer) {\n    return undefined;\n  }\n  if (!(buffer instanceof Uint8Array)) {\n    throw new Error(`Please provide an input of type Uint8Array for converting to Base64Url.`);\n  }\n  // Uint8Array to Base64.\n  const str = base64.encodeByteArray(buffer);\n  // Base64 to Base64Url.\n  return trimEnd(str, \"=\")\n    .replace(/\\+/g, \"-\")\n    .replace(/\\//g, \"_\");\n}\n\nfunction base64UrlToByteArray(str: string): Uint8Array | undefined {\n  if (!str) {\n    return undefined;\n  }\n  if (str && typeof str.valueOf() !== \"string\") {\n    throw new Error(\"Please provide an input of type string for converting to Uint8Array\");\n  }\n  // Base64Url to Base64.\n  str = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  // Base64 to Uint8Array.\n  return base64.decodeString(str);\n}\n\nfunction splitSerializeName(prop: string | undefined): string[] {\n  const classes: string[] = [];\n  let partialclass = \"\";\n  if (prop) {\n    const subwords = prop.split(\".\");\n\n    for (const item of subwords) {\n      if (item.charAt(item.length - 1) === \"\\\\\") {\n        partialclass += item.substr(0, item.length - 1) + \".\";\n      } else {\n        partialclass += item;\n        classes.push(partialclass);\n        partialclass = \"\";\n      }\n    }\n  }\n\n  return classes;\n}\n\nfunction dateToUnixTime(d: string | Date): number | undefined {\n  if (!d) {\n    return undefined;\n  }\n\n  if (typeof d.valueOf() === \"string\") {\n    d = new Date(d as string);\n  }\n  return Math.floor((d as Date).getTime() / 1000);\n}\n\nfunction unixTimeToDate(n: number): Date | undefined {\n  if (!n) {\n    return undefined;\n  }\n  return new Date(n * 1000);\n}\n\nfunction serializeBasicTypes(typeName: string, objectName: string, value: any): any {\n  if (value !== null && value !== undefined) {\n    if (typeName.match(/^Number$/i) !== null) {\n      if (typeof value !== \"number\") {\n        throw new Error(`${objectName} with value ${value} must be of type number.`);\n      }\n    } else if (typeName.match(/^String$/i) !== null) {\n      if (typeof value.valueOf() !== \"string\") {\n        throw new Error(`${objectName} with value \"${value}\" must be of type string.`);\n      }\n    } else if (typeName.match(/^Uuid$/i) !== null) {\n      if (!(typeof value.valueOf() === \"string\" && utils.isValidUuid(value))) {\n        throw new Error(\n          `${objectName} with value \"${value}\" must be of type string and a valid uuid.`\n        );\n      }\n    } else if (typeName.match(/^Boolean$/i) !== null) {\n      if (typeof value !== \"boolean\") {\n        throw new Error(`${objectName} with value ${value} must be of type boolean.`);\n      }\n    } else if (typeName.match(/^Stream$/i) !== null) {\n      const objectType = typeof value;\n      if (\n        objectType !== \"string\" &&\n        objectType !== \"function\" &&\n        !(value instanceof ArrayBuffer) &&\n        !ArrayBuffer.isView(value) &&\n        !((typeof Blob === \"function\" || typeof Blob === \"object\") && value instanceof Blob)\n      ) {\n        throw new Error(\n          `${objectName} must be a string, Blob, ArrayBuffer, ArrayBufferView, or a function returning NodeJS.ReadableStream.`\n        );\n      }\n    }\n  }\n\n  return value;\n}\n\nfunction serializeEnumType(objectName: string, allowedValues: Array<any>, value: any): any {\n  if (!allowedValues) {\n    throw new Error(\n      `Please provide a set of allowedValues to validate ${objectName} as an Enum Type.`\n    );\n  }\n  const isPresent = allowedValues.some((item) => {\n    if (typeof item.valueOf() === \"string\") {\n      return item.toLowerCase() === value.toLowerCase();\n    }\n    return item === value;\n  });\n  if (!isPresent) {\n    throw new Error(\n      `${value} is not a valid value for ${objectName}. The valid values are: ${JSON.stringify(\n        allowedValues\n      )}.`\n    );\n  }\n  return value;\n}\n\nfunction serializeByteArrayType(objectName: string, value: Uint8Array): string {\n  let returnValue: string = \"\";\n  if (value != undefined) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    returnValue = base64.encodeByteArray(value);\n  }\n  return returnValue;\n}\n\nfunction serializeBase64UrlType(objectName: string, value: Uint8Array): string {\n  let returnValue: string = \"\";\n  if (value != undefined) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    returnValue = bufferToBase64Url(value) || \"\";\n  }\n  return returnValue;\n}\n\nfunction serializeDateTypes(typeName: string, value: any, objectName: string): any {\n  if (value != undefined) {\n    if (typeName.match(/^Date$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value =\n        value instanceof Date\n          ? value.toISOString().substring(0, 10)\n          : new Date(value).toISOString().substring(0, 10);\n    } else if (typeName.match(/^DateTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value = value instanceof Date ? value.toISOString() : new Date(value).toISOString();\n    } else if (typeName.match(/^DateTimeRfc1123$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in RFC-1123 format.`);\n      }\n      value = value instanceof Date ? value.toUTCString() : new Date(value).toUTCString();\n    } else if (typeName.match(/^UnixTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(\n          `${objectName} must be an instanceof Date or a string in RFC-1123/ISO8601 format ` +\n            `for it to be serialized in UnixTime/Epoch format.`\n        );\n      }\n      value = dateToUnixTime(value);\n    } else if (typeName.match(/^TimeSpan$/i) !== null) {\n      if (!utils.isDuration(value)) {\n        throw new Error(\n          `${objectName} must be a string in ISO 8601 format. Instead was \"${value}\".`\n        );\n      }\n    }\n  }\n  return value;\n}\n\nfunction serializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): any[] {\n  if (!Array.isArray(object)) {\n    throw new Error(`${objectName} must be of type Array.`);\n  }\n  const elementType = mapper.type.element;\n  if (!elementType || typeof elementType !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`\n    );\n  }\n  const tempArray = [];\n  for (let i = 0; i < object.length; i++) {\n    const serializedValue = serializer.serialize(elementType, object[i], objectName, options);\n\n    if (isXml && elementType.xmlNamespace) {\n      const xmlnsKey = elementType.xmlNamespacePrefix\n        ? `xmlns:${elementType.xmlNamespacePrefix}`\n        : \"xmlns\";\n      if (elementType.type.name === \"Composite\") {\n        tempArray[i] = { ...serializedValue };\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      } else {\n        tempArray[i] = {};\n        tempArray[i][options.xmlCharKey] = serializedValue;\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      }\n    } else {\n      tempArray[i] = serializedValue;\n    }\n  }\n  return tempArray;\n}\n\nfunction serializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): { [key: string]: any } {\n  if (typeof object !== \"object\") {\n    throw new Error(`${objectName} must be of type object.`);\n  }\n  const valueType = mapper.type.value;\n  if (!valueType || typeof valueType !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`\n    );\n  }\n  const tempDictionary: { [key: string]: any } = {};\n  for (const key of Object.keys(object)) {\n    const serializedValue = serializer.serialize(valueType, object[key], objectName, options);\n    // If the element needs an XML namespace we need to add it within the $ property\n    tempDictionary[key] = getXmlObjectValue(valueType, serializedValue, isXml, options);\n  }\n\n  // Add the namespace to the root element if needed\n  if (isXml && mapper.xmlNamespace) {\n    const xmlnsKey = mapper.xmlNamespacePrefix ? `xmlns:${mapper.xmlNamespacePrefix}` : \"xmlns\";\n\n    const result = tempDictionary;\n    result[XML_ATTRKEY] = { [xmlnsKey]: mapper.xmlNamespace };\n    return result;\n  }\n\n  return tempDictionary;\n}\n\n/**\n * Resolves the additionalProperties property from a referenced mapper\n * @param serializer - The serializer containing the entire set of mappers\n * @param mapper - The composite mapper to resolve\n * @param objectName - Name of the object being serialized\n */\nfunction resolveAdditionalProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): SequenceMapper | BaseMapper | CompositeMapper | DictionaryMapper | EnumMapper | undefined {\n  const additionalProperties = mapper.type.additionalProperties;\n\n  if (!additionalProperties && mapper.type.className) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    return modelMapper?.type.additionalProperties;\n  }\n\n  return additionalProperties;\n}\n\n/**\n * Finds the mapper referenced by className\n * @param serializer - The serializer containing the entire set of mappers\n * @param mapper - The composite mapper to resolve\n * @param objectName - Name of the object being serialized\n */\nfunction resolveReferencedMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): CompositeMapper | undefined {\n  const className = mapper.type.className;\n  if (!className) {\n    throw new Error(\n      `Class name for model \"${objectName}\" is not provided in the mapper \"${JSON.stringify(\n        mapper,\n        undefined,\n        2\n      )}\".`\n    );\n  }\n\n  return serializer.modelMappers[className];\n}\n\n/**\n * Resolves a composite mapper's modelProperties.\n * @param serializer - The serializer containing the entire set of mappers\n * @param mapper - The composite mapper to resolve\n */\nfunction resolveModelProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string\n): { [propertyName: string]: Mapper } {\n  let modelProps = mapper.type.modelProperties;\n  if (!modelProps) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    if (!modelMapper) {\n      throw new Error(`mapper() cannot be null or undefined for model \"${mapper.type.className}\".`);\n    }\n    modelProps = modelMapper?.type.modelProperties;\n    if (!modelProps) {\n      throw new Error(\n        `modelProperties cannot be null or undefined in the ` +\n          `mapper \"${JSON.stringify(modelMapper)}\" of type \"${\n            mapper.type.className\n          }\" for object \"${objectName}\".`\n      );\n    }\n  }\n\n  return modelProps;\n}\n\nfunction serializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): any {\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, object, \"clientName\");\n  }\n\n  if (object != undefined) {\n    const payload: any = {};\n    const modelProps = resolveModelProperties(serializer, mapper, objectName);\n    for (const key of Object.keys(modelProps)) {\n      const propertyMapper = modelProps[key];\n      if (propertyMapper.readOnly) {\n        continue;\n      }\n\n      let propName: string | undefined;\n      let parentObject: any = payload;\n      if (serializer.isXML) {\n        if (propertyMapper.xmlIsWrapped) {\n          propName = propertyMapper.xmlName;\n        } else {\n          propName = propertyMapper.xmlElementName || propertyMapper.xmlName;\n        }\n      } else {\n        const paths = splitSerializeName(propertyMapper.serializedName!);\n        propName = paths.pop();\n\n        for (const pathName of paths) {\n          const childObject = parentObject[pathName];\n          if (\n            childObject == undefined &&\n            (object[key] != undefined || propertyMapper.defaultValue !== undefined)\n          ) {\n            parentObject[pathName] = {};\n          }\n          parentObject = parentObject[pathName];\n        }\n      }\n\n      if (parentObject != undefined) {\n        if (isXml && mapper.xmlNamespace) {\n          const xmlnsKey = mapper.xmlNamespacePrefix\n            ? `xmlns:${mapper.xmlNamespacePrefix}`\n            : \"xmlns\";\n          parentObject[XML_ATTRKEY] = {\n            ...parentObject[XML_ATTRKEY],\n            [xmlnsKey]: mapper.xmlNamespace\n          };\n        }\n        const propertyObjectName =\n          propertyMapper.serializedName !== \"\"\n            ? objectName + \".\" + propertyMapper.serializedName\n            : objectName;\n\n        let toSerialize = object[key];\n        const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n        if (\n          polymorphicDiscriminator &&\n          polymorphicDiscriminator.clientName === key &&\n          toSerialize == undefined\n        ) {\n          toSerialize = mapper.serializedName;\n        }\n\n        const serializedValue = serializer.serialize(\n          propertyMapper,\n          toSerialize,\n          propertyObjectName,\n          options\n        );\n\n        if (serializedValue !== undefined && propName != undefined) {\n          const value = getXmlObjectValue(propertyMapper, serializedValue, isXml, options);\n          if (isXml && propertyMapper.xmlIsAttribute) {\n            // XML_ATTRKEY, i.e., $ is the key attributes are kept under in xml2js.\n            // This keeps things simple while preventing name collision\n            // with names in user documents.\n            parentObject[XML_ATTRKEY] = parentObject[XML_ATTRKEY] || {};\n            parentObject[XML_ATTRKEY][propName] = serializedValue;\n          } else if (isXml && propertyMapper.xmlIsWrapped) {\n            parentObject[propName] = { [propertyMapper.xmlElementName!]: value };\n          } else {\n            parentObject[propName] = value;\n          }\n        }\n      }\n    }\n\n    const additionalPropertiesMapper = resolveAdditionalProperties(serializer, mapper, objectName);\n    if (additionalPropertiesMapper) {\n      const propNames = Object.keys(modelProps);\n      for (const clientPropName in object) {\n        const isAdditionalProperty = propNames.every((pn) => pn !== clientPropName);\n        if (isAdditionalProperty) {\n          payload[clientPropName] = serializer.serialize(\n            additionalPropertiesMapper,\n            object[clientPropName],\n            objectName + '[\"' + clientPropName + '\"]',\n            options\n          );\n        }\n      }\n    }\n\n    return payload;\n  }\n  return object;\n}\n\nfunction getXmlObjectValue(\n  propertyMapper: Mapper,\n  serializedValue: any,\n  isXml: boolean,\n  options: Required<SerializerOptions>\n): any {\n  if (!isXml || !propertyMapper.xmlNamespace) {\n    return serializedValue;\n  }\n\n  const xmlnsKey = propertyMapper.xmlNamespacePrefix\n    ? `xmlns:${propertyMapper.xmlNamespacePrefix}`\n    : \"xmlns\";\n  const xmlNamespace = { [xmlnsKey]: propertyMapper.xmlNamespace };\n\n  if ([\"Composite\"].includes(propertyMapper.type.name)) {\n    if (serializedValue[XML_ATTRKEY]) {\n      return serializedValue;\n    } else {\n      const result: any = { ...serializedValue };\n      result[XML_ATTRKEY] = xmlNamespace;\n      return result;\n    }\n  }\n  const result: any = {};\n  result[options.xmlCharKey] = serializedValue;\n  result[XML_ATTRKEY] = xmlNamespace;\n  return result;\n}\n\nfunction isSpecialXmlProperty(propertyName: string, options: Required<SerializerOptions>): boolean {\n  return [XML_ATTRKEY, options.xmlCharKey].includes(propertyName);\n}\n\nfunction deserializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  responseBody: any,\n  objectName: string,\n  options: Required<SerializerOptions>\n): any {\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, responseBody, \"serializedName\");\n  }\n\n  const modelProps = resolveModelProperties(serializer, mapper, objectName);\n  let instance: { [key: string]: any } = {};\n  const handledPropertyNames: string[] = [];\n\n  for (const key of Object.keys(modelProps)) {\n    const propertyMapper = modelProps[key];\n    const paths = splitSerializeName(modelProps[key].serializedName!);\n    handledPropertyNames.push(paths[0]);\n    const { serializedName, xmlName, xmlElementName } = propertyMapper;\n    let propertyObjectName = objectName;\n    if (serializedName !== \"\" && serializedName !== undefined) {\n      propertyObjectName = objectName + \".\" + serializedName;\n    }\n\n    const headerCollectionPrefix = (propertyMapper as DictionaryMapper).headerCollectionPrefix;\n    if (headerCollectionPrefix) {\n      const dictionary: any = {};\n      for (const headerKey of Object.keys(responseBody)) {\n        if (headerKey.startsWith(headerCollectionPrefix)) {\n          dictionary[headerKey.substring(headerCollectionPrefix.length)] = serializer.deserialize(\n            (propertyMapper as DictionaryMapper).type.value,\n            responseBody[headerKey],\n            propertyObjectName,\n            options\n          );\n        }\n\n        handledPropertyNames.push(headerKey);\n      }\n      instance[key] = dictionary;\n    } else if (serializer.isXML) {\n      if (propertyMapper.xmlIsAttribute && responseBody[XML_ATTRKEY]) {\n        instance[key] = serializer.deserialize(\n          propertyMapper,\n          responseBody[XML_ATTRKEY][xmlName!],\n          propertyObjectName,\n          options\n        );\n      } else {\n        const propertyName = xmlElementName || xmlName || serializedName;\n        if (propertyMapper.xmlIsWrapped) {\n          /* a list of <xmlElementName> wrapped by <xmlName>\n            For the xml example below\n              <Cors>\n                <CorsRule>...</CorsRule>\n                <CorsRule>...</CorsRule>\n              </Cors>\n            the responseBody has\n              {\n                Cors: {\n                  CorsRule: [{...}, {...}]\n                }\n              }\n            xmlName is \"Cors\" and xmlElementName is\"CorsRule\".\n          */\n          const wrapped = responseBody[xmlName!];\n          const elementList = wrapped?.[xmlElementName!] ?? [];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            elementList,\n            propertyObjectName,\n            options\n          );\n        } else {\n          const property = responseBody[propertyName!];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            property,\n            propertyObjectName,\n            options\n          );\n        }\n      }\n    } else {\n      // deserialize the property if it is present in the provided responseBody instance\n      let propertyInstance;\n      let res = responseBody;\n      // traversing the object step by step.\n      for (const item of paths) {\n        if (!res) break;\n        res = res[item];\n      }\n      propertyInstance = res;\n      const polymorphicDiscriminator = mapper.type.polymorphicDiscriminator;\n      // checking that the model property name (key)(ex: \"fishtype\") and the\n      // clientName of the polymorphicDiscriminator {metadata} (ex: \"fishtype\")\n      // instead of the serializedName of the polymorphicDiscriminator (ex: \"fish.type\")\n      // is a better approach. The generator is not consistent with escaping '\\.' in the\n      // serializedName of the property (ex: \"fish\\.type\") that is marked as polymorphic discriminator\n      // and the serializedName of the metadata polymorphicDiscriminator (ex: \"fish.type\"). However,\n      // the clientName transformation of the polymorphicDiscriminator (ex: \"fishtype\") and\n      // the transformation of model property name (ex: \"fishtype\") is done consistently.\n      // Hence, it is a safer bet to rely on the clientName of the polymorphicDiscriminator.\n      if (\n        polymorphicDiscriminator &&\n        key === polymorphicDiscriminator.clientName &&\n        propertyInstance == undefined\n      ) {\n        propertyInstance = mapper.serializedName;\n      }\n\n      let serializedValue;\n      // paging\n      if (Array.isArray(responseBody[key]) && modelProps[key].serializedName === \"\") {\n        propertyInstance = responseBody[key];\n        const arrayInstance = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options\n        );\n        // Copy over any properties that have already been added into the instance, where they do\n        // not exist on the newly de-serialized array\n        for (const [k, v] of Object.entries(instance)) {\n          if (!Object.prototype.hasOwnProperty.call(arrayInstance, k)) {\n            arrayInstance[k] = v;\n          }\n        }\n        instance = arrayInstance;\n      } else if (propertyInstance !== undefined || propertyMapper.defaultValue !== undefined) {\n        serializedValue = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options\n        );\n        instance[key] = serializedValue;\n      }\n    }\n  }\n\n  const additionalPropertiesMapper = mapper.type.additionalProperties;\n  if (additionalPropertiesMapper) {\n    const isAdditionalProperty = (responsePropName: string): boolean => {\n      for (const clientPropName in modelProps) {\n        const paths = splitSerializeName(modelProps[clientPropName].serializedName);\n        if (paths[0] === responsePropName) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    for (const responsePropName in responseBody) {\n      if (isAdditionalProperty(responsePropName)) {\n        instance[responsePropName] = serializer.deserialize(\n          additionalPropertiesMapper,\n          responseBody[responsePropName],\n          objectName + '[\"' + responsePropName + '\"]',\n          options\n        );\n      }\n    }\n  } else if (responseBody) {\n    for (const key of Object.keys(responseBody)) {\n      if (\n        instance[key] === undefined &&\n        !handledPropertyNames.includes(key) &&\n        !isSpecialXmlProperty(key, options)\n      ) {\n        instance[key] = responseBody[key];\n      }\n    }\n  }\n\n  return instance;\n}\n\nfunction deserializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  responseBody: any,\n  objectName: string,\n  options: Required<SerializerOptions>\n): { [key: string]: any } {\n  const value = mapper.type.value;\n  if (!value || typeof value !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`\n    );\n  }\n  if (responseBody) {\n    const tempDictionary: { [key: string]: any } = {};\n    for (const key of Object.keys(responseBody)) {\n      tempDictionary[key] = serializer.deserialize(value, responseBody[key], objectName, options);\n    }\n    return tempDictionary;\n  }\n  return responseBody;\n}\n\nfunction deserializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  responseBody: any,\n  objectName: string,\n  options: Required<SerializerOptions>\n): any[] {\n  const element = mapper.type.element;\n  if (!element || typeof element !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`\n    );\n  }\n  if (responseBody) {\n    if (!Array.isArray(responseBody)) {\n      // xml2js will interpret a single element array as just the element, so force it to be an array\n      responseBody = [responseBody];\n    }\n\n    const tempArray = [];\n    for (let i = 0; i < responseBody.length; i++) {\n      tempArray[i] = serializer.deserialize(\n        element,\n        responseBody[i],\n        `${objectName}[${i}]`,\n        options\n      );\n    }\n    return tempArray;\n  }\n  return responseBody;\n}\n\nfunction getPolymorphicMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  polymorphicPropertyName: \"clientName\" | \"serializedName\"\n): CompositeMapper {\n  const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n  if (polymorphicDiscriminator) {\n    const discriminatorName = polymorphicDiscriminator[polymorphicPropertyName];\n    if (discriminatorName != undefined) {\n      const discriminatorValue = object[discriminatorName];\n      if (discriminatorValue != undefined) {\n        const typeName = mapper.type.uberParent || mapper.type.className;\n        const indexDiscriminator =\n          discriminatorValue === typeName\n            ? discriminatorValue\n            : typeName + \".\" + discriminatorValue;\n        const polymorphicMapper = serializer.modelMappers.discriminators[indexDiscriminator];\n        if (polymorphicMapper) {\n          mapper = polymorphicMapper;\n        }\n      }\n    }\n  }\n  return mapper;\n}\n\nfunction getPolymorphicDiscriminatorRecursively(\n  serializer: Serializer,\n  mapper: CompositeMapper\n): PolymorphicDiscriminator | undefined {\n  return (\n    mapper.type.polymorphicDiscriminator ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.uberParent) ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.className)\n  );\n}\n\nfunction getPolymorphicDiscriminatorSafely(serializer: Serializer, typeName?: string): any {\n  return (\n    typeName &&\n    serializer.modelMappers[typeName] &&\n    serializer.modelMappers[typeName].type.polymorphicDiscriminator\n  );\n}\n\nexport interface MapperConstraints {\n  InclusiveMaximum?: number;\n  ExclusiveMaximum?: number;\n  InclusiveMinimum?: number;\n  ExclusiveMinimum?: number;\n  MaxLength?: number;\n  MinLength?: number;\n  Pattern?: RegExp;\n  MaxItems?: number;\n  MinItems?: number;\n  UniqueItems?: true;\n  MultipleOf?: number;\n}\n\nexport type MapperType =\n  | SimpleMapperType\n  | CompositeMapperType\n  | SequenceMapperType\n  | DictionaryMapperType\n  | EnumMapperType;\n\nexport interface SimpleMapperType {\n  name:\n    | \"Base64Url\"\n    | \"Boolean\"\n    | \"ByteArray\"\n    | \"Date\"\n    | \"DateTime\"\n    | \"DateTimeRfc1123\"\n    | \"Object\"\n    | \"Stream\"\n    | \"String\"\n    | \"TimeSpan\"\n    | \"UnixTime\"\n    | \"Uuid\"\n    | \"Number\"\n    | \"any\";\n}\n\nexport interface CompositeMapperType {\n  name: \"Composite\";\n\n  // Only one of the two below properties should be present.\n  // Use className to reference another type definition,\n  // and use modelProperties/additionalProperties when the reference to the other type has been resolved.\n  className?: string;\n\n  modelProperties?: { [propertyName: string]: Mapper };\n  additionalProperties?: Mapper;\n\n  uberParent?: string;\n  polymorphicDiscriminator?: PolymorphicDiscriminator;\n}\n\nexport interface SequenceMapperType {\n  name: \"Sequence\";\n  element: Mapper;\n}\n\nexport interface DictionaryMapperType {\n  name: \"Dictionary\";\n  value: Mapper;\n}\n\nexport interface EnumMapperType {\n  name: \"Enum\";\n  allowedValues: any[];\n}\n\nexport interface BaseMapper {\n  /**\n   * Name for the xml element\n   */\n  xmlName?: string;\n  /**\n   * Xml element namespace\n   */\n  xmlNamespace?: string;\n  /**\n   * Xml element namespace prefix\n   */\n  xmlNamespacePrefix?: string;\n  /**\n   * Determines if the current property should be serialized as an attribute of the parent xml element\n   */\n  xmlIsAttribute?: boolean;\n  /**\n   * Name for the xml elements when serializing an array\n   */\n  xmlElementName?: string;\n  /**\n   * Whether or not the current property should have a wrapping XML element\n   */\n  xmlIsWrapped?: boolean;\n  /**\n   * Whether or not the current property is readonly\n   */\n  readOnly?: boolean;\n  /**\n   * Whether or not the current property is a constant\n   */\n  isConstant?: boolean;\n  /**\n   * Whether or not the current property is required\n   */\n  required?: boolean;\n  /**\n   * Whether or not the current property allows mull as a value\n   */\n  nullable?: boolean;\n  /**\n   * The name to use when serializing\n   */\n  serializedName?: string;\n  /**\n   * Type of the mapper\n   */\n  type: MapperType;\n  /**\n   * Default value when one is not explicitly provided\n   */\n  defaultValue?: any;\n  /**\n   * Constraints to test the current value against\n   */\n  constraints?: MapperConstraints;\n}\n\nexport type Mapper = BaseMapper | CompositeMapper | SequenceMapper | DictionaryMapper | EnumMapper;\n\nexport interface PolymorphicDiscriminator {\n  serializedName: string;\n  clientName: string;\n  [key: string]: string;\n}\n\nexport interface CompositeMapper extends BaseMapper {\n  type: CompositeMapperType;\n}\n\nexport interface SequenceMapper extends BaseMapper {\n  type: SequenceMapperType;\n}\n\nexport interface DictionaryMapper extends BaseMapper {\n  type: DictionaryMapperType;\n  headerCollectionPrefix?: string;\n}\n\nexport interface EnumMapper extends BaseMapper {\n  type: EnumMapperType;\n}\n\nexport interface UrlParameterValue {\n  value: string;\n  skipUrlEncoding: boolean;\n}\n\n// TODO: why is this here?\nexport function serializeObject(toSerialize: unknown): any {\n  const castToSerialize = toSerialize as Record<string, unknown>;\n  if (toSerialize == undefined) return undefined;\n  if (toSerialize instanceof Uint8Array) {\n    toSerialize = base64.encodeByteArray(toSerialize);\n    return toSerialize;\n  } else if (toSerialize instanceof Date) {\n    return toSerialize.toISOString();\n  } else if (Array.isArray(toSerialize)) {\n    const array = [];\n    for (let i = 0; i < toSerialize.length; i++) {\n      array.push(serializeObject(toSerialize[i]));\n    }\n    return array;\n  } else if (typeof toSerialize === \"object\") {\n    const dictionary: { [key: string]: any } = {};\n    for (const property in toSerialize) {\n      dictionary[property] = serializeObject(castToSerialize[property]);\n    }\n    return dictionary;\n  }\n  return toSerialize;\n}\n\n/**\n * Utility function to create a K:V from a list of strings\n */\nfunction strEnum<T extends string>(o: Array<T>): { [K in T]: K } {\n  const result: any = {};\n  for (const key of o) {\n    result[key] = key;\n  }\n  return result;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const MapperType = strEnum([\n  \"Base64Url\",\n  \"Boolean\",\n  \"ByteArray\",\n  \"Composite\",\n  \"Date\",\n  \"DateTime\",\n  \"DateTimeRfc1123\",\n  \"Dictionary\",\n  \"Enum\",\n  \"Number\",\n  \"Object\",\n  \"Sequence\",\n  \"String\",\n  \"Stream\",\n  \"TimeSpan\",\n  \"UnixTime\"\n]);\n"]}