{"version": 3, "file": "nodeFetchHttpClient.js", "sourceRoot": "", "sources": ["../../src/nodeFetchHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,KAAK,MAAM,cAAc,CAAC;AACtC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,UAAU,MAAM,YAAY,CAAC;AAEpC,OAAO,EACL,eAAe,EAIhB,MAAM,mBAAmB,CAAC;AAG3B,OAAO,EAAE,gBAAgB,EAAc,UAAU,EAAE,MAAM,cAAc,CAAC;AAOxE,SAAS,cAAc,CACrB,OAAgB,EAChB,UAAsB;IAEtB,OAAO,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC;AAChE,CAAC;AAED,MAAM,OAAO,mBAAoB,SAAQ,eAAe;IAAxD;;QACE,wFAAwF;QAChF,kBAAa,GAA4B,IAAI,GAAG,EAAE,CAAC;QACnD,oBAAe,GAAe,EAAE,CAAC;QAExB,cAAS,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IA0GnF,CAAC;IAxGS,gBAAgB,CAAC,WAA4B;;QACnD,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAE5C,2DAA2D;QAC3D,6DAA6D;QAC7D,sDAAsD;QACtD,IAAI,WAAW,CAAC,aAAa,EAAE;YAC7B,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,aAAa,CAAC;YACrE,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;YACtD,MAAM,WAAW,GAAG,MAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,mCAAI,EAAE,CAAC;YAEtD,IAAI,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YACjD,IAAI,KAAK,EAAE;gBACT,OAAO,KAAK,CAAC;aACd;YAED,MAAM,MAAM,GAAe,gBAAgB,CACzC,WAAW,CAAC,GAAG,EACf,WAAW,CAAC,aAAa,EACzB,WAAW,CAAC,OAAO,CACpB,CAAC;YAEF,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YACrB,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,WAAW,CAAC,UAAU,GAAG,MAAM,CAAC,KAAoB,CAAC;aACtD;iBAAM;gBACL,WAAW,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;aACtC;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YAEzC,OAAO,KAAK,CAAC;SACd;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE;YAChC,IAAI,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC1D,IAAI,KAAK,EAAE;gBACT,OAAO,KAAK,CAAC;aACd;YAED,MAAM,YAAY,GAA2C;gBAC3D,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC,CAAC;YAEF,IAAI,OAAO,EAAE;gBACX,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;aACzE;iBAAM;gBACL,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;aACvE;YAED,OAAO,KAAK,CAAC;SACd;aAAM;YACL,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;SACvD;IACH,CAAC;IAED,6EAA6E;IAC7E,KAAK,CAAC,KAAK,CAAC,KAAwB,EAAE,IAAwB;QAC5D,OAAQ,UAAU,CAAC,KAAK,EAAE,IAAI,CAAwC,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,WAA4B;QAC/C,MAAM,WAAW,GAA+D,EAAE,CAAC;QAEnF,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACxD,MAAM,YAAY,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACjE,IAAI,CAAC,SAAU,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;oBAC/D,IAAI,GAAG,EAAE;wBACP,MAAM,CAAC,GAAG,CAAC,CAAC;qBACb;yBAAM;wBACL,OAAO,CAAC,MAAM,CAAC,CAAC;qBACjB;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;SACjD;QAED,wBAAwB;QACxB,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAEvD,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,kBAAkB,CAAC;QAEtD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,iBAAwC;QAC3D,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,eAAe,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACpE,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC1C,IAAI,CAAC,SAAU,CAAC,SAAS,CACvB,eAAe,EACf,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAC7B,EAAE,WAAW,EAAE,IAAI,EAAE,EACrB,CAAC,GAAG,EAAE,EAAE;wBACN,IAAI,GAAG,EAAE;4BACP,MAAM,CAAC,GAAG,CAAC,CAAC;yBACb;6BAAM;4BACL,OAAO,EAAE,CAAC;yBACX;oBACH,CAAC,CACF,CAAC;gBACJ,CAAC,CAAC,CAAC;aACJ;SACF;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as tough from \"tough-cookie\";\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport node_fetch from \"node-fetch\";\n\nimport {\n  FetchHttpClient,\n  CommonRequestInfo,\n  CommonRequestInit,\n  CommonResponse\n} from \"./fetchHttpClient\";\nimport { HttpOperationResponse } from \"./httpOperationResponse\";\nimport { WebResourceLike } from \"./webResource\";\nimport { createProxyAgent, ProxyAgent, isUrlHttps } from \"./proxyAgent\";\n\ninterface AgentCache {\n  httpAgent?: http.Agent;\n  httpsAgent?: https.Agent;\n}\n\nfunction getCachedAgent(\n  isHttps: boolean,\n  agentCache: AgentCache\n): http.Agent | https.Agent | undefined {\n  return isHttps ? agentCache.httpsAgent : agentCache.httpAgent;\n}\n\nexport class NodeFetchHttpClient extends FetchHttpClient {\n  // a mapping of proxy settings string `${host}:${port}:${username}:${password}` to agent\n  private proxyAgentMap: Map<string, AgentCache> = new Map();\n  private keepAliveAgents: AgentCache = {};\n\n  private readonly cookieJar = new tough.CookieJar(undefined, { looseMode: true });\n\n  private getOrCreateAgent(httpRequest: WebResourceLike): http.Agent | https.Agent {\n    const isHttps = isUrlHttps(httpRequest.url);\n\n    // At the moment, proxy settings and keepAlive are mutually\n    // exclusive because the 'tunnel' library currently lacks the\n    // ability to create a proxy with keepAlive turned on.\n    if (httpRequest.proxySettings) {\n      const { host, port, username, password } = httpRequest.proxySettings;\n      const key = `${host}:${port}:${username}:${password}`;\n      const proxyAgents = this.proxyAgentMap.get(key) ?? {};\n\n      let agent = getCachedAgent(isHttps, proxyAgents);\n      if (agent) {\n        return agent;\n      }\n\n      const tunnel: ProxyAgent = createProxyAgent(\n        httpRequest.url,\n        httpRequest.proxySettings,\n        httpRequest.headers\n      );\n\n      agent = tunnel.agent;\n      if (tunnel.isHttps) {\n        proxyAgents.httpsAgent = tunnel.agent as https.Agent;\n      } else {\n        proxyAgents.httpAgent = tunnel.agent;\n      }\n      this.proxyAgentMap.set(key, proxyAgents);\n\n      return agent;\n    } else if (httpRequest.keepAlive) {\n      let agent = getCachedAgent(isHttps, this.keepAliveAgents);\n      if (agent) {\n        return agent;\n      }\n\n      const agentOptions: http.AgentOptions | https.AgentOptions = {\n        keepAlive: httpRequest.keepAlive\n      };\n\n      if (isHttps) {\n        agent = this.keepAliveAgents.httpsAgent = new https.Agent(agentOptions);\n      } else {\n        agent = this.keepAliveAgents.httpAgent = new http.Agent(agentOptions);\n      }\n\n      return agent;\n    } else {\n      return isHttps ? https.globalAgent : http.globalAgent;\n    }\n  }\n\n  // eslint-disable-next-line @azure/azure-sdk/ts-apisurface-standardized-verbs\n  async fetch(input: CommonRequestInfo, init?: CommonRequestInit): Promise<CommonResponse> {\n    return (node_fetch(input, init) as unknown) as Promise<CommonResponse>;\n  }\n\n  async prepareRequest(httpRequest: WebResourceLike): Promise<Partial<RequestInit>> {\n    const requestInit: Partial<RequestInit & { agent?: any; compress?: boolean }> = {};\n\n    if (this.cookieJar && !httpRequest.headers.get(\"Cookie\")) {\n      const cookieString = await new Promise<string>((resolve, reject) => {\n        this.cookieJar!.getCookieString(httpRequest.url, (err, cookie) => {\n          if (err) {\n            reject(err);\n          } else {\n            resolve(cookie);\n          }\n        });\n      });\n\n      httpRequest.headers.set(\"Cookie\", cookieString);\n    }\n\n    // Set the http(s) agent\n    requestInit.agent = this.getOrCreateAgent(httpRequest);\n\n    requestInit.compress = httpRequest.decompressResponse;\n\n    return requestInit;\n  }\n\n  async processRequest(operationResponse: HttpOperationResponse): Promise<void> {\n    if (this.cookieJar) {\n      const setCookieHeader = operationResponse.headers.get(\"Set-Cookie\");\n      if (setCookieHeader !== undefined) {\n        await new Promise<void>((resolve, reject) => {\n          this.cookieJar!.setCookie(\n            setCookieHeader,\n            operationResponse.request.url,\n            { ignoreError: true },\n            (err) => {\n              if (err) {\n                reject(err);\n              } else {\n                resolve();\n              }\n            }\n          );\n        });\n      }\n    }\n  }\n}\n"]}