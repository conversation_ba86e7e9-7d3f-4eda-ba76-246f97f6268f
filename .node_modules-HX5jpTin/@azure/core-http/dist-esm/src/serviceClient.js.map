{"version": 3, "file": "serviceClient.js", "sourceRoot": "", "sources": ["../../src/serviceClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAmB,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAItE,OAAO,EAAE,SAAS,EAAoB,MAAM,sBAAsB,CAAC;AAEnE,OAAO,EACL,0BAA0B,EAC1B,8BAA8B,EAG/B,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,4BAA4B,EAAiB,MAAM,iBAAiB,CAAC;AAC9E,OAAO,EACL,qBAAqB,EAErB,6BAA6B,EAC9B,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AAChG,OAAO,EAAE,6BAA6B,EAAE,MAAM,0CAA0C,CAAC;AACzF,OAAO,EACL,eAAe,EACf,6BAA6B,EAC7B,wBAAwB,EACzB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,cAAc,EAAE,sBAAsB,EAAE,MAAM,2BAA2B,CAAC;AACnF,OAAO,EAGL,oBAAoB,EACrB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,oBAAoB,EAAE,MAAM,iCAAiC,CAAC;AACvE,OAAO,EAAE,+BAA+B,EAAE,MAAM,4CAA4C,CAAC;AAC7F,OAAO,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAC;AAC3E,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAA6C,UAAU,EAAc,MAAM,cAAc,CAAC;AACjG,OAAO,EAAE,UAAU,EAAE,MAAM,OAAO,CAAC;AACnC,OAAO,KAAK,KAAK,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,YAAY,EAAE,MAAM,YAAY,CAAC;AAC1C,OAAO,EAGL,WAAW,EAEX,iBAAiB,EAClB,MAAM,eAAe,CAAC;AAEvB,OAAO,EAAmB,MAAM,EAAE,MAAM,cAAc,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AAEzE,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,uBAAuB,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AACtF,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,kCAAkC,EAAE,MAAM,+CAA+C,CAAC;AACnG,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAE,WAAW,EAAqB,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvF,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAC5B,OAAO,EAAE,0BAA0B,EAAE,MAAM,mBAAmB,CAAC;AAgG/D;;GAEG;AACH,MAAM,OAAO,aAAa;IAsBxB;;;;OAIG;IACH,YACE,WAAwD;IACxD,iEAAiE;IACjE,OAA8B;QAE9B,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,EAAE,CAAC;SACd;QAED,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,IAAI,KAAK,CAAC;QACzD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,0BAA0B,EAAE,CAAC;QACtE,IAAI,CAAC,qBAAqB,GAAG,IAAI,oBAAoB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAElF,IAAI,sBAA8C,CAAC;QACnD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE;YACjD,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC5D,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;SACzD;aAAM;YACL,IAAI,iBAAiB,GAAqC,SAAS,CAAC;YACpE,IAAI,iBAAiB,CAAC,WAAW,CAAC,EAAE;gBAClC,MAAM,CAAC,IAAI,CACT,sFAAsF,CACvF,CAAC;gBACF,wEAAwE;gBACxE,yEAAyE;gBACzE,oEAAoE;gBACpE,6EAA6E;gBAC7E,sEAAsE;gBACtE,gCAAgC;gBAChC,MAAM,oBAAoB,GAA+B,GAAG,EAAE;oBAC5D,IAAI,wBAAwB,GAAqC,SAAS,CAAC;oBAC3E,4DAA4D;oBAC5D,MAAM,aAAa,GAAG,IAAI,CAAC;oBAC3B,MAAM,oBAAoB,GAAG,OAAO,CAAC;oBACrC,OAAO;wBACL,MAAM,CAAC,UAAyB,EAAE,aAAmC;4BACnE,MAAM,gBAAgB,GAAG,mBAAmB,CAC1C,oBAAoB,EACpB,aAAa,CAAC,OAAO,CACtB,CAAC;4BAEF,IAAI,CAAC,gBAAgB,EAAE;gCACrB,MAAM,IAAI,KAAK,CACb,mKAAmK,CACpK,CAAC;6BACH;4BAED,IAAI,wBAAwB,KAAK,SAAS,IAAI,wBAAwB,KAAK,IAAI,EAAE;gCAC/E,wBAAwB,GAAG,+BAA+B,CACxD,WAAW,EACX,gBAAgB,CACjB,CAAC;6BACH;4BAED,OAAO,wBAAwB,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;wBACpE,CAAC;qBACF,CAAC;gBACJ,CAAC,CAAC;gBAEF,iBAAiB,GAAG,oBAAoB,EAAE,CAAC;aAC5C;iBAAM,IAAI,WAAW,IAAI,OAAO,WAAW,CAAC,WAAW,KAAK,UAAU,EAAE;gBACvE,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;gBAChF,iBAAiB,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;aAChD;iBAAM,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;gBAC5D,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;aAC1F;YAED,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAC7D,sBAAsB,GAAG,mCAAmC,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YACzF,IAAI,OAAO,CAAC,sBAAsB,EAAE;gBAClC,yEAAyE;gBACzE,2CAA2C;gBAC3C,MAAM,yBAAyB,GAEF,OAAO,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;gBACpF,IAAI,yBAAyB,EAAE;oBAC7B,sBAAsB,GAAG,yBAAyB,CAAC;iBACpD;aACF;SACF;QACD,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAAgD;QAC1D,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC5E,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;SACvF;QAED,IAAI,WAA4B,CAAC;QACjC,IAAI;YACF,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE;gBAC9B,OAAO,CAAC,yBAAyB,EAAE,CAAC;gBACpC,WAAW,GAAG,OAAO,CAAC;aACvB;iBAAM;gBACL,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;gBAChC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAC5C;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,IAAI,YAAY,GAAkB,IAAI,CAAC,WAAW,CAAC;QACnD,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3E,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;gBACjE,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,MAAM,CACnD,YAAY,EACZ,IAAI,CAAC,qBAAqB,CAC3B,CAAC;aACH;SACF;QACD,OAAO,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,oBAAoB,CACxB,kBAAsC,EACtC,aAA4B,EAC5B,QAA+B;;QAE/B,IAAI,OAAO,kBAAkB,CAAC,OAAO,KAAK,UAAU,EAAE;YACpD,QAAQ,GAAG,kBAAkB,CAAC,OAAO,CAAC;YACtC,kBAAkB,CAAC,OAAO,GAAG,SAAS,CAAC;SACxC;QAED,MAAM,iBAAiB,GAAG,MAAA,kBAAkB,CAAC,OAAO,0CAAE,iBAAiB,CAAC;QACxE,MAAM,WAAW,GAAoB,IAAI,WAAW,EAAE,CAAC;QAEvD,IAAI,MAA6B,CAAC;QAClC,IAAI;YACF,MAAM,OAAO,GAAuB,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;YAC1E,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CACb,0IAA0I,CAC3I,CAAC;aACH;YAED,WAAW,CAAC,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC;YAC9C,WAAW,CAAC,aAAa,GAAG,aAAa,CAAC;YAE1C,MAAM,UAAU,GAAe,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzD,IAAI,aAAa,CAAC,IAAI,EAAE;gBACtB,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAC3C;YACD,IAAI,aAAa,CAAC,aAAa,IAAI,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzE,KAAK,MAAM,YAAY,IAAI,aAAa,CAAC,aAAa,EAAE;oBACtD,IAAI,iBAAiB,GAAW,sCAAsC,CACpE,IAAI,EACJ,kBAAkB,EAClB,YAAY,EACZ,aAAa,CAAC,UAAU,CACzB,CAAC;oBACF,iBAAiB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACpD,YAAY,CAAC,MAAM,EACnB,iBAAiB,EACjB,0BAA0B,CAAC,YAAY,CAAC,EACxC,iBAAiB,CAClB,CAAC;oBACF,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;wBAC9B,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;qBAC3D;oBACD,UAAU,CAAC,UAAU,CACnB,IAAI,YAAY,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,YAAY,CAAC,GAAG,EACrF,iBAAiB,CAClB,CAAC;iBACH;aACF;YACD,IAAI,aAAa,CAAC,eAAe,IAAI,aAAa,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7E,KAAK,MAAM,cAAc,IAAI,aAAa,CAAC,eAAe,EAAE;oBAC1D,IAAI,mBAAmB,GAAQ,sCAAsC,CACnE,IAAI,EACJ,kBAAkB,EAClB,cAAc,EACd,aAAa,CAAC,UAAU,CACzB,CAAC;oBACF,IAAI,mBAAmB,KAAK,SAAS,IAAI,mBAAmB,KAAK,IAAI,EAAE;wBACrE,mBAAmB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACtD,cAAc,CAAC,MAAM,EACrB,mBAAmB,EACnB,0BAA0B,CAAC,cAAc,CAAC,EAC1C,iBAAiB,CAClB,CAAC;wBACF,IACE,cAAc,CAAC,gBAAgB,KAAK,SAAS;4BAC7C,cAAc,CAAC,gBAAgB,KAAK,IAAI,EACxC;4BACA,IAAI,cAAc,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,KAAK,EAAE;gCACnE,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;oCACpC,6EAA6E;oCAC7E,SAAS;iCACV;qCAAM;oCACL,KAAK,MAAM,KAAK,IAAI,mBAAmB,EAAE;wCACvC,MAAM,IAAI,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;wCACxC,mBAAmB,CAAC,KAAK,CAAC;4CACxB,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;qCAC9D;iCACF;6BACF;iCAAM,IACL,cAAc,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,GAAG;gCAC7D,cAAc,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,GAAG,EAC7D;gCACA,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;6BACjF;yBACF;wBACD,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;4BAChC,IAAI,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;gCACtC,KAAK,MAAM,KAAK,IAAI,mBAAmB,EAAE;oCACvC,IACE,mBAAmB,CAAC,KAAK,CAAC,KAAK,SAAS;wCACxC,mBAAmB,CAAC,KAAK,CAAC,KAAK,IAAI,EACnC;wCACA,mBAAmB,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;qCAC7E;iCACF;6BACF;iCAAM;gCACL,mBAAmB,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;6BAC/D;yBACF;wBACD,IACE,cAAc,CAAC,gBAAgB,KAAK,SAAS;4BAC7C,cAAc,CAAC,gBAAgB,KAAK,IAAI;4BACxC,cAAc,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,KAAK;4BAC/D,cAAc,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,GAAG;4BAC7D,cAAc,CAAC,gBAAgB,KAAK,qBAAqB,CAAC,GAAG,EAC7D;4BACA,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;yBACjF;wBACD,UAAU,CAAC,iBAAiB,CAC1B,cAAc,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,cAAc,CAAC,EAClF,mBAAmB,CACpB,CAAC;qBACH;iBACF;aACF;YACD,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;YAExC,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,kBAAkB,CAAC;YACzE,IAAI,WAAW,IAAI,aAAa,CAAC,WAAW,EAAE;gBAC5C,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;aACtD;YAED,IAAI,aAAa,CAAC,gBAAgB,EAAE;gBAClC,KAAK,MAAM,eAAe,IAAI,aAAa,CAAC,gBAAgB,EAAE;oBAC5D,IAAI,WAAW,GAAQ,sCAAsC,CAC3D,IAAI,EACJ,kBAAkB,EAClB,eAAe,EACf,aAAa,CAAC,UAAU,CACzB,CAAC;oBACF,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;wBACrD,WAAW,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CAC9C,eAAe,CAAC,MAAM,EACtB,WAAW,EACX,0BAA0B,CAAC,eAAe,CAAC,EAC3C,iBAAiB,CAClB,CAAC;wBACF,MAAM,sBAAsB,GAAI,eAAe,CAAC,MAA2B;6BACxE,sBAAsB,CAAC;wBAC1B,IAAI,sBAAsB,EAAE;4BAC1B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gCAC1C,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;6BACzE;yBACF;6BAAM;4BACL,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,eAAe,CAAC,MAAM,CAAC,cAAc;gCACnC,0BAA0B,CAAC,eAAe,CAAC,EAC7C,WAAW,CACZ,CAAC;yBACH;qBACF;iBACF;aACF;YAED,MAAM,OAAO,GAAmC,kBAAkB,CAAC,OAAO,CAAC;YAC3E,IAAI,OAAO,EAAE;gBACX,IAAI,OAAO,CAAC,aAAa,EAAE;oBACzB,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,aAAa,EAAE;wBACpD,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;qBACpF;iBACF;gBAED,IAAI,OAAO,CAAC,WAAW,EAAE;oBACvB,WAAW,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;iBAC/C;gBAED,IAAI,OAAO,CAAC,OAAO,EAAE;oBACnB,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;iBACvC;gBAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;oBAC5B,WAAW,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;iBACzD;gBAED,IAAI,OAAO,CAAC,kBAAkB,EAAE;oBAC9B,WAAW,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;iBAC7D;gBAED,IAAI,OAAO,CAAC,WAAW,EAAE;oBACvB,+HAA+H;oBAC9H,WAAmB,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;iBACxD;gBAED,IAAI,OAAO,CAAC,cAAc,EAAE;oBAC1B,WAAW,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;iBACrD;gBAED,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,IAAI,OAAO,CAAC,iBAAiB,KAAK,IAAI,EAAE;oBACjF,WAAW,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;iBAC3D;aACF;YAED,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAEpD,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;YAE3E,IAAI,WAAW,CAAC,yBAAyB,KAAK,SAAS,EAAE;gBACvD,WAAW,CAAC,yBAAyB,GAAG,4BAA4B,CAAC,aAAa,CAAC,CAAC;aACrF;YAED,IAAI,WAAkC,CAAC;YACvC,IAAI,gBAAgB,CAAC;YACrB,IAAI;gBACF,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;aACnD;YAAC,OAAO,KAAK,EAAE;gBACd,gBAAgB,GAAG,KAAK,CAAC;aAC1B;YACD,IAAI,gBAAgB,EAAE;gBACpB,IAAI,gBAAgB,CAAC,QAAQ,EAAE;oBAC7B,gBAAgB,CAAC,OAAO,GAAG,eAAe,CACxC,gBAAgB,CAAC,QAAQ,EACzB,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC;wBAClD,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CACrC,CAAC;iBACH;gBACD,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;aAC3C;iBAAM;gBACL,MAAM,GAAG,OAAO,CAAC,OAAO,CACtB,eAAe,CAAC,WAAY,EAAE,aAAa,CAAC,SAAS,CAAC,WAAY,CAAC,MAAM,CAAC,CAAC,CAC5E,CAAC;aACH;SACF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAChC;QAED,MAAM,EAAE,GAAG,QAAQ,CAAC;QACpB,IAAI,EAAE,EAAE;YACN,MAAM;iBACH,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;iBACvF,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;SAC5B;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAED,MAAM,UAAU,oBAAoB,CAClC,aAA4B,EAC5B,WAA4B,EAC5B,kBAAsC,EACtC,aAA4B;;IAE5B,MAAM,iBAAiB,GAAG,MAAA,MAAA,kBAAkB,CAAC,OAAO,0CAAE,iBAAiB,mCAAI,EAAE,CAAC;IAC9E,MAAM,cAAc,GAAgC;QAClD,QAAQ,EAAE,MAAA,iBAAiB,CAAC,QAAQ,mCAAI,EAAE;QAC1C,WAAW,EAAE,MAAA,iBAAiB,CAAC,WAAW,mCAAI,KAAK;QACnD,UAAU,EAAE,MAAA,iBAAiB,CAAC,UAAU,mCAAI,WAAW;KACxD,CAAC;IAEF,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;IAChD,IAAI,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE;QACjE,WAAW,CAAC,IAAI,GAAG,sCAAsC,CACvD,aAAa,EACb,kBAAkB,EAClB,aAAa,CAAC,WAAW,EACzB,aAAa,CAAC,UAAU,CACzB,CAAC;QAEF,MAAM,UAAU,GAAG,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC;QACpD,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,cAAc,EACd,cAAc,EACd,YAAY,EACZ,kBAAkB,EACnB,GAAG,UAAU,CAAC;QACf,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QAEtC,IAAI;YACF,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,QAAQ,EAAE;gBAC7E,MAAM,8BAA8B,GAAW,0BAA0B,CACvE,aAAa,CAAC,WAAW,CAC1B,CAAC;gBACF,WAAW,CAAC,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACnD,UAAU,EACV,WAAW,CAAC,IAAI,EAChB,8BAA8B,EAC9B,cAAc,CACf,CAAC;gBAEF,MAAM,QAAQ,GAAG,QAAQ,KAAK,UAAU,CAAC,MAAM,CAAC;gBAEhD,IAAI,aAAa,CAAC,KAAK,EAAE;oBACvB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,CAAC,CAAC,SAAS,kBAAkB,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;oBAC9E,MAAM,KAAK,GAAG,wBAAwB,CACpC,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,WAAW,CAAC,IAAI,EAChB,cAAc,CACf,CAAC;oBACF,IAAI,QAAQ,KAAK,UAAU,CAAC,QAAQ,EAAE;wBACpC,WAAW,CAAC,IAAI,GAAG,YAAY,CAC7B,KAAK,CAAC,kBAAkB,CACtB,KAAK,EACL,cAAc,IAAI,OAAO,IAAI,cAAe,EAC5C,QAAQ,EACR,YAAY,CACb,EACD;4BACE,QAAQ,EAAE,OAAO,IAAI,cAAc;4BACnC,UAAU;yBACX,CACF,CAAC;qBACH;yBAAM,IAAI,CAAC,QAAQ,EAAE;wBACpB,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE;4BACrC,QAAQ,EAAE,OAAO,IAAI,cAAc;4BACnC,UAAU;yBACX,CAAC,CAAC;qBACJ;iBACF;qBAAM,IACL,QAAQ,KAAK,UAAU,CAAC,MAAM;oBAC9B,CAAC,CAAA,MAAA,aAAa,CAAC,WAAW,0CAAE,KAAK,CAAC,YAAY,CAAC,KAAI,aAAa,CAAC,SAAS,KAAK,MAAM,CAAC,EACtF;oBACA,oEAAoE;oBACpE,2BAA2B;oBAC3B,OAAO;iBACR;qBAAM,IAAI,CAAC,QAAQ,EAAE;oBACpB,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBACrD;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,IAAI,KAAK,CACb,UAAU,KAAK,CAAC,OAAO,2CAA2C,IAAI,CAAC,SAAS,CAC9E,cAAc,EACd,SAAS,EACT,IAAI,CACL,GAAG,CACL,CAAC;SACH;KACF;SAAM,IAAI,aAAa,CAAC,kBAAkB,IAAI,aAAa,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC1F,WAAW,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC1B,KAAK,MAAM,iBAAiB,IAAI,aAAa,CAAC,kBAAkB,EAAE;YAChE,MAAM,sBAAsB,GAAQ,sCAAsC,CACxE,aAAa,EACb,kBAAkB,EAClB,iBAAiB,EACjB,aAAa,CAAC,UAAU,CACzB,CAAC;YACF,IAAI,sBAAsB,KAAK,SAAS,IAAI,sBAAsB,KAAK,IAAI,EAAE;gBAC3E,MAAM,6BAA6B,GACjC,iBAAiB,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;gBAC3F,WAAW,CAAC,QAAQ,CAAC,6BAA6B,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACtF,iBAAiB,CAAC,MAAM,EACxB,sBAAsB,EACtB,0BAA0B,CAAC,iBAAiB,CAAC,EAC7C,cAAc,CACf,CAAC;aACH;SACF;KACF;AACH,CAAC;AAED;;GAEG;AACH,SAAS,wBAAwB,CAC/B,YAAgC,EAChC,QAAgB,EAChB,QAAgB,EAChB,eAAoB,EACpB,OAAoC;IAEpC,2FAA2F;IAC3F,sDAAsD;IACtD,IAAI,YAAY,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC/E,MAAM,MAAM,GAAQ,EAAE,CAAC;QACvB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;QAC7C,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,YAAY,EAAE,CAAC;QACnD,OAAO,MAAM,CAAC;KACf;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,wBAAwB,CAC/B,KAA8D,EAC9D,mBAAiC;IAEjC,IAAI,MAAc,CAAC;IACnB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,GAAG,KAAK,CAAC;KAChB;SAAM;QACL,MAAM,GAAG,mBAAmB,EAAE,CAAC;QAC/B,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC/B,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;SACxB;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,mCAAmC,CAC1C,iBAAmD,EACnD,OAA6B;IAE7B,MAAM,SAAS,GAA2B,EAAE,CAAC;IAE7C,IAAI,OAAO,CAAC,6BAA6B,EAAE;QACzC,SAAS,CAAC,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,CAAC;KAClF;IAED,IAAI,iBAAiB,EAAE;QACrB,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;KACnC;IAED,MAAM,mBAAmB,GAAW,wBAAwB,CAC1D,OAAO,CAAC,mBAAmB,EAC3B,6BAA6B,CAC9B,CAAC;IACF,MAAM,oBAAoB,GAAW,wBAAwB,CAC3D,OAAO,CAAC,SAAS,EACjB,wBAAwB,CACzB,CAAC;IACF,IAAI,mBAAmB,IAAI,oBAAoB,EAAE;QAC/C,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,EAAE,mBAAmB,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC;KAC5F;IACD,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACjC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;IAEzE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;QAC1B,SAAS,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;QACzC,SAAS,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;QACzC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;KACzC;IAED,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC,CAAC;IAE3E,IAAI,MAAM,EAAE;QACV,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;KACpD;IAED,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAEnD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,UAAU,yBAAyB,CACvC,eAAwC,EACxC,iBAAwC;IAExC,MAAM,sBAAsB,GAA2B,EAAE,CAAC;IAE1D,IAAI,eAAe,CAAC,iBAAiB,EAAE;QACrC,sBAAsB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;KAC7C;IAED,IAAI,cAAc,GAAG,SAAS,CAAC;IAC/B,IAAI,eAAe,CAAC,gBAAgB,IAAI,eAAe,CAAC,gBAAgB,CAAC,eAAe,EAAE;QACxF,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QAErE,iEAAiE;QACjE,iCAAiC;QACjC,MAAM,oBAAoB,GAAG,wBAAwB,EAAE,CAAC;QACxD,IAAI,aAAa,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE;YACtD,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC1C;QAED,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC1C;IAED,MAAM,gBAAgB,mCACjB,uBAAuB,GACvB,eAAe,CAAC,gBAAgB,CACpC,CAAC;IAEF,MAAM,YAAY,mCACb,mBAAmB,GACnB,eAAe,CAAC,YAAY,CAChC,CAAC;IAEF,MAAM,eAAe,mCAChB,sBAAsB,GACtB,eAAe,CAAC,eAAe,CACnC,CAAC;IAEF,IAAI,MAAM,EAAE;QACV,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC;KACxE;IAED,MAAM,sBAAsB,mCACvB,6BAA6B,GAC7B,eAAe,CAAC,sBAAsB,CAC1C,CAAC;IAEF,MAAM,cAAc,qBACf,eAAe,CAAC,cAAc,CAClC,CAAC;IAEF,sBAAsB,CAAC,IAAI,CACzB,aAAa,CAAC,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,EAC5C,eAAe,CAAC,gBAAgB,CAAC,EACjC,eAAe,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,EAC1C,6BAA6B,EAAE,EAC/B,qBAAqB,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,EAClE,qBAAqB,EAAE,EACvB,sBAAsB,EAAE,EACxB,sBAAsB,CACpB,YAAY,CAAC,UAAU,EACvB,YAAY,CAAC,cAAc,EAC3B,YAAY,CAAC,iBAAiB,CAC/B,CACF,CAAC;IAEF,IAAI,eAAe,CAAC,eAAe,EAAE;QACnC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;KACzE;IAED,IAAI,iBAAiB,EAAE;QACrB,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAChD;IAED,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC;IAEvD,IAAI,MAAM,IAAI,eAAe,CAAC,kBAAkB,KAAK,KAAK,EAAE;QAC1D,sBAAsB,CAAC,IAAI,CAAC,kCAAkC,EAAE,CAAC,CAAC;KACnE;IAED,OAAO;QACL,UAAU,EAAE,eAAe,CAAC,UAAU;QACtC,sBAAsB;KACvB,CAAC;AACJ,CAAC;AAID;;;GAGG;AACH,MAAM,UAAU,iBAAiB,CAAC,MAAsB,EAAE,YAAsB;IAC9E,IAAI,MAAM,IAAI,YAAY,EAAE;QAC1B,MAAM,kBAAkB,GAAW,YAAY,CAAC,MAAM,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YAC/C,MAAM,YAAY,GAAW,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;gBACzB,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;aAC3B;YACD,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;SAC/B;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,sCAAsC,CAC7C,aAA4B,EAC5B,kBAAsC,EACtC,SAA6B,EAC7B,UAAsB;IAEtB,OAAO,0CAA0C,CAC/C,aAAa,EACb,kBAAkB,EAClB,SAAS,CAAC,aAAa,EACvB,SAAS,CAAC,MAAM,EAChB,UAAU,CACX,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,0CAA0C,CACxD,aAA4B,EAC5B,kBAAsC,EACtC,aAA4B,EAC5B,eAAuB,EACvB,UAAsB;;IAEtB,IAAI,KAAU,CAAC;IACf,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,aAAa,GAAG,CAAC,aAAa,CAAC,CAAC;KACjC;IACD,MAAM,iBAAiB,GAAG,MAAA,kBAAkB,CAAC,OAAO,0CAAE,iBAAiB,CAAC;IACxE,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QAChC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAI,eAAe,CAAC,UAAU,EAAE;gBAC9B,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC;aACtC;iBAAM;gBACL,IAAI,oBAAoB,GAAyB,4BAA4B,CAC3E,kBAAkB,EAClB,aAAa,CACd,CAAC;gBACF,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE;oBACvC,oBAAoB,GAAG,4BAA4B,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;iBACnF;gBAED,IAAI,eAAe,GAAG,KAAK,CAAC;gBAC5B,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE;oBACvC,eAAe;wBACb,eAAe,CAAC,QAAQ;4BACxB,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;iBAClE;gBACD,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC;aAC7F;YAED,0CAA0C;YAC1C,MAAM,mBAAmB,GAAW,8BAA8B,CAChE,aAAa,EACb,eAAe,CAChB,CAAC;YACF,UAAU,CAAC,SAAS,CAAC,eAAe,EAAE,KAAK,EAAE,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;SACtF;KACF;SAAM;QACL,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC5B,KAAK,GAAG,EAAE,CAAC;SACZ;QAED,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,MAAM,cAAc,GAAY,eAAmC,CAAC,IAAI,CAAC,eAAgB,CACvF,YAAY,CACb,CAAC;YACF,MAAM,YAAY,GAAkB,aAAa,CAAC,YAAY,CAAC,CAAC;YAChE,MAAM,aAAa,GAAQ,0CAA0C,CACnE,aAAa,EACb,kBAAkB,EAClB,YAAY,EACZ,cAAc,EACd,UAAU,CACX,CAAC;YACF,0CAA0C;YAC1C,MAAM,kBAAkB,GAAW,8BAA8B,CAC/D,YAAY,EACZ,cAAc,CACf,CAAC;YACF,UAAU,CAAC,SAAS,CAAC,cAAc,EAAE,aAAa,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YAC3F,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;gBACzD,IAAI,CAAC,KAAK,EAAE;oBACV,KAAK,GAAG,EAAE,CAAC;iBACZ;gBACD,KAAK,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;aACrC;SACF;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAOD,SAAS,4BAA4B,CACnC,MAAwC,EACxC,aAAuB;IAEvB,MAAM,MAAM,GAAyB,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;IAC9D,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACpC,MAAM,iBAAiB,GAAW,aAAa,CAAC,CAAC,CAAC,CAAC;QACnD,8EAA8E;QAC9E,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,IAAI,iBAAiB,IAAI,MAAM,EAAE;YAC1E,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;SACpC;aAAM;YACL,MAAM;SACP;KACF;IACD,IAAI,CAAC,KAAK,aAAa,CAAC,MAAM,EAAE;QAC9B,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC;QAC9B,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;KAC7B;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,eAAe,CAC7B,SAAgC,EAChC,YAA2C;IAE3C,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;IAC9C,MAAM,UAAU,GAAG,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC;IAE3D,MAAM,oBAAoB,GAAG,CAC3B,GAA4B,EAG5B,EAAE;QACF,OAAO,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,WAAW,EAAE;YAC7C,KAAK,EAAE,SAAS;SACjB,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,UAAU,EAAE;QACd,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACzB,OAAO,oBAAoB,iCACtB,aAAa,KAChB,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAC5B,kBAAkB,EAAE,SAAS,CAAC,kBAAkB,IAChD,CAAC;SACJ;QAED,MAAM,eAAe,GACnB,CAAC,QAAQ,KAAK,WAAW,IAAK,UAA8B,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAC3F,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAC1D,CAAC,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,cAAc,KAAK,EAAE,CAChD,CAAC;QACF,IAAI,QAAQ,KAAK,UAAU,IAAI,kBAAkB,EAAE;YACjD,MAAM,aAAa,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE,CAAC,CAAyB,CAAC;YAEhF,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;gBAC9C,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE;oBACvC,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;iBAChD;aACF;YAED,IAAI,aAAa,EAAE;gBACjB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;oBAC5C,aAAa,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;iBACzC;aACF;YACD,oBAAoB,CAAC,aAAa,CAAC,CAAC;YACpC,OAAO,aAAa,CAAC;SACtB;QAED,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,YAAY,EAAE;YACzD,OAAO,oBAAoB,iCACtB,aAAa,GACb,SAAS,CAAC,UAAU,EACvB,CAAC;SACJ;KACF;IAED,IACE,UAAU;QACV,SAAS,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM;QACnC,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,UAAU,CAAC,EAC3C;QACA,yCAAyC;QACzC,OAAO,oBAAoB,iCACtB,aAAa,KAChB,IAAI,EAAE,SAAS,CAAC,UAAU,IAC1B,CAAC;KACJ;IAED,OAAO,oBAAoB,iCACtB,aAAa,GACb,SAAS,CAAC,UAAU,EACvB,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAA8B,EAC9B,OAAgB;IAEhB,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,EAAE;QAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACxC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;YAClD,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;KAChC;IAED,IAAI,OAAO,EAAE;QACX,OAAO,GAAG,OAAO,WAAW,CAAC;KAC9B;IACD,OAAO,SAAS,CAAC;AACnB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { TokenCredential, isTokenCredential } from \"@azure/core-auth\";\nimport { HttpClient } from \"./httpClient\";\nimport { HttpOperationResponse, RestResponse } from \"./httpOperationResponse\";\nimport { HttpPipelineLogger } from \"./httpPipelineLogger\";\nimport { logPolicy, LogPolicyOptions } from \"./policies/logPolicy\";\nimport { OperationArguments } from \"./operationArguments\";\nimport {\n  getPathStringFromParameter,\n  getPathStringFromParameterPath,\n  OperationParameter,\n  ParameterPath\n} from \"./operationParameter\";\nimport { getStreamResponseStatusCodes, OperationSpec } from \"./operationSpec\";\nimport {\n  deserializationPolicy,\n  DeserializationContentTypes,\n  DefaultDeserializationOptions\n} from \"./policies/deserializationPolicy\";\nimport { exponentialRetryPolicy, DefaultRetryOptions } from \"./policies/exponentialRetryPolicy\";\nimport { generateClientRequestIdPolicy } from \"./policies/generateClientRequestIdPolicy\";\nimport {\n  userAgentPolicy,\n  getDefaultUserAgentHeaderName,\n  getDefaultUserAgentValue\n} from \"./policies/userAgentPolicy\";\nimport { redirectPolicy, DefaultRedirectOptions } from \"./policies/redirectPolicy\";\nimport {\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptions\n} from \"./policies/requestPolicy\";\nimport { rpRegistrationPolicy } from \"./policies/rpRegistrationPolicy\";\nimport { bearerTokenAuthenticationPolicy } from \"./policies/bearerTokenAuthenticationPolicy\";\nimport { systemErrorRetryPolicy } from \"./policies/systemErrorRetryPolicy\";\nimport { QueryCollectionFormat } from \"./queryCollectionFormat\";\nimport { CompositeMapper, DictionaryMapper, Mapper, MapperType, Serializer } from \"./serializer\";\nimport { URLBuilder } from \"./url\";\nimport * as utils from \"./util/utils\";\nimport { stringifyXML } from \"./util/xml\";\nimport {\n  RequestOptionsBase,\n  RequestPrepareOptions,\n  WebResource,\n  WebResourceLike,\n  isWebResourceLike\n} from \"./webResource\";\nimport { OperationResponse } from \"./operationResponse\";\nimport { ServiceCallback, isNode } from \"./util/utils\";\nimport { proxyPolicy } from \"./policies/proxyPolicy\";\nimport { throttlingRetryPolicy } from \"./policies/throttlingRetryPolicy\";\nimport { ServiceClientCredentials } from \"./credentials/serviceClientCredentials\";\nimport { signingPolicy } from \"./policies/signingPolicy\";\nimport { logger } from \"./log\";\nimport { InternalPipelineOptions } from \"./pipelineOptions\";\nimport { DefaultKeepAliveOptions, keepAlivePolicy } from \"./policies/keepAlivePolicy\";\nimport { tracingPolicy } from \"./policies/tracingPolicy\";\nimport { disableResponseDecompressionPolicy } from \"./policies/disableResponseDecompressionPolicy\";\nimport { ndJsonPolicy } from \"./policies/ndJsonPolicy\";\nimport { XML_ATTRKEY, SerializerOptions, XML_CHARKEY } from \"./util/serializer.common\";\nimport { URL } from \"./url\";\nimport { getCachedDefaultHttpClient } from \"./httpClientCache\";\n\n/**\n * Options to configure a proxy for outgoing requests (Node.js only).\n */\nexport interface ProxySettings {\n  /**\n   * The proxy's host address.\n   */\n  host: string;\n\n  /**\n   * The proxy host's port.\n   */\n  port: number;\n\n  /**\n   * The user name to authenticate with the proxy, if required.\n   */\n  username?: string;\n\n  /**\n   * The password to authenticate with the proxy, if required.\n   */\n  password?: string;\n}\n\nexport type ProxyOptions = ProxySettings; // Alias ProxySettings as ProxyOptions for future use.\n\n/**\n * Options to be provided while creating the client.\n */\nexport interface ServiceClientOptions {\n  /**\n   * An array of factories which get called to create the RequestPolicy pipeline used to send a HTTP\n   * request on the wire, or a function that takes in the defaultRequestPolicyFactories and returns\n   * the requestPolicyFactories that will be used.\n   */\n  requestPolicyFactories?:\n    | RequestPolicyFactory[]\n    | ((defaultRequestPolicyFactories: RequestPolicyFactory[]) => void | RequestPolicyFactory[]);\n  /**\n   * The HttpClient that will be used to send HTTP requests.\n   */\n  httpClient?: HttpClient;\n  /**\n   * The HttpPipelineLogger that can be used to debug RequestPolicies within the HTTP pipeline.\n   */\n  httpPipelineLogger?: HttpPipelineLogger;\n  /**\n   * If set to true, turn off the default retry policy.\n   */\n  noRetryPolicy?: boolean;\n  /**\n   * Gets or sets the retry timeout in seconds for AutomaticRPRegistration. Default value is 30.\n   */\n  rpRegistrationRetryTimeout?: number;\n  /**\n   * Whether or not to generate a client request ID header for each HTTP request.\n   */\n  generateClientRequestIdHeader?: boolean;\n  /**\n   * Whether to include credentials in CORS requests in the browser.\n   * See https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/withCredentials for more information.\n   */\n  withCredentials?: boolean;\n  /**\n   * If specified, a GenerateRequestIdPolicy will be added to the HTTP pipeline that will add a\n   * header to all outgoing requests with this header name and a random UUID as the request ID.\n   */\n  clientRequestIdHeaderName?: string;\n  /**\n   * The content-types that will be associated with JSON or XML serialization.\n   */\n  deserializationContentTypes?: DeserializationContentTypes;\n  /**\n   * The header name to use for the telemetry header while sending the request. If this is not\n   * specified, then \"User-Agent\" will be used when running on Node.js and \"x-ms-useragent\" will\n   * be used when running in a browser.\n   */\n  userAgentHeaderName?: string | ((defaultUserAgentHeaderName: string) => string);\n  /**\n   * The string to be set to the telemetry header while sending the request, or a function that\n   * takes in the default user-agent string and returns the user-agent string that will be used.\n   */\n  userAgent?: string | ((defaultUserAgent: string) => string);\n  /**\n   * Proxy settings which will be used for every HTTP request (Node.js only).\n   */\n  proxySettings?: ProxySettings;\n  /**\n   * If specified, will be used to build the BearerTokenAuthenticationPolicy.\n   */\n  credentialScopes?: string | string[];\n}\n\n/**\n * ServiceClient sends service requests and receives responses.\n */\nexport class ServiceClient {\n  /**\n   * If specified, this is the base URI that requests will be made against for this ServiceClient.\n   * If it is not specified, then all OperationSpecs must contain a baseUrl property.\n   */\n  protected baseUri?: string;\n\n  /**\n   * The default request content type for the service.\n   * Used if no requestContentType is present on an OperationSpec.\n   */\n  protected requestContentType?: string;\n\n  /**\n   * The HTTP client that will be used to send requests.\n   */\n  private readonly _httpClient: HttpClient;\n  private readonly _requestPolicyOptions: RequestPolicyOptions;\n\n  private readonly _requestPolicyFactories: RequestPolicyFactory[];\n  private readonly _withCredentials: boolean;\n\n  /**\n   * The ServiceClient constructor\n   * @param credentials - The credentials used for authentication with the service.\n   * @param options - The service client options that govern the behavior of the client.\n   */\n  constructor(\n    credentials?: TokenCredential | ServiceClientCredentials,\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options */\n    options?: ServiceClientOptions\n  ) {\n    if (!options) {\n      options = {};\n    }\n\n    this._withCredentials = options.withCredentials || false;\n    this._httpClient = options.httpClient || getCachedDefaultHttpClient();\n    this._requestPolicyOptions = new RequestPolicyOptions(options.httpPipelineLogger);\n\n    let requestPolicyFactories: RequestPolicyFactory[];\n    if (Array.isArray(options.requestPolicyFactories)) {\n      logger.info(\"ServiceClient: using custom request policies\");\n      requestPolicyFactories = options.requestPolicyFactories;\n    } else {\n      let authPolicyFactory: RequestPolicyFactory | undefined = undefined;\n      if (isTokenCredential(credentials)) {\n        logger.info(\n          \"ServiceClient: creating bearer token authentication policy from provided credentials\"\n        );\n        // Create a wrapped RequestPolicyFactory here so that we can provide the\n        // correct scope to the BearerTokenAuthenticationPolicy at the first time\n        // one is requested.  This is needed because generated ServiceClient\n        // implementations do not set baseUri until after ServiceClient's constructor\n        // is finished, leaving baseUri empty at the time when it is needed to\n        // build the correct scope name.\n        const wrappedPolicyFactory: () => RequestPolicyFactory = () => {\n          let bearerTokenPolicyFactory: RequestPolicyFactory | undefined = undefined;\n          // eslint-disable-next-line @typescript-eslint/no-this-alias\n          const serviceClient = this;\n          const serviceClientOptions = options;\n          return {\n            create(nextPolicy: RequestPolicy, createOptions: RequestPolicyOptions): RequestPolicy {\n              const credentialScopes = getCredentialScopes(\n                serviceClientOptions,\n                serviceClient.baseUri\n              );\n\n              if (!credentialScopes) {\n                throw new Error(\n                  `When using credential, the ServiceClient must contain a baseUri or a credentialScopes in ServiceClientOptions. Unable to create a bearerTokenAuthenticationPolicy`\n                );\n              }\n\n              if (bearerTokenPolicyFactory === undefined || bearerTokenPolicyFactory === null) {\n                bearerTokenPolicyFactory = bearerTokenAuthenticationPolicy(\n                  credentials,\n                  credentialScopes\n                );\n              }\n\n              return bearerTokenPolicyFactory.create(nextPolicy, createOptions);\n            }\n          };\n        };\n\n        authPolicyFactory = wrappedPolicyFactory();\n      } else if (credentials && typeof credentials.signRequest === \"function\") {\n        logger.info(\"ServiceClient: creating signing policy from provided credentials\");\n        authPolicyFactory = signingPolicy(credentials);\n      } else if (credentials !== undefined && credentials !== null) {\n        throw new Error(\"The credentials argument must implement the TokenCredential interface\");\n      }\n\n      logger.info(\"ServiceClient: using default request policies\");\n      requestPolicyFactories = createDefaultRequestPolicyFactories(authPolicyFactory, options);\n      if (options.requestPolicyFactories) {\n        // options.requestPolicyFactories can also be a function that manipulates\n        // the default requestPolicyFactories array\n        const newRequestPolicyFactories:\n          | void\n          | RequestPolicyFactory[] = options.requestPolicyFactories(requestPolicyFactories);\n        if (newRequestPolicyFactories) {\n          requestPolicyFactories = newRequestPolicyFactories;\n        }\n      }\n    }\n    this._requestPolicyFactories = requestPolicyFactories;\n  }\n\n  /**\n   * Send the provided httpRequest.\n   */\n  sendRequest(options: RequestPrepareOptions | WebResourceLike): Promise<HttpOperationResponse> {\n    if (options === null || options === undefined || typeof options !== \"object\") {\n      throw new Error(\"options cannot be null or undefined and it must be of type object.\");\n    }\n\n    let httpRequest: WebResourceLike;\n    try {\n      if (isWebResourceLike(options)) {\n        options.validateRequestProperties();\n        httpRequest = options;\n      } else {\n        httpRequest = new WebResource();\n        httpRequest = httpRequest.prepare(options);\n      }\n    } catch (error) {\n      return Promise.reject(error);\n    }\n\n    let httpPipeline: RequestPolicy = this._httpClient;\n    if (this._requestPolicyFactories && this._requestPolicyFactories.length > 0) {\n      for (let i = this._requestPolicyFactories.length - 1; i >= 0; --i) {\n        httpPipeline = this._requestPolicyFactories[i].create(\n          httpPipeline,\n          this._requestPolicyOptions\n        );\n      }\n    }\n    return httpPipeline.sendRequest(httpRequest);\n  }\n\n  /**\n   * Send an HTTP request that is populated using the provided OperationSpec.\n   * @param operationArguments - The arguments that the HTTP request's templated values will be populated from.\n   * @param operationSpec - The OperationSpec to use to populate the httpRequest.\n   * @param callback - The callback to call when the response is received.\n   */\n  async sendOperationRequest(\n    operationArguments: OperationArguments,\n    operationSpec: OperationSpec,\n    callback?: ServiceCallback<any>\n  ): Promise<RestResponse> {\n    if (typeof operationArguments.options === \"function\") {\n      callback = operationArguments.options;\n      operationArguments.options = undefined;\n    }\n\n    const serializerOptions = operationArguments.options?.serializerOptions;\n    const httpRequest: WebResourceLike = new WebResource();\n\n    let result: Promise<RestResponse>;\n    try {\n      const baseUri: string | undefined = operationSpec.baseUrl || this.baseUri;\n      if (!baseUri) {\n        throw new Error(\n          \"If operationSpec.baseUrl is not specified, then the ServiceClient must have a baseUri string property that contains the base URL to use.\"\n        );\n      }\n\n      httpRequest.method = operationSpec.httpMethod;\n      httpRequest.operationSpec = operationSpec;\n\n      const requestUrl: URLBuilder = URLBuilder.parse(baseUri);\n      if (operationSpec.path) {\n        requestUrl.appendPath(operationSpec.path);\n      }\n      if (operationSpec.urlParameters && operationSpec.urlParameters.length > 0) {\n        for (const urlParameter of operationSpec.urlParameters) {\n          let urlParameterValue: string = getOperationArgumentValueFromParameter(\n            this,\n            operationArguments,\n            urlParameter,\n            operationSpec.serializer\n          );\n          urlParameterValue = operationSpec.serializer.serialize(\n            urlParameter.mapper,\n            urlParameterValue,\n            getPathStringFromParameter(urlParameter),\n            serializerOptions\n          );\n          if (!urlParameter.skipEncoding) {\n            urlParameterValue = encodeURIComponent(urlParameterValue);\n          }\n          requestUrl.replaceAll(\n            `{${urlParameter.mapper.serializedName || getPathStringFromParameter(urlParameter)}}`,\n            urlParameterValue\n          );\n        }\n      }\n      if (operationSpec.queryParameters && operationSpec.queryParameters.length > 0) {\n        for (const queryParameter of operationSpec.queryParameters) {\n          let queryParameterValue: any = getOperationArgumentValueFromParameter(\n            this,\n            operationArguments,\n            queryParameter,\n            operationSpec.serializer\n          );\n          if (queryParameterValue !== undefined && queryParameterValue !== null) {\n            queryParameterValue = operationSpec.serializer.serialize(\n              queryParameter.mapper,\n              queryParameterValue,\n              getPathStringFromParameter(queryParameter),\n              serializerOptions\n            );\n            if (\n              queryParameter.collectionFormat !== undefined &&\n              queryParameter.collectionFormat !== null\n            ) {\n              if (queryParameter.collectionFormat === QueryCollectionFormat.Multi) {\n                if (queryParameterValue.length === 0) {\n                  // The collection is empty, no need to try serializing the current queryParam\n                  continue;\n                } else {\n                  for (const index in queryParameterValue) {\n                    const item = queryParameterValue[index];\n                    queryParameterValue[index] =\n                      item === undefined || item === null ? \"\" : item.toString();\n                  }\n                }\n              } else if (\n                queryParameter.collectionFormat === QueryCollectionFormat.Ssv ||\n                queryParameter.collectionFormat === QueryCollectionFormat.Tsv\n              ) {\n                queryParameterValue = queryParameterValue.join(queryParameter.collectionFormat);\n              }\n            }\n            if (!queryParameter.skipEncoding) {\n              if (Array.isArray(queryParameterValue)) {\n                for (const index in queryParameterValue) {\n                  if (\n                    queryParameterValue[index] !== undefined &&\n                    queryParameterValue[index] !== null\n                  ) {\n                    queryParameterValue[index] = encodeURIComponent(queryParameterValue[index]);\n                  }\n                }\n              } else {\n                queryParameterValue = encodeURIComponent(queryParameterValue);\n              }\n            }\n            if (\n              queryParameter.collectionFormat !== undefined &&\n              queryParameter.collectionFormat !== null &&\n              queryParameter.collectionFormat !== QueryCollectionFormat.Multi &&\n              queryParameter.collectionFormat !== QueryCollectionFormat.Ssv &&\n              queryParameter.collectionFormat !== QueryCollectionFormat.Tsv\n            ) {\n              queryParameterValue = queryParameterValue.join(queryParameter.collectionFormat);\n            }\n            requestUrl.setQueryParameter(\n              queryParameter.mapper.serializedName || getPathStringFromParameter(queryParameter),\n              queryParameterValue\n            );\n          }\n        }\n      }\n      httpRequest.url = requestUrl.toString();\n\n      const contentType = operationSpec.contentType || this.requestContentType;\n      if (contentType && operationSpec.requestBody) {\n        httpRequest.headers.set(\"Content-Type\", contentType);\n      }\n\n      if (operationSpec.headerParameters) {\n        for (const headerParameter of operationSpec.headerParameters) {\n          let headerValue: any = getOperationArgumentValueFromParameter(\n            this,\n            operationArguments,\n            headerParameter,\n            operationSpec.serializer\n          );\n          if (headerValue !== undefined && headerValue !== null) {\n            headerValue = operationSpec.serializer.serialize(\n              headerParameter.mapper,\n              headerValue,\n              getPathStringFromParameter(headerParameter),\n              serializerOptions\n            );\n            const headerCollectionPrefix = (headerParameter.mapper as DictionaryMapper)\n              .headerCollectionPrefix;\n            if (headerCollectionPrefix) {\n              for (const key of Object.keys(headerValue)) {\n                httpRequest.headers.set(headerCollectionPrefix + key, headerValue[key]);\n              }\n            } else {\n              httpRequest.headers.set(\n                headerParameter.mapper.serializedName ||\n                  getPathStringFromParameter(headerParameter),\n                headerValue\n              );\n            }\n          }\n        }\n      }\n\n      const options: RequestOptionsBase | undefined = operationArguments.options;\n      if (options) {\n        if (options.customHeaders) {\n          for (const customHeaderName in options.customHeaders) {\n            httpRequest.headers.set(customHeaderName, options.customHeaders[customHeaderName]);\n          }\n        }\n\n        if (options.abortSignal) {\n          httpRequest.abortSignal = options.abortSignal;\n        }\n\n        if (options.timeout) {\n          httpRequest.timeout = options.timeout;\n        }\n\n        if (options.onUploadProgress) {\n          httpRequest.onUploadProgress = options.onUploadProgress;\n        }\n\n        if (options.onDownloadProgress) {\n          httpRequest.onDownloadProgress = options.onDownloadProgress;\n        }\n\n        if (options.spanOptions) {\n          // By passing spanOptions if they exist at runtime, we're backwards compatible with @azure/core-tracing@preview.13 and earlier.\n          (httpRequest as any).spanOptions = options.spanOptions;\n        }\n\n        if (options.tracingContext) {\n          httpRequest.tracingContext = options.tracingContext;\n        }\n\n        if (options.shouldDeserialize !== undefined && options.shouldDeserialize !== null) {\n          httpRequest.shouldDeserialize = options.shouldDeserialize;\n        }\n      }\n\n      httpRequest.withCredentials = this._withCredentials;\n\n      serializeRequestBody(this, httpRequest, operationArguments, operationSpec);\n\n      if (httpRequest.streamResponseStatusCodes === undefined) {\n        httpRequest.streamResponseStatusCodes = getStreamResponseStatusCodes(operationSpec);\n      }\n\n      let rawResponse: HttpOperationResponse;\n      let sendRequestError;\n      try {\n        rawResponse = await this.sendRequest(httpRequest);\n      } catch (error) {\n        sendRequestError = error;\n      }\n      if (sendRequestError) {\n        if (sendRequestError.response) {\n          sendRequestError.details = flattenResponse(\n            sendRequestError.response,\n            operationSpec.responses[sendRequestError.statusCode] ||\n              operationSpec.responses[\"default\"]\n          );\n        }\n        result = Promise.reject(sendRequestError);\n      } else {\n        result = Promise.resolve(\n          flattenResponse(rawResponse!, operationSpec.responses[rawResponse!.status])\n        );\n      }\n    } catch (error) {\n      result = Promise.reject(error);\n    }\n\n    const cb = callback;\n    if (cb) {\n      result\n        .then((res) => cb(null, res._response.parsedBody, res._response.request, res._response))\n        .catch((err) => cb(err));\n    }\n\n    return result;\n  }\n}\n\nexport function serializeRequestBody(\n  serviceClient: ServiceClient,\n  httpRequest: WebResourceLike,\n  operationArguments: OperationArguments,\n  operationSpec: OperationSpec\n): void {\n  const serializerOptions = operationArguments.options?.serializerOptions ?? {};\n  const updatedOptions: Required<SerializerOptions> = {\n    rootName: serializerOptions.rootName ?? \"\",\n    includeRoot: serializerOptions.includeRoot ?? false,\n    xmlCharKey: serializerOptions.xmlCharKey ?? XML_CHARKEY\n  };\n\n  const xmlCharKey = serializerOptions.xmlCharKey;\n  if (operationSpec.requestBody && operationSpec.requestBody.mapper) {\n    httpRequest.body = getOperationArgumentValueFromParameter(\n      serviceClient,\n      operationArguments,\n      operationSpec.requestBody,\n      operationSpec.serializer\n    );\n\n    const bodyMapper = operationSpec.requestBody.mapper;\n    const {\n      required,\n      xmlName,\n      xmlElementName,\n      serializedName,\n      xmlNamespace,\n      xmlNamespacePrefix\n    } = bodyMapper;\n    const typeName = bodyMapper.type.name;\n\n    try {\n      if ((httpRequest.body !== undefined && httpRequest.body !== null) || required) {\n        const requestBodyParameterPathString: string = getPathStringFromParameter(\n          operationSpec.requestBody\n        );\n        httpRequest.body = operationSpec.serializer.serialize(\n          bodyMapper,\n          httpRequest.body,\n          requestBodyParameterPathString,\n          updatedOptions\n        );\n\n        const isStream = typeName === MapperType.Stream;\n\n        if (operationSpec.isXML) {\n          const xmlnsKey = xmlNamespacePrefix ? `xmlns:${xmlNamespacePrefix}` : \"xmlns\";\n          const value = getXmlValueWithNamespace(\n            xmlNamespace,\n            xmlnsKey,\n            typeName,\n            httpRequest.body,\n            updatedOptions\n          );\n          if (typeName === MapperType.Sequence) {\n            httpRequest.body = stringifyXML(\n              utils.prepareXMLRootList(\n                value,\n                xmlElementName || xmlName || serializedName!,\n                xmlnsKey,\n                xmlNamespace\n              ),\n              {\n                rootName: xmlName || serializedName,\n                xmlCharKey\n              }\n            );\n          } else if (!isStream) {\n            httpRequest.body = stringifyXML(value, {\n              rootName: xmlName || serializedName,\n              xmlCharKey\n            });\n          }\n        } else if (\n          typeName === MapperType.String &&\n          (operationSpec.contentType?.match(\"text/plain\") || operationSpec.mediaType === \"text\")\n        ) {\n          // the String serializer has validated that request body is a string\n          // so just send the string.\n          return;\n        } else if (!isStream) {\n          httpRequest.body = JSON.stringify(httpRequest.body);\n        }\n      }\n    } catch (error) {\n      throw new Error(\n        `Error \"${error.message}\" occurred in serializing the payload - ${JSON.stringify(\n          serializedName,\n          undefined,\n          \"  \"\n        )}.`\n      );\n    }\n  } else if (operationSpec.formDataParameters && operationSpec.formDataParameters.length > 0) {\n    httpRequest.formData = {};\n    for (const formDataParameter of operationSpec.formDataParameters) {\n      const formDataParameterValue: any = getOperationArgumentValueFromParameter(\n        serviceClient,\n        operationArguments,\n        formDataParameter,\n        operationSpec.serializer\n      );\n      if (formDataParameterValue !== undefined && formDataParameterValue !== null) {\n        const formDataParameterPropertyName: string =\n          formDataParameter.mapper.serializedName || getPathStringFromParameter(formDataParameter);\n        httpRequest.formData[formDataParameterPropertyName] = operationSpec.serializer.serialize(\n          formDataParameter.mapper,\n          formDataParameterValue,\n          getPathStringFromParameter(formDataParameter),\n          updatedOptions\n        );\n      }\n    }\n  }\n}\n\n/**\n * Adds an xml namespace to the xml serialized object if needed, otherwise it just returns the value itself\n */\nfunction getXmlValueWithNamespace(\n  xmlNamespace: string | undefined,\n  xmlnsKey: string,\n  typeName: string,\n  serializedValue: any,\n  options: Required<SerializerOptions>\n): any {\n  // Composite and Sequence schemas already got their root namespace set during serialization\n  // We just need to add xmlns to the other schema types\n  if (xmlNamespace && ![\"Composite\", \"Sequence\", \"Dictionary\"].includes(typeName)) {\n    const result: any = {};\n    result[options.xmlCharKey] = serializedValue;\n    result[XML_ATTRKEY] = { [xmlnsKey]: xmlNamespace };\n    return result;\n  }\n\n  return serializedValue;\n}\n\nfunction getValueOrFunctionResult(\n  value: undefined | string | ((defaultValue: string) => string),\n  defaultValueCreator: () => string\n): string {\n  let result: string;\n  if (typeof value === \"string\") {\n    result = value;\n  } else {\n    result = defaultValueCreator();\n    if (typeof value === \"function\") {\n      result = value(result);\n    }\n  }\n  return result;\n}\n\nfunction createDefaultRequestPolicyFactories(\n  authPolicyFactory: RequestPolicyFactory | undefined,\n  options: ServiceClientOptions\n): RequestPolicyFactory[] {\n  const factories: RequestPolicyFactory[] = [];\n\n  if (options.generateClientRequestIdHeader) {\n    factories.push(generateClientRequestIdPolicy(options.clientRequestIdHeaderName));\n  }\n\n  if (authPolicyFactory) {\n    factories.push(authPolicyFactory);\n  }\n\n  const userAgentHeaderName: string = getValueOrFunctionResult(\n    options.userAgentHeaderName,\n    getDefaultUserAgentHeaderName\n  );\n  const userAgentHeaderValue: string = getValueOrFunctionResult(\n    options.userAgent,\n    getDefaultUserAgentValue\n  );\n  if (userAgentHeaderName && userAgentHeaderValue) {\n    factories.push(userAgentPolicy({ key: userAgentHeaderName, value: userAgentHeaderValue }));\n  }\n  factories.push(redirectPolicy());\n  factories.push(rpRegistrationPolicy(options.rpRegistrationRetryTimeout));\n\n  if (!options.noRetryPolicy) {\n    factories.push(exponentialRetryPolicy());\n    factories.push(systemErrorRetryPolicy());\n    factories.push(throttlingRetryPolicy());\n  }\n\n  factories.push(deserializationPolicy(options.deserializationContentTypes));\n\n  if (isNode) {\n    factories.push(proxyPolicy(options.proxySettings));\n  }\n\n  factories.push(logPolicy({ logger: logger.info }));\n\n  return factories;\n}\n\nexport function createPipelineFromOptions(\n  pipelineOptions: InternalPipelineOptions,\n  authPolicyFactory?: RequestPolicyFactory\n): ServiceClientOptions {\n  const requestPolicyFactories: RequestPolicyFactory[] = [];\n\n  if (pipelineOptions.sendStreamingJson) {\n    requestPolicyFactories.push(ndJsonPolicy());\n  }\n\n  let userAgentValue = undefined;\n  if (pipelineOptions.userAgentOptions && pipelineOptions.userAgentOptions.userAgentPrefix) {\n    const userAgentInfo: string[] = [];\n    userAgentInfo.push(pipelineOptions.userAgentOptions.userAgentPrefix);\n\n    // Add the default user agent value if it isn't already specified\n    // by the userAgentPrefix option.\n    const defaultUserAgentInfo = getDefaultUserAgentValue();\n    if (userAgentInfo.indexOf(defaultUserAgentInfo) === -1) {\n      userAgentInfo.push(defaultUserAgentInfo);\n    }\n\n    userAgentValue = userAgentInfo.join(\" \");\n  }\n\n  const keepAliveOptions = {\n    ...DefaultKeepAliveOptions,\n    ...pipelineOptions.keepAliveOptions\n  };\n\n  const retryOptions = {\n    ...DefaultRetryOptions,\n    ...pipelineOptions.retryOptions\n  };\n\n  const redirectOptions = {\n    ...DefaultRedirectOptions,\n    ...pipelineOptions.redirectOptions\n  };\n\n  if (isNode) {\n    requestPolicyFactories.push(proxyPolicy(pipelineOptions.proxyOptions));\n  }\n\n  const deserializationOptions = {\n    ...DefaultDeserializationOptions,\n    ...pipelineOptions.deserializationOptions\n  };\n\n  const loggingOptions: LogPolicyOptions = {\n    ...pipelineOptions.loggingOptions\n  };\n\n  requestPolicyFactories.push(\n    tracingPolicy({ userAgent: userAgentValue }),\n    keepAlivePolicy(keepAliveOptions),\n    userAgentPolicy({ value: userAgentValue }),\n    generateClientRequestIdPolicy(),\n    deserializationPolicy(deserializationOptions.expectedContentTypes),\n    throttlingRetryPolicy(),\n    systemErrorRetryPolicy(),\n    exponentialRetryPolicy(\n      retryOptions.maxRetries,\n      retryOptions.retryDelayInMs,\n      retryOptions.maxRetryDelayInMs\n    )\n  );\n\n  if (redirectOptions.handleRedirects) {\n    requestPolicyFactories.push(redirectPolicy(redirectOptions.maxRetries));\n  }\n\n  if (authPolicyFactory) {\n    requestPolicyFactories.push(authPolicyFactory);\n  }\n\n  requestPolicyFactories.push(logPolicy(loggingOptions));\n\n  if (isNode && pipelineOptions.decompressResponse === false) {\n    requestPolicyFactories.push(disableResponseDecompressionPolicy());\n  }\n\n  return {\n    httpClient: pipelineOptions.httpClient,\n    requestPolicyFactories\n  };\n}\n\nexport type PropertyParent = { [propertyName: string]: any };\n\n/**\n * Get the property parent for the property at the provided path when starting with the provided\n * parent object.\n */\nexport function getPropertyParent(parent: PropertyParent, propertyPath: string[]): PropertyParent {\n  if (parent && propertyPath) {\n    const propertyPathLength: number = propertyPath.length;\n    for (let i = 0; i < propertyPathLength - 1; ++i) {\n      const propertyName: string = propertyPath[i];\n      if (!parent[propertyName]) {\n        parent[propertyName] = {};\n      }\n      parent = parent[propertyName];\n    }\n  }\n  return parent;\n}\n\nfunction getOperationArgumentValueFromParameter(\n  serviceClient: ServiceClient,\n  operationArguments: OperationArguments,\n  parameter: OperationParameter,\n  serializer: Serializer\n): any {\n  return getOperationArgumentValueFromParameterPath(\n    serviceClient,\n    operationArguments,\n    parameter.parameterPath,\n    parameter.mapper,\n    serializer\n  );\n}\n\nexport function getOperationArgumentValueFromParameterPath(\n  serviceClient: ServiceClient,\n  operationArguments: OperationArguments,\n  parameterPath: ParameterPath,\n  parameterMapper: Mapper,\n  serializer: Serializer\n): any {\n  let value: any;\n  if (typeof parameterPath === \"string\") {\n    parameterPath = [parameterPath];\n  }\n  const serializerOptions = operationArguments.options?.serializerOptions;\n  if (Array.isArray(parameterPath)) {\n    if (parameterPath.length > 0) {\n      if (parameterMapper.isConstant) {\n        value = parameterMapper.defaultValue;\n      } else {\n        let propertySearchResult: PropertySearchResult = getPropertyFromParameterPath(\n          operationArguments,\n          parameterPath\n        );\n        if (!propertySearchResult.propertyFound) {\n          propertySearchResult = getPropertyFromParameterPath(serviceClient, parameterPath);\n        }\n\n        let useDefaultValue = false;\n        if (!propertySearchResult.propertyFound) {\n          useDefaultValue =\n            parameterMapper.required ||\n            (parameterPath[0] === \"options\" && parameterPath.length === 2);\n        }\n        value = useDefaultValue ? parameterMapper.defaultValue : propertySearchResult.propertyValue;\n      }\n\n      // Serialize just for validation purposes.\n      const parameterPathString: string = getPathStringFromParameterPath(\n        parameterPath,\n        parameterMapper\n      );\n      serializer.serialize(parameterMapper, value, parameterPathString, serializerOptions);\n    }\n  } else {\n    if (parameterMapper.required) {\n      value = {};\n    }\n\n    for (const propertyName in parameterPath) {\n      const propertyMapper: Mapper = (parameterMapper as CompositeMapper).type.modelProperties![\n        propertyName\n      ];\n      const propertyPath: ParameterPath = parameterPath[propertyName];\n      const propertyValue: any = getOperationArgumentValueFromParameterPath(\n        serviceClient,\n        operationArguments,\n        propertyPath,\n        propertyMapper,\n        serializer\n      );\n      // Serialize just for validation purposes.\n      const propertyPathString: string = getPathStringFromParameterPath(\n        propertyPath,\n        propertyMapper\n      );\n      serializer.serialize(propertyMapper, propertyValue, propertyPathString, serializerOptions);\n      if (propertyValue !== undefined && propertyValue !== null) {\n        if (!value) {\n          value = {};\n        }\n        value[propertyName] = propertyValue;\n      }\n    }\n  }\n  return value;\n}\n\ninterface PropertySearchResult {\n  propertyValue?: any;\n  propertyFound: boolean;\n}\n\nfunction getPropertyFromParameterPath(\n  parent: { [parameterName: string]: any },\n  parameterPath: string[]\n): PropertySearchResult {\n  const result: PropertySearchResult = { propertyFound: false };\n  let i = 0;\n  for (; i < parameterPath.length; ++i) {\n    const parameterPathPart: string = parameterPath[i];\n    // Make sure to check inherited properties too, so don't use hasOwnProperty().\n    if (parent !== undefined && parent !== null && parameterPathPart in parent) {\n      parent = parent[parameterPathPart];\n    } else {\n      break;\n    }\n  }\n  if (i === parameterPath.length) {\n    result.propertyValue = parent;\n    result.propertyFound = true;\n  }\n  return result;\n}\n\nexport function flattenResponse(\n  _response: HttpOperationResponse,\n  responseSpec: OperationResponse | undefined\n): RestResponse {\n  const parsedHeaders = _response.parsedHeaders;\n  const bodyMapper = responseSpec && responseSpec.bodyMapper;\n\n  const addOperationResponse = (\n    obj: Record<string, unknown>\n  ): {\n    _response: HttpOperationResponse;\n  } => {\n    return Object.defineProperty(obj, \"_response\", {\n      value: _response\n    });\n  };\n\n  if (bodyMapper) {\n    const typeName = bodyMapper.type.name;\n    if (typeName === \"Stream\") {\n      return addOperationResponse({\n        ...parsedHeaders,\n        blobBody: _response.blobBody,\n        readableStreamBody: _response.readableStreamBody\n      });\n    }\n\n    const modelProperties =\n      (typeName === \"Composite\" && (bodyMapper as CompositeMapper).type.modelProperties) || {};\n    const isPageableResponse = Object.keys(modelProperties).some(\n      (k) => modelProperties[k].serializedName === \"\"\n    );\n    if (typeName === \"Sequence\" || isPageableResponse) {\n      const arrayResponse = [...(_response.parsedBody || [])] as RestResponse & any[];\n\n      for (const key of Object.keys(modelProperties)) {\n        if (modelProperties[key].serializedName) {\n          arrayResponse[key] = _response.parsedBody[key];\n        }\n      }\n\n      if (parsedHeaders) {\n        for (const key of Object.keys(parsedHeaders)) {\n          arrayResponse[key] = parsedHeaders[key];\n        }\n      }\n      addOperationResponse(arrayResponse);\n      return arrayResponse;\n    }\n\n    if (typeName === \"Composite\" || typeName === \"Dictionary\") {\n      return addOperationResponse({\n        ...parsedHeaders,\n        ..._response.parsedBody\n      });\n    }\n  }\n\n  if (\n    bodyMapper ||\n    _response.request.method === \"HEAD\" ||\n    utils.isPrimitiveType(_response.parsedBody)\n  ) {\n    // primitive body types and HEAD booleans\n    return addOperationResponse({\n      ...parsedHeaders,\n      body: _response.parsedBody\n    });\n  }\n\n  return addOperationResponse({\n    ...parsedHeaders,\n    ..._response.parsedBody\n  });\n}\n\nfunction getCredentialScopes(\n  options?: ServiceClientOptions,\n  baseUri?: string\n): string | string[] | undefined {\n  if (options?.credentialScopes) {\n    const scopes = options.credentialScopes;\n    return Array.isArray(scopes)\n      ? scopes.map((scope) => new URL(scope).toString())\n      : new URL(scopes).toString();\n  }\n\n  if (baseUri) {\n    return `${baseUri}/.default`;\n  }\n  return undefined;\n}\n"]}