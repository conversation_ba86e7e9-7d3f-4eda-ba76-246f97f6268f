import { BaseRequestPolicy, RequestPolicy, RequestPolicyOptions, RequestPolicyFactory } from "./requestPolicy";
import { WebResourceLike } from "../webResource";
import { HttpOperationResponse } from "../httpOperationResponse";
declare type ResponseHandler = (httpRequest: WebResourceLike, response: HttpOperationResponse) => Promise<HttpOperationResponse>;
export declare function throttlingRetryPolicy(): RequestPolicyFactory;
/**
 * To learn more, please refer to
 * https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-manager-request-limits,
 * https://docs.microsoft.com/en-us/azure/azure-subscription-service-limits and
 * https://docs.microsoft.com/en-us/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors
 */
export declare class ThrottlingRetryPolicy extends BaseRequestPolicy {
    private _handleResponse;
    private numberOfRetries;
    constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions, _handleResponse?: ResponseHandler);
    sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse>;
    private _defaultResponseHandler;
    static parseRetryAfterHeader(headerValue: string): number | undefined;
    static parseDateRetryAfterHeader(headerValue: string): number | undefined;
}
export {};
//# sourceMappingURL=throttlingRetryPolicy.d.ts.map
