import { BaseRequestPolicy, RequestPolicy, RequestPolicyOptions, RequestPolicyFactory } from "./requestPolicy";
import { WebResourceLike } from "../webResource";
import { HttpOperationResponse } from "../httpOperationResponse";
/**
 * Options for how HTTP connections should be maintained for future
 * requests.
 */
export interface KeepAliveOptions {
    enable: boolean;
}
export declare const DefaultKeepAliveOptions: KeepAliveOptions;
export declare function keepAlivePolicy(keepAliveOptions?: KeepAliveOptions): RequestPolicyFactory;
/**
 * KeepAlivePolicy is a policy used to control keep alive settings for every request.
 */
export declare class KeepAlivePolicy extends BaseRequestPolicy {
    private readonly keepAliveOptions;
    /**
     * Creates an instance of KeepAlivePolicy.
     *
     * @param nextPolicy -
     * @param options -
     * @param keepAliveOptions -
     */
    constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions, keepAliveOptions: KeepAliveOptions);
    /**
     * Sends out request.
     *
     * @param request -
     * @returns
     */
    sendRequest(request: WebResourceLike): Promise<HttpOperationResponse>;
}
//# sourceMappingURL=keepAlivePolicy.d.ts.map
