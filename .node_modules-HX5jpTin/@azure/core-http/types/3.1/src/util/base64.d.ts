/**
 * Encodes a string in base64 format.
 * @param value - The string to encode
 */
export declare function encodeString(value: string): string;
/**
 * Encodes a byte array in base64 format.
 * @param value - The Uint8Aray to encode
 */
export declare function encodeByteArray(value: Uint8Array): string;
/**
 * Decodes a base64 string into a byte array.
 * @param value - The base64 string to decode
 */
export declare function decodeString(value: string): Uint8Array;
//# sourceMappingURL=base64.d.ts.map
