{"version": 3, "file": "fetchHttpClient.d.ts", "sourceRoot": "", "sources": ["../../../src/fetchHttpClient.ts"], "names": [], "mappings": ";AAMA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,qBAAqB,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AACvE,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAe,eAAe,EAAE,MAAM,eAAe,CAAC;AAE7D,OAAO,EAAY,SAAS,EAAE,MAAM,QAAQ,CAAC;AAS7C,oBAAY,iBAAiB,GAAG,MAAM,CAAC;AAEvC,oBAAY,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC,GAAG;IACjF,IAAI,CAAC,EAAE,GAAG,CAAC;IACX,OAAO,CAAC,EAAE,GAAG,CAAC;IACd,MAAM,CAAC,EAAE,GAAG,CAAC;CACd,CAAC;AAEF,oBAAY,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG,UAAU,CAAC,GAAG;IAC7E,IAAI,EAAE,GAAG,CAAC;IACV,OAAO,EAAE,GAAG,CAAC;IACb,QAAQ,EAAE,GAAG,CAAC;CACf,CAAC;AAEF,qBAAa,eAAgB,SAAQ,SAAS;IAShC,OAAO,CAAC,gBAAgB;IARpC,OAAO,CAAC,WAAW,CAAa;IAChC,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,GAAG,IAAI;gBAOrE,gBAAgB,EAAE,CAAC,QAAQ,EAAE,qBAAqB,KAAK,IAAI;CAGhF;AAED,8BAAsB,eAAgB,YAAW,UAAU;IACnD,WAAW,CAAC,WAAW,EAAE,eAAe,GAAG,OAAO,CAAC,qBAAqB,CAAC;IAwL/E,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACpF,QAAQ,CAAC,cAAc,CAAC,iBAAiB,EAAE,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC;IAChF,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,cAAc,CAAC;CAC5F;AAiBD,wBAAgB,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,eAAe,CAQ9D"}