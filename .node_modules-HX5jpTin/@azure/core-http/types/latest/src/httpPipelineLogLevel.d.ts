/**
 * The different levels of logs that can be used with the HttpPipelineLogger.
 */
export declare enum HttpPipelineLogLevel {
    /**
     * A log level that indicates that no logs will be logged.
     */
    OFF = 0,
    /**
     * An error log.
     */
    ERROR = 1,
    /**
     * A warning log.
     */
    WARNING = 2,
    /**
     * An information log.
     */
    INFO = 3
}
//# sourceMappingURL=httpPipelineLogLevel.d.ts.map