{"version": 3, "file": "url.d.ts", "sourceRoot": "", "sources": ["../../../src/url.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAIjC;;GAEG;AACH,qBAAa,QAAQ;IACnB,OAAO,CAAC,QAAQ,CAAC,SAAS,CAA2D;IAErF;;OAEG;IACI,GAAG,IAAI,OAAO;IAIrB;;OAEG;IACI,IAAI,IAAI,MAAM,EAAE;IAIvB;;;;OAIG;IACI,GAAG,CAAC,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,GAAG,IAAI;IAgBhE;;;OAGG;IACI,GAAG,CAAC,aAAa,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,GAAG,SAAS;IAIhE;;OAEG;IACI,QAAQ,IAAI,MAAM;IAoBzB;;OAEG;WACW,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ;CA0D5C;AAED;;GAEG;AACH,qBAAa,UAAU;IACrB,OAAO,CAAC,OAAO,CAAqB;IACpC,OAAO,CAAC,KAAK,CAAqB;IAClC,OAAO,CAAC,KAAK,CAAqB;IAClC,OAAO,CAAC,KAAK,CAAqB;IAClC,OAAO,CAAC,MAAM,CAAuB;IAErC;;;OAGG;IACI,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAQlD;;OAEG;IACI,SAAS,IAAI,MAAM,GAAG,SAAS;IAItC;;;OAGG;IACI,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAQ9C;;OAEG;IACI,OAAO,IAAI,MAAM,GAAG,SAAS;IAIpC;;;OAGG;IACI,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,IAAI;IAQvD;;OAEG;IACI,OAAO,IAAI,MAAM,GAAG,SAAS;IAIpC;;;OAGG;IACI,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAgB9C;;;OAGG;IACI,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAkBjD;;OAEG;IACI,OAAO,IAAI,MAAM,GAAG,SAAS;IAIpC;;OAEG;IACI,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAQhD;;;;OAIG;IACI,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,GAAG,IAAI;IASxF;;;OAGG;IACI,sBAAsB,CAAC,kBAAkB,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,GAAG,SAAS;IAIxF;;OAEG;IACI,QAAQ,IAAI,MAAM,GAAG,SAAS;IAIrC;;OAEG;IACH,OAAO,CAAC,GAAG;IAsCJ,QAAQ,IAAI,MAAM;IA6BzB;;;OAGG;IACI,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,IAAI;WAUpD,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,UAAU;CAK9C;AAED,aAAK,iBAAiB,GAAG,QAAQ,GAAG,gBAAgB,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,CAAC;AAEnG,aAAK,YAAY,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;AAElE,qBAAa,QAAQ;aACgB,IAAI,EAAE,MAAM;aAAkB,IAAI,EAAE,YAAY;gBAAhD,IAAI,EAAE,MAAM,EAAkB,IAAI,EAAE,YAAY;WAErE,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ;WAI9B,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ;WAI5B,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ;WAI5B,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ;WAI5B,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ;CAG5C;AAED;;;GAGG;AACH,wBAAgB,uBAAuB,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAOlE;AAED;;GAEG;AACH,qBAAa,YAAY;IAMJ,QAAQ,CAAC,KAAK,EAAE,MAAM;IALzC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;IAC7B,aAAa,EAAE,iBAAiB,CAAC;IACjC,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,QAAQ,GAAG,SAAS,CAAC;gBAER,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,iBAAiB;IAMpE;;;OAGG;IACI,OAAO,IAAI,QAAQ,GAAG,SAAS;IAItC;;OAEG;IACI,IAAI,IAAI,OAAO;CAmCvB"}