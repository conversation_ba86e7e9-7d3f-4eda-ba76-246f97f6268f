/// <reference types="node" />
import { HttpClient } from "./httpClient";
import { TransferProgressEvent, WebResourceLike } from "./webResource";
import { HttpOperationResponse } from "./httpOperationResponse";
import { HttpHeadersLike } from "./httpHeaders";
import { Transform } from "stream";
export declare type CommonRequestInfo = string;
export declare type CommonRequestInit = Omit<RequestInit, "body" | "headers" | "signal"> & {
    body?: any;
    headers?: any;
    signal?: any;
};
export declare type CommonResponse = Omit<Response, "body" | "trailer" | "formData"> & {
    body: any;
    trailer: any;
    formData: any;
};
export declare class ReportTransform extends Transform {
    private progressCallback;
    private loadedBytes;
    _transform(chunk: string | Buffer, _encoding: string, callback: (arg: any) => void): void;
    constructor(progressCallback: (progress: TransferProgressEvent) => void);
}
export declare abstract class FetchHttpClient implements HttpClient {
    sendRequest(httpRequest: WebResourceLike): Promise<HttpOperationResponse>;
    abstract prepareRequest(httpRequest: WebResourceLike): Promise<Partial<RequestInit>>;
    abstract processRequest(operationResponse: HttpOperationResponse): Promise<void>;
    abstract fetch(input: CommonRequestInfo, init?: CommonRequestInit): Promise<CommonResponse>;
}
export declare function parseHeaders(headers: Headers): HttpHeadersLike;
//# sourceMappingURL=fetchHttpClient.d.ts.map