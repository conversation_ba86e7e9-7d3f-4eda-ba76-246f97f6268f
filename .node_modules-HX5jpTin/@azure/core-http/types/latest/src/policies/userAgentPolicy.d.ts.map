{"version": 3, "file": "userAgentPolicy.d.ts", "sourceRoot": "", "sources": ["../../../../src/policies/userAgentPolicy.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,qBAAqB,EAAE,MAAM,0BAA0B,CAAC;AAEjE,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AACjD,OAAO,EAAE,sBAAsB,EAA2B,MAAM,yBAAyB,CAAC;AAC1F,OAAO,EACL,iBAAiB,EACjB,aAAa,EACb,oBAAoB,EACpB,oBAAoB,EACrB,MAAM,iBAAiB,CAAC;AAEzB,oBAAY,aAAa,GAAG;IAAE,GAAG,CAAC,EAAE,MAAM,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAE7D;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAK/B,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B;AAwBD,eAAO,MAAM,6BAA6B,+BAAyB,CAAC;AAEpE,wBAAgB,wBAAwB,IAAI,MAAM,CAKjD;AAED,wBAAgB,eAAe,CAAC,aAAa,CAAC,EAAE,aAAa,GAAG,oBAAoB,CAenF;AAED,qBAAa,eAAgB,SAAQ,iBAAiB;IAElD,QAAQ,CAAC,WAAW,EAAE,aAAa;IACnC,QAAQ,CAAC,QAAQ,EAAE,oBAAoB;IACvC,SAAS,CAAC,SAAS,EAAE,MAAM;IAC3B,SAAS,CAAC,WAAW,EAAE,MAAM;gBAHpB,WAAW,EAAE,aAAa,EAC1B,QAAQ,EAAE,oBAAoB,EAC7B,SAAS,EAAE,MAAM,EACjB,WAAW,EAAE,MAAM;IAK/B,WAAW,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,qBAAqB,CAAC;IAKrE,kBAAkB,CAAC,OAAO,EAAE,eAAe,GAAG,IAAI;CASnD"}