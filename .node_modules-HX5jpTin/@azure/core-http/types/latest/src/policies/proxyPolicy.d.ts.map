{"version": 3, "file": "proxyPolicy.d.ts", "sourceRoot": "", "sources": ["../../../../src/policies/proxyPolicy.ts"], "names": [], "mappings": "AAGA,OAAO,EACL,iBAAiB,EACjB,aAAa,EACb,oBAAoB,EACpB,oBAAoB,EACrB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,qBAAqB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAKjD;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE,MAAM,EAAO,CAAC;AAyD9C;;GAEG;AACH,wBAAgB,WAAW,IAAI,MAAM,EAAE,CAWtC;AAED,wBAAgB,uBAAuB,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,aAAa,GAAG,SAAS,CAiBpF;AAED;;;;;;GAMG;AACH,wBAAgB,WAAW,CACzB,aAAa,CAAC,EAAE,aAAa,EAC7B,OAAO,CAAC,EAAE;IACR,sFAAsF;IACtF,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC;CAC9B,GACA,oBAAoB,CAiBtB;AAyBD,qBAAa,WAAY,SAAQ,iBAAiB;IAIvC,aAAa,EAAE,aAAa;IACnC,OAAO,CAAC,iBAAiB,CAAC;gBAH1B,UAAU,EAAE,aAAa,EACzB,OAAO,EAAE,oBAAoB,EACtB,aAAa,EAAE,aAAa,EAC3B,iBAAiB,CAAC,sBAAU;IAK/B,WAAW,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,qBAAqB,CAAC;CAa7E"}