{"version": 3, "file": "throttlingRetryPolicy.d.ts", "sourceRoot": "", "sources": ["../../../../src/policies/throttlingRetryPolicy.ts"], "names": [], "mappings": "AAIA,OAAO,EACL,iBAAiB,EACjB,aAAa,EACb,oBAAoB,EACpB,oBAAoB,EACrB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AACjD,OAAO,EAAE,qBAAqB,EAAE,MAAM,0BAA0B,CAAC;AAKjE,aAAK,eAAe,GAAG,CACrB,WAAW,EAAE,eAAe,EAC5B,QAAQ,EAAE,qBAAqB,KAC5B,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAGpC,wBAAgB,qBAAqB,IAAI,oBAAoB,CAM5D;AAID;;;;;GAKG;AACH,qBAAa,qBAAsB,SAAQ,iBAAiB;IAC1D,OAAO,CAAC,eAAe,CAAkB;IACzC,OAAO,CAAC,eAAe,CAAK;gBAG1B,UAAU,EAAE,aAAa,EACzB,OAAO,EAAE,oBAAoB,EAC7B,eAAe,CAAC,EAAE,eAAe;IAMtB,WAAW,CAAC,WAAW,EAAE,eAAe,GAAG,OAAO,CAAC,qBAAqB,CAAC;YAYxE,uBAAuB;WAmCvB,qBAAqB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;WAS9D,yBAAyB,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;CAWjF"}