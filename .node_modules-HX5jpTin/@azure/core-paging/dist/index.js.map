{"version": 3, "file": "index.js", "sources": ["../src/getPagedAsyncIterator.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PagedAsyncIterableIterator, PageSettings, PagedResult } from \"./models\";\n\n/**\n * returns an async iterator that iterates over results. It also has a `byPage`\n * method that returns pages of items at once.\n *\n * @param pagedResult - an object that specifies how to get pages.\n * @returns a paged async iterator that iterates over results.\n */\nexport function getPagedAsyncIterator<\n  TElement,\n  TPage = TElement[],\n  TPageSettings = PageSettings,\n  TLink = string\n>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>\n): PagedAsyncIterableIterator<TElement, TPage, TPageSettings> {\n  const iter = getItemAsyncIterator<TElement, TPage, TLink, TPageSettings>(pagedResult);\n  return {\n    next() {\n      return iter.next();\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n    byPage:\n      pagedResult?.byPage ??\n      ((settings?: PageSettings) => {\n        return getPageAsyncIterator(\n          pagedResult as PagedResult<TPage, PageSettings, TLink>,\n          settings?.maxPageSize\n        );\n      })\n  };\n}\n\nasync function* getItemAsyncIterator<TElement, TPage, TLink, TPageSettings>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>,\n  maxPageSize?: number\n): AsyncIterableIterator<TElement> {\n  const pages = getPageAsyncIterator(pagedResult, maxPageSize);\n  const firstVal = await pages.next();\n  // if the result does not have an array shape, i.e. TPage = TElement, then we return it as is\n  if (!Array.isArray(firstVal.value)) {\n    yield firstVal.value;\n    // `pages` is of type `AsyncIterableIterator<TPage>` but TPage = TElement in this case\n    yield* (pages as unknown) as AsyncIterableIterator<TElement>;\n  } else {\n    yield* firstVal.value;\n    for await (const page of pages) {\n      // pages is of type `AsyncIterableIterator<TPage>` so `page` is of type `TPage`. In this branch,\n      // it must be the case that `TPage = TElement[]`\n      yield* (page as unknown) as TElement[];\n    }\n  }\n}\n\nasync function* getPageAsyncIterator<TPage, TLink, TPageSettings>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>,\n  maxPageSize?: number\n): AsyncIterableIterator<TPage> {\n  let response = await pagedResult.getPage(pagedResult.firstPageLink, maxPageSize);\n  yield response.page;\n  while (response.nextPageLink) {\n    response = await pagedResult.getPage(response.nextPageLink, maxPageSize);\n    yield response.page;\n  }\n}\n"], "names": ["__asyncDelegator", "__asyncValues"], "mappings": ";;;;;;;AAAA;AAKA;;;;;;;SAOgB,qBAAqB,CAMnC,WAAqD;;IAErD,MAAM,IAAI,GAAG,oBAAoB,CAAwC,WAAW,CAAC,CAAC;IACtF,OAAO;QACL,IAAI;YACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;SACpB;QACD,CAAC,MAAM,CAAC,aAAa,CAAC;YACpB,OAAO,IAAI,CAAC;SACb;QACD,MAAM,EACJ,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,oCAClB,CAAC,QAAuB;YACvB,OAAO,oBAAoB,CACzB,WAAsD,EACtD,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,WAAW,CACtB,CAAC;SACH,CAAC;KACL,CAAC;AACJ,CAAC;AAED,SAAgB,oBAAoB,CAClC,WAAqD,EACrD,WAAoB;;;QAEpB,MAAM,KAAK,GAAG,oBAAoB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAC7D,MAAM,QAAQ,GAAG,oBAAM,KAAK,CAAC,IAAI,EAAE,CAAA,CAAC;;QAEpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClC,0BAAM,QAAQ,CAAC,KAAK,CAAA,CAAC;;YAErB,oBAAA,OAAOA,uBAAAC,oBAAC,KAAoD,CAAA,CAAA,CAAA,CAAC;SAC9D;aAAM;YACL,oBAAA,OAAOD,uBAAAC,oBAAA,QAAQ,CAAC,KAAK,CAAA,CAAA,CAAA,CAAC;;gBACtB,KAAyB,IAAA,UAAAA,oBAAA,KAAK,CAAA,WAAA;oBAAnB,MAAM,IAAI,kBAAA,CAAA;;;oBAGnB,oBAAA,OAAOD,uBAAAC,oBAAC,IAA8B,CAAA,CAAA,CAAA,CAAC;iBACxC;;;;;;;;;SACF;KACF;CAAA;AAED,SAAgB,oBAAoB,CAClC,WAAqD,EACrD,WAAoB;;QAEpB,IAAI,QAAQ,GAAG,oBAAM,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA,CAAC;QACjF,0BAAM,QAAQ,CAAC,IAAI,CAAA,CAAC;QACpB,OAAO,QAAQ,CAAC,YAAY,EAAE;YAC5B,QAAQ,GAAG,oBAAM,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA,CAAC;YACzE,0BAAM,QAAQ,CAAC,IAAI,CAAA,CAAC;SACrB;KACF;;;;;"}