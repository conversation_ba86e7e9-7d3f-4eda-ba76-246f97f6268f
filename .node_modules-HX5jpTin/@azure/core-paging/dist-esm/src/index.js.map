{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,oCAAoC,CAAC;AAE5C,cAAc,UAAU,CAAC;AACzB,cAAc,yBAAyB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport \"@azure/core-asynciterator-polyfill\";\n\nexport * from \"./models\";\nexport * from \"./getPagedAsyncIterator\";\n"]}