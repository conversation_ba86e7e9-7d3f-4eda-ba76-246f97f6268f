{"version": 3, "file": "getPagedAsyncIterator.js", "sourceRoot": "", "sources": ["../../src/getPagedAsyncIterator.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAIlC;;;;;;GAMG;AACH,MAAM,UAAU,qBAAqB,CAMnC,WAAqD;;IAErD,MAAM,IAAI,GAAG,oBAAoB,CAAwC,WAAW,CAAC,CAAC;IACtF,OAAO;QACL,IAAI;YACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;QACrB,CAAC;QACD,CAAC,MAAM,CAAC,aAAa,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,EACJ,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,mCACnB,CAAC,CAAC,QAAuB,EAAE,EAAE;YAC3B,OAAO,oBAAoB,CACzB,WAAsD,EACtD,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,WAAW,CACtB,CAAC;QACJ,CAAC,CAAC;KACL,CAAC;AACJ,CAAC;AAED,SAAgB,oBAAoB,CAClC,WAAqD,EACrD,WAAoB;;;QAEpB,MAAM,KAAK,GAAG,oBAAoB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAC7D,MAAM,QAAQ,GAAG,cAAM,KAAK,CAAC,IAAI,EAAE,CAAA,CAAC;QACpC,6FAA6F;QAC7F,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClC,oBAAM,QAAQ,CAAC,KAAK,CAAA,CAAC;YACrB,sFAAsF;YACtF,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAC,KAAoD,CAAA,CAAA,CAAA,CAAC;SAC9D;aAAM;YACL,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,QAAQ,CAAC,KAAK,CAAA,CAAA,CAAA,CAAC;;gBACtB,KAAyB,IAAA,UAAA,cAAA,KAAK,CAAA,WAAA;oBAAnB,MAAM,IAAI,kBAAA,CAAA;oBACnB,gGAAgG;oBAChG,gDAAgD;oBAChD,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAC,IAA8B,CAAA,CAAA,CAAA,CAAC;iBACxC;;;;;;;;;SACF;IACH,CAAC;CAAA;AAED,SAAgB,oBAAoB,CAClC,WAAqD,EACrD,WAAoB;;QAEpB,IAAI,QAAQ,GAAG,cAAM,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA,CAAC;QACjF,oBAAM,QAAQ,CAAC,IAAI,CAAA,CAAC;QACpB,OAAO,QAAQ,CAAC,YAAY,EAAE;YAC5B,QAAQ,GAAG,cAAM,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA,CAAC;YACzE,oBAAM,QAAQ,CAAC,IAAI,CAAA,CAAC;SACrB;IACH,CAAC;CAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PagedAsyncIterableIterator, PageSettings, PagedResult } from \"./models\";\n\n/**\n * returns an async iterator that iterates over results. It also has a `byPage`\n * method that returns pages of items at once.\n *\n * @param pagedResult - an object that specifies how to get pages.\n * @returns a paged async iterator that iterates over results.\n */\nexport function getPagedAsyncIterator<\n  TElement,\n  TPage = TElement[],\n  TPageSettings = PageSettings,\n  TLink = string\n>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>\n): PagedAsyncIterableIterator<TElement, TPage, TPageSettings> {\n  const iter = getItemAsyncIterator<TElement, TPage, TLink, TPageSettings>(pagedResult);\n  return {\n    next() {\n      return iter.next();\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n    byPage:\n      pagedResult?.byPage ??\n      ((settings?: PageSettings) => {\n        return getPageAsyncIterator(\n          pagedResult as PagedResult<TPage, PageSettings, TLink>,\n          settings?.maxPageSize\n        );\n      })\n  };\n}\n\nasync function* getItemAsyncIterator<TElement, TPage, TLink, TPageSettings>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>,\n  maxPageSize?: number\n): AsyncIterableIterator<TElement> {\n  const pages = getPageAsyncIterator(pagedResult, maxPageSize);\n  const firstVal = await pages.next();\n  // if the result does not have an array shape, i.e. TPage = TElement, then we return it as is\n  if (!Array.isArray(firstVal.value)) {\n    yield firstVal.value;\n    // `pages` is of type `AsyncIterableIterator<TPage>` but TPage = TElement in this case\n    yield* (pages as unknown) as AsyncIterableIterator<TElement>;\n  } else {\n    yield* firstVal.value;\n    for await (const page of pages) {\n      // pages is of type `AsyncIterableIterator<TPage>` so `page` is of type `TPage`. In this branch,\n      // it must be the case that `TPage = TElement[]`\n      yield* (page as unknown) as TElement[];\n    }\n  }\n}\n\nasync function* getPageAsyncIterator<TPage, TLink, TPageSettings>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>,\n  maxPageSize?: number\n): AsyncIterableIterator<TPage> {\n  let response = await pagedResult.getPage(pagedResult.firstPageLink, maxPageSize);\n  yield response.page;\n  while (response.nextPageLink) {\n    response = await pagedResult.getPage(response.nextPageLink, maxPageSize);\n    yield response.page;\n  }\n}\n"]}