{"version": 3, "file": "azureNamedKeyCredential.js", "sourceRoot": "", "sources": ["../../src/azureNamedKeyCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,sBAAsB,EAAE,MAAM,kBAAkB,CAAC;AAgB1D;;;GAGG;AACH,MAAM,OAAO,uBAAuB;IAIlC;;OAEG;IACH,IAAW,GAAG;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,GAAW;QACnC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAClB,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAClB,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,OAAe,EAAE,MAAc;QAC3C,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;YACxB,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;IACrB,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,UAAU,oBAAoB,CAAC,UAAmB;IACtD,OAAO,CACL,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACnD,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ;QAClC,OAAO,UAAU,CAAC,IAAI,KAAK,QAAQ,CACpC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isObjectWithProperties } from \"@azure/core-util\";\n\n/**\n * Represents a credential defined by a static API name and key.\n */\nexport interface NamedKeyCredential {\n  /**\n   * The value of the API key represented as a string\n   */\n  readonly key: string;\n  /**\n   * The value of the API name represented as a string.\n   */\n  readonly name: string;\n}\n\n/**\n * A static name/key-based credential that supports updating\n * the underlying name and key values.\n */\nexport class AzureNamedKeyCredential implements NamedKeyCredential {\n  private _key: string;\n  private _name: string;\n\n  /**\n   * The value of the key to be used in authentication.\n   */\n  public get key(): string {\n    return this._key;\n  }\n\n  /**\n   * The value of the name to be used in authentication.\n   */\n  public get name(): string {\n    return this._name;\n  }\n\n  /**\n   * Create an instance of an AzureNamedKeyCredential for use\n   * with a service client.\n   *\n   * @param name - The initial value of the name to use in authentication.\n   * @param key - The initial value of the key to use in authentication.\n   */\n  constructor(name: string, key: string) {\n    if (!name || !key) {\n      throw new TypeError(\"name and key must be non-empty strings\");\n    }\n\n    this._name = name;\n    this._key = key;\n  }\n\n  /**\n   * Change the value of the key.\n   *\n   * Updates will take effect upon the next request after\n   * updating the key value.\n   *\n   * @param newName - The new name value to be used.\n   * @param newKey - The new key value to be used.\n   */\n  public update(newName: string, newKey: string): void {\n    if (!newName || !newKey) {\n      throw new TypeError(\"newName and newKey must be non-empty strings\");\n    }\n\n    this._name = newName;\n    this._key = newKey;\n  }\n}\n\n/**\n * Tests an object to determine whether it implements NamedKeyCredential.\n *\n * @param credential - The assumed NamedKeyCredential to be tested.\n */\nexport function isNamedKeyCredential(credential: unknown): credential is NamedKeyCredential {\n  return (\n    isObjectWithProperties(credential, [\"name\", \"key\"]) &&\n    typeof credential.key === \"string\" &&\n    typeof credential.name === \"string\"\n  );\n}\n"]}