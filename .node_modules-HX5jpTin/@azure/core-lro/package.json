{"name": "@azure/core-lro", "author": "Microsoft Corporation", "sdk-type": "client", "version": "2.2.1", "description": "Isomorphic client library for supporting long-running operations in node.js and browser.", "tags": ["isomorphic", "browser", "javascript", "node", "microsoft", "lro", "polling"], "keywords": ["isomorphic", "browser", "javascript", "node", "microsoft", "lro", "polling", "azure", "cloud"], "main": "./dist/index.js", "module": "dist-esm/src/index.js", "types": "./types/core-lro.d.ts", "files": ["types/core-lro.d.ts", "dist-esm/src", "dist/", "README.md", "LICENSE"], "engines": {"node": ">=12.0.0"}, "browser": {"os": false, "process": false}, "react-native": {"./dist/index.js": "./dist-esm/src/index.js"}, "license": "MIT", "homepage": "https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/core/core-lro/README.md", "repository": "github:Azure/azure-sdk-for-js", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "nyc": {"extension": [".ts"], "exclude": ["coverage/**/*", "**/*.d.ts", "**/*.js"], "reporter": ["text", "html", "cobertura"], "all": true}, "scripts": {"audit": "node ../../../common/scripts/rush-audit.js && rimraf node_modules package-lock.json && npm i --package-lock-only 2>&1 && npm audit", "build:samples": "echo Obsolete", "build:test": "tsc -p . && rollup -c 2>&1", "build": "npm run clean && tsc -p . && rollup -c 2>&1 && api-extractor run --local", "docs": "typedoc --excludePrivate --excludeNotExported --excludeExternals --stripInternal --mode file --out ./dist/docs ./src", "check-format": "prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"*.{js,json}\"", "clean": "rimraf dist dist-* types *.log browser statistics.html coverage src/**/*.js test/**/*.js", "execute:samples": "echo skipped", "extract-api": "tsc -p . && api-extractor run --local", "format": "prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "integration-test:browser": "echo skipped", "integration-test:node": "echo skipped", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "lint:fix": "eslint package.json api-extractor.json src test --ext .ts --fix --fix-type [problem,suggestion]", "lint": "eslint package.json api-extractor.json src test  --ext .ts", "pack": "npm pack 2>&1", "test:browser": "npm run build:test && npm run unit-test:browser && npm run integration-test:browser", "test:node": "npm run build:test && npm run unit-test:node && npm run integration-test:node", "test": "npm run build:test && npm run unit-test", "unit-test": "npm run unit-test:node && npm run unit-test:browser", "unit-test:browser": "karma start --single-run", "unit-test:node": "cross-env TS_NODE_FILES=true TS_NODE_COMPILER_OPTIONS=\"{\\\"module\\\":\\\"commonjs\\\"}\" nyc mocha -r ts-node/register --reporter ../../../common/tools/mocha-multi-reporter.js --timeout 1200000 --full-trace  --exclude \"test/**/browser/*.spec.ts\" \"test/**/*.spec.ts\""}, "sideEffects": false, "dependencies": {"@azure/abort-controller": "^1.0.0", "@azure/core-tracing": "1.0.0-preview.13", "@azure/logger": "^1.0.0", "tslib": "^2.2.0"}, "devDependencies": {"@azure/core-http": "^2.0.0", "@azure/core-rest-pipeline": "^1.1.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@azure/dev-tool": "^1.0.0", "@microsoft/api-extractor": "^7.18.11", "@types/chai": "^4.1.6", "@types/mocha": "^7.0.2", "@types/node": "^12.0.0", "chai": "^4.2.0", "cross-env": "^7.0.2", "eslint": "^7.15.0", "karma": "^6.2.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.0", "karma-edge-launcher": "^0.4.2", "karma-env-preprocessor": "^0.1.1", "karma-firefox-launcher": "^1.1.0", "karma-ie-launcher": "^1.0.0", "karma-junit-reporter": "^2.0.1", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sourcemap-loader": "^0.3.8", "mocha": "^7.1.1", "mocha-junit-reporter": "^1.18.0", "npm-run-all": "^4.1.5", "nyc": "^14.0.0", "prettier": "^1.16.4", "rimraf": "^3.0.0", "rollup": "^1.16.3", "ts-node": "^10.0.0", "typescript": "~4.2.0", "uglify-js": "^3.4.9", "typedoc": "0.15.2"}}