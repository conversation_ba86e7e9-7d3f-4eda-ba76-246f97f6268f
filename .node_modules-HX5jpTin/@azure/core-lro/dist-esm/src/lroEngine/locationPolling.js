// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
import { isUnexpectedPollingResponse } from "./requestUtils";
function isLocationPollingDone(rawResponse) {
    return !isUnexpectedPollingResponse(rawResponse) && rawResponse.statusCode !== 202;
}
export function processLocationPollingOperationResult(response) {
    return Object.assign(Object.assign({}, response), { done: isLocationPollingDone(response.rawResponse) });
}
//# sourceMappingURL=locationPolling.js.map