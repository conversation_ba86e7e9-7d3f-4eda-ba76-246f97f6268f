{"version": 3, "file": "bodyPolling.js", "sourceRoot": "", "sources": ["../../../src/lroEngine/bodyPolling.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,aAAa,EAKb,aAAa,EACd,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,2BAA2B,EAAE,MAAM,gBAAgB,CAAC;AAE7D,SAAS,oBAAoB,CAAC,WAAwB;;IACpD,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,GAAG,MAAC,WAAW,CAAC,IAAgB,mCAAI,EAAE,CAAC;IAC9E,MAAM,KAAK,GAAuB,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,iBAAiB,mCAAI,iBAAiB,CAAC;IACrF,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;AACvE,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,WAAwB;IACxD,MAAM,KAAK,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;IAChD,IAAI,2BAA2B,CAAC,WAAW,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7E,MAAM,IAAI,KAAK,CAAC,kEAAkE,KAAK,GAAG,CAAC,CAAC;KAC7F;IACD,OAAO,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,iCAAiC,CAC/C,QAA8B;IAE9B,uCACK,QAAQ,KACX,IAAI,EAAE,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,IAC7C;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  failureStates,\n  LroBody,\n  LroResponse,\n  LroStatus,\n  RawResponse,\n  successStates\n} from \"./models\";\nimport { isUnexpectedPollingResponse } from \"./requestUtils\";\n\nfunction getProvisioningState(rawResponse: RawResponse): string {\n  const { properties, provisioningState } = (rawResponse.body as LroBody) ?? {};\n  const state: string | undefined = properties?.provisioningState ?? provisioningState;\n  return typeof state === \"string\" ? state.toLowerCase() : \"succeeded\";\n}\n\nexport function isBodyPollingDone(rawResponse: RawResponse): boolean {\n  const state = getProvisioningState(rawResponse);\n  if (isUnexpectedPollingResponse(rawResponse) || failureStates.includes(state)) {\n    throw new Error(`The long running operation has failed. The provisioning state: ${state}.`);\n  }\n  return successStates.includes(state);\n}\n\n/**\n * Creates a polling strategy based on BodyPolling which uses the provisioning state\n * from the result to determine the current operation state\n */\nexport function processBodyPollingOperationResult<TResult>(\n  response: LroResponse<TResult>\n): LroStatus<TResult> {\n  return {\n    ...response,\n    done: isBodyPollingDone(response.rawResponse)\n  };\n}\n"]}