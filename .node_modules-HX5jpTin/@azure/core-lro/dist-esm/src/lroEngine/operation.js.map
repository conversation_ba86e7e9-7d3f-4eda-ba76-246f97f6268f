{"version": 3, "file": "operation.js", "sourceRoot": "", "sources": ["../../../src/lroEngine/operation.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAWlC,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,EAAE,8BAA8B,EAAE,qBAAqB,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAEnG,MAAM,OAAO,oBAAoB;IAU/B,YACS,KAAoD,EACnD,GAAkC,EAClC,yBAAqD,EACrD,aAA2D,EAC3D,WAAgE,EAChE,MAA0D;QAL3D,UAAK,GAAL,KAAK,CAA+C;QACnD,QAAG,GAAH,GAAG,CAA+B;QAClC,8BAAyB,GAAzB,yBAAyB,CAA4B;QACrD,kBAAa,GAAb,aAAa,CAA8C;QAC3D,gBAAW,GAAX,WAAW,CAAqD;QAChE,WAAM,GAAN,MAAM,CAAoD;IACjE,CAAC;IAEG,eAAe,CAAC,YAA0B;QAC/C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,MAAM,CAAC,OAGZ;;QACC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,YAAY,GAAqC,SAAS,CAAC;QAC/D,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACpB,MAAM,eAAe,GAAG,qBAAqB,CAC3C,KAAK,EACL,IAAI,CAAC,GAAG,CAAC,WAAW,EACpB,IAAI,CAAC,GAAG,CAAC,aAAa,CACvB,CAAC;YACF,YAAY,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC;YACnD,eAAe,CAAC,YAAY,CAAC,CAAC;SAC/B;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAChD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;oBACjB,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAC;iBACH;gBACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAI,CAAC,wBAAwB,GAAG,MAAM;oBACpC,CAAC,CAAC,CAAC,QAA8B,EAAE,EAAE,CAAC,iCAC/B,QAAQ,KACX,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAC/C;oBACJ,CAAC,CAAC,8BAA8B,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAC3F,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClC;YACD,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;gBACrB,MAAM,IAAI,KAAK,CACb,2FAA2F,CAC5F,CAAC;aACH;YACD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,CAClC,KAAK,CAAC,UAAU,EAChB,IAAI,CAAC,YAAa,EAClB,IAAI,CAAC,wBAAwB,CAC9B,CAAC;YACF,MAAM,CAAC,OAAO,CAAC,0BAA0B,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACrF,IAAI,YAAY,CAAC,IAAI,EAAE;gBACrB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa;oBAC/B,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC;oBACtD,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC;gBAC9B,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;aAC1B;iBAAM;gBACL,IAAI,CAAC,IAAI,GAAG,MAAA,YAAY,CAAC,IAAI,mCAAI,IAAI,CAAC,IAAI,CAAC;gBAC3C,KAAK,CAAC,UAAU,GAAG,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;aAC9E;YACD,YAAY,GAAG,YAAY,CAAC;SAC7B;QACD,MAAM,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/D,IAAI,YAAY,EAAE;YAChB,MAAA,IAAI,CAAC,WAAW,+CAAhB,IAAI,EAAe,KAAK,EAAE,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,WAAW,CAAC,CAAC;SACtD;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAC/C;QACD,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,+CAArB,OAAO,EAAiB,KAAK,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { PollOperation, PollOperationState } from \"../pollOperation\";\nimport { logger } from \"./logger\";\nimport {\n  PollerConfig,\n  ResumablePollOperationState,\n  LongRunningOperation,\n  GetLroStatusFromResponse,\n  LroResourceLocationConfig,\n  LroStatus,\n  LroResponse,\n  RawResponse\n} from \"./models\";\nimport { getPollingUrl } from \"./requestUtils\";\nimport { createGetLroStatusFromResponse, createInitializeState, createPoll } from \"./stateMachine\";\n\nexport class GenericPollOperation<TResult, TState extends PollOperationState<TResult>>\n  implements PollOperation<TState, TResult> {\n  private poll?: (\n    pollingURL: string,\n    pollerConfig: PollerConfig,\n    getLroStatusFromResponse: GetLroStatusFromResponse<TResult>\n  ) => Promise<LroStatus<TResult>>;\n  private pollerConfig?: PollerConfig;\n  private getLroStatusFromResponse?: GetLroStatusFromResponse<TResult>;\n\n  constructor(\n    public state: TState & ResumablePollOperationState<TResult>,\n    private lro: LongRunningOperation<TResult>,\n    private lroResourceLocationConfig?: LroResourceLocationConfig,\n    private processResult?: (result: unknown, state: TState) => TResult,\n    private updateState?: (state: TState, lastResponse: RawResponse) => void,\n    private isDone?: (lastResponse: TResult, state: TState) => boolean\n  ) {}\n\n  public setPollerConfig(pollerConfig: PollerConfig): void {\n    this.pollerConfig = pollerConfig;\n  }\n\n  /**\n   * General update function for LROPoller, the general process is as follows\n   * 1. Check initial operation result to determine the strategy to use\n   *  - Strategies: Location, Azure-AsyncOperation, Original Uri\n   * 2. Check if the operation result has a terminal state\n   *  - Terminal state will be determined by each strategy\n   *  2.1 If it is terminal state Check if a final GET request is required, if so\n   *      send final GET request and return result from operation. If no final GET\n   *      is required, just return the result from operation.\n   *      - Determining what to call for final request is responsibility of each strategy\n   *  2.2 If it is not terminal state, call the polling operation and go to step 1\n   *      - Determining what to call for polling is responsibility of each strategy\n   *      - Strategies will always use the latest URI for polling if provided otherwise\n   *        the last known one\n   */\n  async update(options?: {\n    abortSignal?: AbortSignalLike;\n    fireProgress?: (state: TState) => void;\n  }): Promise<PollOperation<TState, TResult>> {\n    const state = this.state;\n    let lastResponse: LroResponse<TResult> | undefined = undefined;\n    if (!state.isStarted) {\n      const initializeState = createInitializeState(\n        state,\n        this.lro.requestPath,\n        this.lro.requestMethod\n      );\n      lastResponse = await this.lro.sendInitialRequest();\n      initializeState(lastResponse);\n    }\n\n    if (!state.isCompleted) {\n      if (!this.poll || !this.getLroStatusFromResponse) {\n        if (!state.config) {\n          throw new Error(\n            \"Bad state: LRO mode is undefined. Please check if the serialized state is well-formed.\"\n          );\n        }\n        const isDone = this.isDone;\n        this.getLroStatusFromResponse = isDone\n          ? (response: LroResponse<TResult>) => ({\n              ...response,\n              done: isDone(response.flatResponse, this.state)\n            })\n          : createGetLroStatusFromResponse(this.lro, state.config, this.lroResourceLocationConfig);\n        this.poll = createPoll(this.lro);\n      }\n      if (!state.pollingURL) {\n        throw new Error(\n          \"Bad state: polling URL is undefined. Please check if the serialized state is well-formed.\"\n        );\n      }\n      const currentState = await this.poll(\n        state.pollingURL,\n        this.pollerConfig!,\n        this.getLroStatusFromResponse\n      );\n      logger.verbose(`LRO: polling response: ${JSON.stringify(currentState.rawResponse)}`);\n      if (currentState.done) {\n        state.result = this.processResult\n          ? this.processResult(currentState.flatResponse, state)\n          : currentState.flatResponse;\n        state.isCompleted = true;\n      } else {\n        this.poll = currentState.next ?? this.poll;\n        state.pollingURL = getPollingUrl(currentState.rawResponse, state.pollingURL);\n      }\n      lastResponse = currentState;\n    }\n    logger.verbose(`LRO: current state: ${JSON.stringify(state)}`);\n    if (lastResponse) {\n      this.updateState?.(state, lastResponse?.rawResponse);\n    } else {\n      logger.error(`LRO: no response was received`);\n    }\n    options?.fireProgress?.(state);\n    return this;\n  }\n\n  async cancel(): Promise<PollOperation<TState, TResult>> {\n    this.state.isCancelled = true;\n    return this;\n  }\n\n  /**\n   * Serializes the Poller operation.\n   */\n  public toString(): string {\n    return JSON.stringify({\n      state: this.state\n    });\n  }\n}\n"]}