{"version": 3, "file": "stateMachine.js", "sourceRoot": "", "sources": ["../../../src/lroEngine/stateMachine.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,gCAAgC,EAAE,MAAM,qBAAqB,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,iCAAiC,EAAE,MAAM,eAAe,CAAC;AACrF,OAAO,EAAE,qCAAqC,EAAE,MAAM,mBAAmB,CAAC;AAC1E,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAWlC,OAAO,EAAE,iCAAiC,EAAE,MAAM,eAAe,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,2BAA2B,EAAE,MAAM,gBAAgB,CAAC;AAE1F;;GAEG;AACH,MAAM,UAAU,8BAA8B,CAC5C,aAA4C,EAC5C,MAAiB,EACjB,yBAAqD;IAErD,QAAQ,MAAM,CAAC,IAAI,EAAE;QACnB,KAAK,YAAY,CAAC,CAAC;YACjB,OAAO,gCAAgC,CACrC,aAAa,EACb,MAAM,CAAC,gBAAgB,EACvB,yBAAyB,CAC1B,CAAC;SACH;QACD,KAAK,UAAU,CAAC,CAAC;YACf,OAAO,qCAAqC,CAAC;SAC9C;QACD,KAAK,MAAM,CAAC,CAAC;YACX,OAAO,iCAAiC,CAAC;SAC1C;QACD,OAAO,CAAC,CAAC;YACP,OAAO,iCAAiC,CAAC;SAC1C;KACF;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CACxB,aAA4C;IAM5C,OAAO,KAAK,EACV,IAAY,EACZ,YAA0B,EAC1B,wBAA2D,EAC9B,EAAE;QAC/B,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM,UAAU,GAAuB,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACnF,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC5C,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC,cAAc,CAAC;gBAC/C,CAAC,CAAC,gCAAgC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC;gBACnF,CAAC,CAAC,cAAc,CAAC;SACpB;QACD,OAAO,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,gCAAgC,CACvC,cAAoB,EACpB,mBAA2B;IAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;IACjD,MAAM,cAAc,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC;IAChD,IAAI,OAAO,GAAG,cAAc,EAAE;QAC5B,OAAO,cAAc,GAAG,OAAO,CAAC;KACjC;IACD,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,qBAAqB,CACnC,KAA2C,EAC3C,WAAmB,EACnB,aAAqB;IAErB,OAAO,CAAC,QAA8B,EAAW,EAAE;QACjD,IAAI,2BAA2B,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,IAAI,CAAC;QACnE,KAAK,CAAC,kBAAkB,GAAG,QAAQ,CAAC,WAAW,CAAC;QAChD,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QACvB,KAAK,CAAC,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;QACxE,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAClF,2EAA2E;QAC3E,IACE,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS;YAC/B,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,iBAAiB,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAC7E;YACA,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,YAAuB,CAAC;YAChD,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;SAC1B;QACD,MAAM,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/D,OAAO,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACpC,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { processAzureAsyncOperationResult } from \"./azureAsyncPolling\";\nimport { isBodyPollingDone, processBodyPollingOperationResult } from \"./bodyPolling\";\nimport { processLocationPollingOperationResult } from \"./locationPolling\";\nimport { logger } from \"./logger\";\nimport {\n  LroResourceLocationConfig,\n  GetLroStatusFromResponse,\n  LongRunningOperation,\n  LroConfig,\n  PollerConfig,\n  ResumablePollOperationState,\n  LroResponse,\n  LroStatus\n} from \"./models\";\nimport { processPassthroughOperationResult } from \"./passthrough\";\nimport { getPollingUrl, inferLroMode, isUnexpectedInitialResponse } from \"./requestUtils\";\n\n/**\n * creates a stepping function that maps an LRO state to another.\n */\nexport function createGetLroStatusFromResponse<TResult>(\n  lroPrimitives: LongRunningOperation<TResult>,\n  config: LroConfig,\n  lroResourceLocationConfig?: LroResourceLocationConfig\n): GetLroStatusFromResponse<TResult> {\n  switch (config.mode) {\n    case \"AzureAsync\": {\n      return processAzureAsyncOperationResult(\n        lroPrimitives,\n        config.resourceLocation,\n        lroResourceLocationConfig\n      );\n    }\n    case \"Location\": {\n      return processLocationPollingOperationResult;\n    }\n    case \"Body\": {\n      return processBodyPollingOperationResult;\n    }\n    default: {\n      return processPassthroughOperationResult;\n    }\n  }\n}\n\n/**\n * Creates a polling operation.\n */\nexport function createPoll<TResult>(\n  lroPrimitives: LongRunningOperation<TResult>\n): (\n  pollingURL: string,\n  pollerConfig: PollerConfig,\n  getLroStatusFromResponse: GetLroStatusFromResponse<TResult>\n) => Promise<LroStatus<TResult>> {\n  return async (\n    path: string,\n    pollerConfig: PollerConfig,\n    getLroStatusFromResponse: GetLroStatusFromResponse<TResult>\n  ): Promise<LroStatus<TResult>> => {\n    const response = await lroPrimitives.sendPollRequest(path);\n    const retryAfter: string | undefined = response.rawResponse.headers[\"retry-after\"];\n    if (retryAfter !== undefined) {\n      const retryAfterInMs = parseInt(retryAfter);\n      pollerConfig.intervalInMs = isNaN(retryAfterInMs)\n        ? calculatePollingIntervalFromDate(new Date(retryAfter), pollerConfig.intervalInMs)\n        : retryAfterInMs;\n    }\n    return getLroStatusFromResponse(response);\n  };\n}\n\nfunction calculatePollingIntervalFromDate(\n  retryAfterDate: Date,\n  defaultIntervalInMs: number\n): number {\n  const timeNow = Math.floor(new Date().getTime());\n  const retryAfterTime = retryAfterDate.getTime();\n  if (timeNow < retryAfterTime) {\n    return retryAfterTime - timeNow;\n  }\n  return defaultIntervalInMs;\n}\n\n/**\n * Creates a callback to be used to initialize the polling operation state.\n * @param state - of the polling operation\n * @param operationSpec - of the LRO\n * @param callback - callback to be called when the operation is done\n * @returns callback that initializes the state of the polling operation\n */\nexport function createInitializeState<TResult>(\n  state: ResumablePollOperationState<TResult>,\n  requestPath: string,\n  requestMethod: string\n): (response: LroResponse<TResult>) => boolean {\n  return (response: LroResponse<TResult>): boolean => {\n    if (isUnexpectedInitialResponse(response.rawResponse)) return true;\n    state.initialRawResponse = response.rawResponse;\n    state.isStarted = true;\n    state.pollingURL = getPollingUrl(state.initialRawResponse, requestPath);\n    state.config = inferLroMode(requestPath, requestMethod, state.initialRawResponse);\n    /** short circuit polling if body polling is done in the initial request */\n    if (\n      state.config.mode === undefined ||\n      (state.config.mode === \"Body\" && isBodyPollingDone(state.initialRawResponse))\n    ) {\n      state.result = response.flatResponse as TResult;\n      state.isCompleted = true;\n    }\n    logger.verbose(`LRO: initial state: ${JSON.stringify(state)}`);\n    return Boolean(state.isCompleted);\n  };\n}\n"]}