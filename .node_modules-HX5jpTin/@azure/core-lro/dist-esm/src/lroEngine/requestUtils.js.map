{"version": 3, "file": "requestUtils.js", "sourceRoot": "", "sources": ["../../../src/lroEngine/requestUtils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC;;;;;GAKG;AACH,MAAM,UAAU,aAAa,CAAC,WAAwB,EAAE,WAAmB;;IACzE,OAAO,CACL,MAAA,MAAA,MAAA,sBAAsB,CAAC,WAAW,CAAC,mCACnC,WAAW,CAAC,WAAW,CAAC,mCACxB,oBAAoB,CAAC,WAAW,CAAC,mCACjC,WAAW,CACZ,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,WAAwB;IAC3C,OAAO,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,oBAAoB,CAAC,WAAwB;IACpD,OAAO,WAAW,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,sBAAsB,CAAC,WAAwB;IACtD,OAAO,WAAW,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACrD,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,WAAmB,EACnB,aAAqB,EACrB,WAAwB;IAExB,IAAI,sBAAsB,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;QACrD,OAAO;YACL,IAAI,EAAE,YAAY;YAClB,gBAAgB,EACd,aAAa,KAAK,KAAK;gBACrB,CAAC,CAAC,WAAW;gBACb,CAAC,CAAC,aAAa,KAAK,MAAM;oBAC1B,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC;oBAC1B,CAAC,CAAC,SAAS;SAChB,CAAC;KACH;SAAM,IACL,WAAW,CAAC,WAAW,CAAC,KAAK,SAAS;QACtC,oBAAoB,CAAC,WAAW,CAAC,KAAK,SAAS,EAC/C;QACA,OAAO;YACL,IAAI,EAAE,UAAU;SACjB,CAAC;KACH;SAAM,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;QACnD,OAAO;YACL,IAAI,EAAE,MAAM;SACb,CAAC;KACH;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,eAAgB,SAAQ,KAAK;IAEjC,YAAY,OAAe,EAAE,UAAkB;QAC7C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;CACF;AAED,MAAM,UAAU,2BAA2B,CAAC,WAAwB;IAClE,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC;IACpC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClD,MAAM,IAAI,eAAe,CACvB,wCAAwC,IAAI,6DAA6D,EACzG,IAAI,CACL,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,2BAA2B,CAAC,WAAwB;IAClE,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC;IACpC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACxC,MAAM,IAAI,eAAe,CACvB,wCAAwC,IAAI,mDAAmD,EAC/F,IAAI,CACL,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LroConfig, RawResponse } from \"./models\";\n\n/**\n * Detects where the continuation token is and returns it. Notice that azure-asyncoperation\n * must be checked first before the other location headers because there are scenarios\n * where both azure-asyncoperation and location could be present in the same response but\n * azure-asyncoperation should be the one to use for polling.\n */\nexport function getPollingUrl(rawResponse: RawResponse, defaultPath: string): string {\n  return (\n    getAzureAsyncOperation(rawResponse) ??\n    getLocation(rawResponse) ??\n    getOperationLocation(rawResponse) ??\n    defaultPath\n  );\n}\n\nfunction getLocation(rawResponse: RawResponse): string | undefined {\n  return rawResponse.headers[\"location\"];\n}\n\nfunction getOperationLocation(rawResponse: RawResponse): string | undefined {\n  return rawResponse.headers[\"operation-location\"];\n}\n\nfunction getAzureAsyncOperation(rawResponse: RawResponse): string | undefined {\n  return rawResponse.headers[\"azure-asyncoperation\"];\n}\n\nexport function inferLroMode(\n  requestPath: string,\n  requestMethod: string,\n  rawResponse: RawResponse\n): LroConfig {\n  if (getAzureAsyncOperation(rawResponse) !== undefined) {\n    return {\n      mode: \"AzureAsync\",\n      resourceLocation:\n        requestMethod === \"PUT\"\n          ? requestPath\n          : requestMethod === \"POST\"\n          ? getLocation(rawResponse)\n          : undefined\n    };\n  } else if (\n    getLocation(rawResponse) !== undefined ||\n    getOperationLocation(rawResponse) !== undefined\n  ) {\n    return {\n      mode: \"Location\"\n    };\n  } else if ([\"PUT\", \"PATCH\"].includes(requestMethod)) {\n    return {\n      mode: \"Body\"\n    };\n  }\n  return {};\n}\n\nclass SimpleRestError extends Error {\n  public statusCode?: number;\n  constructor(message: string, statusCode: number) {\n    super(message);\n    this.name = \"RestError\";\n    this.statusCode = statusCode;\n\n    Object.setPrototypeOf(this, SimpleRestError.prototype);\n  }\n}\n\nexport function isUnexpectedInitialResponse(rawResponse: RawResponse): boolean {\n  const code = rawResponse.statusCode;\n  if (![203, 204, 202, 201, 200, 500].includes(code)) {\n    throw new SimpleRestError(\n      `Received unexpected HTTP status code ${code} in the initial response. This may indicate a server issue.`,\n      code\n    );\n  }\n  return false;\n}\n\nexport function isUnexpectedPollingResponse(rawResponse: RawResponse): boolean {\n  const code = rawResponse.statusCode;\n  if (![202, 201, 200, 500].includes(code)) {\n    throw new SimpleRestError(\n      `Received unexpected HTTP status code ${code} while polling. This may indicate a server issue.`,\n      code\n    );\n  }\n  return false;\n}\n"]}