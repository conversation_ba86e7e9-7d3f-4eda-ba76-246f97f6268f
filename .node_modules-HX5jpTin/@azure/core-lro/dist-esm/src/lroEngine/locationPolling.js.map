{"version": 3, "file": "locationPolling.js", "sourceRoot": "", "sources": ["../../../src/lroEngine/locationPolling.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,2BAA2B,EAAE,MAAM,gBAAgB,CAAC;AAE7D,SAAS,qBAAqB,CAAC,WAAwB;IACrD,OAAO,CAAC,2BAA2B,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,UAAU,KAAK,GAAG,CAAC;AACrF,CAAC;AAED,MAAM,UAAU,qCAAqC,CACnD,QAA8B;IAE9B,uCACK,QAAQ,KACX,IAAI,EAAE,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,IACjD;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LroResponse, LroStatus, RawResponse } from \"./models\";\nimport { isUnexpectedPollingResponse } from \"./requestUtils\";\n\nfunction isLocationPollingDone(rawResponse: RawResponse): boolean {\n  return !isUnexpectedPollingResponse(rawResponse) && rawResponse.statusCode !== 202;\n}\n\nexport function processLocationPollingOperationResult<TResult>(\n  response: LroResponse<TResult>\n): LroStatus<TResult> {\n  return {\n    ...response,\n    done: isLocationPollingDone(response.rawResponse)\n  };\n}\n"]}