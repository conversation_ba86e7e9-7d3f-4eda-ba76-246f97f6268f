// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
import { failureStates, successStates } from "./models";
import { isUnexpectedPollingResponse } from "./requestUtils";
function getProvisioningState(rawResponse) {
    var _a, _b;
    const { properties, provisioningState } = (_a = rawResponse.body) !== null && _a !== void 0 ? _a : {};
    const state = (_b = properties === null || properties === void 0 ? void 0 : properties.provisioningState) !== null && _b !== void 0 ? _b : provisioningState;
    return typeof state === "string" ? state.toLowerCase() : "succeeded";
}
export function isBodyPollingDone(rawResponse) {
    const state = getProvisioningState(rawResponse);
    if (isUnexpectedPollingResponse(rawResponse) || failureStates.includes(state)) {
        throw new Error(`The long running operation has failed. The provisioning state: ${state}.`);
    }
    return successStates.includes(state);
}
/**
 * Creates a polling strategy based on BodyPolling which uses the provisioning state
 * from the result to determine the current operation state
 */
export function processBodyPollingOperationResult(response) {
    return Object.assign(Object.assign({}, response), { done: isBodyPollingDone(response.rawResponse) });
}
//# sourceMappingURL=bodyPolling.js.map