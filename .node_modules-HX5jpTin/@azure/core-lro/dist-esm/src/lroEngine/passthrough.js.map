{"version": 3, "file": "passthrough.js", "sourceRoot": "", "sources": ["../../../src/lroEngine/passthrough.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,MAAM,UAAU,iCAAiC,CAC/C,QAA8B;IAE9B,uCACK,QAAQ,KACX,IAAI,EAAE,IAAI,IACV;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LroResponse, LroStatus } from \"./models\";\n\nexport function processPassthroughOperationResult<TResult>(\n  response: LroResponse<TResult>\n): LroStatus<TResult> {\n  return {\n    ...response,\n    done: true\n  };\n}\n"]}