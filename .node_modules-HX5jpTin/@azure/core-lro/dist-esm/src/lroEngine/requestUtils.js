// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
/**
 * Detects where the continuation token is and returns it. Notice that azure-asyncoperation
 * must be checked first before the other location headers because there are scenarios
 * where both azure-asyncoperation and location could be present in the same response but
 * azure-asyncoperation should be the one to use for polling.
 */
export function getPollingUrl(rawResponse, defaultPath) {
    var _a, _b, _c;
    return ((_c = (_b = (_a = getAzureAsyncOperation(rawResponse)) !== null && _a !== void 0 ? _a : getLocation(rawResponse)) !== null && _b !== void 0 ? _b : getOperationLocation(rawResponse)) !== null && _c !== void 0 ? _c : defaultPath);
}
function getLocation(rawResponse) {
    return rawResponse.headers["location"];
}
function getOperationLocation(rawResponse) {
    return rawResponse.headers["operation-location"];
}
function getAzureAsyncOperation(rawResponse) {
    return rawResponse.headers["azure-asyncoperation"];
}
export function inferLroMode(requestPath, requestMethod, rawResponse) {
    if (getAzureAsyncOperation(rawResponse) !== undefined) {
        return {
            mode: "AzureAsync",
            resourceLocation: requestMethod === "PUT"
                ? requestPath
                : requestMethod === "POST"
                    ? getLocation(rawResponse)
                    : undefined
        };
    }
    else if (getLocation(rawResponse) !== undefined ||
        getOperationLocation(rawResponse) !== undefined) {
        return {
            mode: "Location"
        };
    }
    else if (["PUT", "PATCH"].includes(requestMethod)) {
        return {
            mode: "Body"
        };
    }
    return {};
}
class SimpleRestError extends Error {
    constructor(message, statusCode) {
        super(message);
        this.name = "RestError";
        this.statusCode = statusCode;
        Object.setPrototypeOf(this, SimpleRestError.prototype);
    }
}
export function isUnexpectedInitialResponse(rawResponse) {
    const code = rawResponse.statusCode;
    if (![203, 204, 202, 201, 200, 500].includes(code)) {
        throw new SimpleRestError(`Received unexpected HTTP status code ${code} in the initial response. This may indicate a server issue.`, code);
    }
    return false;
}
export function isUnexpectedPollingResponse(rawResponse) {
    const code = rawResponse.statusCode;
    if (![202, 201, 200, 500].includes(code)) {
        throw new SimpleRestError(`Received unexpected HTTP status code ${code} while polling. This may indicate a server issue.`, code);
    }
    return false;
}
//# sourceMappingURL=requestUtils.js.map