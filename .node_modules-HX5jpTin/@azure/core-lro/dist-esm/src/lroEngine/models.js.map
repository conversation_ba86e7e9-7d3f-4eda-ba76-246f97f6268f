{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../src/lroEngine/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAkClC,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,WAAW,CAAC,CAAC;AAC3C,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;AACjE;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PollOperationState } from \"../pollOperation\";\n\n/**\n * Options for the LRO poller.\n */\nexport interface LroEngineOptions<TResult, TState> {\n  /**\n   * Defines how much time the poller is going to wait before making a new request to the service.\n   */\n  intervalInMs?: number;\n  /**\n   * A serialized poller which can be used to resume an existing paused Long-Running-Operation.\n   */\n  resumeFrom?: string;\n  /**\n   * The potential location of the result of the LRO if specified by the LRO extension in the swagger.\n   */\n  lroResourceLocationConfig?: LroResourceLocationConfig;\n  /**\n   * A function to process the result of the LRO.\n   */\n  processResult?: (result: unknown, state: TState) => TResult;\n  /**\n   * A function to process the state of the LRO.\n   */\n  updateState?: (state: TState, lastResponse: RawResponse) => void;\n  /**\n   * A predicate to determine whether the LRO finished processing.\n   */\n  isDone?: (lastResponse: unknown, state: TState) => boolean;\n}\n\nexport const successStates = [\"succeeded\"];\nexport const failureStates = [\"failed\", \"canceled\", \"cancelled\"];\n/**\n * The LRO states that signal that the LRO has completed.\n */\nexport const terminalStates = successStates.concat(failureStates);\n\n/**\n * The potential location of the result of the LRO if specified by the LRO extension in the swagger.\n */\nexport type LroResourceLocationConfig = \"azure-async-operation\" | \"location\" | \"original-uri\";\n\n/**\n * The type of a LRO response body. This is just a convenience type for checking the status of the operation.\n */\n\nexport interface LroBody extends Record<string, unknown> {\n  /** The status of the operation. */\n  status?: string;\n  /** The state of the provisioning process */\n  provisioningState?: string;\n  /** The properties of the provisioning process */\n  properties?: { provisioningState?: string } & Record<string, unknown>;\n}\n\n/**\n * Simple type of the raw response.\n */\nexport interface RawResponse {\n  /** The HTTP status code */\n  statusCode: number;\n  /** A HttpHeaders collection in the response represented as a simple JSON object where all header names have been normalized to be lower-case. */\n  headers: {\n    [headerName: string]: string;\n  };\n  /** The parsed response body */\n  body?: unknown;\n}\n\n/**\n * The type of the response of a LRO.\n */\nexport interface LroResponse<T> {\n  /** The flattened response */\n  flatResponse: T;\n  /** The raw response */\n  rawResponse: RawResponse;\n}\n\n/** The type of which LRO implementation being followed by a specific API. */\nexport type LroMode = \"AzureAsync\" | \"Location\" | \"Body\";\n\n/**\n * The configuration of a LRO to determine how to perform polling and checking whether the operation has completed.\n */\nexport interface LroConfig {\n  /** The LRO mode */\n  mode?: LroMode;\n  /** The path of a provisioned resource */\n  resourceLocation?: string;\n}\n\n/**\n * Type of a polling operation state that can actually be resumed.\n */\nexport type ResumablePollOperationState<T> = PollOperationState<T> & {\n  initialRawResponse?: RawResponse;\n  config?: LroConfig;\n  pollingURL?: string;\n};\n\nexport interface PollerConfig {\n  intervalInMs: number;\n}\n\n/**\n * The type of a terminal state of an LRO.\n */\nexport interface LroTerminalState<T> extends LroResponse<T> {\n  /**\n   * Whether the operation has finished.\n   */\n  done: true;\n}\n\n/**\n * The type of an in-progress state of an LRO.\n */\nexport interface LroInProgressState<T> extends LroResponse<T> {\n  /**\n   * Whether the operation has finished.\n   */\n  done: false;\n  /**\n   * The request to be sent next if it is different from the standard polling one.\n   * Notice that it will disregard any polling URLs provided to it.\n   */\n  next?: () => Promise<LroStatus<T>>;\n}\n\n/**\n * The type of an LRO state which is a tagged union of terminal and in-progress states.\n */\nexport type LroStatus<T> = LroTerminalState<T> | LroInProgressState<T>;\n\n/**\n * The type of the getLROStatusFromResponse method. It takes the response as input and returns along the response whether the operation has finished.\n */\nexport type GetLroStatusFromResponse<T> = (response: LroResponse<T>) => LroStatus<T>;\n\n/**\n * Description of a long running operation.\n */\nexport interface LongRunningOperation<T> {\n  /**\n   * The request path.\n   */\n  requestPath: string;\n  /**\n   * The HTTP request method.\n   */\n  requestMethod: string;\n  /**\n   * A function that can be used to send initial request to the service.\n   */\n  sendInitialRequest: () => Promise<LroResponse<T>>;\n  /**\n   * A function that can be used to poll for the current status of a long running operation.\n   */\n  sendPollRequest: (path: string) => Promise<LroResponse<T>>;\n}\n"]}