{"version": 3, "file": "lroEngine.js", "sourceRoot": "", "sources": ["../../../src/lroEngine/lroEngine.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAQnC,OAAO,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAC;AAEnD,SAAS,gBAAgB,CACvB,eAAuB;IAEvB,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC;KAC1C;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,2CAA2C,eAAe,EAAE,CAAC,CAAC;KAC/E;AACH,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,SAA+D,SAAQ,MAGnF;IAGC,YAAY,GAAkC,EAAE,OAA2C;QACzF,MAAM,EAAE,YAAY,GAAG,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;QAC1D,MAAM,KAAK,GAAkD,UAAU;YACrE,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC;YAC9B,CAAC,CAAE,EAAoD,CAAC;QAE1D,MAAM,SAAS,GAAG,IAAI,oBAAoB,CACxC,KAAK,EACL,GAAG,EACH,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,yBAAyB,EAClC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,EACtB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EACpB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAChB,CAAC;QACF,KAAK,CAAC,SAAS,CAAC,CAAC;QAEjB,IAAI,CAAC,MAAM,GAAG,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;QAC7C,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;IACzF,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Poller } from \"../poller\";\nimport { PollOperationState } from \"../pollOperation\";\nimport {\n  LongRunningOperation,\n  LroEngineOptions,\n  PollerConfig,\n  ResumablePollOperationState\n} from \"./models\";\nimport { GenericPollOperation } from \"./operation\";\n\nfunction deserializeState<TResult, TState>(\n  serializedState: string\n): TState & ResumablePollOperationState<TResult> {\n  try {\n    return JSON.parse(serializedState).state;\n  } catch (e) {\n    throw new Error(`LroEngine: Unable to deserialize state: ${serializedState}`);\n  }\n}\n\n/**\n * The LRO Engine, a class that performs polling.\n */\nexport class LroEngine<TResult, TState extends PollOperationState<TResult>> extends Poller<\n  TState,\n  TResult\n> {\n  private config: PollerConfig;\n\n  constructor(lro: LongRunningOperation<TResult>, options?: LroEngineOptions<TResult, TState>) {\n    const { intervalInMs = 2000, resumeFrom } = options || {};\n    const state: TState & ResumablePollOperationState<TResult> = resumeFrom\n      ? deserializeState(resumeFrom)\n      : ({} as TState & ResumablePollOperationState<TResult>);\n\n    const operation = new GenericPollOperation(\n      state,\n      lro,\n      options?.lroResourceLocationConfig,\n      options?.processResult,\n      options?.updateState,\n      options?.isDone\n    );\n    super(operation);\n\n    this.config = { intervalInMs: intervalInMs };\n    operation.setPollerConfig(this.config);\n  }\n\n  /**\n   * The method used by the poller to wait before attempting to update its operation.\n   */\n  delay(): Promise<void> {\n    return new Promise((resolve) => setTimeout(() => resolve(), this.config.intervalInMs));\n  }\n}\n"]}