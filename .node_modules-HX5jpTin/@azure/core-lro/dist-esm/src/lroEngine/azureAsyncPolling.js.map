{"version": 3, "file": "azureAsyncPolling.js", "sourceRoot": "", "sources": ["../../../src/lroEngine/azureAsyncPolling.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,aAAa,EAMb,aAAa,EAEd,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,2BAA2B,EAAE,MAAM,gBAAgB,CAAC;AAE7D,SAAS,iBAAiB,CAAC,WAAwB;;IACjD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAC,WAAW,CAAC,IAAgB,mCAAI,EAAE,CAAC;IACvD,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;AACzE,CAAC;AAED,SAAS,uBAAuB,CAAC,WAAwB;IACvD,MAAM,KAAK,GAAG,iBAAiB,CAAC,WAAW,CAAC,CAAC;IAC7C,IAAI,2BAA2B,CAAC,WAAW,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7E,MAAM,IAAI,KAAK,CAAC,kEAAkE,KAAK,GAAG,CAAC,CAAC;KAC7F;IACD,OAAO,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,gBAAgB,CAC7B,GAAkC,EAClC,gBAAwB,EACxB,yBAAqD;IAErD,QAAQ,yBAAyB,EAAE;QACjC,KAAK,cAAc;YACjB,OAAO,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC9C,KAAK,uBAAuB;YAC1B,OAAO,SAAS,CAAC;QACnB,KAAK,UAAU,CAAC;QAChB;YACE,OAAO,GAAG,CAAC,eAAe,CAAC,gBAAgB,aAAhB,gBAAgB,cAAhB,gBAAgB,GAAI,GAAG,CAAC,WAAW,CAAC,CAAC;KACnE;AACH,CAAC;AAED,MAAM,UAAU,gCAAgC,CAC9C,GAAkC,EAClC,gBAAyB,EACzB,yBAAqD;IAErD,OAAO,CAAC,QAA8B,EAAsB,EAAE;QAC5D,IAAI,uBAAuB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YACjD,IAAI,gBAAgB,KAAK,SAAS,EAAE;gBAClC,uCAAY,QAAQ,KAAE,IAAI,EAAE,IAAI,IAAG;aACpC;iBAAM;gBACL,uCACK,QAAQ,KACX,IAAI,EAAE,KAAK,EACX,IAAI,EAAE,KAAK,IAAI,EAAE;wBACf,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAC1C,GAAG,EACH,gBAAgB,EAChB,yBAAyB,CAC1B,CAAC;wBACF,uCACK,CAAC,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,QAAQ,CAAC,KAC9B,IAAI,EAAE,IAAI,IACV;oBACJ,CAAC,IACD;aACH;SACF;QACD,uCACK,QAAQ,KACX,IAAI,EAAE,KAAK,IACX;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  failureStates,\n  LroResourceLocationConfig,\n  LongRunningOperation,\n  LroBody,\n  LroResponse,\n  LroStatus,\n  successStates,\n  RawResponse\n} from \"./models\";\nimport { isUnexpectedPollingResponse } from \"./requestUtils\";\n\nfunction getResponseStatus(rawResponse: RawResponse): string {\n  const { status } = (rawResponse.body as LroBody) ?? {};\n  return typeof status === \"string\" ? status.toLowerCase() : \"succeeded\";\n}\n\nfunction isAzureAsyncPollingDone(rawResponse: RawResponse): boolean {\n  const state = getResponseStatus(rawResponse);\n  if (isUnexpectedPollingResponse(rawResponse) || failureStates.includes(state)) {\n    throw new Error(`The long running operation has failed. The provisioning state: ${state}.`);\n  }\n  return successStates.includes(state);\n}\n\n/**\n * Sends a request to the URI of the provisioned resource if needed.\n */\nasync function sendFinalRequest<TResult>(\n  lro: LongRunningOperation<TResult>,\n  resourceLocation: string,\n  lroResourceLocationConfig?: LroResourceLocationConfig\n): Promise<LroResponse<TResult> | undefined> {\n  switch (lroResourceLocationConfig) {\n    case \"original-uri\":\n      return lro.sendPollRequest(lro.requestPath);\n    case \"azure-async-operation\":\n      return undefined;\n    case \"location\":\n    default:\n      return lro.sendPollRequest(resourceLocation ?? lro.requestPath);\n  }\n}\n\nexport function processAzureAsyncOperationResult<TResult>(\n  lro: LongRunningOperation<TResult>,\n  resourceLocation?: string,\n  lroResourceLocationConfig?: LroResourceLocationConfig\n): (response: LroResponse<TResult>) => LroStatus<TResult> {\n  return (response: LroResponse<TResult>): LroStatus<TResult> => {\n    if (isAzureAsyncPollingDone(response.rawResponse)) {\n      if (resourceLocation === undefined) {\n        return { ...response, done: true };\n      } else {\n        return {\n          ...response,\n          done: false,\n          next: async () => {\n            const finalResponse = await sendFinalRequest(\n              lro,\n              resourceLocation,\n              lroResourceLocationConfig\n            );\n            return {\n              ...(finalResponse ?? response),\n              done: true\n            };\n          }\n        };\n      }\n    }\n    return {\n      ...response,\n      done: false\n    };\n  };\n}\n"]}