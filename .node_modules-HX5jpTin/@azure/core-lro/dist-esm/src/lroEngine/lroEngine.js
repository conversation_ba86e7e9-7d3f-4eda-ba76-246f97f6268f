// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
import { Poller } from "../poller";
import { GenericPollOperation } from "./operation";
function deserializeState(serializedState) {
    try {
        return JSON.parse(serializedState).state;
    }
    catch (e) {
        throw new Error(`LroEngine: Unable to deserialize state: ${serializedState}`);
    }
}
/**
 * The LRO Engine, a class that performs polling.
 */
export class LroEngine extends Poller {
    constructor(lro, options) {
        const { intervalInMs = 2000, resumeFrom } = options || {};
        const state = resumeFrom
            ? deserializeState(resumeFrom)
            : {};
        const operation = new GenericPollOperation(state, lro, options === null || options === void 0 ? void 0 : options.lroResourceLocationConfig, options === null || options === void 0 ? void 0 : options.processResult, options === null || options === void 0 ? void 0 : options.updateState, options === null || options === void 0 ? void 0 : options.isDone);
        super(operation);
        this.config = { intervalInMs: intervalInMs };
        operation.setPollerConfig(this.config);
    }
    /**
     * The method used by the poller to wait before attempting to update its operation.
     */
    delay() {
        return new Promise((resolve) => setTimeout(() => resolve(), this.config.intervalInMs));
    }
}
//# sourceMappingURL=lroEngine.js.map