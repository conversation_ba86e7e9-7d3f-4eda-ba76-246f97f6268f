{"version": 3, "file": "index.js", "sources": ["../src/poller.ts", "../src/lroEngine/logger.ts", "../src/lroEngine/requestUtils.ts", "../src/lroEngine/models.ts", "../src/lroEngine/azureAsyncPolling.ts", "../src/lroEngine/bodyPolling.ts", "../src/lroEngine/locationPolling.ts", "../src/lroEngine/passthrough.ts", "../src/lroEngine/stateMachine.ts", "../src/lroEngine/operation.ts", "../src/lroEngine/lroEngine.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PollOperation, PollOperationState } from \"./pollOperation\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\n\n/**\n * CancelOnProgress is used as the return value of a Poller's onProgress method.\n * When a user invokes onProgress, they're required to pass in a function that will be\n * called as a callback with the new data received each time the poll operation is updated.\n * onProgress returns a function that will prevent any further update to reach the original callback.\n */\nexport type CancelOnProgress = () => void;\n\n/**\n * PollProgressCallback<TState> is the type of the callback functions sent to onProgress.\n * These functions will receive a TState that is defined by your implementation of\n * the Poller class.\n */\nexport type PollProgressCallback<TState> = (state: TState) => void;\n\n/**\n * When a poller is manually stopped through the `stopPolling` method,\n * the poller will be rejected with an instance of the PollerStoppedError.\n */\nexport class PollerStoppedError extends Error {\n  constructor(message: string) {\n    super(message);\n    this.name = \"PollerStoppedError\";\n    Object.setPrototypeOf(this, PollerStoppedError.prototype);\n  }\n}\n\n/**\n * When a poller is cancelled through the `cancelOperation` method,\n * the poller will be rejected with an instance of the PollerCancelledError.\n */\nexport class PollerCancelledError extends Error {\n  constructor(message: string) {\n    super(message);\n    this.name = \"PollerCancelledError\";\n    Object.setPrototypeOf(this, PollerCancelledError.prototype);\n  }\n}\n\n/**\n * Abstract representation of a poller, intended to expose just the minimal API that the user needs to work with.\n */\n// eslint-disable-next-line no-use-before-define\nexport interface PollerLike<TState extends PollOperationState<TResult>, TResult> {\n  /**\n   * Returns a promise that will resolve once a single polling request finishes.\n   * It does this by calling the update method of the Poller's operation.\n   */\n  poll(options?: { abortSignal?: AbortSignalLike }): Promise<void>;\n  /**\n   * Returns a promise that will resolve once the underlying operation is completed.\n   */\n  pollUntilDone(): Promise<TResult>;\n  /**\n   * Invokes the provided callback after each polling is completed,\n   * sending the current state of the poller's operation.\n   *\n   * It returns a method that can be used to stop receiving updates on the given callback function.\n   */\n  onProgress(callback: (state: TState) => void): CancelOnProgress;\n  /**\n   * Returns true if the poller has finished polling.\n   */\n  isDone(): boolean;\n  /**\n   * Stops the poller. After this, no manual or automated requests can be sent.\n   */\n  stopPolling(): void;\n  /**\n   * Returns true if the poller is stopped.\n   */\n  isStopped(): boolean;\n  /**\n   * Attempts to cancel the underlying operation.\n   */\n  cancelOperation(options?: { abortSignal?: AbortSignalLike }): Promise<void>;\n  /**\n   * Returns the state of the operation.\n   * The TState defined in PollerLike can be a subset of the TState defined in\n   * the Poller implementation.\n   */\n  getOperationState(): TState;\n  /**\n   * Returns the result value of the operation,\n   * regardless of the state of the poller.\n   * It can return undefined or an incomplete form of the final TResult value\n   * depending on the implementation.\n   */\n  getResult(): TResult | undefined;\n  /**\n   * Returns a serialized version of the poller's operation\n   * by invoking the operation's toString method.\n   */\n  toString(): string;\n}\n\n/**\n * A class that represents the definition of a program that polls through consecutive requests\n * until it reaches a state of completion.\n *\n * A poller can be executed manually, by polling request by request by calling to the `poll()` method repeatedly, until its operation is completed.\n * It also provides a way to wait until the operation completes, by calling `pollUntilDone()` and waiting until the operation finishes.\n * Pollers can also request the cancellation of the ongoing process to whom is providing the underlying long running operation.\n *\n * ```ts\n * const poller = new MyPoller();\n *\n * // Polling just once:\n * await poller.poll();\n *\n * // We can try to cancel the request here, by calling:\n * //\n * //     await poller.cancelOperation();\n * //\n *\n * // Getting the final result:\n * const result = await poller.pollUntilDone();\n * ```\n *\n * The Poller is defined by two types, a type representing the state of the poller, which\n * must include a basic set of properties from `PollOperationState<TResult>`,\n * and a return type defined by `TResult`, which can be anything.\n *\n * The Poller class implements the `PollerLike` interface, which allows poller implementations to avoid having\n * to export the Poller's class directly, and instead only export the already instantiated poller with the PollerLike type.\n *\n * ```ts\n * class Client {\n *   public async makePoller: PollerLike<MyOperationState, MyResult> {\n *     const poller = new MyPoller({});\n *     // It might be preferred to return the poller after the first request is made,\n *     // so that some information can be obtained right away.\n *     await poller.poll();\n *     return poller;\n *   }\n * }\n *\n * const poller: PollerLike<MyOperationState, MyResult> = myClient.makePoller();\n * ```\n *\n * A poller can be created through its constructor, then it can be polled until it's completed.\n * At any point in time, the state of the poller can be obtained without delay through the getOperationState method.\n * At any point in time, the intermediate forms of the result type can be requested without delay.\n * Once the underlying operation is marked as completed, the poller will stop and the final value will be returned.\n *\n * ```ts\n * const poller = myClient.makePoller();\n * const state: MyOperationState = poller.getOperationState();\n *\n * // The intermediate result can be obtained at any time.\n * const result: MyResult | undefined = poller.getResult();\n *\n * // The final result can only be obtained after the poller finishes.\n * const result: MyResult = await poller.pollUntilDone();\n * ```\n *\n */\n// eslint-disable-next-line no-use-before-define\nexport abstract class Poller<TState extends PollOperationState<TResult>, TResult>\n  implements PollerLike<TState, TResult> {\n  private stopped: boolean = true;\n  private resolve?: (value: TResult) => void;\n  private reject?: (error: PollerStoppedError | PollerCancelledError | Error) => void;\n  private pollOncePromise?: Promise<void>;\n  private cancelPromise?: Promise<void>;\n  private promise: Promise<TResult>;\n  private pollProgressCallbacks: PollProgressCallback<TState>[] = [];\n\n  /**\n   * The poller's operation is available in full to any of the methods of the Poller class\n   * and any class extending the Poller class.\n   */\n  protected operation: PollOperation<TState, TResult>;\n\n  /**\n   * A poller needs to be initialized by passing in at least the basic properties of the `PollOperation<TState, TResult>`.\n   *\n   * When writing an implementation of a Poller, this implementation needs to deal with the initialization\n   * of any custom state beyond the basic definition of the poller. The basic poller assumes that the poller's\n   * operation has already been defined, at least its basic properties. The code below shows how to approach\n   * the definition of the constructor of a new custom poller.\n   *\n   * ```ts\n   * export class MyPoller extends Poller<MyOperationState, string> {\n   *   constructor({\n   *     // Anything you might need outside of the basics\n   *   }) {\n   *     let state: MyOperationState = {\n   *       privateProperty: private,\n   *       publicProperty: public,\n   *     };\n   *\n   *     const operation = {\n   *       state,\n   *       update,\n   *       cancel,\n   *       toString\n   *     }\n   *\n   *     // Sending the operation to the parent's constructor.\n   *     super(operation);\n   *\n   *     // You can assign more local properties here.\n   *   }\n   * }\n   * ```\n   *\n   * Inside of this constructor, a new promise is created. This will be used to\n   * tell the user when the poller finishes (see `pollUntilDone()`). The promise's\n   * resolve and reject methods are also used internally to control when to resolve\n   * or reject anyone waiting for the poller to finish.\n   *\n   * The constructor of a custom implementation of a poller is where any serialized version of\n   * a previous poller's operation should be deserialized into the operation sent to the\n   * base constructor. For example:\n   *\n   * ```ts\n   * export class MyPoller extends Poller<MyOperationState, string> {\n   *   constructor(\n   *     baseOperation: string | undefined\n   *   ) {\n   *     let state: MyOperationState = {};\n   *     if (baseOperation) {\n   *       state = {\n   *         ...JSON.parse(baseOperation).state,\n   *         ...state\n   *       };\n   *     }\n   *     const operation = {\n   *       state,\n   *       // ...\n   *     }\n   *     super(operation);\n   *   }\n   * }\n   * ```\n   *\n   * @param operation - Must contain the basic properties of `PollOperation<State, TResult>`.\n   */\n  constructor(operation: PollOperation<TState, TResult>) {\n    this.operation = operation;\n    this.promise = new Promise<TResult>(\n      (\n        resolve: (result: TResult) => void,\n        reject: (error: PollerStoppedError | PollerCancelledError | Error) => void\n      ) => {\n        this.resolve = resolve;\n        this.reject = reject;\n      }\n    );\n    // This prevents the UnhandledPromiseRejectionWarning in node.js from being thrown.\n    // The above warning would get thrown if `poller.poll` is called, it returns an error,\n    // and pullUntilDone did not have a .catch or await try/catch on it's return value.\n    this.promise.catch(() => {\n      /* intentionally blank */\n    });\n  }\n\n  /**\n   * Defines how much to wait between each poll request.\n   * This has to be implemented by your custom poller.\n   *\n   * \\@azure/core-http has a simple implementation of a delay function that waits as many milliseconds as specified.\n   * This can be used as follows:\n   *\n   * ```ts\n   * import { delay } from \"@azure/core-http\";\n   *\n   * export class MyPoller extends Poller<MyOperationState, string> {\n   *   // The other necessary definitions.\n   *\n   *   async delay(): Promise<void> {\n   *     const milliseconds = 1000;\n   *     return delay(milliseconds);\n   *   }\n   * }\n   * ```\n   *\n   */\n  protected abstract delay(): Promise<void>;\n\n  /**\n   * @internal\n   * Starts a loop that will break only if the poller is done\n   * or if the poller is stopped.\n   */\n  private async startPolling(): Promise<void> {\n    if (this.stopped) {\n      this.stopped = false;\n    }\n    while (!this.isStopped() && !this.isDone()) {\n      await this.poll();\n      await this.delay();\n    }\n  }\n\n  /**\n   * @internal\n   * pollOnce does one polling, by calling to the update method of the underlying\n   * poll operation to make any relevant change effective.\n   *\n   * It only optionally receives an object with an abortSignal property, from \\@azure/abort-controller's AbortSignalLike.\n   *\n   * @param options - Optional properties passed to the operation's update method.\n   */\n  private async pollOnce(options: { abortSignal?: AbortSignalLike } = {}): Promise<void> {\n    try {\n      if (!this.isDone()) {\n        this.operation = await this.operation.update({\n          abortSignal: options.abortSignal,\n          fireProgress: this.fireProgress.bind(this)\n        });\n        if (this.isDone() && this.resolve) {\n          // If the poller has finished polling, this means we now have a result.\n          // However, it can be the case that TResult is instantiated to void, so\n          // we are not expecting a result anyway. To assert that we might not\n          // have a result eventually after finishing polling, we cast the result\n          // to TResult.\n          this.resolve(this.operation.state.result as TResult);\n        }\n      }\n    } catch (e) {\n      this.operation.state.error = e;\n      if (this.reject) {\n        this.reject(e);\n      }\n      throw e;\n    }\n  }\n\n  /**\n   * @internal\n   * fireProgress calls the functions passed in via onProgress the method of the poller.\n   *\n   * It loops over all of the callbacks received from onProgress, and executes them, sending them\n   * the current operation state.\n   *\n   * @param state - The current operation state.\n   */\n  private fireProgress(state: TState): void {\n    for (const callback of this.pollProgressCallbacks) {\n      callback(state);\n    }\n  }\n\n  /**\n   * @internal\n   * Invokes the underlying operation's cancel method, and rejects the\n   * pollUntilDone promise.\n   */\n  private async cancelOnce(options: { abortSignal?: AbortSignalLike } = {}): Promise<void> {\n    this.operation = await this.operation.cancel(options);\n    if (this.reject) {\n      this.reject(new PollerCancelledError(\"Poller cancelled\"));\n    }\n  }\n\n  /**\n   * Returns a promise that will resolve once a single polling request finishes.\n   * It does this by calling the update method of the Poller's operation.\n   *\n   * It only optionally receives an object with an abortSignal property, from \\@azure/abort-controller's AbortSignalLike.\n   *\n   * @param options - Optional properties passed to the operation's update method.\n   */\n  public poll(options: { abortSignal?: AbortSignalLike } = {}): Promise<void> {\n    if (!this.pollOncePromise) {\n      this.pollOncePromise = this.pollOnce(options);\n      const clearPollOncePromise = (): void => {\n        this.pollOncePromise = undefined;\n      };\n      this.pollOncePromise.then(clearPollOncePromise, clearPollOncePromise).catch(this.reject);\n    }\n    return this.pollOncePromise;\n  }\n\n  /**\n   * Returns a promise that will resolve once the underlying operation is completed.\n   */\n  public async pollUntilDone(): Promise<TResult> {\n    if (this.stopped) {\n      this.startPolling().catch(this.reject);\n    }\n    return this.promise;\n  }\n\n  /**\n   * Invokes the provided callback after each polling is completed,\n   * sending the current state of the poller's operation.\n   *\n   * It returns a method that can be used to stop receiving updates on the given callback function.\n   */\n  public onProgress(callback: (state: TState) => void): CancelOnProgress {\n    this.pollProgressCallbacks.push(callback);\n    return (): void => {\n      this.pollProgressCallbacks = this.pollProgressCallbacks.filter((c) => c !== callback);\n    };\n  }\n\n  /**\n   * Returns true if the poller has finished polling.\n   */\n  public isDone(): boolean {\n    const state: PollOperationState<TResult> = this.operation.state;\n    return Boolean(state.isCompleted || state.isCancelled || state.error);\n  }\n\n  /**\n   * Stops the poller from continuing to poll.\n   */\n  public stopPolling(): void {\n    if (!this.stopped) {\n      this.stopped = true;\n      if (this.reject) {\n        this.reject(new PollerStoppedError(\"This poller is already stopped\"));\n      }\n    }\n  }\n\n  /**\n   * Returns true if the poller is stopped.\n   */\n  public isStopped(): boolean {\n    return this.stopped;\n  }\n\n  /**\n   * Attempts to cancel the underlying operation.\n   *\n   * It only optionally receives an object with an abortSignal property, from \\@azure/abort-controller's AbortSignalLike.\n   *\n   * If it's called again before it finishes, it will throw an error.\n   *\n   * @param options - Optional properties passed to the operation's update method.\n   */\n  public cancelOperation(options: { abortSignal?: AbortSignalLike } = {}): Promise<void> {\n    if (!this.stopped) {\n      this.stopped = true;\n    }\n    if (!this.cancelPromise) {\n      this.cancelPromise = this.cancelOnce(options);\n    } else if (options.abortSignal) {\n      throw new Error(\"A cancel request is currently pending\");\n    }\n    return this.cancelPromise;\n  }\n\n  /**\n   * Returns the state of the operation.\n   *\n   * Even though TState will be the same type inside any of the methods of any extension of the Poller class,\n   * implementations of the pollers can customize what's shared with the public by writing their own\n   * version of the `getOperationState` method, and by defining two types, one representing the internal state of the poller\n   * and a public type representing a safe to share subset of the properties of the internal state.\n   * Their definition of getOperationState can then return their public type.\n   *\n   * Example:\n   *\n   * ```ts\n   * // Let's say we have our poller's operation state defined as:\n   * interface MyOperationState extends PollOperationState<ResultType> {\n   *   privateProperty?: string;\n   *   publicProperty?: string;\n   * }\n   *\n   * // To allow us to have a true separation of public and private state, we have to define another interface:\n   * interface PublicState extends PollOperationState<ResultType> {\n   *   publicProperty?: string;\n   * }\n   *\n   * // Then, we define our Poller as follows:\n   * export class MyPoller extends Poller<MyOperationState, ResultType> {\n   *   // ... More content is needed here ...\n   *\n   *   public getOperationState(): PublicState {\n   *     const state: PublicState = this.operation.state;\n   *     return {\n   *       // Properties from PollOperationState<TResult>\n   *       isStarted: state.isStarted,\n   *       isCompleted: state.isCompleted,\n   *       isCancelled: state.isCancelled,\n   *       error: state.error,\n   *       result: state.result,\n   *\n   *       // The only other property needed by PublicState.\n   *       publicProperty: state.publicProperty\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * You can see this in the tests of this repository, go to the file:\n   * `../test/utils/testPoller.ts`\n   * and look for the getOperationState implementation.\n   */\n  public getOperationState(): TState {\n    return this.operation.state;\n  }\n\n  /**\n   * Returns the result value of the operation,\n   * regardless of the state of the poller.\n   * It can return undefined or an incomplete form of the final TResult value\n   * depending on the implementation.\n   */\n  public getResult(): TResult | undefined {\n    const state: PollOperationState<TResult> = this.operation.state;\n    return state.result;\n  }\n\n  /**\n   * Returns a serialized version of the poller's operation\n   * by invoking the operation's toString method.\n   */\n  public toString(): string {\n    return this.operation.toString();\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { createClientLogger } from \"@azure/logger\";\n\n/**\n * The `@azure/logger` configuration for this package.\n * @internal\n */\nexport const logger = createClientLogger(\"core-lro\");\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LroConfig, RawResponse } from \"./models\";\n\n/**\n * Detects where the continuation token is and returns it. Notice that azure-asyncoperation\n * must be checked first before the other location headers because there are scenarios\n * where both azure-asyncoperation and location could be present in the same response but\n * azure-asyncoperation should be the one to use for polling.\n */\nexport function getPollingUrl(rawResponse: RawResponse, defaultPath: string): string {\n  return (\n    getAzureAsyncOperation(rawResponse) ??\n    getLocation(rawResponse) ??\n    getOperationLocation(rawResponse) ??\n    defaultPath\n  );\n}\n\nfunction getLocation(rawResponse: RawResponse): string | undefined {\n  return rawResponse.headers[\"location\"];\n}\n\nfunction getOperationLocation(rawResponse: RawResponse): string | undefined {\n  return rawResponse.headers[\"operation-location\"];\n}\n\nfunction getAzureAsyncOperation(rawResponse: RawResponse): string | undefined {\n  return rawResponse.headers[\"azure-asyncoperation\"];\n}\n\nexport function inferLroMode(\n  requestPath: string,\n  requestMethod: string,\n  rawResponse: RawResponse\n): LroConfig {\n  if (getAzureAsyncOperation(rawResponse) !== undefined) {\n    return {\n      mode: \"AzureAsync\",\n      resourceLocation:\n        requestMethod === \"PUT\"\n          ? requestPath\n          : requestMethod === \"POST\"\n          ? getLocation(rawResponse)\n          : undefined\n    };\n  } else if (\n    getLocation(rawResponse) !== undefined ||\n    getOperationLocation(rawResponse) !== undefined\n  ) {\n    return {\n      mode: \"Location\"\n    };\n  } else if ([\"PUT\", \"PATCH\"].includes(requestMethod)) {\n    return {\n      mode: \"Body\"\n    };\n  }\n  return {};\n}\n\nclass SimpleRestError extends Error {\n  public statusCode?: number;\n  constructor(message: string, statusCode: number) {\n    super(message);\n    this.name = \"RestError\";\n    this.statusCode = statusCode;\n\n    Object.setPrototypeOf(this, SimpleRestError.prototype);\n  }\n}\n\nexport function isUnexpectedInitialResponse(rawResponse: RawResponse): boolean {\n  const code = rawResponse.statusCode;\n  if (![203, 204, 202, 201, 200, 500].includes(code)) {\n    throw new SimpleRestError(\n      `Received unexpected HTTP status code ${code} in the initial response. This may indicate a server issue.`,\n      code\n    );\n  }\n  return false;\n}\n\nexport function isUnexpectedPollingResponse(rawResponse: RawResponse): boolean {\n  const code = rawResponse.statusCode;\n  if (![202, 201, 200, 500].includes(code)) {\n    throw new SimpleRestError(\n      `Received unexpected HTTP status code ${code} while polling. This may indicate a server issue.`,\n      code\n    );\n  }\n  return false;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PollOperationState } from \"../pollOperation\";\n\n/**\n * Options for the LRO poller.\n */\nexport interface LroEngineOptions<TResult, TState> {\n  /**\n   * Defines how much time the poller is going to wait before making a new request to the service.\n   */\n  intervalInMs?: number;\n  /**\n   * A serialized poller which can be used to resume an existing paused Long-Running-Operation.\n   */\n  resumeFrom?: string;\n  /**\n   * The potential location of the result of the LRO if specified by the LRO extension in the swagger.\n   */\n  lroResourceLocationConfig?: LroResourceLocationConfig;\n  /**\n   * A function to process the result of the LRO.\n   */\n  processResult?: (result: unknown, state: TState) => TResult;\n  /**\n   * A function to process the state of the LRO.\n   */\n  updateState?: (state: TState, lastResponse: RawResponse) => void;\n  /**\n   * A predicate to determine whether the LRO finished processing.\n   */\n  isDone?: (lastResponse: unknown, state: TState) => boolean;\n}\n\nexport const successStates = [\"succeeded\"];\nexport const failureStates = [\"failed\", \"canceled\", \"cancelled\"];\n/**\n * The LRO states that signal that the LRO has completed.\n */\nexport const terminalStates = successStates.concat(failureStates);\n\n/**\n * The potential location of the result of the LRO if specified by the LRO extension in the swagger.\n */\nexport type LroResourceLocationConfig = \"azure-async-operation\" | \"location\" | \"original-uri\";\n\n/**\n * The type of a LRO response body. This is just a convenience type for checking the status of the operation.\n */\n\nexport interface LroBody extends Record<string, unknown> {\n  /** The status of the operation. */\n  status?: string;\n  /** The state of the provisioning process */\n  provisioningState?: string;\n  /** The properties of the provisioning process */\n  properties?: { provisioningState?: string } & Record<string, unknown>;\n}\n\n/**\n * Simple type of the raw response.\n */\nexport interface RawResponse {\n  /** The HTTP status code */\n  statusCode: number;\n  /** A HttpHeaders collection in the response represented as a simple JSON object where all header names have been normalized to be lower-case. */\n  headers: {\n    [headerName: string]: string;\n  };\n  /** The parsed response body */\n  body?: unknown;\n}\n\n/**\n * The type of the response of a LRO.\n */\nexport interface LroResponse<T> {\n  /** The flattened response */\n  flatResponse: T;\n  /** The raw response */\n  rawResponse: RawResponse;\n}\n\n/** The type of which LRO implementation being followed by a specific API. */\nexport type LroMode = \"AzureAsync\" | \"Location\" | \"Body\";\n\n/**\n * The configuration of a LRO to determine how to perform polling and checking whether the operation has completed.\n */\nexport interface LroConfig {\n  /** The LRO mode */\n  mode?: LroMode;\n  /** The path of a provisioned resource */\n  resourceLocation?: string;\n}\n\n/**\n * Type of a polling operation state that can actually be resumed.\n */\nexport type ResumablePollOperationState<T> = PollOperationState<T> & {\n  initialRawResponse?: RawResponse;\n  config?: LroConfig;\n  pollingURL?: string;\n};\n\nexport interface PollerConfig {\n  intervalInMs: number;\n}\n\n/**\n * The type of a terminal state of an LRO.\n */\nexport interface LroTerminalState<T> extends LroResponse<T> {\n  /**\n   * Whether the operation has finished.\n   */\n  done: true;\n}\n\n/**\n * The type of an in-progress state of an LRO.\n */\nexport interface LroInProgressState<T> extends LroResponse<T> {\n  /**\n   * Whether the operation has finished.\n   */\n  done: false;\n  /**\n   * The request to be sent next if it is different from the standard polling one.\n   * Notice that it will disregard any polling URLs provided to it.\n   */\n  next?: () => Promise<LroStatus<T>>;\n}\n\n/**\n * The type of an LRO state which is a tagged union of terminal and in-progress states.\n */\nexport type LroStatus<T> = LroTerminalState<T> | LroInProgressState<T>;\n\n/**\n * The type of the getLROStatusFromResponse method. It takes the response as input and returns along the response whether the operation has finished.\n */\nexport type GetLroStatusFromResponse<T> = (response: LroResponse<T>) => LroStatus<T>;\n\n/**\n * Description of a long running operation.\n */\nexport interface LongRunningOperation<T> {\n  /**\n   * The request path.\n   */\n  requestPath: string;\n  /**\n   * The HTTP request method.\n   */\n  requestMethod: string;\n  /**\n   * A function that can be used to send initial request to the service.\n   */\n  sendInitialRequest: () => Promise<LroResponse<T>>;\n  /**\n   * A function that can be used to poll for the current status of a long running operation.\n   */\n  sendPollRequest: (path: string) => Promise<LroResponse<T>>;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  failureStates,\n  LroResourceLocationConfig,\n  LongRunningOperation,\n  LroBody,\n  LroResponse,\n  LroStatus,\n  successStates,\n  RawResponse\n} from \"./models\";\nimport { isUnexpectedPollingResponse } from \"./requestUtils\";\n\nfunction getResponseStatus(rawResponse: RawResponse): string {\n  const { status } = (rawResponse.body as LroBody) ?? {};\n  return typeof status === \"string\" ? status.toLowerCase() : \"succeeded\";\n}\n\nfunction isAzureAsyncPollingDone(rawResponse: RawResponse): boolean {\n  const state = getResponseStatus(rawResponse);\n  if (isUnexpectedPollingResponse(rawResponse) || failureStates.includes(state)) {\n    throw new Error(`The long running operation has failed. The provisioning state: ${state}.`);\n  }\n  return successStates.includes(state);\n}\n\n/**\n * Sends a request to the URI of the provisioned resource if needed.\n */\nasync function sendFinalRequest<TResult>(\n  lro: LongRunningOperation<TResult>,\n  resourceLocation: string,\n  lroResourceLocationConfig?: LroResourceLocationConfig\n): Promise<LroResponse<TResult> | undefined> {\n  switch (lroResourceLocationConfig) {\n    case \"original-uri\":\n      return lro.sendPollRequest(lro.requestPath);\n    case \"azure-async-operation\":\n      return undefined;\n    case \"location\":\n    default:\n      return lro.sendPollRequest(resourceLocation ?? lro.requestPath);\n  }\n}\n\nexport function processAzureAsyncOperationResult<TResult>(\n  lro: LongRunningOperation<TResult>,\n  resourceLocation?: string,\n  lroResourceLocationConfig?: LroResourceLocationConfig\n): (response: LroResponse<TResult>) => LroStatus<TResult> {\n  return (response: LroResponse<TResult>): LroStatus<TResult> => {\n    if (isAzureAsyncPollingDone(response.rawResponse)) {\n      if (resourceLocation === undefined) {\n        return { ...response, done: true };\n      } else {\n        return {\n          ...response,\n          done: false,\n          next: async () => {\n            const finalResponse = await sendFinalRequest(\n              lro,\n              resourceLocation,\n              lroResourceLocationConfig\n            );\n            return {\n              ...(finalResponse ?? response),\n              done: true\n            };\n          }\n        };\n      }\n    }\n    return {\n      ...response,\n      done: false\n    };\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  failureStates,\n  LroBody,\n  LroResponse,\n  LroStatus,\n  RawResponse,\n  successStates\n} from \"./models\";\nimport { isUnexpectedPollingResponse } from \"./requestUtils\";\n\nfunction getProvisioningState(rawResponse: RawResponse): string {\n  const { properties, provisioningState } = (rawResponse.body as LroBody) ?? {};\n  const state: string | undefined = properties?.provisioningState ?? provisioningState;\n  return typeof state === \"string\" ? state.toLowerCase() : \"succeeded\";\n}\n\nexport function isBodyPollingDone(rawResponse: RawResponse): boolean {\n  const state = getProvisioningState(rawResponse);\n  if (isUnexpectedPollingResponse(rawResponse) || failureStates.includes(state)) {\n    throw new Error(`The long running operation has failed. The provisioning state: ${state}.`);\n  }\n  return successStates.includes(state);\n}\n\n/**\n * Creates a polling strategy based on BodyPolling which uses the provisioning state\n * from the result to determine the current operation state\n */\nexport function processBodyPollingOperationResult<TResult>(\n  response: LroResponse<TResult>\n): LroStatus<TResult> {\n  return {\n    ...response,\n    done: isBodyPollingDone(response.rawResponse)\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LroResponse, LroStatus, RawResponse } from \"./models\";\nimport { isUnexpectedPollingResponse } from \"./requestUtils\";\n\nfunction isLocationPollingDone(rawResponse: RawResponse): boolean {\n  return !isUnexpectedPollingResponse(rawResponse) && rawResponse.statusCode !== 202;\n}\n\nexport function processLocationPollingOperationResult<TResult>(\n  response: LroResponse<TResult>\n): LroStatus<TResult> {\n  return {\n    ...response,\n    done: isLocationPollingDone(response.rawResponse)\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LroResponse, LroStatus } from \"./models\";\n\nexport function processPassthroughOperationResult<TResult>(\n  response: LroResponse<TResult>\n): LroStatus<TResult> {\n  return {\n    ...response,\n    done: true\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { processAzureAsyncOperationResult } from \"./azureAsyncPolling\";\nimport { isBodyPollingDone, processBodyPollingOperationResult } from \"./bodyPolling\";\nimport { processLocationPollingOperationResult } from \"./locationPolling\";\nimport { logger } from \"./logger\";\nimport {\n  LroResourceLocationConfig,\n  GetLroStatusFromResponse,\n  LongRunningOperation,\n  LroConfig,\n  PollerConfig,\n  ResumablePollOperationState,\n  LroResponse,\n  LroStatus\n} from \"./models\";\nimport { processPassthroughOperationResult } from \"./passthrough\";\nimport { getPollingUrl, inferLroMode, isUnexpectedInitialResponse } from \"./requestUtils\";\n\n/**\n * creates a stepping function that maps an LRO state to another.\n */\nexport function createGetLroStatusFromResponse<TResult>(\n  lroPrimitives: LongRunningOperation<TResult>,\n  config: LroConfig,\n  lroResourceLocationConfig?: LroResourceLocationConfig\n): GetLroStatusFromResponse<TResult> {\n  switch (config.mode) {\n    case \"AzureAsync\": {\n      return processAzureAsyncOperationResult(\n        lroPrimitives,\n        config.resourceLocation,\n        lroResourceLocationConfig\n      );\n    }\n    case \"Location\": {\n      return processLocationPollingOperationResult;\n    }\n    case \"Body\": {\n      return processBodyPollingOperationResult;\n    }\n    default: {\n      return processPassthroughOperationResult;\n    }\n  }\n}\n\n/**\n * Creates a polling operation.\n */\nexport function createPoll<TResult>(\n  lroPrimitives: LongRunningOperation<TResult>\n): (\n  pollingURL: string,\n  pollerConfig: PollerConfig,\n  getLroStatusFromResponse: GetLroStatusFromResponse<TResult>\n) => Promise<LroStatus<TResult>> {\n  return async (\n    path: string,\n    pollerConfig: PollerConfig,\n    getLroStatusFromResponse: GetLroStatusFromResponse<TResult>\n  ): Promise<LroStatus<TResult>> => {\n    const response = await lroPrimitives.sendPollRequest(path);\n    const retryAfter: string | undefined = response.rawResponse.headers[\"retry-after\"];\n    if (retryAfter !== undefined) {\n      const retryAfterInMs = parseInt(retryAfter);\n      pollerConfig.intervalInMs = isNaN(retryAfterInMs)\n        ? calculatePollingIntervalFromDate(new Date(retryAfter), pollerConfig.intervalInMs)\n        : retryAfterInMs;\n    }\n    return getLroStatusFromResponse(response);\n  };\n}\n\nfunction calculatePollingIntervalFromDate(\n  retryAfterDate: Date,\n  defaultIntervalInMs: number\n): number {\n  const timeNow = Math.floor(new Date().getTime());\n  const retryAfterTime = retryAfterDate.getTime();\n  if (timeNow < retryAfterTime) {\n    return retryAfterTime - timeNow;\n  }\n  return defaultIntervalInMs;\n}\n\n/**\n * Creates a callback to be used to initialize the polling operation state.\n * @param state - of the polling operation\n * @param operationSpec - of the LRO\n * @param callback - callback to be called when the operation is done\n * @returns callback that initializes the state of the polling operation\n */\nexport function createInitializeState<TResult>(\n  state: ResumablePollOperationState<TResult>,\n  requestPath: string,\n  requestMethod: string\n): (response: LroResponse<TResult>) => boolean {\n  return (response: LroResponse<TResult>): boolean => {\n    if (isUnexpectedInitialResponse(response.rawResponse)) return true;\n    state.initialRawResponse = response.rawResponse;\n    state.isStarted = true;\n    state.pollingURL = getPollingUrl(state.initialRawResponse, requestPath);\n    state.config = inferLroMode(requestPath, requestMethod, state.initialRawResponse);\n    /** short circuit polling if body polling is done in the initial request */\n    if (\n      state.config.mode === undefined ||\n      (state.config.mode === \"Body\" && isBodyPollingDone(state.initialRawResponse))\n    ) {\n      state.result = response.flatResponse as TResult;\n      state.isCompleted = true;\n    }\n    logger.verbose(`LRO: initial state: ${JSON.stringify(state)}`);\n    return Boolean(state.isCompleted);\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { PollOperation, PollOperationState } from \"../pollOperation\";\nimport { logger } from \"./logger\";\nimport {\n  PollerConfig,\n  ResumablePollOperationState,\n  LongRunningOperation,\n  GetLroStatusFromResponse,\n  LroResourceLocationConfig,\n  LroStatus,\n  LroResponse,\n  RawResponse\n} from \"./models\";\nimport { getPollingUrl } from \"./requestUtils\";\nimport { createGetLroStatusFromResponse, createInitializeState, createPoll } from \"./stateMachine\";\n\nexport class GenericPollOperation<TResult, TState extends PollOperationState<TResult>>\n  implements PollOperation<TState, TResult> {\n  private poll?: (\n    pollingURL: string,\n    pollerConfig: PollerConfig,\n    getLroStatusFromResponse: GetLroStatusFromResponse<TResult>\n  ) => Promise<LroStatus<TResult>>;\n  private pollerConfig?: PollerConfig;\n  private getLroStatusFromResponse?: GetLroStatusFromResponse<TResult>;\n\n  constructor(\n    public state: TState & ResumablePollOperationState<TResult>,\n    private lro: LongRunningOperation<TResult>,\n    private lroResourceLocationConfig?: LroResourceLocationConfig,\n    private processResult?: (result: unknown, state: TState) => TResult,\n    private updateState?: (state: TState, lastResponse: RawResponse) => void,\n    private isDone?: (lastResponse: TResult, state: TState) => boolean\n  ) {}\n\n  public setPollerConfig(pollerConfig: PollerConfig): void {\n    this.pollerConfig = pollerConfig;\n  }\n\n  /**\n   * General update function for LROPoller, the general process is as follows\n   * 1. Check initial operation result to determine the strategy to use\n   *  - Strategies: Location, Azure-AsyncOperation, Original Uri\n   * 2. Check if the operation result has a terminal state\n   *  - Terminal state will be determined by each strategy\n   *  2.1 If it is terminal state Check if a final GET request is required, if so\n   *      send final GET request and return result from operation. If no final GET\n   *      is required, just return the result from operation.\n   *      - Determining what to call for final request is responsibility of each strategy\n   *  2.2 If it is not terminal state, call the polling operation and go to step 1\n   *      - Determining what to call for polling is responsibility of each strategy\n   *      - Strategies will always use the latest URI for polling if provided otherwise\n   *        the last known one\n   */\n  async update(options?: {\n    abortSignal?: AbortSignalLike;\n    fireProgress?: (state: TState) => void;\n  }): Promise<PollOperation<TState, TResult>> {\n    const state = this.state;\n    let lastResponse: LroResponse<TResult> | undefined = undefined;\n    if (!state.isStarted) {\n      const initializeState = createInitializeState(\n        state,\n        this.lro.requestPath,\n        this.lro.requestMethod\n      );\n      lastResponse = await this.lro.sendInitialRequest();\n      initializeState(lastResponse);\n    }\n\n    if (!state.isCompleted) {\n      if (!this.poll || !this.getLroStatusFromResponse) {\n        if (!state.config) {\n          throw new Error(\n            \"Bad state: LRO mode is undefined. Please check if the serialized state is well-formed.\"\n          );\n        }\n        const isDone = this.isDone;\n        this.getLroStatusFromResponse = isDone\n          ? (response: LroResponse<TResult>) => ({\n              ...response,\n              done: isDone(response.flatResponse, this.state)\n            })\n          : createGetLroStatusFromResponse(this.lro, state.config, this.lroResourceLocationConfig);\n        this.poll = createPoll(this.lro);\n      }\n      if (!state.pollingURL) {\n        throw new Error(\n          \"Bad state: polling URL is undefined. Please check if the serialized state is well-formed.\"\n        );\n      }\n      const currentState = await this.poll(\n        state.pollingURL,\n        this.pollerConfig!,\n        this.getLroStatusFromResponse\n      );\n      logger.verbose(`LRO: polling response: ${JSON.stringify(currentState.rawResponse)}`);\n      if (currentState.done) {\n        state.result = this.processResult\n          ? this.processResult(currentState.flatResponse, state)\n          : currentState.flatResponse;\n        state.isCompleted = true;\n      } else {\n        this.poll = currentState.next ?? this.poll;\n        state.pollingURL = getPollingUrl(currentState.rawResponse, state.pollingURL);\n      }\n      lastResponse = currentState;\n    }\n    logger.verbose(`LRO: current state: ${JSON.stringify(state)}`);\n    if (lastResponse) {\n      this.updateState?.(state, lastResponse?.rawResponse);\n    } else {\n      logger.error(`LRO: no response was received`);\n    }\n    options?.fireProgress?.(state);\n    return this;\n  }\n\n  async cancel(): Promise<PollOperation<TState, TResult>> {\n    this.state.isCancelled = true;\n    return this;\n  }\n\n  /**\n   * Serializes the Poller operation.\n   */\n  public toString(): string {\n    return JSON.stringify({\n      state: this.state\n    });\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Poller } from \"../poller\";\nimport { PollOperationState } from \"../pollOperation\";\nimport {\n  LongRunningOperation,\n  LroEngineOptions,\n  PollerConfig,\n  ResumablePollOperationState\n} from \"./models\";\nimport { GenericPollOperation } from \"./operation\";\n\nfunction deserializeState<TResult, TState>(\n  serializedState: string\n): TState & ResumablePollOperationState<TResult> {\n  try {\n    return JSON.parse(serializedState).state;\n  } catch (e) {\n    throw new Error(`LroEngine: Unable to deserialize state: ${serializedState}`);\n  }\n}\n\n/**\n * The LRO Engine, a class that performs polling.\n */\nexport class LroEngine<TResult, TState extends PollOperationState<TResult>> extends Poller<\n  TState,\n  TResult\n> {\n  private config: PollerConfig;\n\n  constructor(lro: LongRunningOperation<TResult>, options?: LroEngineOptions<TResult, TState>) {\n    const { intervalInMs = 2000, resumeFrom } = options || {};\n    const state: TState & ResumablePollOperationState<TResult> = resumeFrom\n      ? deserializeState(resumeFrom)\n      : ({} as TState & ResumablePollOperationState<TResult>);\n\n    const operation = new GenericPollOperation(\n      state,\n      lro,\n      options?.lroResourceLocationConfig,\n      options?.processResult,\n      options?.updateState,\n      options?.isDone\n    );\n    super(operation);\n\n    this.config = { intervalInMs: intervalInMs };\n    operation.setPollerConfig(this.config);\n  }\n\n  /**\n   * The method used by the poller to wait before attempting to update its operation.\n   */\n  delay(): Promise<void> {\n    return new Promise((resolve) => setTimeout(() => resolve(), this.config.intervalInMs));\n  }\n}\n"], "names": ["createClientLogger"], "mappings": ";;;;;;AAAA;AACA;AAoBA;;;;MAIa,kBAAmB,SAAQ,KAAK;IAC3C,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACjC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC;KAC3D;CACF;AAED;;;;MAIa,oBAAqB,SAAQ,KAAK;IAC7C,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;QACnC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,oBAAoB,CAAC,SAAS,CAAC,CAAC;KAC7D;CACF;AA2DD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;MACsB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiF1B,YAAY,SAAyC;QA/E7C,YAAO,GAAY,IAAI,CAAC;QAMxB,0BAAqB,GAAmC,EAAE,CAAC;QA0EjE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CACxB,CACE,OAAkC,EAClC,MAA0E;YAE1E,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB,CACF,CAAC;;;;QAIF,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;;SAElB,CAAC,CAAC;KACJ;;;;;;IA8BO,MAAM,YAAY;QACxB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACtB;QACD,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YAC1C,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;SACpB;KACF;;;;;;;;;;IAWO,MAAM,QAAQ,CAAC,UAA6C,EAAE;QACpE,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;gBAClB,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC3C,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;iBAC3C,CAAC,CAAC;gBACH,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE;;;;;;oBAMjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAiB,CAAC,CAAC;iBACtD;aACF;SACF;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;YAC/B,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAChB;YACD,MAAM,CAAC,CAAC;SACT;KACF;;;;;;;;;;IAWO,YAAY,CAAC,KAAa;QAChC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE;YACjD,QAAQ,CAAC,KAAK,CAAC,CAAC;SACjB;KACF;;;;;;IAOO,MAAM,UAAU,CAAC,UAA6C,EAAE;QACtE,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC;SAC3D;KACF;;;;;;;;;IAUM,IAAI,CAAC,UAA6C,EAAE;QACzD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,oBAAoB,GAAG;gBAC3B,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;aAClC,CAAC;YACF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC1F;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;;;;IAKM,MAAM,aAAa;QACxB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACxC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;;;;;;;IAQM,UAAU,CAAC,QAAiC;QACjD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO;YACL,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC,CAAC;SACvF,CAAC;KACH;;;;IAKM,MAAM;QACX,MAAM,KAAK,GAAgC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAChE,OAAO,OAAO,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;KACvE;;;;IAKM,WAAW;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,kBAAkB,CAAC,gCAAgC,CAAC,CAAC,CAAC;aACvE;SACF;KACF;;;;IAKM,SAAS;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;;;;;;;;;;IAWM,eAAe,CAAC,UAA6C,EAAE;QACpE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SAC/C;aAAM,IAAI,OAAO,CAAC,WAAW,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkDM,iBAAiB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;KAC7B;;;;;;;IAQM,SAAS;QACd,MAAM,KAAK,GAAgC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAChE,OAAO,KAAK,CAAC,MAAM,CAAC;KACrB;;;;;IAMM,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;KAClC;;;AC1gBH;AACA,AAIA;;;;AAIA,AAAO,MAAM,MAAM,GAAGA,2BAAkB,CAAC,UAAU,CAAC,CAAC;;ACTrD;AACA;AAIA;;;;;;AAMA,SAAgB,aAAa,CAAC,WAAwB,EAAE,WAAmB;;IACzE,QACE,MAAA,MAAA,MAAA,sBAAsB,CAAC,WAAW,CAAC,mCACnC,WAAW,CAAC,WAAW,CAAC,mCACxB,oBAAoB,CAAC,WAAW,CAAC,mCACjC,WAAW,EACX;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,WAAwB;IAC3C,OAAO,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,oBAAoB,CAAC,WAAwB;IACpD,OAAO,WAAW,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,sBAAsB,CAAC,WAAwB;IACtD,OAAO,WAAW,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACrD,CAAC;AAED,SAAgB,YAAY,CAC1B,WAAmB,EACnB,aAAqB,EACrB,WAAwB;IAExB,IAAI,sBAAsB,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;QACrD,OAAO;YACL,IAAI,EAAE,YAAY;YAClB,gBAAgB,EACd,aAAa,KAAK,KAAK;kBACnB,WAAW;kBACX,aAAa,KAAK,MAAM;sBACxB,WAAW,CAAC,WAAW,CAAC;sBACxB,SAAS;SAChB,CAAC;KACH;SAAM,IACL,WAAW,CAAC,WAAW,CAAC,KAAK,SAAS;QACtC,oBAAoB,CAAC,WAAW,CAAC,KAAK,SAAS,EAC/C;QACA,OAAO;YACL,IAAI,EAAE,UAAU;SACjB,CAAC;KACH;SAAM,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;QACnD,OAAO;YACL,IAAI,EAAE,MAAM;SACb,CAAC;KACH;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,eAAgB,SAAQ,KAAK;IAEjC,YAAY,OAAe,EAAE,UAAkB;QAC7C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;KACxD;CACF;AAED,SAAgB,2BAA2B,CAAC,WAAwB;IAClE,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC;IACpC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClD,MAAM,IAAI,eAAe,CACvB,wCAAwC,IAAI,6DAA6D,EACzG,IAAI,CACL,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,2BAA2B,CAAC,WAAwB;IAClE,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC;IACpC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACxC,MAAM,IAAI,eAAe,CACvB,wCAAwC,IAAI,mDAAmD,EAC/F,IAAI,CACL,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;;AC7FD;AACA;AAkCA,AAAO,MAAM,aAAa,GAAG,CAAC,WAAW,CAAC,CAAC;AAC3C,AAAO,MAAM,aAAa,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;;ACpCjE;AACA,AAcA,SAAS,iBAAiB,CAAC,WAAwB;;IACjD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAC,WAAW,CAAC,IAAgB,mCAAI,EAAE,CAAC;IACvD,OAAO,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC;AACzE,CAAC;AAED,SAAS,uBAAuB,CAAC,WAAwB;IACvD,MAAM,KAAK,GAAG,iBAAiB,CAAC,WAAW,CAAC,CAAC;IAC7C,IAAI,2BAA2B,CAAC,WAAW,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7E,MAAM,IAAI,KAAK,CAAC,kEAAkE,KAAK,GAAG,CAAC,CAAC;KAC7F;IACD,OAAO,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC;AAED;;;AAGA,eAAe,gBAAgB,CAC7B,GAAkC,EAClC,gBAAwB,EACxB,yBAAqD;IAErD,QAAQ,yBAAyB;QAC/B,KAAK,cAAc;YACjB,OAAO,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC9C,KAAK,uBAAuB;YAC1B,OAAO,SAAS,CAAC;QACnB,KAAK,UAAU,CAAC;QAChB;YACE,OAAO,GAAG,CAAC,eAAe,CAAC,gBAAgB,aAAhB,gBAAgB,cAAhB,gBAAgB,GAAI,GAAG,CAAC,WAAW,CAAC,CAAC;KACnE;AACH,CAAC;AAED,SAAgB,gCAAgC,CAC9C,GAAkC,EAClC,gBAAyB,EACzB,yBAAqD;IAErD,OAAO,CAAC,QAA8B;QACpC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YACjD,IAAI,gBAAgB,KAAK,SAAS,EAAE;gBAClC,uCAAY,QAAQ,KAAE,IAAI,EAAE,IAAI,IAAG;aACpC;iBAAM;gBACL,uCACK,QAAQ,KACX,IAAI,EAAE,KAAK,EACX,IAAI,EAAE;wBACJ,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAC1C,GAAG,EACH,gBAAgB,EAChB,yBAAyB,CAC1B,CAAC;wBACF,wCACM,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,QAAQ,MAC7B,IAAI,EAAE,IAAI,IACV;qBACH,IACD;aACH;SACF;QACD,uCACK,QAAQ,KACX,IAAI,EAAE,KAAK,IACX;KACH,CAAC;AACJ,CAAC;;AC/ED;AACA,AAYA,SAAS,oBAAoB,CAAC,WAAwB;;IACpD,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,GAAG,MAAC,WAAW,CAAC,IAAgB,mCAAI,EAAE,CAAC;IAC9E,MAAM,KAAK,GAAuB,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,iBAAiB,mCAAI,iBAAiB,CAAC;IACrF,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC;AACvE,CAAC;AAED,SAAgB,iBAAiB,CAAC,WAAwB;IACxD,MAAM,KAAK,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;IAChD,IAAI,2BAA2B,CAAC,WAAW,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7E,MAAM,IAAI,KAAK,CAAC,kEAAkE,KAAK,GAAG,CAAC,CAAC;KAC7F;IACD,OAAO,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC;AAED;;;;AAIA,SAAgB,iCAAiC,CAC/C,QAA8B;IAE9B,uCACK,QAAQ,KACX,IAAI,EAAE,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,IAC7C;AACJ,CAAC;;ACtCD;AACA,AAKA,SAAS,qBAAqB,CAAC,WAAwB;IACrD,OAAO,CAAC,2BAA2B,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,UAAU,KAAK,GAAG,CAAC;AACrF,CAAC;AAED,SAAgB,qCAAqC,CACnD,QAA8B;IAE9B,uCACK,QAAQ,KACX,IAAI,EAAE,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,IACjD;AACJ,CAAC;;ACjBD;AACA;AAIA,SAAgB,iCAAiC,CAC/C,QAA8B;IAE9B,uCACK,QAAQ,KACX,IAAI,EAAE,IAAI,IACV;AACJ,CAAC;;ACZD;AACA,AAmBA;;;AAGA,SAAgB,8BAA8B,CAC5C,aAA4C,EAC5C,MAAiB,EACjB,yBAAqD;IAErD,QAAQ,MAAM,CAAC,IAAI;QACjB,KAAK,YAAY,EAAE;YACjB,OAAO,gCAAgC,CACrC,aAAa,EACb,MAAM,CAAC,gBAAgB,EACvB,yBAAyB,CAC1B,CAAC;SACH;QACD,KAAK,UAAU,EAAE;YACf,OAAO,qCAAqC,CAAC;SAC9C;QACD,KAAK,MAAM,EAAE;YACX,OAAO,iCAAiC,CAAC;SAC1C;QACD,SAAS;YACP,OAAO,iCAAiC,CAAC;SAC1C;KACF;AACH,CAAC;AAED;;;AAGA,SAAgB,UAAU,CACxB,aAA4C;IAM5C,OAAO,OACL,IAAY,EACZ,YAA0B,EAC1B,wBAA2D;QAE3D,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM,UAAU,GAAuB,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACnF,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC5C,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC,cAAc,CAAC;kBAC7C,gCAAgC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC;kBACjF,cAAc,CAAC;SACpB;QACD,OAAO,wBAAwB,CAAC,QAAQ,CAAC,CAAC;KAC3C,CAAC;AACJ,CAAC;AAED,SAAS,gCAAgC,CACvC,cAAoB,EACpB,mBAA2B;IAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;IACjD,MAAM,cAAc,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC;IAChD,IAAI,OAAO,GAAG,cAAc,EAAE;QAC5B,OAAO,cAAc,GAAG,OAAO,CAAC;KACjC;IACD,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED;;;;;;;AAOA,SAAgB,qBAAqB,CACnC,KAA2C,EAC3C,WAAmB,EACnB,aAAqB;IAErB,OAAO,CAAC,QAA8B;QACpC,IAAI,2BAA2B,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,CAAY;QACnE,KAAK,CAAC,kBAAkB,GAAG,QAAQ,CAAC,WAAW,CAAC;QAChD,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QACvB,KAAK,CAAC,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;QACxE,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC;;QAElF,IACE,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS;aAC9B,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,iBAAiB,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAC7E;YACA,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,YAAuB,CAAC;YAChD,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;SAC1B;QACD,MAAM,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/D,OAAO,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACnC,CAAC;AACJ,CAAC;;ACpHD;AACA,MAkBa,oBAAoB;IAU/B,YACS,KAAoD,EACnD,GAAkC,EAClC,yBAAqD,EACrD,aAA2D,EAC3D,WAAgE,EAChE,MAA0D;QAL3D,UAAK,GAAL,KAAK,CAA+C;QACnD,QAAG,GAAH,GAAG,CAA+B;QAClC,8BAAyB,GAAzB,yBAAyB,CAA4B;QACrD,kBAAa,GAAb,aAAa,CAA8C;QAC3D,gBAAW,GAAX,WAAW,CAAqD;QAChE,WAAM,GAAN,MAAM,CAAoD;KAChE;IAEG,eAAe,CAAC,YAA0B;QAC/C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KAClC;;;;;;;;;;;;;;;;IAiBD,MAAM,MAAM,CAAC,OAGZ;;QACC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,YAAY,GAAqC,SAAS,CAAC;QAC/D,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACpB,MAAM,eAAe,GAAG,qBAAqB,CAC3C,KAAK,EACL,IAAI,CAAC,GAAG,CAAC,WAAW,EACpB,IAAI,CAAC,GAAG,CAAC,aAAa,CACvB,CAAC;YACF,YAAY,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC;YACnD,eAAe,CAAC,YAAY,CAAC,CAAC;SAC/B;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAChD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;oBACjB,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAC;iBACH;gBACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAI,CAAC,wBAAwB,GAAG,MAAM;sBAClC,CAAC,QAA8B,sCAC1B,QAAQ,KACX,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAC/C;sBACF,8BAA8B,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAC3F,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClC;YACD,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;gBACrB,MAAM,IAAI,KAAK,CACb,2FAA2F,CAC5F,CAAC;aACH;YACD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,CAClC,KAAK,CAAC,UAAU,EAChB,IAAI,CAAC,YAAa,EAClB,IAAI,CAAC,wBAAwB,CAC9B,CAAC;YACF,MAAM,CAAC,OAAO,CAAC,0BAA0B,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACrF,IAAI,YAAY,CAAC,IAAI,EAAE;gBACrB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa;sBAC7B,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC;sBACpD,YAAY,CAAC,YAAY,CAAC;gBAC9B,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;aAC1B;iBAAM;gBACL,IAAI,CAAC,IAAI,GAAG,MAAA,YAAY,CAAC,IAAI,mCAAI,IAAI,CAAC,IAAI,CAAC;gBAC3C,KAAK,CAAC,UAAU,GAAG,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;aAC9E;YACD,YAAY,GAAG,YAAY,CAAC;SAC7B;QACD,MAAM,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/D,IAAI,YAAY,EAAE;YAChB,MAAA,IAAI,CAAC,WAAW,+CAAhB,IAAI,EAAe,KAAK,EAAE,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,WAAW,CAAC,CAAC;SACtD;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAC/C;QACD,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,+CAArB,OAAO,EAAiB,KAAK,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;KACb;IAED,MAAM,MAAM;QACV,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;QAC9B,OAAO,IAAI,CAAC;KACb;;;;IAKM,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAC;KACJ;CACF;;ACtID;AACA,AAYA,SAAS,gBAAgB,CACvB,eAAuB;IAEvB,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC;KAC1C;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,2CAA2C,eAAe,EAAE,CAAC,CAAC;KAC/E;AACH,CAAC;AAED;;;AAGA,MAAa,SAA+D,SAAQ,MAGnF;IAGC,YAAY,GAAkC,EAAE,OAA2C;QACzF,MAAM,EAAE,YAAY,GAAG,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;QAC1D,MAAM,KAAK,GAAkD,UAAU;cACnE,gBAAgB,CAAC,UAAU,CAAC;cAC3B,EAAoD,CAAC;QAE1D,MAAM,SAAS,GAAG,IAAI,oBAAoB,CACxC,KAAK,EACL,GAAG,EACH,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,yBAAyB,EAClC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,EACtB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EACpB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAChB,CAAC;QACF,KAAK,CAAC,SAAS,CAAC,CAAC;QAEjB,IAAI,CAAC,MAAM,GAAG,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;QAC7C,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACxC;;;;IAKD,KAAK;QACH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,MAAM,OAAO,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;KACxF;CACF;;;;;;;"}