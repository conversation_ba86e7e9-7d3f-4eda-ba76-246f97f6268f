{"version": 3, "file": "formDataPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/formDataPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AAClE,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAWtD;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAEnD,SAAS,qBAAqB,CAAC,QAAkB;;IAC/C,MAAM,WAAW,GAAgB,EAAE,CAAC;IACpC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;QAC9C,MAAA,WAAW,CAAC,GAAG,qCAAf,WAAW,CAAC,GAAG,IAAM,EAAE,EAAC;QACvB,WAAW,CAAC,GAAG,CAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc;IAC5B,OAAO;QACL,IAAI,EAAE,kBAAkB;QACxB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,UAAU,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,OAAO,CAAC,IAAI,YAAY,QAAQ,EAAE,CAAC;gBACtF,OAAO,CAAC,QAAQ,GAAG,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACvD,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC;YAC3B,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBACxD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACnF,OAAO,CAAC,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACN,MAAM,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBACnD,CAAC;gBAED,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC;YAC/B,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAqB;IAC7C,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;IAC9C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QACpD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;gBAC7B,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IACD,OAAO,eAAe,CAAC,QAAQ,EAAE,CAAC;AACpC,CAAC;AAED,KAAK,UAAU,eAAe,CAAC,QAAqB,EAAE,OAAwB;IAC5E,8CAA8C;IAC9C,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACxD,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,CAAC;QAClE,kEAAkE;QAClE,OAAO;IACT,CAAC;IAED,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,qBAAqB,CAAC,CAAC;IAE1E,kEAAkE;IAClE,MAAM,KAAK,GAAe,EAAE,CAAC;IAE7B,KAAK,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC3D,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,KAAK,CAAC,IAAI,CAAC;oBACT,OAAO,EAAE,iBAAiB,CAAC;wBACzB,qBAAqB,EAAE,oBAAoB,SAAS,GAAG;qBACxD,CAAC;oBACF,IAAI,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC;iBACzC,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9E,MAAM,IAAI,KAAK,CACb,4BAA4B,SAAS,KAAK,KAAK,+CAA+C,CAC/F,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,wFAAwF;gBACxF,MAAM,QAAQ,GAAI,KAAc,CAAC,IAAI,IAAI,MAAM,CAAC;gBAChD,MAAM,OAAO,GAAG,iBAAiB,EAAE,CAAC;gBACpC,OAAO,CAAC,GAAG,CACT,qBAAqB,EACrB,oBAAoB,SAAS,gBAAgB,QAAQ,GAAG,CACzD,CAAC;gBAEF,8EAA8E;gBAC9E,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,IAAI,0BAA0B,CAAC,CAAC;gBAEtE,KAAK,CAAC,IAAI,CAAC;oBACT,OAAO;oBACP,IAAI,EAAE,KAAK;iBACZ,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,CAAC,aAAa,GAAG,EAAE,KAAK,EAAE,CAAC;AACpC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { isNodeLike, stringToUint8Array } from \"@azure/core-util\";\nimport { createHttpHeaders } from \"../httpHeaders.js\";\nimport type {\n  BodyPart,\n  FormDataMap,\n  FormDataValue,\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n} from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\n/**\n * The programmatic identifier of the formDataPolicy.\n */\nexport const formDataPolicyName = \"formDataPolicy\";\n\nfunction formDataToFormDataMap(formData: FormData): FormDataMap {\n  const formDataMap: FormDataMap = {};\n  for (const [key, value] of formData.entries()) {\n    formDataMap[key] ??= [];\n    (formDataMap[key] as FormDataValue[]).push(value);\n  }\n  return formDataMap;\n}\n\n/**\n * A policy that encodes FormData on the request into the body.\n */\nexport function formDataPolicy(): PipelinePolicy {\n  return {\n    name: formDataPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (isNodeLike && typeof FormData !== \"undefined\" && request.body instanceof FormData) {\n        request.formData = formDataToFormDataMap(request.body);\n        request.body = undefined;\n      }\n\n      if (request.formData) {\n        const contentType = request.headers.get(\"Content-Type\");\n        if (contentType && contentType.indexOf(\"application/x-www-form-urlencoded\") !== -1) {\n          request.body = wwwFormUrlEncode(request.formData);\n        } else {\n          await prepareFormData(request.formData, request);\n        }\n\n        request.formData = undefined;\n      }\n      return next(request);\n    },\n  };\n}\n\nfunction wwwFormUrlEncode(formData: FormDataMap): string {\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(formData)) {\n    if (Array.isArray(value)) {\n      for (const subValue of value) {\n        urlSearchParams.append(key, subValue.toString());\n      }\n    } else {\n      urlSearchParams.append(key, value.toString());\n    }\n  }\n  return urlSearchParams.toString();\n}\n\nasync function prepareFormData(formData: FormDataMap, request: PipelineRequest): Promise<void> {\n  // validate content type (multipart/form-data)\n  const contentType = request.headers.get(\"Content-Type\");\n  if (contentType && !contentType.startsWith(\"multipart/form-data\")) {\n    // content type is specified and is not multipart/form-data. Exit.\n    return;\n  }\n\n  request.headers.set(\"Content-Type\", contentType ?? \"multipart/form-data\");\n\n  // set body to MultipartRequestBody using content from FormDataMap\n  const parts: BodyPart[] = [];\n\n  for (const [fieldName, values] of Object.entries(formData)) {\n    for (const value of Array.isArray(values) ? values : [values]) {\n      if (typeof value === \"string\") {\n        parts.push({\n          headers: createHttpHeaders({\n            \"Content-Disposition\": `form-data; name=\"${fieldName}\"`,\n          }),\n          body: stringToUint8Array(value, \"utf-8\"),\n        });\n      } else if (value === undefined || value === null || typeof value !== \"object\") {\n        throw new Error(\n          `Unexpected value for key ${fieldName}: ${value}. Value should be serialized to string first.`,\n        );\n      } else {\n        // using || instead of ?? here since if value.name is empty we should create a file name\n        const fileName = (value as File).name || \"blob\";\n        const headers = createHttpHeaders();\n        headers.set(\n          \"Content-Disposition\",\n          `form-data; name=\"${fieldName}\"; filename=\"${fileName}\"`,\n        );\n\n        // again, || is used since an empty value.type means the content type is unset\n        headers.set(\"Content-Type\", value.type || \"application/octet-stream\");\n\n        parts.push({\n          headers,\n          body: value,\n        });\n      }\n    }\n  }\n  request.multipartBody = { parts };\n}\n"]}