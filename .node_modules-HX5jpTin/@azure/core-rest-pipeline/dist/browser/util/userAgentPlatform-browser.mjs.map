{"version": 3, "file": "userAgentPlatform-browser.mjs", "sourceRoot": "", "sources": ["../../../src/util/userAgentPlatform-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AAEH;;GAEG;AACH,MAAM,UAAU,aAAa;IAC3B,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAQD;;GAEG;AACH,MAAM,UAAU,uBAAuB,CAAC,GAAwB;;IAC9D,MAAM,cAAc,GAAG,UAAU,CAAC,SAAwB,CAAC;IAC3D,GAAG,CAAC,GAAG,CACL,IAAI,EACJ,CAAC,MAAA,MAAA,MAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,aAAa,0CAAE,QAAQ,mCAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,QAAQ,mCAAI,SAAS,CAAC,CAAC,OAAO,CACxF,GAAG,EACH,EAAE,CACH,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/*\n * NOTE: When moving this file, please update \"browser\" section in package.json.\n */\n\n/**\n * @internal\n */\nexport function getHeaderName(): string {\n  return \"x-ms-useragent\";\n}\n\ninterface NavigatorEx extends Navigator {\n  userAgentData?: {\n    platform?: string;\n  };\n}\n\n/**\n * @internal\n */\nexport function setPlatformSpecificData(map: Map<string, string>): void {\n  const localNavigator = globalThis.navigator as NavigatorEx;\n  map.set(\n    \"OS\",\n    (localNavigator?.userAgentData?.platform ?? localNavigator?.platform ?? \"unknown\").replace(\n      \" \",\n      \"\",\n    ),\n  );\n}\n"]}