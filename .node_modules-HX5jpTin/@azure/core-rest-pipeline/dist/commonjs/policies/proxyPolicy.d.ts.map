{"version": 3, "file": "proxyPolicy.d.ts", "sourceRoot": "", "sources": ["../../../src/policies/proxyPolicy.ts"], "names": [], "mappings": "AAOA,OAAO,KAAK,EAGV,aAAa,EAEd,MAAM,kBAAkB,CAAC;AAC1B,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAQrD;;GAEG;AACH,eAAO,MAAM,eAAe,gBAAgB,CAAC;AAE7C;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE,MAAM,EAAO,CAAC;AAkE9C,wBAAgB,WAAW,IAAI,MAAM,EAAE,CAWtC;AAED;;;;;;GAMG;AACH,wBAAgB,uBAAuB,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,aAAa,GAAG,SAAS,CAgBpF;AAyED;;;;;;GAMG;AACH,wBAAgB,WAAW,CACzB,aAAa,CAAC,EAAE,aAAa,EAC7B,OAAO,CAAC,EAAE;IACR,sFAAsF;IACtF,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC;CAC9B,GACA,cAAc,CAkChB"}