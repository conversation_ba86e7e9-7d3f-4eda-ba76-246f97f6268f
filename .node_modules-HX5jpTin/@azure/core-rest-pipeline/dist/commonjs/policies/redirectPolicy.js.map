{"version": 3, "file": "redirectPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/redirectPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAKlC;;GAEG;AACU,QAAA,kBAAkB,GAAG,gBAAgB,CAAC;AAEnD;;GAEG;AACH,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAaxC;;;;;GAKG;AACH,SAAgB,cAAc,CAAC,UAAiC,EAAE;IAChE,MAAM,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IACpC,OAAO;QACL,IAAI,EAAE,0BAAkB;QACxB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC;KACF,CAAC;AACJ,CAAC;AATD,wCASC;AAED,KAAK,UAAU,cAAc,CAC3B,IAAiB,EACjB,QAA0B,EAC1B,UAAkB,EAClB,iBAAyB,CAAC;IAE1B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC;IAC9C,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC/C,IACE,cAAc;QACd,CAAC,MAAM,KAAK,GAAG;YACb,CAAC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC5D,CAAC,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC5D,CAAC,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC;YAC7C,MAAM,KAAK,GAAG,CAAC;QACjB,cAAc,GAAG,UAAU,EAC3B,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QAE7B,+DAA+D;QAC/D,+EAA+E;QAC/E,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;YACnB,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;YACvB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACzC,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB,CAAC;QAED,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAExC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\n/**\n * The programmatic identifier of the redirectPolicy.\n */\nexport const redirectPolicyName = \"redirectPolicy\";\n\n/**\n * Methods that are allowed to follow redirects 301 and 302\n */\nconst allowedRedirect = [\"GET\", \"HEAD\"];\n\n/**\n * Options for how redirect responses are handled.\n */\nexport interface RedirectPolicyOptions {\n  /**\n   * The maximum number of times the redirect URL will be tried before\n   * failing.  Defaults to 20.\n   */\n  maxRetries?: number;\n}\n\n/**\n * A policy to follow Location headers from the server in order\n * to support server-side redirection.\n * In the browser, this policy is not used.\n * @param options - Options to control policy behavior.\n */\nexport function redirectPolicy(options: RedirectPolicyOptions = {}): PipelinePolicy {\n  const { maxRetries = 20 } = options;\n  return {\n    name: redirectPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      const response = await next(request);\n      return handleRedirect(next, response, maxRetries);\n    },\n  };\n}\n\nasync function handleRedirect(\n  next: SendRequest,\n  response: PipelineResponse,\n  maxRetries: number,\n  currentRetries: number = 0,\n): Promise<PipelineResponse> {\n  const { request, status, headers } = response;\n  const locationHeader = headers.get(\"location\");\n  if (\n    locationHeader &&\n    (status === 300 ||\n      (status === 301 && allowedRedirect.includes(request.method)) ||\n      (status === 302 && allowedRedirect.includes(request.method)) ||\n      (status === 303 && request.method === \"POST\") ||\n      status === 307) &&\n    currentRetries < maxRetries\n  ) {\n    const url = new URL(locationHeader, request.url);\n    request.url = url.toString();\n\n    // POST request with Status code 303 should be converted into a\n    // redirected GET request if the redirect url is present in the location header\n    if (status === 303) {\n      request.method = \"GET\";\n      request.headers.delete(\"Content-Length\");\n      delete request.body;\n    }\n\n    request.headers.delete(\"Authorization\");\n\n    const res = await next(request);\n    return handleRedirect(next, res, maxRetries, currentRetries + 1);\n  }\n\n  return response;\n}\n"]}