{"version": 3, "file": "multipartPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/multipartPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,gDAAkE;AAGlE,iDAA2C;AAC3C,yDAA+C;AAE/C,SAAS,gBAAgB;IACvB,OAAO,wBAAwB,IAAA,sBAAU,GAAE,EAAE,CAAC;AAChD,CAAC;AAED,SAAS,aAAa,CAAC,OAAoB;IACzC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,OAAO,EAAE,CAAC;QACnC,MAAM,IAAI,GAAG,GAAG,KAAK,KAAK,MAAM,CAAC;IACnC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,SAAS,CAChB,MAMyB;IAEzB,IAAI,MAAM,YAAY,UAAU,EAAE,CAAC;QACjC,OAAO,MAAM,CAAC,UAAU,CAAC;IAC3B,CAAC;SAAM,IAAI,IAAA,sBAAM,EAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,wEAAwE;QACxE,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;IACtD,CAAC;SAAM,CAAC;QACN,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CACrB,OAOG;IAEH,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,OAAO,SAAS,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,KAAK,IAAI,UAAU,CAAC;QACtB,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,KAAK,UAAU,gBAAgB,CAC7B,OAAwB,EACxB,KAAiB,EACjB,QAAgB;IAEhB,MAAM,OAAO,GAAG;QACd,IAAA,8BAAkB,EAAC,KAAK,QAAQ,EAAE,EAAE,OAAO,CAAC;QAC5C,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;YACzB,IAAA,8BAAkB,EAAC,MAAM,EAAE,OAAO,CAAC;YACnC,IAAA,8BAAkB,EAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;YACxD,IAAA,8BAAkB,EAAC,MAAM,EAAE,OAAO,CAAC;YACnC,IAAI,CAAC,IAAI;YACT,IAAA,8BAAkB,EAAC,SAAS,QAAQ,EAAE,EAAE,OAAO,CAAC;SACjD,CAAC;QACF,IAAA,8BAAkB,EAAC,YAAY,EAAE,OAAO,CAAC;KAC1C,CAAC;IAEF,MAAM,aAAa,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;IAC9C,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,CAAC,IAAI,GAAG,MAAM,IAAA,kBAAM,EAAC,OAAO,CAAC,CAAC;AACvC,CAAC;AAED;;GAEG;AACU,QAAA,mBAAmB,GAAG,iBAAiB,CAAC;AAErD,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAC7B,MAAM,uBAAuB,GAAG,IAAI,GAAG,CACrC,2EAA2E,CAC5E,CAAC;AAEF,SAAS,mBAAmB,CAAC,QAAgB;IAC3C,IAAI,QAAQ,CAAC,MAAM,GAAG,iBAAiB,EAAE,CAAC;QACxC,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,2CAA2C,CAAC,CAAC;IAC9F,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtE,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,+BAA+B,CAAC,CAAC;IAClF,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,eAAe;IAC7B,OAAO;QACL,IAAI,EAAE,2BAAmB;QACzB,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI;;YAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;YACnF,CAAC;YAED,IAAI,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;YAE9C,MAAM,iBAAiB,GAAG,MAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,mCAAI,iBAAiB,CAAC;YACnF,MAAM,YAAY,GAAG,iBAAiB,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAC3F,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CACb,0EAA0E,iBAAiB,EAAE,CAC9F,CAAC;YACJ,CAAC;YAED,MAAM,CAAC,EAAE,WAAW,EAAE,cAAc,CAAC,GAAG,YAAY,CAAC;YACrD,IAAI,cAAc,IAAI,QAAQ,IAAI,cAAc,KAAK,QAAQ,EAAE,CAAC;gBAC9D,MAAM,IAAI,KAAK,CACb,uCAAuC,cAAc,2BAA2B,QAAQ,sBAAsB,CAC/G,CAAC;YACJ,CAAC;YAED,QAAQ,aAAR,QAAQ,cAAR,QAAQ,IAAR,QAAQ,GAAK,cAAc,EAAC;YAC5B,IAAI,QAAQ,EAAE,CAAC;gBACb,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,gBAAgB,EAAE,CAAC;YAChC,CAAC;YACD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,WAAW,cAAc,QAAQ,EAAE,CAAC,CAAC;YAC5E,MAAM,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAEvE,OAAO,CAAC,aAAa,GAAG,SAAS,CAAC;YAElC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC;AA3CD,0CA2CC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { randomUUID, stringToUint8Array } from \"@azure/core-util\";\nimport type { BodyPart, HttpHeaders, PipelineRequest, PipelineResponse } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { concat } from \"../util/concat.js\";\nimport { isBlob } from \"../util/typeGuards.js\";\n\nfunction generateBoundary(): string {\n  return `----AzSDKFormBoundary${randomUUID()}`;\n}\n\nfunction encodeHeaders(headers: HttpHeaders): string {\n  let result = \"\";\n  for (const [key, value] of headers) {\n    result += `${key}: ${value}\\r\\n`;\n  }\n  return result;\n}\n\nfunction getLength(\n  source:\n    | (() => ReadableStream<Uint8Array>)\n    | (() => NodeJS.ReadableStream)\n    | Uint8Array\n    | Blob\n    | ReadableStream\n    | NodeJS.ReadableStream,\n): number | undefined {\n  if (source instanceof Uint8Array) {\n    return source.byteLength;\n  } else if (isBlob(source)) {\n    // if was created using createFile then -1 means we have an unknown size\n    return source.size === -1 ? undefined : source.size;\n  } else {\n    return undefined;\n  }\n}\n\nfunction getTotalLength(\n  sources: (\n    | (() => ReadableStream<Uint8Array>)\n    | (() => NodeJS.ReadableStream)\n    | Uint8Array\n    | Blob\n    | ReadableStream\n    | NodeJS.ReadableStream\n  )[],\n): number | undefined {\n  let total = 0;\n  for (const source of sources) {\n    const partLength = getLength(source);\n    if (partLength === undefined) {\n      return undefined;\n    } else {\n      total += partLength;\n    }\n  }\n  return total;\n}\n\nasync function buildRequestBody(\n  request: PipelineRequest,\n  parts: BodyPart[],\n  boundary: string,\n): Promise<void> {\n  const sources = [\n    stringToUint8Array(`--${boundary}`, \"utf-8\"),\n    ...parts.flatMap((part) => [\n      stringToUint8Array(\"\\r\\n\", \"utf-8\"),\n      stringToUint8Array(encodeHeaders(part.headers), \"utf-8\"),\n      stringToUint8Array(\"\\r\\n\", \"utf-8\"),\n      part.body,\n      stringToUint8Array(`\\r\\n--${boundary}`, \"utf-8\"),\n    ]),\n    stringToUint8Array(\"--\\r\\n\\r\\n\", \"utf-8\"),\n  ];\n\n  const contentLength = getTotalLength(sources);\n  if (contentLength) {\n    request.headers.set(\"Content-Length\", contentLength);\n  }\n\n  request.body = await concat(sources);\n}\n\n/**\n * Name of multipart policy\n */\nexport const multipartPolicyName = \"multipartPolicy\";\n\nconst maxBoundaryLength = 70;\nconst validBoundaryCharacters = new Set(\n  `abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'()+,-./:=?`,\n);\n\nfunction assertValidBoundary(boundary: string): void {\n  if (boundary.length > maxBoundaryLength) {\n    throw new Error(`Multipart boundary \"${boundary}\" exceeds maximum length of 70 characters`);\n  }\n\n  if (Array.from(boundary).some((x) => !validBoundaryCharacters.has(x))) {\n    throw new Error(`Multipart boundary \"${boundary}\" contains invalid characters`);\n  }\n}\n\n/**\n * Pipeline policy for multipart requests\n */\nexport function multipartPolicy(): PipelinePolicy {\n  return {\n    name: multipartPolicyName,\n    async sendRequest(request, next): Promise<PipelineResponse> {\n      if (!request.multipartBody) {\n        return next(request);\n      }\n\n      if (request.body) {\n        throw new Error(\"multipartBody and regular body cannot be set at the same time\");\n      }\n\n      let boundary = request.multipartBody.boundary;\n\n      const contentTypeHeader = request.headers.get(\"Content-Type\") ?? \"multipart/mixed\";\n      const parsedHeader = contentTypeHeader.match(/^(multipart\\/[^ ;]+)(?:; *boundary=(.+))?$/);\n      if (!parsedHeader) {\n        throw new Error(\n          `Got multipart request body, but content-type header was not multipart: ${contentTypeHeader}`,\n        );\n      }\n\n      const [, contentType, parsedBoundary] = parsedHeader;\n      if (parsedBoundary && boundary && parsedBoundary !== boundary) {\n        throw new Error(\n          `Multipart boundary was specified as ${parsedBoundary} in the header, but got ${boundary} in the request body`,\n        );\n      }\n\n      boundary ??= parsedBoundary;\n      if (boundary) {\n        assertValidBoundary(boundary);\n      } else {\n        boundary = generateBoundary();\n      }\n      request.headers.set(\"Content-Type\", `${contentType}; boundary=${boundary}`);\n      await buildRequestBody(request, request.multipartBody.parts, boundary);\n\n      request.multipartBody = undefined;\n\n      return next(request);\n    },\n  };\n}\n"]}