# Wayland环境下全流程录制功能测试指南

## 功能概述

我们已经成功实现了Wayland环境下的全流程录制功能，主要包括以下特性：

### 1. 环境自动检测
- 自动检测当前显示服务器环境（X11/Wayland）
- 根据环境选择合适的录制脚本

### 2. 密码管理
- 支持从配置文件读取用户密码
- 如果配置中没有密码，会弹出密码输入框
- 密码仅在Wayland环境下需要

### 3. 脚本选择
- X11环境：使用 `scripts/auto_recording_manager.py`
- Wayland环境：使用 `scripts/auto_recording_manager_v11.py`

## 配置说明

### 新增配置项

在VSCode设置中新增了以下配置项：

```json
{
  "gat.userPassword": "your_password_here"
}
```

- **类型**: string
- **默认值**: ""
- **作用域**: MACHINE_OVERRIDABLE
- **描述**: 用于Wayland环境下全流程录制的用户密码

## 使用方法

### 1. 配置密码（可选）

在VSCode设置中设置用户密码：
1. 打开设置 (Ctrl+,)
2. 搜索 "gat.userPassword"
3. 输入当前用户的密码

### 2. 启动录制

1. 在GAT视图中选择一个测试用例
2. 点击"开始录制"按钮
3. 系统会立即：
   - 检测显示服务器环境
   - 如果是Wayland环境且未配置密码，**立即弹出密码输入框**
   - 选择合适的录制脚本
   - 显示录制控制窗口

### 3. 录制过程

- X11环境：正常录制，无需额外操作
- Wayland环境：录制脚本会使用提供的密码进行权限提升

## ⚠️ 重要改进

**密码输入时机优化**：
- 密码输入框现在在录制控制窗口显示**之前**弹出
- 用户可以在主界面正常显示时输入密码
- 避免了录制窗口遮挡主界面导致的输入困难

## 技术实现

### 1. 录制准备流程

```typescript
// 新增：录制环境准备方法
private async prepareRecordingEnvironment(): Promise<void> {
    // 1. 检测显示服务器环境
    // 2. 如果是Wayland环境，立即获取密码
    // 3. 将环境信息和密码存储到实例变量
    // 4. 如果密码获取失败，抛出异常阻止录制
}
```

### 2. 环境检测逻辑

```typescript
// 检测显示服务器环境
private async detectDisplayServer(): Promise<'x11' | 'wayland' | 'unknown'> {
    // 1. 检查环境变量 WAYLAND_DISPLAY
    // 2. 检查环境变量 XDG_SESSION_TYPE
    // 3. 检查运行进程
    // 4. 默认返回 x11
}
```

### 3. 密码获取逻辑

```typescript
// 获取用户密码（在录制窗口显示前调用）
private async getUserPassword(): Promise<string | null> {
    // 1. 从配置中读取密码
    // 2. 如果没有配置，弹出输入框
    // 3. 返回密码或null
}
```

### 4. 流程时序优化

**旧流程**：
1. 显示录制窗口
2. 启动Python进程时检测环境
3. 弹出密码输入框（可能被录制窗口遮挡）

**新流程**：
1. 检测环境并获取密码
2. 显示录制窗口
3. 启动Python进程（使用预先准备的信息）

### 3. IPC通信

主进程接收以下新参数：
- `displayServer`: 显示服务器类型
- `password`: 用户密码（仅Wayland需要）

## 测试步骤

### 1. X11环境测试

1. 确保在X11环境下运行
2. 启动录制功能
3. 验证使用 `auto_recording_manager.py` 脚本

### 2. Wayland环境测试

1. 确保在Wayland环境下运行
2. 不配置密码，启动录制
3. 验证弹出密码输入框
4. 输入正确密码，验证录制启动
5. 验证使用 `auto_recording_manager_v11.py` 脚本

### 3. 配置密码测试

1. 在设置中配置用户密码
2. 在Wayland环境下启动录制
3. 验证不弹出密码输入框，直接使用配置的密码

## 故障排除

### 1. 环境检测失败
- 检查日志中的环境检测信息
- 手动验证环境变量：`echo $WAYLAND_DISPLAY $XDG_SESSION_TYPE`

### 2. 密码输入框不显示
- 检查是否在Wayland环境
- 检查配置中是否已设置密码

### 3. 录制启动失败
- 检查脚本文件是否存在
- 检查密码是否正确
- 查看控制台错误信息

## 文件修改清单

### 1. 配置文件
- `src/vs/workbench/contrib/gat/browser/gat.contribution.ts`: 添加密码配置项

### 2. 录制器
- `src/vs/workbench/contrib/gat/browser/features/testCaseRecorder.ts`:
  - 添加环境检测方法
  - 添加密码获取方法
  - 修改录制启动逻辑

### 3. 主进程
- `src/vs/platform/gat/electron-main/systemWidgetCaptureMain.ts`:
  - 添加显示服务器检测方法
  - 修改Python进程启动参数
  - 添加密码参数处理

## 注意事项

1. 密码以明文形式存储在配置中，请注意安全性
2. Wayland环境下的录制需要用户权限，密码用于sudo操作
3. 环境检测有超时机制，默认5秒后回退到X11模式
4. 建议在生产环境中使用更安全的密码管理方案

## 后续改进建议

1. 实现密码加密存储
2. 支持密钥文件认证
3. 添加密码有效性验证
4. 优化环境检测性能
5. 添加更多Wayland合成器支持
