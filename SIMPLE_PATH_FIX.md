# 简单路径修复方案

## 问题
在scripts目录外运行`auto_recording_manager_v11.py`时，出现"无法找到输入监听服务器进程"错误。

## 根本原因
当从scripts目录外运行脚本时，Python无法找到`input_listener_client`模块，因为它不在Python的搜索路径中。

## 解决方案
在`auto_recording_manager_v11.py`开头添加路径处理代码：

```python
# 添加scripts目录到Python路径，确保能找到相关模块
script_dir = os.path.dirname(os.path.abspath(__file__))
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)
```

## 修改内容

**文件**: `scripts/auto_recording_manager_v11.py`

**修改位置**: 第15-19行

**修改前**:
```python
import importlib.util
from input_listener_client import InputListener
from libinput.evcodes import Button
```

**修改后**:
```python
import importlib.util

# 添加scripts目录到Python路径，确保能找到相关模块
script_dir = os.path.dirname(os.path.abspath(__file__))
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)

from input_listener_client import InputListener
from libinput.evcodes import Button
```

## 测试验证

### 测试命令
```bash
cd /home/<USER>/kylin-robot-ide
python3 scripts/auto_recording_manager_v11.py --debug --duration 1 --test-case-id test --password test123
```

### 测试结果
```
✅ 脚本正常启动
✅ 成功导入input_listener_client模块
✅ 录制功能正常工作
✅ 创建录制会话: session_1753857196408
✅ 无"无法找到输入监听服务器进程"错误
```

## 工作原理

1. **获取脚本目录**: `os.path.dirname(os.path.abspath(__file__))`获取脚本所在的绝对路径
2. **添加到Python路径**: `sys.path.insert(0, script_dir)`将scripts目录添加到Python模块搜索路径的最前面
3. **正常导入**: 现在Python可以找到`input_listener_client.py`和其他相关模块

## 优势

- ✅ **简单有效**: 只需要几行代码
- ✅ **不影响现有功能**: 完全向后兼容
- ✅ **自动适应**: 无论从哪个目录运行都能正确找到模块
- ✅ **无副作用**: 不会影响其他Python脚本

## 使用方法

现在可以从项目根目录正常运行Wayland录制脚本：

```bash
# 基本用法
python3 scripts/auto_recording_manager_v11.py --password your_password

# 带参数的用法
python3 scripts/auto_recording_manager_v11.py \
    --debug \
    --duration 30 \
    --test-case-id my_test \
    --password your_password
```

## 总结

通过在脚本开头添加简单的路径处理代码，彻底解决了"无法找到输入监听服务器进程"的问题。这是一个最小化、最直接的修复方案，不需要复杂的工作区路径处理，完美适合您的使用场景。
