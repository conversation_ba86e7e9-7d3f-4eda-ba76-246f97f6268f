#!/usr/bin/env node

/**
 * 测试显示服务器检测功能
 * 这个脚本模拟主进程的环境检测逻辑
 */

const { spawn } = require('child_process');

function detectDisplayServer() {
    return new Promise((resolve) => {
        console.log('开始检测显示服务器环境...');
        
        // 检查环境变量
        const waylandDisplay = process.env.WAYLAND_DISPLAY;
        const x11Display = process.env.DISPLAY;
        const sessionType = process.env.XDG_SESSION_TYPE;

        console.log(`环境变量检查:`);
        console.log(`  WAYLAND_DISPLAY: ${waylandDisplay || '未设置'}`);
        console.log(`  DISPLAY: ${x11Display || '未设置'}`);
        console.log(`  XDG_SESSION_TYPE: ${sessionType || '未设置'}`);

        // 优先检查环境变量
        if (waylandDisplay) {
            console.log('✅ 检测到Wayland环境（WAYLAND_DISPLAY存在）');
            resolve('wayland');
            return;
        }

        if (sessionType && sessionType.toLowerCase() === 'wayland') {
            console.log('✅ 检测到Wayland环境（XDG_SESSION_TYPE=wayland）');
            resolve('wayland');
            return;
        }

        if (x11Display) {
            console.log('✅ 检测到X11环境（DISPLAY存在）');
            resolve('x11');
            return;
        }

        if (sessionType && sessionType.toLowerCase() === 'x11') {
            console.log('✅ 检测到X11环境（XDG_SESSION_TYPE=x11）');
            resolve('x11');
            return;
        }

        // 如果环境变量检查不出来，尝试检查进程
        console.log('环境变量检查无结果，尝试检查运行进程...');
        
        // 检查Wayland相关进程
        const waylandCheck = spawn('pgrep', ['-f', 'wayland|weston|sway|gnome-shell|kwin_wayland'], { stdio: 'pipe' });
        
        waylandCheck.on('exit', (code) => {
            if (code === 0) {
                console.log('✅ 检测到Wayland环境（找到Wayland相关进程）');
                resolve('wayland');
            } else {
                // 检查X11相关进程
                const x11Check = spawn('pgrep', ['-f', 'Xorg|X11|Xwayland'], { stdio: 'pipe' });
                
                x11Check.on('exit', (x11Code) => {
                    if (x11Code === 0) {
                        console.log('✅ 检测到X11环境（找到X11相关进程）');
                        resolve('x11');
                    } else {
                        console.log('⚠️  无法确定显示服务器环境，默认使用X11');
                        resolve('x11');
                    }
                });

                x11Check.on('error', () => {
                    console.log('❌ 检查X11进程失败，默认使用X11');
                    resolve('x11');
                });
            }
        });

        waylandCheck.on('error', () => {
            console.log('❌ 检查Wayland进程失败，默认使用X11');
            resolve('x11');
        });
    });
}

function getScriptPath(displayServer) {
    if (displayServer === 'wayland') {
        return 'scripts/auto_recording_manager_v11.py';
    } else {
        return 'scripts/auto_recording_manager.py';
    }
}

async function main() {
    console.log('=== 显示服务器环境检测测试 ===\n');
    
    try {
        const displayServer = await detectDisplayServer();
        const scriptPath = getScriptPath(displayServer);
        
        console.log('\n=== 检测结果 ===');
        console.log(`显示服务器: ${displayServer}`);
        console.log(`录制脚本: ${scriptPath}`);
        
        // 检查脚本文件是否存在
        const fs = require('fs');
        if (fs.existsSync(scriptPath)) {
            console.log(`✅ 录制脚本文件存在: ${scriptPath}`);
        } else {
            console.log(`❌ 录制脚本文件不存在: ${scriptPath}`);
        }
        
        console.log('\n=== 建议的启动命令 ===');
        if (displayServer === 'wayland') {
            console.log(`python3 ${scriptPath} --json-output --debug --duration 300 --test-case-id test_case --password YOUR_PASSWORD`);
            console.log('\n注意: Wayland环境需要提供用户密码');
        } else {
            console.log(`python3 ${scriptPath} --json-output --debug --duration 300 --test-case-id test_case`);
        }
        
    } catch (error) {
        console.error('检测失败:', error);
    }
}

if (require.main === module) {
    main();
}

module.exports = { detectDisplayServer, getScriptPath };
