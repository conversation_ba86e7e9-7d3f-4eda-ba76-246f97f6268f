/*---------------------------------------------------------------------------------------------
 *  Copyright (c) <PERSON>ylin <PERSON>. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { BrowserWindow, ipcMain, screen, globalShortcut } from 'electron';
import { spawn, ChildProcessWithoutNullStreams } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import { ILogService } from '../../../platform/log/common/log.js';
import { Disposable } from '../../../base/common/lifecycle.js';

/**
 * 控件信息接口
 */
interface IWidgetInfo {
    /** 控件名称 */
    name: string;
    /** 控件描述 */
    description?: string;
    /** 控件角色名称 */
    roleName?: string;
    /** 控件坐标和大小 */
    coords?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    /** UNI控件的完整信息 */
    datamap?: Record<string, unknown>;
    /** 控件捕获状态，例如 'success', 'fallback', 'error' */
    capture_status?: string;
    /** 当捕获状态非success时，可能包含的错误或警告信息 */
    error?: string;
}

/**
 * 系统级控件捕获器（主进程）
 * 负责在系统级别捕获控件，并将信息发送给渲染进程
 */
export class SystemWidgetCaptureMain extends Disposable {
    private isCapturing: boolean = false;
    private highlightWindow: BrowserWindow | null = null;
    private pythonProcess: ChildProcessWithoutNullStreams | null = null;
    private targetWindow: BrowserWindow | null = null;
    // 保存最后一次捕获的信息，用于 stop-widget-capture 时弹窗
    private lastWidgetInfo: IWidgetInfo | null = null;

    constructor(
        private readonly logService: ILogService
    ) {
        super();
        this.setupIpcHandlers();
        // 暴露控制模态创建函数到全局
        (global as any).showControlModal = this.createAndShowControlModal.bind(this);
        (globalThis as any).showControlModal = this.createAndShowControlModal.bind(this);
        this.logService.info('已将 createAndShowControlModal 暴露到全局');
    }

    /**
     * 设置IPC处理器
     */
    private setupIpcHandlers(): void {
        // 处理开始捕获请求
        ipcMain.on('start-widget-capture', (event) => {
            this.logService.info('收到开始捕获控件请求');
            this.targetWindow = BrowserWindow.fromWebContents(event.sender);
            this.startCapture();
        });

        // 支持 GAT 消息触发开始捕获
        ipcMain.on('gat:captureControl', (event) => {
            this.logService.info('收到 gat:captureControl 请求');
            this.targetWindow = BrowserWindow.fromWebContents(event.sender);
            this.startCapture();
        });

        // 处理停止捕获请求
        ipcMain.on('stop-widget-capture', () => {
            this.logService.info('收到停止捕获控件请求');
            this.stopCapture();
        });

        // 支持 GAT 消息结束捕获并打开 control-modal
        ipcMain.on('gat:captureCompleted', (_event, data: IWidgetInfo) => {
            this.logService.info('收到 gat:captureCompleted 请求，停止捕获并弹出控制模态窗口');
            this.lastWidgetInfo = data;
            this.stopCapture();
        });

        // 处理外部调用 show-control-modal，用于备用方案
        ipcMain.on('show-control-modal', (_event, data: IWidgetInfo) => {
            this.logService.info('收到 show-control-modal 请求，通过 IPC 显示控制模态窗口');
            this.createAndShowControlModal(data);
        });

        // 处理控制模态窗口确认按钮点击
        ipcMain.on('control-modal-confirm', (_event, data: any) => {
            this.logService.info('收到 control-modal-confirm 请求，用户点击了确认按钮');
            console.log('======= 用户点击了确认按钮 =======');

            // 如果有目标窗口，将消息转发给目标窗口
            if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                this.targetWindow.webContents.send('control-modal-result', {
                    confirmed: true,
                    data: data
                });
                this.logService.info('已将确认结果转发到目标窗口');
            } else {
                // 如果没有目标窗口，尝试向所有窗口广播
                this.logService.warn('目标窗口不可用，尝试向所有窗口广播确认消息');
                const allWindows = BrowserWindow.getAllWindows();
                for (const win of allWindows) {
                    if (!win.isDestroyed()) {
                        win.webContents.send('control-modal-result', {
                            confirmed: true,
                            data: data
                        });
                    }
                }
            }
        });

        // 截图相关 IPC 处理
        ipcMain.on('gat:startScreenshotCapture', (event, data: { scriptPath: string, args: string[] }) => {
            this.logService.info('收到 gat:startScreenshotCapture 请求');
            this.targetWindow = BrowserWindow.fromWebContents(event.sender);
            this.pythonProcess = spawn('python3', [data.scriptPath, ...data.args]);
            // 转发 stdout
            this.pythonProcess.stdout.on('data', (chunk: Buffer) => {
                const msg = chunk.toString();
                this.targetWindow!.webContents.send('gat:screenshotCaptureOutput', msg);
            });
            // 转发 stderr
            this.pythonProcess.stderr.on('data', (chunk: Buffer) => {
                const msg = chunk.toString();
                this.targetWindow!.webContents.send('gat:screenshotCaptureError', msg);
            });
            // 转发退出事件
            this.pythonProcess.on('exit', (code: number | null) => {
                this.targetWindow!.webContents.send('gat:screenshotCaptureExit', code);
            });
        });

        ipcMain.on('gat:stopScreenshotCapture', () => {
            this.logService.info('收到 gat:stopScreenshotCapture 请求');
            if (this.pythonProcess) {
                this.pythonProcess.kill();
                this.pythonProcess = null;
            }
        });

        // Python录制相关 IPC 处理
        ipcMain.on('gat:start-python-recording', (event, data: { scriptPath: string, workingDirectory: string, duration?: number, testCaseId?: string, testcasePath?: string, displayServer?: string, password?: string }) => {
            this.logService.info('收到 gat:start-python-recording 请求');
            this.logService.info(`脚本路径: ${data.scriptPath}, 工作目录: ${data.workingDirectory}, 时长: ${data.duration || 300}秒, 测试用例ID: ${data.testCaseId || 'unknown'}, testcase路径: ${data.testcasePath || '未配置'}, 显示服务器: ${data.displayServer || 'unknown'}`);

            this.targetWindow = BrowserWindow.fromWebContents(event.sender);
            this.startPythonRecordingProcess(data);
        });

        ipcMain.on('gat:stop-python-recording', () => {
            this.logService.info('收到 gat:stop-python-recording 请求');
            this.stopPythonRecordingProcess();
        });

        // VSCode IPC 兼容的Python录制处理器
        ipcMain.on('vscode:gat-start-python-recording', (event, data: { scriptPath: string, workingDirectory: string, duration?: number, testCaseId?: string, testcasePath?: string, displayServer?: string, password?: string }) => {
            this.logService.info('收到 vscode:gat-start-python-recording 请求');
            this.logService.info(`脚本路径: ${data.scriptPath}, 工作目录: ${data.workingDirectory}, 时长: ${data.duration || 300}秒, 测试用例ID: ${data.testCaseId || 'unknown'}, testcase路径: ${data.testcasePath || '未配置'}, 显示服务器: ${data.displayServer || 'unknown'}`);

            this.targetWindow = BrowserWindow.fromWebContents(event.sender);
            this.startPythonRecordingProcess(data);
        });

        ipcMain.on('vscode:gat-stop-python-recording', () => {
            this.logService.info('收到 vscode:gat-stop-python-recording 请求');
            this.stopPythonRecordingProcess();
        });

        // Python录制暂停和恢复处理
        ipcMain.on('gat:pause-python-recording', () => {
            this.logService.info('收到 gat:pause-python-recording 请求');
            this.pausePythonRecordingProcess();
        });

        ipcMain.on('gat:resume-python-recording', () => {
            this.logService.info('收到 gat:resume-python-recording 请求');
            this.resumePythonRecordingProcess();
        });

        // VSCode IPC 兼容的Python录制暂停和恢复处理器
        ipcMain.on('vscode:gat-pause-python-recording', () => {
            this.logService.info('收到 vscode:gat-pause-python-recording 请求');
            this.pausePythonRecordingProcess();
        });

        ipcMain.on('vscode:gat-resume-python-recording', () => {
            this.logService.info('收到 vscode:gat-resume-python-recording 请求');
            this.resumePythonRecordingProcess();
        });

        // 显示服务器检测 IPC 处理
        ipcMain.on('gat:detect-display-server', (event) => {
            this.logService.info('收到 gat:detect-display-server 请求');
            this.detectDisplayServer(event);
        });

        // 录制控制器窗口位置更新处理
        ipcMain.on('gat:update-recorder-window-bounds', (event, data: { bounds: { x: number; y: number; width: number; height: number } }) => {
            this.logService.debug(`收到录制控制器窗口位置更新: x=${data.bounds.x}, y=${data.bounds.y}, width=${data.bounds.width}, height=${data.bounds.height}`);
            this.updateRecorderWindowBounds(data.bounds);
        });

        // 区域捕获相关 IPC 处理
        ipcMain.on('gat:startRegionCapture', (event, scriptPath: string) => {
            this.logService.info('收到 gat:startRegionCapture 请求');
            this.targetWindow = BrowserWindow.fromWebContents(event.sender);

            // 如果已有进程在运行，先终止它
            if (this.pythonProcess) {
                this.logService.info('终止现有的Python进程');
                this.pythonProcess.kill('SIGTERM');
                // 如果进程在3秒内没有终止，强制杀死
                setTimeout(() => {
                    if (this.pythonProcess && !this.pythonProcess.killed) {
                        this.logService.warn('Python进程未响应SIGTERM，使用SIGKILL强制终止');
                        this.pythonProcess.kill('SIGKILL');
                    }
                }, 3000);
                this.pythonProcess = null;
            }

            try {
                // 设置Python脚本环境变量，启用调试输出用于排错
                const env = { ...process.env, GAT_REGION_DEBUG: '1' };
                this.pythonProcess = spawn('python3', [scriptPath], { env });

                this.logService.info(`启动区域捕获Python进程，PID: ${this.pythonProcess.pid}`);

                // 转发 stdout
                this.pythonProcess.stdout.on('data', (chunk: Buffer) => {
                    const msg = chunk.toString();
                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        this.targetWindow.webContents.send('gat:regionCaptureOutput', msg);
                    }
                });

                // 转发 stderr
                this.pythonProcess.stderr.on('data', (chunk: Buffer) => {
                    const msg = chunk.toString();
                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        this.targetWindow.webContents.send('gat:regionCaptureError', msg);
                    }
                });

                // 监听进程错误
                this.pythonProcess.on('error', (error) => {
                    this.logService.error(`区域捕获脚本进程错误: ${error.message}`);
                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        this.targetWindow.webContents.send('gat:regionCaptureError', `进程错误: ${error.message}`);
                    }
                    this.pythonProcess = null;
                });

                // 监听脚本退出
                this.pythonProcess.on('exit', (code: number | null, signal: string | null) => {
                    this.logService.info(`区域捕获脚本退出: code=${code}, signal=${signal}`);
                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        if (code === 2) {
                            this.logService.info('区域捕获被用户取消');
                            this.targetWindow.webContents.send('gat:regionCaptureCancelled');
                        } else if (signal) {
                            this.logService.warn(`区域捕获脚本被信号 ${signal} 终止`);
                            this.targetWindow.webContents.send('gat:regionCaptureCancelled'); // 也视为取消
                        } else if (code !== 0 && code !== null) {
                            this.logService.error(`区域捕获脚本异常退出，退出码: ${code}`);
                            this.targetWindow.webContents.send('gat:regionCaptureError', `脚本异常退出，退出码: ${code}`);
                        } else {
                            this.logService.info('区域捕获脚本正常退出');
                            // 正常退出时，输出由'data'事件处理，这里无需额外操作
                        }
                    }
                    this.pythonProcess = null;
                });

            } catch (error: any) {
                this.logService.error(`启动区域捕获脚本失败: ${error.message}`);
                if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                    this.targetWindow.webContents.send('gat:regionCaptureError', `启动脚本失败: ${error.message}`);
                }
                this.pythonProcess = null;
            }
        });

        ipcMain.on('gat:stopRegionCapture', () => {
            this.logService.info('收到 gat:stopRegionCapture 请求');
            if (this.pythonProcess) {
                this.logService.info(`正在终止区域捕获进程，PID: ${this.pythonProcess.pid}`);

                // 尝试优雅终止
                this.pythonProcess.kill('SIGTERM');

                // 设置超时强制终止
                setTimeout(() => {
                    if (this.pythonProcess && !this.pythonProcess.killed) {
                        this.logService.warn('区域捕获进程未响应SIGTERM，使用SIGKILL强制终止');
                        this.pythonProcess.kill('SIGKILL');
                    }
                }, 2000);

                this.pythonProcess = null;
            } else {
                this.logService.info('没有运行的区域捕获进程需要停止');
            }
        });

        // 位置捕获相关 IPC 处理
        ipcMain.on('gat:startPositionCapture', (event, scriptPath: string, args: string[], type?: string, name?: string) => {
            this.logService.info(`[SWCM] 收到 gat:startPositionCapture 请求, 脚本: ${scriptPath}, 参数: ${args.join(' ')}, 类型: ${type || 'N/A'}, 名称: ${name || 'N/A'}`);
            this.targetWindow = BrowserWindow.fromWebContents(event.sender);

            if (this.pythonProcess) {
                this.logService.warn('[SWCM] 已有 Python 进程正在运行。正在终止旧进程以开始位置捕获。');
                this.pythonProcess.kill();
                this.pythonProcess = null;
            }

            try {
                const scriptArgs = [...args];
                if (type) {
                    scriptArgs.push('--type', type);
                }
                if (name) {
                    scriptArgs.push('--name', name);
                }

                this.pythonProcess = spawn('python3', [scriptPath, ...scriptArgs]);
                this.logService.info(`[SWCM] 已启动位置捕获的 Python 进程 (PID: ${this.pythonProcess.pid}) with args: ${scriptArgs.join(' ')}`);

                let stdoutData = '';
                let stderrData = '';

                this.pythonProcess.stdout.on('data', (data: Buffer) => {
                    const msg = data.toString();
                    stdoutData += msg;
                    this.logService.info(`[SWCM] PositionCapture STDOUT: ${msg.trim()}`);
                });

                this.pythonProcess.stderr.on('data', (data: Buffer) => {
                    const msg = data.toString();
                    stderrData += msg;
                    // 区分真正的错误和调试信息
                    if (msg.includes('错误:') || msg.includes('Error:') || msg.includes('Traceback')) {
                        this.logService.error(`[SWCM] PositionCapture 真正错误: ${msg.trim()}`);
                    } else {
                        this.logService.info(`[SWCM] PositionCapture 调试信息: ${msg.trim()}`);
                    }
                });

                this.pythonProcess.on('error', (err) => {
                    this.logService.error(`[SWCM] 启动位置捕获脚本失败: ${err.message}`);
                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        this.targetWindow.webContents.send('gat:positionCaptureError', `启动脚本失败: ${err.message}`);
                    }
                    this.pythonProcess = null;
                });

                this.pythonProcess.on('exit', (code: number | null, signal: string | null) => {
                    const rawStdout = stdoutData.trim();
                    const rawStderr = stderrData.trim();

                    // 从 stdout 中提取有效负载（JSON 或 cancelled），忽略诸如 Xlib.xauth 的警告信息
                    const extractPayload = (data: string): string | null => {
                        const lines = data.split(/\r?\n/).map(l => l.trim()).filter(l => l.length > 0);
                        // 从最后一行开始反向查找，先找业务有效行
                        for (let i = lines.length - 1; i >= 0; i--) {
                            const line = lines[i];
                            if (line === 'cancelled') {
                                return 'cancelled';
                            }
                            // 粗略判断单行 JSON：以 { 开头，以 } 结尾
                            if (line.startsWith('{') && line.endsWith('}')) {
                                return line;
                            }
                        }
                        return null;
                    };

                    const finalStdout = extractPayload(rawStdout) ?? '';
                    const finalStderr = rawStderr;

                    this.logService.info(`[SWCM] 位置捕获脚本已退出。代码: ${code}, 信号: ${signal}, 原始STDOUT: "${rawStdout}", 有效STDOUT: "${finalStdout}", STDERR: "${finalStderr}"`);

                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        if (signal) {
                            this.logService.warn(`[SWCM] 位置捕获脚本被信号 ${signal} 终止。作为取消处理。`);
                            this.targetWindow.webContents.send('gat:positionCaptureCancelled');
                        } else if (code === 0) {
                            if (finalStdout === 'cancelled') { // Python 脚本显式返回 "cancelled"
                                this.logService.info('[SWCM] 位置捕获脚本显式返回 "cancelled".');
                                this.targetWindow.webContents.send('gat:positionCaptureCancelled');
                            } else {
                                try {
                                    const resultObject = JSON.parse(finalStdout);
                                    // 基本验证：检查 x 和 y 是否为数字
                                    // 您可以根据 IPositionCaptureResult 接口添加更严格的验证
                                    if (typeof resultObject.x === 'number' && typeof resultObject.y === 'number') {
                                        this.logService.info(`[SWCM] 成功解析位置捕获结果: ${JSON.stringify(resultObject)}`);
                                        // 发送整个解析后的对象
                                        this.targetWindow.webContents.send('gat:positionCaptureResult', resultObject);
                                    } else {
                                        this.logService.error(`[SWCM] STDOUT 解析成功但缺少 x/y 坐标或类型错误: "${finalStdout}".`);
                                        // 检查stderr是否包含真正的错误
                                        const hasRealError = finalStderr && (finalStderr.includes('错误:') || finalStderr.includes('Error:') || finalStderr.includes('Traceback'));
                                        if (hasRealError) {
                                            this.targetWindow.webContents.send('gat:positionCaptureError', finalStderr);
                                        } else {
                                            this.targetWindow.webContents.send('gat:positionCaptureError', `STDOUT 解析成功但缺少 x/y 坐标或类型错误: ${finalStdout}`);
                                        }
                                    }
                                } catch (e: any) {
                                    this.logService.error(`[SWCM] STDOUT 不是有效的JSON: "${finalStdout}". 错误: ${e.message}. STDERR: "${finalStderr}"`);
                                    // 检查stderr是否包含真正的错误
                                    const hasRealError = finalStderr && (finalStderr.includes('错误:') || finalStderr.includes('Error:') || finalStderr.includes('Traceback'));
                                    if (hasRealError) {
                                        this.targetWindow.webContents.send('gat:positionCaptureError', finalStderr);
                                    } else {
                                        this.targetWindow.webContents.send('gat:positionCaptureError', `STDOUT 不是有效的JSON: ${finalStdout}`);
                                    }
                                }
                            }
                        } else {
                            // 检查是否是真正的错误还是仅仅是调试信息
                            const hasRealError = finalStderr && (finalStderr.includes('错误:') || finalStderr.includes('Error:') || finalStderr.includes('Traceback'));
                            if (hasRealError) {
                                this.logService.error(`[SWCM] 脚本真正错误。代码: ${code}. STDERR: "${finalStderr}"`);
                                this.targetWindow.webContents.send('gat:positionCaptureError', finalStderr);
                            } else {
                                this.logService.warn(`[SWCM] 脚本退出，但可能是正常退出。代码: ${code}. 调试信息: "${finalStderr}"`);
                                // 只有在没有stdout输出时才当作错误
                                if (!finalStdout) {
                                    this.targetWindow.webContents.send('gat:positionCaptureError', `脚本退出，代码: ${code}`);
                                }
                            }
                        }
                    } else {
                        this.logService.warn('[SWCM] 位置捕获脚本退出时，目标窗口已销毁或不可用。');
                    }
                    this.pythonProcess = null;
                });
            } catch (error: any) {
                this.logService.error(`[SWCM] 创建位置捕获脚本进程时出错: ${error.message}`);
                if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                    this.targetWindow.webContents.send('gat:positionCaptureError', `创建脚本进程时出错: ${error.message}`);
                }
                this.pythonProcess = null;
            }
        });

        ipcMain.on('gat:stopPositionCapture', () => {
            this.logService.info('[SWCM] 收到 gat:stopPositionCapture 请求');
            if (this.pythonProcess) {
                this.logService.info('[SWCM] 正在终止当前活动的 Python 位置捕获进程。');
                this.pythonProcess.kill();
                this.pythonProcess = null;
            } else {
                this.logService.info('[SWCM] 没有活动的 Python 位置捕获进程可以停止。');
            }
        });
    }

    /**
     * 开始捕获控件
     */
    private startCapture(): void {
        if (this.isCapturing) {
            this.stopCapture();
        }

        this.isCapturing = true;

        // 创建高亮窗口
        this.createHighlightWindow();

        // 注册全局快捷键
        this.registerGlobalShortcuts();

        // 启动Python进程
        this.startPythonProcess();

        // 开始监听鼠标移动
        this.startMouseTracking();
    }

    /**
     * 停止捕获控件
     */
    private stopCapture(): void {
        if (!this.isCapturing) {
            return;
        }

        this.isCapturing = false;

        // 关闭高亮窗口
        if (this.highlightWindow) {
            this.highlightWindow.close();
            this.highlightWindow = null;
        }

        // 注销全局快捷键
        globalShortcut.unregisterAll();

        // 终止Python进程
        if (this.pythonProcess) {
            this.pythonProcess.kill();
            this.pythonProcess = null;
        }

        console.log('======= 停止捕获，检查是否有lastWidgetInfo =======');
        this.logService.info('控件捕获已停止');
        this.logService.info('捕获结束后弹出控制模态窗口');

        // 弹出控制模态窗口（如果有捕获结果）
        if (this.lastWidgetInfo) {
            console.log('======= 有捕获结果，准备弹出控制模态窗口 =======');
            this.logService.info(`准备弹出控制模态窗口，控件信息类型: ${typeof this.lastWidgetInfo}, 内容: ${JSON.stringify(this.lastWidgetInfo).substring(0, 100)}...`);

            try {
                console.log('======= 调用createAndShowControlModal方法 =======');
                this.logService.info('调用createControlModalWindow()');
                this.createAndShowControlModal(this.lastWidgetInfo);
            } catch (error) {
                console.error('======= 弹出控制模态窗口时发生错误 =======', error);
                this.logService.error(`弹出控制模态窗口时发生错误: ${error}`);
            }

            this.lastWidgetInfo = null;
        } else {
            console.log('======= 没有捕获结果，不弹出控制模态窗口 =======');
            this.logService.info('没有捕获结果，不弹出控制模态窗口');
        }
    }

    /**
     * 创建高亮窗口
     */
    private createHighlightWindow(): void {
        // 创建一个透明的、始终置顶的窗口用于高亮显示控件
        this.highlightWindow = new BrowserWindow({
            frame: false,
            transparent: true,
            alwaysOnTop: true,
            skipTaskbar: true,
            focusable: false,
            show: false,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: true
            }
        });

        // 加载高亮窗口的HTML内容
        const highlightHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    overflow: hidden;
                    background-color: transparent;
                }
                #highlight {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border: 3px solid red;
                    background-color: rgba(255, 0, 0, 0.2);
                    box-sizing: border-box;
                    box-shadow: 0 0 10px rgba(255, 0, 0, 0.7);
                }
                #inner-border {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    border: 1px dashed white;
                    box-sizing: border-box;
                }
            </style>
        </head>
        <body>
            <div id="highlight">
                <div id="inner-border"></div>
            </div>
        </body>
        </html>
        `;

        this.highlightWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(highlightHtml)}`);
    }

    /**
     * 注册全局快捷键
     */
    private registerGlobalShortcuts(): void {
        // 注册Escape键用于取消捕获
        globalShortcut.register('Escape', () => {
            this.logService.info('按下Escape键，取消捕获');

            if (this.targetWindow) {
                this.targetWindow.webContents.send('widget-capture-cancelled');
            }

            this.stopCapture();
        });

        // 注册鼠标点击事件用于完成捕获
        // 注意：Electron不直接支持全局鼠标事件，这里需要使用系统特定的方法
        // 在实际实现中，可能需要使用原生模块或其他方法

        // 这里使用一个定时器模拟鼠标跟踪
        const trackingInterval = setInterval(() => {
            if (!this.isCapturing) {
                clearInterval(trackingInterval);
                return;
            }

            // 获取当前鼠标位置
            const mousePosition = screen.getCursorScreenPoint();

            // 向Python进程发送鼠标位置
            if (this.pythonProcess && this.pythonProcess.stdin.writable) {
                this.pythonProcess.stdin.write(JSON.stringify({
                    type: 'mouse-position',
                    x: mousePosition.x,
                    y: mousePosition.y
                }) + '\\n');
            }
        }, 100); // 每100毫秒更新一次
    }

    /**
     * 启动Python进程
     */
    private startPythonProcess(): void {
        try {
            // 获取Python脚本路径
            // 查找UNI.py脚本的实际位置
            const possiblePythonScriptPaths = [
                path.join(process.resourcesPath || '', 'app', 'scripts', 'UNI.py'),
                path.join(process.cwd(), 'scripts', 'UNI.py')
            ];

            // 使用第一个可能的路径作为默认值，实际使用时应该检查文件是否存在
            const pythonScriptPath = possiblePythonScriptPaths[0];

            // 启动Python进程
            this.pythonProcess = spawn('python3', [pythonScriptPath, '--mode=capture']);

            // 处理Python进程的输出
            this.pythonProcess.stdout.on('data', (data: Buffer) => {
                try {
                    const output = data.toString().trim();
                    this.logService.info(`Python进程输出: ${output}`);

                    // 尝试解析JSON输出
                    if (output.startsWith('{') && output.endsWith('}')) {
                        const widgetInfo = JSON.parse(output) as IWidgetInfo;
                        // 保存最后一次捕获信息
                        this.lastWidgetInfo = widgetInfo;

                        // 发送至主窗口（高亮）
                        if (this.targetWindow) {
                            this.targetWindow.webContents.send('widget-info', widgetInfo);
                        }

                        // 更新高亮窗口
                        this.updateHighlightWindow(widgetInfo);

                        // 直接输出到控制台，确保可见
                        console.log('======= UNI捕获成功，找到控件 =======');
                        console.log('控件信息:', JSON.stringify(widgetInfo).substring(0, 300) + '...');

                        // 捕获完成：停止捕获并弹出控制模态窗口
                        this.stopCapture();
                        return;
                    }
                } catch (error) {
                    this.logService.error(`解析Python输出失败: ${error}`);
                    console.error('======= 解析Python输出失败 =======', error);
                }
            });

            // 处理Python进程的错误
            this.pythonProcess.stderr.on('data', (data: Buffer) => {
                const error = data.toString().trim();
                this.logService.error(`Python进程错误: ${error}`);

                if (this.targetWindow) {
                    this.targetWindow.webContents.send('widget-capture-error', error);
                }
            });

            // 处理Python进程的退出
            this.pythonProcess.on('close', (code: number | null) => {
                this.logService.info(`Python进程退出，退出码: ${code}`);
                this.pythonProcess = null;
            });
        } catch (error) {
            this.logService.error(`启动Python进程失败: ${error}`);

            if (this.targetWindow) {
                this.targetWindow.webContents.send('widget-capture-error', String(error));
            }
        }
    }

    /**
     * 开始监听鼠标移动
     */
    private startMouseTracking(): void {
        // 注意：Electron不直接支持全局鼠标事件，这里需要使用系统特定的方法
        // 在实际实现中，可能需要使用原生模块或其他方法

        // 这里使用一个定时器模拟鼠标跟踪
        const trackingInterval = setInterval(() => {
            if (!this.isCapturing) {
                clearInterval(trackingInterval);
                return;
            }

            // 获取当前鼠标位置
            const mousePosition = screen.getCursorScreenPoint();

            // 向Python进程发送鼠标位置
            if (this.pythonProcess && this.pythonProcess.stdin.writable) {
                this.pythonProcess.stdin.write(JSON.stringify({
                    type: 'mouse-position',
                    x: mousePosition.x,
                    y: mousePosition.y
                }) + '\\n');
            }
        }, 100); // 每100毫秒更新一次
    }

    /**
     * 更新高亮窗口
     */
    private updateHighlightWindow(widgetInfo: IWidgetInfo): void {
        if (!this.highlightWindow || !widgetInfo.coords) {
            return;
        }

        const { x, y, width, height } = widgetInfo.coords;

        // 设置高亮窗口的位置和大小
        this.highlightWindow.setBounds({
            x,
            y,
            width,
            height
        });

        // 显示高亮窗口
        if (!this.highlightWindow.isVisible()) {
            this.highlightWindow.show();
        }
    }

    /**
     * 创建并显示控制模态窗口
     * @param widgetInfo 控件信息
     */
    public createAndShowControlModal(widgetInfo: any): void {
        console.log('======= 开始创建控制模态窗口 =======');
        this.logService.info('开始创建控制模态窗口');

        // 使用安全的路径获取方式
        const safeDir = process.cwd();
        this.logService.info(`process.cwd() 作为 safeDir: ${safeDir}`);

        // 获取资源目录的绝对路径
        const appRootDir = path.resolve(process.cwd());
        this.logService.info(`process.cwd() 作为 appRootDir: ${appRootDir}`);

        // 获取预加载脚本路径
        let preloadPath = '';
        const possiblePreloadPaths = [
            path.join(process.resourcesPath, 'app', 'out', 'modalPreload.js'),
            path.join(process.resourcesPath, 'app', 'modalPreload.js'),
            path.join(process.resourcesPath, 'modalPreload.js'),
            path.join(safeDir, '..', '..', '..', '..', 'modalPreload.js'),
            path.join(safeDir, '..', '..', '..', 'modalPreload.js'),
            path.join(safeDir, '..', '..', 'modalPreload.js'),
            path.join(safeDir, '..', 'modalPreload.js'),
            path.join(safeDir, 'modalPreload.js')
        ];

        for (const possiblePath of possiblePreloadPaths) {
            try {
                this.logService.info(`尝试预加载脚本路径: ${possiblePath}`);
                if (fs.existsSync(possiblePath)) {
                    preloadPath = possiblePath;
                    console.log(`======= 找到预加载脚本: ${preloadPath} =======`);
                    this.logService.info(`找到预加载脚本: ${preloadPath}`);
                    break;
                }
            } catch (error) {
                this.logService.warn(`检查预加载脚本路径出错: ${possiblePath}, 错误: ${error}`);
            }
        }

        if (!preloadPath) {
            console.log('======= 未找到预加载脚本，将使用内联脚本 =======');
            this.logService.warn('未找到预加载脚本，将使用内联脚本');
        }

        // 获取HTML文件路径 - 尝试多个可能的路径
        let htmlPath = '';
        const possibleHtmlPaths = [
            // 开发环境 dist 目录
            path.join(appRootDir, 'src', 'control-modal', 'dist', 'index.html'),
            path.join(appRootDir, 'control-modal', 'dist', 'index.html'),
            // 生产环境 resourcesPath
            path.join(process.resourcesPath, 'app', 'control-modal', 'dist', 'index.html'),
            path.join(process.resourcesPath, 'app', 'src', 'control-modal', 'dist', 'index.html'),
            path.join(process.resourcesPath, 'control-modal', 'dist', 'index.html'),
            // SafeDir 上溯
            path.join(safeDir, '..', '..', '..', '..', 'control-modal', 'dist', 'index.html'),
            path.join(safeDir, '..', '..', '..', 'control-modal', 'dist', 'index.html'),
            path.join(safeDir, '..', '..', 'control-modal', 'dist', 'index.html'),
            path.join(safeDir, '..', 'control-modal', 'dist', 'index.html'),
            path.join(safeDir, 'control-modal', 'dist', 'index.html')
        ];

        // 确保所有路径都尝试正确
        for (const possiblePath of possibleHtmlPaths) {
            try {
                this.logService.info(`尝试HTML文件路径: ${possiblePath}`);
                const exists = fs.existsSync(possiblePath);
                this.logService.info(`路径 ${possiblePath} 是否存在: ${exists}`);
                if (exists) {
                    htmlPath = possiblePath;
                    console.log(`======= 找到HTML文件: ${htmlPath} =======`);
                    this.logService.info(`找到HTML文件: ${htmlPath}`);
                    break;
                }
            } catch (error) {
                this.logService.warn(`检查HTML路径出错: ${possiblePath}, 错误: ${error}`);
            }
        }

        if (!htmlPath) {
            console.log('======= 未找到HTML文件，将使用备用HTML内容 =======');
        }

        // 创建一个简单的HTML内容作为备用
        // 根据capture_status确定标题
        const getTitle = () => {
            const captureStatus = widgetInfo?.capture_status;
            if (captureStatus === 'error' || captureStatus === 'fallback') {
                return '捕获失败';
            }
            return '控件信息';
        };

        const fallbackHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>${getTitle()}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    background-color: white;
                    border-radius: 5px;
                    padding: 20px;
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                }
                h1 {
                    margin-top: 0;
                    color: #333;
                }
                pre {
                    background-color: #f0f0f0;
                    padding: 10px;
                    border-radius: 3px;
                    overflow: auto;
                }
                .info-section {
                    margin-bottom: 20px;
                }
                .info-title {
                    font-weight: bold;
                    margin-bottom: 5px;
                    color: #444;
                }
                .info-value {
                    background: #f9f9f9;
                    padding: 8px;
                    border-radius: 3px;
                    border: 1px solid #ddd;
                    overflow: auto;
                    word-break: break-all;
                }
                .actions, .states {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 5px;
                    margin-top: 5px;
                }
                .tag {
                    background: #e1f5fe;
                    color: #0277bd;
                    padding: 3px 8px;
                    border-radius: 3px;
                    font-size: 0.9em;
                }
                .coordinates {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px;
                }
                .coordinate {
                    background: #f0f0f0;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 0.9em;
                }
            </style>
            <script>
                // 全局标志，标记用户是否点击了确认
                let userConfirmed = false;

                // 暴露给主进程调用的全局函数
                window.getConfirmationResult = function() {
                    return userConfirmed;
                };

                // 发送确认消息的函数
                function sendConfirmMessage() {
                    try {
                        console.log('用户点击了确认按钮，设置确认标志');
                        // 设置确认标志
                        userConfirmed = true;

                        // 尝试各种方式发送消息，但不影响关闭窗口
                        try {
                            if (window.electronIPC) {
                                window.electronIPC.send('control-modal-confirm', { name: 'OK' });
                                console.log('通过electronIPC发送了确认消息');
                            }
                        } catch (e) {
                            console.error('通过electronIPC发送失败:', e);
                        }

                        // 返回true以便调用者知道处理成功
                        return true;
                    } catch (e) {
                        console.error('发送确认消息时出错:', e);
                        return false;
                    }
                }
            </script>
        </head>
        <body>
            <div class="container">
                <h1>${getTitle()}</h1>
                <div id="control-info">
                    ${this.formatWidgetInfoForDisplay(widgetInfo)}
                </div>
                <!-- 添加确定和取消按钮 -->
                <div style="display: flex; justify-content: flex-end; margin-top: 20px; gap: 10px;">
                    <button onclick="window.close()" style="padding: 8px 16px; background-color: #f0f0f0; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                    <button onclick="window.userConfirmed = true; sendConfirmMessage(); window.close();" style="padding: 8px 16px; background-color: #1e88e5; color: white; border: none; border-radius: 4px; cursor: pointer;">确定</button>
                </div>
            </div>
        </body>
        </html>
        `;

        // 创建控制模态窗口
        const controlWindowOptions: Electron.BrowserWindowConstructorOptions = {
            width: 500,
            height: 700,
            title: '控件信息',
            alwaysOnTop: false,  // 先设为false，避免阻挡开发者工具
            frame: true,         // 启用窗口框架，便于调试
            show: false,
            webPreferences: {
                contextIsolation: false,  // 必须设置为false以允许直接访问ipcRenderer
                nodeIntegration: true,    // 启用Node集成
                webSecurity: false,       // 开发阶段禁用web安全限制，便于本地加载文件
                // 如果找到了预加载脚本，添加到选项中
                preload: preloadPath || undefined
            }
        };

        console.log('======= 创建BrowserWindow实例 =======');
        const controlModalWindow = new BrowserWindow(controlWindowOptions);
        console.log('======= BrowserWindow实例已创建 =======');
        this.logService.info('控制模态窗口实例已创建');

        // 添加加载事件监听
        controlModalWindow.webContents.on('did-finish-load', () => {
            console.log('======= 控制模态窗口加载完成 =======');
            this.logService.info('控制模态窗口加载完成，显示窗口');
            try {
                controlModalWindow.show();
                controlModalWindow.focus();
                console.log('======= 控制模态窗口已显示并聚焦 =======');
            } catch (error) {
                console.error('======= 显示控制模态窗口失败 =======', error);
                this.logService.error(`显示控制模态窗口失败: ${error}`);
            }

            // 确保IPC和widgetInfo被正确注入
            try {
                // 注入完整的widgetInfo对象
                this.logService.info('直接注入widgetInfo对象和IPC接口');
                const injectionScript = `
                    try {
                        // 设置widgetInfo
                        window.widgetInfo = ${JSON.stringify(widgetInfo)};

                        // 确保IPC接口可用
                        if (!window.electronIPC && window.require) {
                            try {
                                const { ipcRenderer } = require('electron');
                                window.electronIPC = ipcRenderer;
                                console.log('成功注入IPC接口和widgetInfo对象');
                            } catch (e) {
                                console.error('无法注入IPC接口:', e);
                            }
                        }
                    } catch (error) {
                        console.error('注入数据时出错:', error);
                    }
                `;
                controlModalWindow.webContents.executeJavaScript(injectionScript);
            } catch (error) {
                console.error('======= 注入数据和IPC接口失败 =======', error);
                this.logService.error(`注入数据和IPC接口失败: ${error}`);
            }

            // 发送控件数据到窗口
            try {
                // 格式化控件信息为可读结构
                const formattedWidgetInfo = this.formatWidgetInfoForDisplay(widgetInfo);
                controlModalWindow.webContents.send('control-data', formattedWidgetInfo);
                console.log('======= 已发送控件数据到窗口 =======');
                this.logService.info('已发送控件数据到窗口');
            } catch (error) {
                console.error('======= 发送控件数据到窗口失败 =======', error);
                this.logService.error(`发送控件数据到窗口失败: ${error}`);

                // 尝试备用方法发送数据
                try {
                    this.logService.info('尝试使用executeJavaScript注入数据');
                    const formattedWidgetInfo = this.formatWidgetInfoForDisplay(widgetInfo);
                    const script = `
                        try {
                            window.widgetInfo = ${JSON.stringify(JSON.stringify(formattedWidgetInfo))};
                            if (window.onWidgetInfoReceived) {
                                window.onWidgetInfoReceived(${JSON.stringify(JSON.stringify(formattedWidgetInfo))});
                            }
                            console.log('数据已注入', window.widgetInfo);
                        } catch (error) {
                            console.error('注入数据时出错:', error);
                        }
                    `;
                    controlModalWindow.webContents.executeJavaScript(script);
                } catch (jsError) {
                    this.logService.error(`执行JavaScript注入数据失败: ${jsError}`);
                }
            }
        });

        // 添加加载失败事件监听
        controlModalWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
            console.error(`======= 控制模态窗口加载失败: ${errorDescription} (${errorCode}) =======`);
            this.logService.error(`控制模态窗口加载失败: ${errorDescription} (${errorCode})`);

            // 加载失败时使用data:URL创建内置HTML
            // 格式化控件信息为可读结构
            const formattedWidgetInfo = this.formatWidgetInfoForDisplay(widgetInfo);

            try {
                console.log('======= 尝试加载备用HTML内容 =======');
                this.logService.info('尝试加载备用HTML内容');
                const backupHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>${getTitle()}</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 20px;
                            background: #f5f5f5;
                        }
                        .container {
                            background: white;
                            border-radius: 5px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                            padding: 20px;
                            max-width: 700px;
                            margin: 0 auto;
                        }
                        h1 {
                            color: #333;
                            border-bottom: 1px solid #eee;
                            padding-bottom: 10px;
                            margin-top: 0;
                        }
                        .info-section {
                            margin-bottom: 20px;
                        }
                        .info-title {
                            font-weight: bold;
                            margin-bottom: 5px;
                            color: #444;
                        }
                        .info-value {
                            background: #f9f9f9;
                            padding: 8px;
                            border-radius: 3px;
                            border: 1px solid #ddd;
                            overflow: auto;
                            word-break: break-all;
                        }
                        .actions, .states {
                            display: flex;
                            flex-wrap: wrap;
                            gap: 5px;
                            margin-top: 5px;
                        }
                        .tag {
                            background: #e1f5fe;
                            color: #0277bd;
                            padding: 3px 8px;
                            border-radius: 3px;
                            font-size: 0.9em;
                        }
                        .coordinates {
                            display: flex;
                            flex-wrap: wrap;
                            gap: 10px;
                        }
                        .coordinate {
                            background: #f0f0f0;
                            padding: 5px 10px;
                            border-radius: 3px;
                            font-size: 0.9em;
                        }
                        pre {
                            white-space: pre-wrap;
                            word-break: break-all;
                            background: #f5f5f5;
                            padding: 10px;
                            border-radius: 3px;
                            max-height: 200px;
                            overflow: auto;
                        }
                    </style>
                    <script>
                        // 全局标志，标记用户是否点击了确认
                        let userConfirmed = false;

                        // 暴露给主进程调用的全局函数
                        window.getConfirmationResult = function() {
                            return userConfirmed;
                        };

                        // 发送确认消息的函数
                        function sendConfirmMessage() {
                            try {
                                console.log('用户点击了确认按钮，设置确认标志');
                                // 设置确认标志
                                userConfirmed = true;

                                // 尝试各种方式发送消息，但不影响关闭窗口
                                try {
                                    if (window.electronIPC) {
                                        window.electronIPC.send('control-modal-confirm', { name: 'OK' });
                                        console.log('通过electronIPC发送了确认消息');
                                    }
                                } catch (e) {
                                    console.error('通过electronIPC发送失败:', e);
                                }

                                // 返回true以便调用者知道处理成功
                                return true;
                            } catch (e) {
                                console.error('发送确认消息时出错:', e);
                                return false;
                            }
                        }
                    </script>
                </head>
                <body>
                    <div class="container">
                        <h1>${getTitle()}</h1>
                        <div id="control-info">
                            ${formattedWidgetInfo}
                        </div>
                        <!-- 添加确定和取消按钮 -->
                        <div style="display: flex; justify-content: flex-end; margin-top: 20px; gap: 10px;">
                            <button onclick="window.close()" style="padding: 8px 16px; background-color: #f0f0f0; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                            <button onclick="window.userConfirmed = true; sendConfirmMessage(); window.close();" style="padding: 8px 16px; background-color: #1e88e5; color: white; border: none; border-radius: 4px; cursor: pointer;">确定</button>
                        </div>
                    </div>
                </body>
                </html>
                `;

                controlModalWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(backupHtml)}`);
            } catch (error) {
                console.error('======= 加载备用HTML内容失败 =======', error);
                this.logService.error(`加载备用HTML内容失败: ${error}`);
            }
        });

        // 如果找到了HTML文件，加载它；否则，加载备用HTML内容
        try {
            if (htmlPath) {
                console.log(`======= 加载HTML文件: ${htmlPath} =======`);
                this.logService.info(`加载HTML文件: ${htmlPath}`);
                controlModalWindow.loadURL(`file://${htmlPath}`)
                    .catch(err => {
                        console.error(`======= 加载HTML文件失败: ${err} =======`);
                        this.logService.error(`加载HTML文件失败: ${err}`);
                        console.log('======= 尝试加载备用HTML内容 =======');
                        this.logService.info('尝试加载备用HTML内容');
                        controlModalWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(fallbackHtml)}`);
                    });
            } else {
                console.log('======= 未找到HTML文件，加载备用HTML内容 =======');
                this.logService.info('未找到HTML文件，加载备用HTML内容');
                controlModalWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(fallbackHtml)}`);
            }
        } catch (error) {
            console.error(`======= 加载控制模态窗口内容失败: ${error} =======`);
            this.logService.error(`加载控制模态窗口内容失败: ${error}`);
            throw error;
        }

        // 添加beforeUnload事件监听
        controlModalWindow.webContents.on('dom-ready', () => {
            try {
                const script = `
                    window.addEventListener('beforeunload', () => {
                        // 不再使用localStorage，直接依赖全局变量userConfirmed
                        console.log('窗口即将关闭，当前确认状态:', window.userConfirmed ? '已确认' : '未确认');
                    });
                `;
                controlModalWindow.webContents.executeJavaScript(script);
                this.logService.info('成功添加beforeunload事件监听');
            } catch (error) {
                console.error('======= 添加beforeunload事件失败 =======', error);
                this.logService.error(`添加beforeunload事件失败: ${error}`);
            }
        });

        // 添加关闭事件监听
        controlModalWindow.on('close', async () => {
            console.log('======= 控制模态窗口即将关闭 =======');
            this.logService.info('控制模态窗口即将关闭，尝试获取确认结果');

            try {
                // 尝试获取用户确认结果
                const confirmed = await controlModalWindow.webContents.executeJavaScript('window.userConfirmed || false');

                if (confirmed) {
                    console.log('======= 用户点击了确认按钮 =======');
                    this.logService.info('用户点击了确认按钮，发送确认结果');

                    // 向目标窗口发送确认消息
                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        this.targetWindow.webContents.send('control-modal-result', {
                            confirmed: true,
                            data: { name: 'OK' }
                        });
                        this.logService.info('成功向目标窗口发送确认结果');
                    } else {
                        // 如果没有目标窗口，尝试向所有窗口广播
                        this.logService.warn('目标窗口不可用，尝试向所有窗口广播确认消息');
                        const allWindows = BrowserWindow.getAllWindows();
                        for (const win of allWindows) {
                            if (!win.isDestroyed()) {
                                win.webContents.send('control-modal-result', {
                                    confirmed: true,
                                    data: { name: 'OK' }
                                });
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('======= 获取确认结果时出错 =======', error);
                this.logService.error(`获取确认结果时出错: ${error}`);
            }
        });
    }

    /**
     * 将控件信息格式化为可显示的HTML内容
     * @param widgetInfo 控件信息
     * @returns 格式化后的HTML内容
     */
    private formatWidgetInfoForDisplay(widgetInfo: IWidgetInfo): string {
        if (!widgetInfo) {
            return '<div class="error">没有可用的控件信息</div>';
        }

        try {
            let html = '';

            // 添加基本信息
            if (widgetInfo.name) {
                html += `
                <div class="info-section">
                    <div class="info-title">控件名称</div>
                    <div class="info-value">${this.escapeHtml(widgetInfo.name)}</div>
                </div>
                `;
            }

            if (widgetInfo.roleName) {
                html += `
                <div class="info-section">
                    <div class="info-title">控件角色</div>
                    <div class="info-value">${this.escapeHtml(widgetInfo.roleName)}</div>
                </div>
                `;
            }

            if (widgetInfo.description) {
                html += `
                <div class="info-section">
                    <div class="info-title">控件描述</div>
                    <div class="info-value">${this.escapeHtml(widgetInfo.description)}</div>
                </div>
                `;
            }

            // 添加坐标信息
            if (widgetInfo.coords) {
                const { x, y, width, height } = widgetInfo.coords;
                html += `
                <div class="info-section">
                    <div class="info-title">控件坐标和大小</div>
                    <div class="coordinates">
                        <span class="coordinate">X: ${x}</span>
                        <span class="coordinate">Y: ${y}</span>
                        <span class="coordinate">宽度: ${width}</span>
                        <span class="coordinate">高度: ${height}</span>
                    </div>
                </div>
                `;
            }

            // 添加数据映射信息
            if (widgetInfo.datamap && Object.keys(widgetInfo.datamap).length > 0) {
                html += `
                <div class="info-section">
                    <div class="info-title">控件属性</div>
                    <div class="info-value">
                        <pre>${this.escapeHtml(JSON.stringify(widgetInfo.datamap, null, 2))}</pre>
                    </div>
                </div>
                `;
            }

            return html;
        } catch (error) {
            return `<div class="error">格式化控件信息时出错: ${error}</div>`;
        }
    }

    /**
     * 转义HTML特殊字符
     * @param text 需要转义的文本
     * @returns 转义后的文本
     */
    private escapeHtml(text: string): string {
        if (!text) return '';

        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    /**
     * 启动Python录制进程
     */
    private startPythonRecordingProcess(data: { scriptPath: string, workingDirectory: string, duration?: number, testCaseId?: string, testcasePath?: string, displayServer?: string, password?: string }): void {
        try {
            // 解析脚本的实际路径，支持开发环境和安装后环境
            // 使用与其他Python脚本相同的路径解析方式
            const possibleScriptPaths = [
                // 安装后的路径 (DEB包安装后) - 优先使用
                path.join(process.resourcesPath || '', 'app', 'scripts', path.basename(data.scriptPath)),
                // 当前工作目录下的相对路径
                path.join(data.workingDirectory, data.scriptPath),
                // 绝对路径（如果用户提供了绝对路径）
                data.scriptPath
            ];

            let actualScriptPath = data.scriptPath;

            // 查找第一个存在的脚本路径
            for (const possiblePath of possibleScriptPaths) {
                if (fs.existsSync(possiblePath)) {
                    actualScriptPath = possiblePath;
                    this.logService.info(`找到Python录制脚本: ${actualScriptPath}`);
                    break;
                }
            }

            // 如果没有找到脚本文件，记录所有尝试的路径和调试信息
            if (!fs.existsSync(actualScriptPath)) {
                this.logService.error(`无法找到Python录制脚本，调试信息:`);
                this.logService.error(`  请求的脚本路径: ${data.scriptPath}`);
                this.logService.error(`  工作目录: ${data.workingDirectory}`);
                this.logService.error(`  process.resourcesPath: ${process.resourcesPath || 'undefined'}`);
                this.logService.error(`  process.cwd(): ${process.cwd()}`);
                this.logService.error(`尝试的路径:`);
                possibleScriptPaths.forEach((p, i) => {
                    const exists = fs.existsSync(p);
                    this.logService.error(`  ${i + 1}. ${p} [${exists ? '存在' : '不存在'}]`);
                });
                throw new Error(`Python录制脚本不存在: ${data.scriptPath}`);
            }

            // 构建Python脚本参数
            const duration = data.duration || 300; // 默认300秒
            const testCaseId = data.testCaseId || `recording_${Date.now()}`;
            const testcasePath = data.testcasePath;

            const pythonArgs = [
                '-u',
                actualScriptPath,
                '--json-output',
                '--debug',
                '--duration', duration.toString(),
                '--test-case-id', testCaseId
            ];

            // 如果提供了testcase路径，添加到参数中
            if (testcasePath && testcasePath.trim() !== '') {
                pythonArgs.push('--testcase-path', testcasePath.trim());
                this.logService.info(`添加testcase路径参数: ${testcasePath.trim()}`);
            }

            // 如果是Wayland环境且提供了密码，添加密码参数
            if (data.displayServer === 'wayland' && data.password && data.password.trim() !== '') {
                pythonArgs.push('--password', data.password.trim());
                this.logService.info('添加用户密码参数（Wayland环境）');
            }

            this.logService.info(`启动Python录制进程，参数: ${pythonArgs.join(' ')}`);

            // 启动Python录制进程，添加JSON输出模式和无缓冲输出
            this.pythonProcess = spawn('python3', pythonArgs, {
                cwd: data.workingDirectory,
                stdio: ['pipe', 'pipe', 'pipe'],
                env: { ...process.env, PYTHONUNBUFFERED: '1' }
            });

            if (this.pythonProcess && this.pythonProcess.pid) {
                this.logService.info(`成功启动Python录制进程，PID: ${this.pythonProcess.pid}`);

                // 转发 stdout
                this.pythonProcess.stdout.on('data', (chunk: Buffer) => {
                    const output = chunk.toString().trim();
                    this.logService.info(`Python录制输出: ${output}`);
                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        this.targetWindow.webContents.send('gat:python-recording-output', { output });
                    }
                });

                // 转发 stderr，但区分真正的错误和调试信息
                this.pythonProcess.stderr.on('data', (chunk: Buffer) => {
                    const error = chunk.toString().trim();

                    // 过滤掉常见的调试信息，只处理真正的错误
                    if (this.isRealError(error)) {
                        this.logService.error(`Python录制错误: ${error}`);
                        if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                            this.targetWindow.webContents.send('gat:python-recording-error', { error });
                        }
                    } else {
                        // 调试信息只记录到日志，不发送错误通知
                        this.logService.info(`Python录制调试: ${error}`);
                    }
                });

                // 转发退出事件
                this.pythonProcess.on('exit', (code: number | null) => {
                    this.logService.info(`Python录制进程退出，代码: ${code}`);
                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        this.targetWindow.webContents.send('gat:python-recording-exit', { code });
                    }
                    this.pythonProcess = null;
                });

                // 处理进程错误
                this.pythonProcess.on('error', (error: Error) => {
                    this.logService.error(`Python录制进程错误: ${error.message}`);
                    if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                        this.targetWindow.webContents.send('gat:python-recording-error', { error: error.message });
                    }
                });
            } else {
                throw new Error('Python进程启动失败');
            }
        } catch (error) {
            this.logService.error(`启动Python录制进程失败: ${error}`);
            if (this.targetWindow && !this.targetWindow.isDestroyed()) {
                this.targetWindow.webContents.send('gat:python-recording-error', { error: String(error) });
            }
        }
    }

    /**
     * 判断stderr输出是否为真正的错误
     */
    private isRealError(message: string): boolean {
        // 过滤掉常见的调试信息和正常日志
        const debugPatterns = [
            /^\[INFO\]/,
            /^\[DEBUG\]/,
            /^\[WARNING\]/,
            /^Unable to init server:/,
            /^Unable to init server: 无法连接/,
            /X11环境检测成功/,
            /UNI模块从路径加载成功/,
            /高亮显示模块加载成功/,
            /初始化完成，显示服务器/,
            /开始录制会话/,
            /在坐标.*处找到控件/,
            /在坐标.*处未找到符合条件的控件/,
            /录制会话已保存/,
            /录制时长/,
            /鼠标事件数/,
            /键盘事件数/
        ];

        // 如果匹配任何调试模式，则不是真正的错误
        return !debugPatterns.some(pattern => pattern.test(message));
    }

    /**
     * 停止Python录制进程
     */
    private stopPythonRecordingProcess(): void {
        if (this.pythonProcess) {
            try {
                this.logService.info('正在终止Python录制进程...');
                this.pythonProcess.kill('SIGTERM');

                // 设置超时，如果SIGTERM不起作用则使用SIGKILL
                setTimeout(() => {
                    if (this.pythonProcess) {
                        this.logService.info('使用SIGKILL强制终止Python录制进程');
                        this.pythonProcess.kill('SIGKILL');
                        this.pythonProcess = null;
                    }
                }, 3000);
            } catch (error) {
                this.logService.error(`终止Python录制进程失败: ${error}`);
                this.pythonProcess = null;
            }
        }
    }

    /**
     * 暂停Python录制进程
     */
    private pausePythonRecordingProcess(): void {
        if (this.pythonProcess) {
            try {
                this.logService.info('正在暂停Python录制进程...');

                // 向Python进程发送暂停命令
                // 我们通过stdin发送暂停命令
                this.logService.info('发送暂停命令到Python进程: pause');
                this.pythonProcess.stdin.write('pause\n');

                // 确保数据被发送（使用end事件或cork/uncork）
                this.pythonProcess.stdin.uncork();
                this.logService.info('暂停命令已发送并刷新');

                // 通知渲染进程暂停状态
                if (this.targetWindow) {
                    this.targetWindow.webContents.send('gat:python-recording-paused');
                }

                this.logService.info('Python录制进程已暂停');
            } catch (error) {
                this.logService.error(`暂停Python录制进程失败: ${error}`);
            }
        } else {
            this.logService.warn('没有运行的Python录制进程需要暂停');
        }
    }

    /**
     * 恢复Python录制进程
     */
    private resumePythonRecordingProcess(): void {
        if (this.pythonProcess) {
            try {
                this.logService.info('正在恢复Python录制进程...');

                // 向Python进程发送恢复命令
                // 我们通过stdin发送恢复命令
                this.logService.info('发送恢复命令到Python进程: resume');
                this.pythonProcess.stdin.write('resume\n');

                // 确保数据被发送
                this.pythonProcess.stdin.uncork();
                this.logService.info('恢复命令已发送并刷新');

                // 通知渲染进程恢复状态
                if (this.targetWindow) {
                    this.targetWindow.webContents.send('gat:python-recording-resumed');
                }

                this.logService.info('Python录制进程已恢复');
            } catch (error) {
                this.logService.error(`恢复Python录制进程失败: ${error}`);
            }
        } else {
            this.logService.warn('没有运行的Python录制进程需要恢复');
        }
    }

    /**
     * 更新录制控制器窗口位置信息
     */
    private updateRecorderWindowBounds(bounds: { x: number; y: number; width: number; height: number }): void {
        if (this.pythonProcess && this.pythonProcess.stdin && !this.pythonProcess.killed) {
            try {
                // 构建窗口位置更新命令
                const command = {
                    action: 'update_recorder_window_bounds',
                    bounds: bounds
                };

                // 发送命令给Python进程
                this.pythonProcess.stdin.write(JSON.stringify(command) + '\n');
                this.pythonProcess.stdin.cork();
                this.pythonProcess.stdin.uncork();

                this.logService.debug(`已发送录制控制器窗口位置信息给Python进程: x=${bounds.x}, y=${bounds.y}, width=${bounds.width}, height=${bounds.height}`);
            } catch (error) {
                this.logService.error(`发送窗口位置信息给Python进程失败: ${error}`);
            }
        } else {
            this.logService.debug('Python录制进程未运行，跳过窗口位置更新');
        }
    }

    /**
     * 检测显示服务器环境
     */
    private detectDisplayServer(event: Electron.IpcMainEvent): void {
        try {
            this.logService.info('开始检测显示服务器环境...');

            // 检查环境变量
            const waylandDisplay = process.env.WAYLAND_DISPLAY;
            const x11Display = process.env.DISPLAY;
            const sessionType = process.env.XDG_SESSION_TYPE;

            this.logService.info(`环境变量检查: WAYLAND_DISPLAY=${waylandDisplay}, DISPLAY=${x11Display}, XDG_SESSION_TYPE=${sessionType}`);

            // 优先检查环境变量
            if (waylandDisplay) {
                this.logService.info('检测到Wayland环境（WAYLAND_DISPLAY存在）');
                event.sender.send('gat:display-server-detected', 'wayland');
                return;
            }

            if (sessionType && sessionType.toLowerCase() === 'wayland') {
                this.logService.info('检测到Wayland环境（XDG_SESSION_TYPE=wayland）');
                event.sender.send('gat:display-server-detected', 'wayland');
                return;
            }

            if (x11Display) {
                this.logService.info('检测到X11环境（DISPLAY存在）');
                event.sender.send('gat:display-server-detected', 'x11');
                return;
            }

            if (sessionType && sessionType.toLowerCase() === 'x11') {
                this.logService.info('检测到X11环境（XDG_SESSION_TYPE=x11）');
                event.sender.send('gat:display-server-detected', 'x11');
                return;
            }

            // 如果环境变量检查不出来，尝试检查进程
            this.logService.info('环境变量检查无结果，尝试检查运行进程...');

            const { spawn } = require('child_process');

            // 检查Wayland相关进程
            const waylandCheck = spawn('pgrep', ['-f', 'wayland|weston|sway|gnome-shell|kwin_wayland'], { stdio: 'pipe' });

            waylandCheck.on('exit', (code: number) => {
                if (code === 0) {
                    this.logService.info('检测到Wayland环境（找到Wayland相关进程）');
                    event.sender.send('gat:display-server-detected', 'wayland');
                } else {
                    // 检查X11相关进程
                    const x11Check = spawn('pgrep', ['-f', 'Xorg|X11|Xwayland'], { stdio: 'pipe' });

                    x11Check.on('exit', (x11Code: number) => {
                        if (x11Code === 0) {
                            this.logService.info('检测到X11环境（找到X11相关进程）');
                            event.sender.send('gat:display-server-detected', 'x11');
                        } else {
                            this.logService.warn('无法确定显示服务器环境，默认使用X11');
                            event.sender.send('gat:display-server-detected', 'x11');
                        }
                    });

                    x11Check.on('error', () => {
                        this.logService.warn('检查X11进程失败，默认使用X11');
                        event.sender.send('gat:display-server-detected', 'x11');
                    });
                }
            });

            waylandCheck.on('error', () => {
                this.logService.warn('检查Wayland进程失败，默认使用X11');
                event.sender.send('gat:display-server-detected', 'x11');
            });

        } catch (error) {
            this.logService.error(`检测显示服务器环境失败: ${error}`);
            event.sender.send('gat:display-server-detected', 'x11');
        }
    }
}
