/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2023-2024 <PERSON><PERSON><PERSON>. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { Disposable, DisposableStore } from '../../../../../base/common/lifecycle.js';
import { IWorkbenchContribution } from '../../../../common/contributions.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { localize } from '../../../../../nls.js';
import { INativeHostService } from '../../../../../platform/native/common/native.js';
import { CommandsRegistry } from '../../../../../platform/commands/common/commands.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { InsertMethodWindow } from './methodWindow/index.js';
import { Emitter } from '../../../../../base/common/event.js';
import { GatMessageService, GatMessageType } from './messaging/gatMessageService.js';
import { MethodInsertionHandler } from './methodInsertion/methodInsertionHandler.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { URI } from '../../../../../base/common/uri.js';
import { joinPath } from '../../../../../base/common/resources.js';
import { GATPathResolver } from '../common/pathResolver.js';
import { OperationRecordWindow, OperationRecord } from './operationRecordWindow.js';
import { ActionYamlGenerator } from './actionYamlGenerator.js';
import { ITextFileService } from '../../../../services/textfile/common/textfiles.js';
import { IDialogService } from '../../../../../platform/dialogs/common/dialogs.js';

// 为window添加electronAPI类型定义
declare global {
	interface Window {
		electronAPI?: {
			ipcRenderer: {
				send: (channel: string, ...args: any[]) => void;
				on: (channel: string, listener: (event: any, ...args: any[]) => void) => void;
			};
		};
	}
}

/**
 * 测试用例录制器，用于执行GAT测试用例的录制功能
 */
export class TestCaseRecorder extends Disposable implements IWorkbenchContribution {
	// 录制工具条ID
	// private static readonly RECORDING_TOOLBAR_ID = 'gat-recording-toolbar';
	private isRecording = false;
	private isPaused = false;
	private currentTestCase: any | null = null;
	private readonly _disposables = new DisposableStore();

	// 录制工具条窗口
	private toolbarWindow: Window | null = null;

	// 操作记录窗口
	private operationRecordWindow: OperationRecordWindow | null = null;

	// Python录制进程
	private pythonRecordingProcess: any = null;

	private insertMethodWindow: InsertMethodWindow | null = null;

	// 标记方法数据是否已预加载
	private methodDataPreloaded = false;

	// 录制模式下的插入公共方法状态
	private isInsertingMethodInRecording = false;
	private pendingMethodInsertionData: { stepIndex: number; stepName: string } | null = null;

	// 消息服务
	private messageService: GatMessageService;

	// 录制状态事件
	private readonly _onPauseRecording = new Emitter<void>();
	readonly onPauseRecording = this._onPauseRecording.event;

	private readonly _onResumeRecording = new Emitter<void>();
	readonly onResumeRecording = this._onResumeRecording.event;

	private readonly _onStopRecording = new Emitter<void>();
	readonly onStopRecording = this._onStopRecording.event;

	private readonly methodInsertionHandler: MethodInsertionHandler;
	private readonly pathResolver: GATPathResolver;
	private readonly actionYamlGenerator: ActionYamlGenerator;

	constructor(
		@ILogService private readonly logService: ILogService,
		@INotificationService private readonly notificationService: INotificationService,
		@INativeHostService private readonly nativeHostService: INativeHostService,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
		@IDialogService private readonly dialogService: IDialogService
	) {
		super();

		// 初始化路径解析器
		const configService = this.instantiationService.invokeFunction(accessor => accessor.get(IConfigurationService));
		const workspaceService = this.instantiationService.invokeFunction(accessor => accessor.get(IWorkspaceContextService));
		const fileService = this.instantiationService.invokeFunction(accessor => accessor.get(IFileService));
		this.pathResolver = new GATPathResolver(configService, workspaceService, fileService, this.logService);

		this.logService.info('初始化测试用例录制器');

		// 初始化消息服务
		this.messageService = GatMessageService.getInstance(this.logService, 'TestCaseRecorder');
		this.messageService.initPostMessageListener();

		// 注册消息处理器
		this.registerMessageHandlers();

		// 注册事件监听器
		this.registerEventListeners();

		this.methodInsertionHandler = new MethodInsertionHandler(
			this.sendStatusMessage.bind(this),
			this.logService,
			this.notificationService,
			this.instantiationService
		);

		// 初始化ActionYamlGenerator
		const textFileService = this.instantiationService.invokeFunction(accessor => accessor.get(ITextFileService));
		this.actionYamlGenerator = new ActionYamlGenerator(
			this.logService,
			textFileService,
			this.instantiationService,
			configService,
			fileService
		);
	}

	/**
	 * 开始录制测试用例
	 * @param testCase 测试用例对象
	 */
	private async startRecording(testCase: any): Promise<void> {
		try {
			// 如果已经在录制中，且是不同的测试用例，先停止当前录制
			if (this.isRecording) {
				if (this.currentTestCase && this.currentTestCase.id !== testCase.id) {
					this.logService.info(`切换录制到新的测试用例: 从 ${this.currentTestCase.id} 到 ${testCase.id}`);
					await this.stopRecording();
					// 确保停止录制后再继续
					await new Promise(resolve => setTimeout(resolve, 300));
				} else {
					this.logService.warn('已有录制任务正在进行中');
					this.notificationService.warn(localize('testCaseRecordAlreadyRunning', "已有录制任务正在进行中"));
					return;
				}
			}

			this.logService.info('测试用例详细信息:', JSON.stringify(testCase, null, 2));
			this.currentTestCase = testCase;
			this.isRecording = true;
			this.isPaused = false;

			// 清空 recording_actions 目录
			await this.clearRecordingActionsDirectory();

			// 设置ActionYamlGenerator的当前测试用例
			this.actionYamlGenerator.setCurrentTestCase(testCase);

			// 通知视图录制状态变化
			document.dispatchEvent(new CustomEvent('gat:recording-state-changed', { detail: { isRecording: true, currentTestCaseId: testCase.id } }));

			// 尝试读取完整的测试用例文件内容并打印日志（仅用于调试）
			try {
				const fileService = this.instantiationService.invokeFunction(acc => acc.get(IFileService));
				let fileUri: URI;
				// 优先使用 TestCase 中已解析的 ymlPath
				if ((testCase as any).ymlPath) {
					fileUri = URI.parse((testCase as any).ymlPath as string);
				} else {
					// 使用路径解析器获取测试用例根目录
					const rootUri = await this.pathResolver.getTestcasePath();
					if (!rootUri) {
						throw new Error('无法获取有效的测试用例路径');
					}
					const typeDir = joinPath(rootUri, (testCase.recordType || 'GAT').toUpperCase());
					const appName = Array.isArray((testCase as any).TestCaseAppList)
						? ((testCase as any).TestCaseAppList[0] || '')
						: ((testCase as any).TestCaseAppList || '');
					fileUri = joinPath(typeDir, appName, `${testCase.id}.yml`);
				}
				const result = await fileService.readFile(fileUri);
				const content = result.value.toString();
				this.logService.info(`完整测试用例文件内容 (${fileUri.toString()}):\n${content}`);
			} catch (err) {
				this.logService.error('获取完整测试用例文件内容失败:', err);
			}

			// 预加载方法数据，提高插入公共方法窗口的响应速度
			this.preloadMethodData();

			// 将测试用例上下文传递给方法插入处理器
			this.methodInsertionHandler.setCurrentTestCase(this.currentTestCase);
			this.methodInsertionHandler.setRecordingState(this.isRecording, this.isPaused);

			// 在显示录制窗口之前，先检测环境并处理密码
			await this.prepareRecordingEnvironment();

			// 显示通知
			this.notificationService.info(localize('testCaseRecordStarted', "开始录制测试用例: {0}", testCase.id));

			// 创建并显示操作记录窗口
			await this.showOperationRecordWindow();

			// 设置操作记录窗口的测试用例信息
			if (this.operationRecordWindow) {
				// 确保测试用例包含YAML文件路径信息
				let enrichedTestCase = { ...testCase };

				// 如果没有ymlPath，尝试构建YAML文件路径
				if (!enrichedTestCase.ymlPath) {
					try {
						// 使用路径解析器获取测试用例根目录
						const rootUri = await this.pathResolver.getTestcasePath();
						if (rootUri) {
							const typeDir = joinPath(rootUri, (testCase.recordType || 'GAT').toUpperCase());
							const appName = Array.isArray((testCase as any).TestCaseAppList)
								? ((testCase as any).TestCaseAppList[0] || '')
								: ((testCase as any).TestCaseAppList || '');
							const yamlUri = joinPath(typeDir, appName, `${testCase.id}.yml`);
							enrichedTestCase.ymlPath = yamlUri.toString();
							this.logService.info(`构建YAML文件路径: ${enrichedTestCase.ymlPath}`);
						}
					} catch (error) {
						this.logService.warn(`构建YAML文件路径失败: ${error}`);
					}
				}

				this.operationRecordWindow.setCurrentTestCase(enrichedTestCase);
			}

			// 注意：现在我们使用独立窗口，不需要最小化主窗口
			// 但是我们仍然可以最小化主窗口，这样用户可以更好地关注被测试的应用
			await this.nativeHostService.minimizeWindow(undefined);

		} catch (error) {
			this.logService.error(`录制测试用例时出错: ${error instanceof Error ? error.message : String(error)}`);
			this.notificationService.error(localize('testCaseRecordError', "录制测试用例时出错: {0}",
				error instanceof Error ? error.message : String(error)));
			this.stopRecording();
		}
	}

	/**
	 * 暂停录制
	 */
	private pauseRecording(): void {
		if (!this.isRecording || this.isPaused) {
			return;
		}

		this.isPaused = true;
		this.logService.info(`暂停录制测试用例: ${this.currentTestCase?.id}`);

		// 暂停全流程录制（停止事件监听）
		if (this.pythonRecordingProcess) {
			this.pauseFullRecording();
		}

		// 更新操作记录窗口状态
		if (this.operationRecordWindow) {
			this.operationRecordWindow.setPaused(true);
			this.operationRecordWindow.setRecordingStatus('paused');
		}

		// 更新方法插入处理器状态
		this.methodInsertionHandler.setRecordingState(this.isRecording, this.isPaused);

		// 触发暂停事件
		this._onPauseRecording.fire();

		// 显示通知
		this.notificationService.info(localize('testCaseRecordPaused', "已暂停录制测试用例"));
	}

	/**
	 * 恢复录制
	 */
	private resumeRecording(): void {
		if (!this.isRecording || !this.isPaused) {
			return;
		}

		this.isPaused = false;
		this.logService.info(`恢复录制测试用例: ${this.currentTestCase?.id}`);

		// 恢复全流程录制（重新开始事件监听）
		if (this.pythonRecordingProcess && this.pythonRecordingProcess.paused) {
			this.resumeFullRecording();
		}

		// 更新操作记录窗口状态
		if (this.operationRecordWindow) {
			this.operationRecordWindow.setPaused(false);
			this.operationRecordWindow.setRecordingStatus('recording');
		}

		// 更新方法插入处理器状态
		this.methodInsertionHandler.setRecordingState(this.isRecording, this.isPaused);

		// 触发恢复事件
		this._onResumeRecording.fire();

		// 显示通知
		this.notificationService.info(localize('testCaseRecordResumed', "已恢复录制测试用例"));
	}

	/**
	 * 暂停/恢复录制
	 */
	private togglePauseRecording(): void {
		if (!this.isRecording) {
			return;
		}

		if (this.isPaused) {
			this.resumeRecording();
		} else {
			this.pauseRecording();
		}
	}



	/**
	 * 预加载方法数据
	 * 使用 MethodInsertionHandler 统一预加载逻辑
	 */
	private preloadMethodData(): void {
		if (this.methodDataPreloaded) {
			return;
		}
		// 预加载插入逻辑数据
		this.methodInsertionHandler.setCurrentTestCase(this.currentTestCase);
		this.methodInsertionHandler.setRecordingState(this.isRecording, this.isPaused);
		this.methodInsertionHandler.preloadMethodData();
		this.methodDataPreloaded = true;
	}



	/**
	 * 显示操作记录窗口
	 */
	private async showOperationRecordWindow(): Promise<void> {
		// 先隐藏现有窗口
		this.hideOperationRecordWindow();

		try {
			this.operationRecordWindow = new OperationRecordWindow(
				this.logService,
				this.notificationService,
				this.instantiationService.invokeFunction(acc => acc.get(IFileService)),
				this.instantiationService.invokeFunction(acc => acc.get(ITextFileService))
			);

			// 监听窗口关闭事件
			this.operationRecordWindow.onWindowClosed(() => {
				this.stopRecording();
			});

			// 监听全流程录制切换事件
			this.operationRecordWindow.onFullRecordingToggled((isActive) => {
				this.handleFullRecordingToggle(isActive);
			});

			await this.operationRecordWindow.show();
			this.logService.info('操作记录窗口显示成功');
		} catch (error) {
			this.logService.error(`显示操作记录窗口时出错: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 隐藏操作记录窗口
	 */
	private hideOperationRecordWindow(): void {
		if (this.operationRecordWindow) {
			this.operationRecordWindow.hide();
			this.operationRecordWindow.dispose();
			this.operationRecordWindow = null;
		}
	}



	/**
	 * 停止录制
	 */
	private async stopRecording(): Promise<void> {
		if (!this.isRecording) {
			return;
		}

		this.logService.info(`停止录制测试用例: ${this.currentTestCase?.id}`);
		this.isRecording = false;
		this.isPaused = false;

		// 通知视图录制状态变化
		document.dispatchEvent(new CustomEvent('gat:recording-state-changed', { detail: { isRecording: false, currentTestCaseId: null } }));

		// 停止全流程录制
		if (this.pythonRecordingProcess) {
			await this.stopFullRecording();
		}

		// 生成完整的测试用例YAML文件
		await this.generateCompleteTestCaseYaml();

		// 隐藏操作记录窗口
		this.hideOperationRecordWindow();

		// 恢复主窗口
		await this.nativeHostService.focusWindow(undefined);

		// 显示通知
		this.notificationService.info(localize('testCaseRecordStopped', "已停止录制测试用例: {0}", this.currentTestCase?.id));

		// 清理当前测试用例
		this.currentTestCase = null;
	}



	/**
	 * 注册消息处理器
	 */
	private registerMessageHandlers(): void {
		// 注册处理来自插入公共方法窗口的状态消息
		this.messageService.registerHandler(GatMessageType.StatusMessage, (message) => {
			if (message.source !== 'TestCaseRecorder') {
				this.logService.info(`收到来自 ${message.source} 的状态信息: ${message.data}`);
				// 如果是操作记录，添加到操作记录窗口
				this.handleOperationRecord(message.data);
			}
		});

		// 注册处理捕获控件消息
		this.messageService.registerHandler(GatMessageType.CaptureControl, (message) => {
			this.logService.info(`收到捕获控件消息: ${message.data}`);
			// 添加到操作记录窗口
			this.addOperationRecord('mouse', '捕获控件', message.data);
		});

		// 注册处理捕获完成消息
		this.messageService.registerHandler(GatMessageType.CaptureCompleted, (message) => {
			this.logService.info(`收到捕获完成消息: ${message.data}`);
			// 添加到操作记录窗口
			this.addOperationRecord('mouse', '捕获完成', message.data);
		});
	}

	/**
	 * 处理操作记录
	 */
	private handleOperationRecord(data: any): void {
		try {
			// 尝试解析操作记录数据
			if (typeof data === 'string') {
				// 如果是字符串，尝试解析为JSON
				try {
					const parsed = JSON.parse(data);
					if (parsed.type && parsed.action) {
						this.addOperationRecordFromData(parsed);
						return;
					}
				} catch (e) {
					// 解析失败，作为普通消息处理
				}
			}

			// 作为普通操作记录添加
			this.addOperationRecord('window', '系统操作', data);
		} catch (error) {
			this.logService.error(`处理操作记录时出错: ${error}`);
		}
	}

	/**
	 * 从数据添加操作记录
	 */
	private addOperationRecordFromData(data: any): void {
		if (!this.operationRecordWindow) {
			return;
		}

		const operation: OperationRecord = {
			id: data.id || `op_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
			timestamp: this.convertTimestamp(data.timestamp),
			type: data.type || 'mouse',
			action: data.action || '未知操作',
			target: data.target || {},
			details: data.details || {},
			widget_info: data.widget_info
		};

		this.operationRecordWindow.addOperation(operation);
	}

	/**
	 * 添加操作记录
	 */
	private addOperationRecord(type: 'mouse' | 'keyboard' | 'window' | 'menu', action: string, target: any): void {
		if (!this.operationRecordWindow) {
			return;
		}

		const operation: OperationRecord = {
			id: `op_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
			timestamp: Date.now(),
			type: type,
			action: action,
			target: typeof target === 'string' ? { name: target } : target,
			details: {}
		};

		this.operationRecordWindow.addOperation(operation);
	}

	/**
	 * 注册命令
	 */
	private registerEventListeners(): void {
		// 监听测试用例录制事件
		document.addEventListener('gat:record-testcase', (event: any) => {
			if (event.detail) {
				const { testCase } = event.detail;
				this.logService.info(`收到测试用例录制事件: ${testCase.id}`);
				this.startRecording(testCase);
			}
		});

		// 监听来自插入公共方法窗口和操作记录窗口的消息
		window.addEventListener('message', (event) => {
			if (!event.data || typeof event.data !== 'object') {
				return;
			}

			switch (event.data.type) {
				case 'gat:sendStatusMessage':
					this.logService.info(`收到来自方法窗口的状态信息请求: ${event.data.message}`);
					this.sendStatusMessage(event.data.message);
					break;

				case 'gat:pauseRecording':
					this.logService.info('收到暂停录制消息');
					this.pauseRecording();
					break;

				case 'gat:resumeRecording':
					this.logService.info('收到恢复录制消息');
					this.resumeRecording();
					break;

				case 'gat:insertCommonMethod':
					this.logService.info('收到插入公共方法消息');
					this.handleInsertCommonMethod();
					break;

				case 'gat:insertCommonMethodInRecording':
					this.logService.info(`收到录制模式下插入公共方法消息: 步骤${event.data.stepIndex} - ${event.data.stepName}`);
					this.handleInsertCommonMethodInRecording(event.data.stepIndex, event.data.stepName);
					break;

				case 'gat:addCommonMethodNode':
					this.logService.info(`收到添加公共方法节点消息: ${event.data.methodName}`);
					this.handleAddCommonMethodNode(event.data.methodName, event.data.formattedCode, event.data.stepIndex, event.data.stepName);
					break;

				case 'gat:stopRecording':
					this.logService.info('收到停止录制消息');
					this.stopRecording();
					break;

				case 'gat:updateRecorderWindowBounds':
					this.logService.debug('收到录制控制器窗口位置更新消息');
					this.handleRecorderWindowBoundsUpdate(event.data.bounds);
					break;
			}
		});

		// 注册命令
		this._register(CommandsRegistry.registerCommand('gat.togglePauseRecording', () => {
			this.togglePauseRecording();
		}));

		this._register(CommandsRegistry.registerCommand('gat.stopRecording', () => {
			this.stopRecording();
		}));
	}

	/**
	 * 处理插入公共方法请求
	 */
	private async handleInsertCommonMethod(): Promise<void> {
		if (!this.isRecording) {
			this.notificationService.warn(localize('notRecording', "当前没有在录制测试用例"));
			return;
		}

		if (this.isPaused) {
			this.notificationService.warn(localize('recordingPaused', "录制已暂停，无法插入公共方法"));
			return;
		}

		try {
			// 委托给 MethodInsertionHandler 处理
			this.methodInsertionHandler.setCurrentTestCase(this.currentTestCase);
			this.methodInsertionHandler.setRecordingState(this.isRecording, this.isPaused);
			await this.methodInsertionHandler.insertCommonMethod();
		} catch (error) {
			this.logService.error(`插入公共方法时出错: ${error}`);
			this.notificationService.error(localize('insertMethodError', "插入公共方法时出错"));
		}
	}

	/**
	 * 处理录制模式下的插入公共方法请求
	 */
	private async handleInsertCommonMethodInRecording(stepIndex: number, stepName: string): Promise<void> {
		if (!this.isRecording) {
			this.notificationService.warn(localize('notRecording', "当前没有在录制测试用例"));
			return;
		}

		try {
			this.logService.info(`录制模式下插入公共方法，目标步骤: ${stepName} (索引: ${stepIndex})`);

			// 设置录制模式插入状态
			this.isInsertingMethodInRecording = true;
			this.pendingMethodInsertionData = { stepIndex, stepName };

			// 暂停录制
			this.pauseRecording();

			// 委托给 MethodInsertionHandler 处理，设置录制模式
			this.methodInsertionHandler.setCurrentTestCase(this.currentTestCase);
			this.methodInsertionHandler.setRecordingState(this.isRecording, this.isPaused);
			this.methodInsertionHandler.setRecordingMode(true, stepIndex, stepName);
			await this.methodInsertionHandler.insertCommonMethod();
		} catch (error) {
			this.logService.error(`录制模式下插入公共方法时出错: ${error}`);
			this.notificationService.error(localize('insertMethodInRecordingError', "录制模式下插入公共方法时出错"));

			// 重置状态
			this.isInsertingMethodInRecording = false;
			this.pendingMethodInsertionData = null;
		}
	}

	/**
	 * 处理添加公共方法节点到录制窗口
	 */
	private handleAddCommonMethodNode(methodName: string, formattedCode: string, stepIndex: number, stepName: string): void {
		if (!this.operationRecordWindow) {
			this.logService.warn('操作记录窗口不存在，无法添加公共方法节点');
			return;
		}

		// 检查是否处于录制模式插入状态
		if (!this.isInsertingMethodInRecording) {
			this.logService.warn('当前不处于录制模式插入状态，忽略添加公共方法节点请求');
			return;
		}

		try {
			// 验证步骤信息是否匹配
			if (this.pendingMethodInsertionData &&
				(this.pendingMethodInsertionData.stepIndex !== stepIndex ||
					this.pendingMethodInsertionData.stepName !== stepName)) {
				this.logService.warn(`步骤信息不匹配，期望: ${this.pendingMethodInsertionData.stepName}(${this.pendingMethodInsertionData.stepIndex}), 实际: ${stepName}(${stepIndex})`);
			}

			// 添加公共方法节点到操作记录窗口
			this.operationRecordWindow.addCommonMethodNode(methodName, formattedCode, stepIndex, stepName);
			this.logService.info(`已添加公共方法节点到录制窗口: ${methodName}`);

			// 重置录制模式状态
			this.isInsertingMethodInRecording = false;
			this.pendingMethodInsertionData = null;

			// 恢复录制
			this.resumeRecording();
		} catch (error) {
			this.logService.error(`添加公共方法节点时出错: ${error}`);
			this.notificationService.error(localize('addMethodNodeError', "添加公共方法节点时出错"));

			// 重置状态
			this.isInsertingMethodInRecording = false;
			this.pendingMethodInsertionData = null;
		}
	}

	/**
	 * 处理全流程录制切换
	 */
	private async handleFullRecordingToggle(isActive: boolean): Promise<void> {
		if (isActive) {
			await this.startFullRecording();
		} else {
			await this.stopFullRecording();
		}
	}

	/**
	 * 启动全流程录制
	 */
	private async startFullRecording(): Promise<void> {
		try {
			this.logService.info('启动全流程录制...');

			// 立即显示"录制服务启动中"状态
			if (this.operationRecordWindow) {
				this.operationRecordWindow.setRecordingStatus('starting');
			}

			// 检查环境并记录详细信息
			this.logService.info(`环境检查: window=${typeof window !== 'undefined'}, process=${typeof process !== 'undefined'}`);
			if (typeof process !== 'undefined') {
				this.logService.info(`process.type=${process.type}, process.versions=${JSON.stringify(process.versions)}`);
			}

			// 检查是否在Electron环境中并且有IPC可用
			const hasVSCodeAPI = typeof window !== 'undefined' && (window as any).vscode && (window as any).vscode.ipcRenderer;
			const hasElectronAPI = typeof window !== 'undefined' && (window as any).electronAPI && (window as any).electronAPI.ipcRenderer;
			const isElectron = typeof process !== 'undefined' && process.versions && process.versions.electron;

			this.logService.info(`Electron环境检测: ${isElectron ? '是' : '否'}`);
			this.logService.info(`VSCodeAPI可用: ${hasVSCodeAPI ? '是' : '否'}`);
			this.logService.info(`ElectronAPI可用: ${hasElectronAPI ? '是' : '否'}`);

			if (hasVSCodeAPI || hasElectronAPI) {
				// 在Electron环境中，使用IPC启动Python进程
				try {
					await this.startRealPythonRecording();
				} catch (processError) {
					this.logService.error(`无法启动真实Python进程: ${processError}`);
					this.logService.warn('回退到模拟录制模式');
					this.startMockFullRecording();
				}
			} else {
				// 在浏览器环境中或IPC不可用，使用模拟数据
				this.logService.warn('浏览器环境或IPC不可用，使用模拟全流程录制数据');
				this.startMockFullRecording();
			}

			this.notificationService.info(localize('fullRecordingStarted', "全流程录制已启动"));

		} catch (error) {
			this.logService.error(`启动全流程录制时出错: ${error}`);
			this.notificationService.error(localize('startFullRecordingError', "启动全流程录制时出错"));

			// 更新UI状态
			if (this.operationRecordWindow) {
				this.operationRecordWindow.setFullRecording(false);
			}
		}
	}

	/**
	 * 准备录制环境（检测环境并处理密码）
	 */
	private async prepareRecordingEnvironment(): Promise<void> {
		try {
			this.logService.info('准备录制环境...');

			// 检测显示服务器环境
			const displayServer = await this.detectDisplayServer();
			this.logService.info(`检测到显示服务器环境: ${displayServer}`);

			// 如果是Wayland环境，提前获取密码
			if (displayServer === 'wayland') {
				this.logService.info('Wayland环境检测到，准备获取用户密码...');
				const password = await this.getUserPassword();
				if (!password) {
					this.logService.error('Wayland环境下需要用户密码，录制准备失败');
					this.notificationService.error(localize('passwordRequired', "Wayland环境下的全流程录制需要用户密码"));
					throw new Error('密码获取失败');
				}
				// 将密码和环境信息存储到实例变量中，供后续使用
				(this as any)._preparedDisplayServer = displayServer;
				(this as any)._preparedPassword = password;
				this.logService.info('Wayland环境密码准备完成');
			} else {
				// X11环境不需要密码
				(this as any)._preparedDisplayServer = displayServer;
				(this as any)._preparedPassword = '';
				this.logService.info('X11环境准备完成，无需密码');
			}
		} catch (error) {
			this.logService.error(`准备录制环境失败: ${error}`);
			// 重置录制状态
			this.isRecording = false;
			this.currentTestCase = null;
			throw error;
		}
	}

	/**
	 * 检测显示服务器环境
	 */
	private async detectDisplayServer(): Promise<'x11' | 'wayland' | 'unknown'> {
		try {
			const ipcRenderer = this.getIpcRenderer();
			if (!ipcRenderer) {
				this.logService.warn('IPC不可用，无法检测显示服务器环境');
				return 'unknown';
			}

			// 通过IPC请求主进程检测显示服务器环境
			return new Promise((resolve) => {
				const timeout = setTimeout(() => {
					this.logService.warn('检测显示服务器环境超时，默认使用X11');
					resolve('x11');
				}, 5000);

				ipcRenderer.once('gat:display-server-detected', (_event: any, result: string) => {
					clearTimeout(timeout);
					this.logService.info(`检测到显示服务器环境: ${result}`);
					resolve(result as 'x11' | 'wayland' | 'unknown');
				});

				ipcRenderer.send('gat:detect-display-server');
			});
		} catch (error) {
			this.logService.error(`检测显示服务器环境失败: ${error}`);
			return 'x11'; // 默认返回X11
		}
	}

	/**
	 * 获取用户密码（从配置或弹窗输入）
	 */
	private async getUserPassword(): Promise<string | null> {
		try {
			// 首先尝试从配置中获取密码
			const configService = this.instantiationService.invokeFunction(accessor => accessor.get(IConfigurationService));
			const configuredPassword = configService.getValue<string>('gat.userPassword');

			if (configuredPassword && configuredPassword.trim()) {
				this.logService.info('使用配置中的用户密码');
				return configuredPassword.trim();
			}

			// 如果配置中没有密码，弹出输入框
			this.logService.info('配置中未设置密码，弹出密码输入框');
			const result = await this.dialogService.input({
				title: localize('enterPassword', "输入用户密码"),
				message: localize('passwordRequired', "Wayland环境下的全流程录制需要用户密码："),
				inputs: [{
					type: 'password',
					placeholder: localize('passwordPlaceholder', "请输入当前用户的密码")
				}]
			});

			if (result.confirmed && result.values && result.values[0] && result.values[0].trim()) {
				return result.values[0].trim();
			}

			return null;
		} catch (error) {
			this.logService.error(`获取用户密码失败: ${error}`);
			this.notificationService.error(localize('passwordError', "获取用户密码失败"));
			return null;
		}
	}

	/**
	 * 启动真实的Python录制进程
	 */
	private async startRealPythonRecording(): Promise<void> {
		this.logService.info('启动真实Python录制进程...');

		const ipcRenderer = this.getIpcRenderer();
		if (ipcRenderer) {
			this.logService.info('使用IPC API进行Python录制通信');
		}

		if (ipcRenderer) {
			// 清理之前的监听器，避免重复监听
			this.cleanupPythonRecordingListeners();

			// 获取录制时长设置
			let recordingDuration = 300; // 默认300秒
			try {
				// 尝试从录制控制窗口获取设置
				if (this.operationRecordWindow && this.operationRecordWindow.getRecordingDuration) {
					recordingDuration = this.operationRecordWindow.getRecordingDuration();
				}
			} catch (error) {
				this.logService.warn('无法获取录制时长设置，使用默认值300秒:', error);
			}

			// 发送启动Python录制的IPC消息
			// 注意：在渲染进程中process对象不可用，使用默认工作目录
			const channelName = ipcRenderer === (window as any).vscode?.ipcRenderer ?
				'vscode:gat-start-python-recording' : 'gat:start-python-recording';

			// 使用预先准备好的环境信息和密码
			const displayServer = (this as any)._preparedDisplayServer || 'x11';
			const password = (this as any)._preparedPassword || '';
			this.logService.info(`使用预先检测的显示服务器环境: ${displayServer}`);

			// 获取配置的testcase路径
			const configService = this.instantiationService.invokeFunction(accessor => accessor.get(IConfigurationService));
			const configuredTestcasePath = configService.getValue<string>('gat.testcasePath');

			// 根据环境选择脚本
			let scriptPath = 'scripts/auto_recording_manager.py';
			if (displayServer === 'wayland') {
				scriptPath = 'scripts/auto_recording_manager_v11.py';
				this.logService.info('使用Wayland环境录制脚本');
			} else {
				this.logService.info('使用X11环境录制脚本');
			}

			this.logService.info(`启动全流程录制，时长: ${recordingDuration}秒，环境: ${displayServer}`);
			if (configuredTestcasePath) {
				this.logService.info(`使用配置的testcase路径: ${configuredTestcasePath}`);
			}

			ipcRenderer.send(channelName, {
				scriptPath: scriptPath,
				workingDirectory: '.',  // 使用当前目录作为默认值
				duration: recordingDuration,  // 传递录制时长
				testCaseId: this.currentTestCase?.id || 'unknown',  // 传递测试用例ID
				testcasePath: configuredTestcasePath || '',  // 传递testcase路径配置
				displayServer: displayServer,  // 传递显示服务器类型
				password: password  // 传递用户密码（仅Wayland环境需要）
			});

			// 监听Python进程的输出
			ipcRenderer.on('gat:python-recording-output', (_event: any, data: any) => {
				this.logService.info(`Python录制输出: ${data.output}`);

				// 处理可能包含多个JSON对象的输出
				this.parseAndHandleMultipleJsonEvents(data.output);
			});

			// 监听Python进程的错误输出
			ipcRenderer.on('gat:python-recording-error', (_event: any, data: any) => {
				this.logService.error(`Python录制错误: ${data.error}`);
				// this.notificationService.error(`Python录制错误: ${data.error}`);
			});

			// 监听Python进程退出
			ipcRenderer.on('gat:python-recording-exit', (_event: any, data: any) => {
				this.logService.info(`Python录制进程退出，代码: ${data.code}`);
				this.pythonRecordingProcess = null;

				// 更新UI状态
				if (this.operationRecordWindow) {
					this.operationRecordWindow.setFullRecording(false);
				}
			});

			// 监听Python进程暂停
			ipcRenderer.on('gat:python-recording-paused', () => {
				this.logService.info('Python录制进程已暂停');
				if (this.pythonRecordingProcess) {
					this.pythonRecordingProcess.paused = true;
				}
			});

			// 监听Python进程恢复
			ipcRenderer.on('gat:python-recording-resumed', () => {
				this.logService.info('Python录制进程已恢复');
				if (this.pythonRecordingProcess) {
					this.pythonRecordingProcess.paused = false;
				}
			});

			// 标记进程已启动
			this.pythonRecordingProcess = { type: 'real-python' };

		} else {
			throw new Error('IPC不可用，无法启动Python进程');
		}
	}

	/**
	 * 启动模拟全流程录制（用于非Electron环境）
	 */
	private startMockFullRecording(): void {
		this.logService.info('启动模拟全流程录制...');

		// 模拟一些操作事件
		const mockEvents = [
			{
				id: 'mock_1',
				timestamp: Date.now(),
				type: 'mouse_click',
				action: '左键单击',
				target_name: '开始按钮',
				target_type: 'button',
				position: { x: 100, y: 200 },
				widget_info: { class_name: 'Button' }
			},
			{
				id: 'mock_2',
				timestamp: Date.now() + 1000,
				type: 'keyboard_input',
				action: '键盘输入',
				target_name: '文本框',
				target_type: 'textbox',
				text: 'Hello World',
				widget_info: { class_name: 'TextEdit' }
			},
			{
				id: 'mock_3',
				timestamp: Date.now() + 2000,
				type: 'window_focus',
				action: '窗口激活',
				target_name: '应用程序窗口',
				target_type: 'window',
				widget_info: { class_name: 'MainWindow' }
			}
		];

		// 模拟定时发送事件
		mockEvents.forEach((event, index) => {
			setTimeout(() => {
				this.handlePythonRecordingEvent(event);
			}, index * 2000);
		});

		// 标记进程已启动
		this.pythonRecordingProcess = { type: 'mock' };
	}

	/**
	 * 暂停全流程录制
	 */
	private async pauseFullRecording(): Promise<void> {
		try {
			this.logService.info('暂停全流程录制...');

			if (this.pythonRecordingProcess) {
				if (this.pythonRecordingProcess.type === 'real-python') {
					// 通过IPC暂停Python进程
					let ipcRenderer: any = null;

					if (typeof window !== 'undefined' && (window as any).electronAPI && (window as any).electronAPI.ipcRenderer) {
						ipcRenderer = (window as any).electronAPI.ipcRenderer;
					} else if (typeof window !== 'undefined' && (window as any).vscode && (window as any).vscode.ipcRenderer) {
						ipcRenderer = (window as any).vscode.ipcRenderer;
					}

					if (ipcRenderer) {
						const channelName = ipcRenderer === (window as any).vscode?.ipcRenderer ?
							'vscode:gat-pause-python-recording' : 'gat:pause-python-recording';
						ipcRenderer.send(channelName);
					}
				} else if (this.pythonRecordingProcess.type === 'mock') {
					// 暂停模拟录制
					this.logService.info('暂停模拟全流程录制');
				}

				// 标记为暂停状态
				this.pythonRecordingProcess.paused = true;
			}

			this.logService.info('全流程录制已暂停');

		} catch (error) {
			this.logService.error(`暂停全流程录制时出错: ${error}`);
		}
	}

	/**
	 * 恢复全流程录制
	 */
	private async resumeFullRecording(): Promise<void> {
		try {
			this.logService.info('恢复全流程录制...');

			if (this.pythonRecordingProcess) {
				if (this.pythonRecordingProcess.type === 'real-python') {
					// 通过IPC恢复Python进程
					let ipcRenderer: any = null;

					if (typeof window !== 'undefined' && (window as any).electronAPI && (window as any).electronAPI.ipcRenderer) {
						ipcRenderer = (window as any).electronAPI.ipcRenderer;
					} else if (typeof window !== 'undefined' && (window as any).vscode && (window as any).vscode.ipcRenderer) {
						ipcRenderer = (window as any).vscode.ipcRenderer;
					}

					if (ipcRenderer) {
						const channelName = ipcRenderer === (window as any).vscode?.ipcRenderer ?
							'vscode:gat-resume-python-recording' : 'gat:resume-python-recording';
						ipcRenderer.send(channelName);
					}
				} else if (this.pythonRecordingProcess.type === 'mock') {
					// 恢复模拟录制
					this.logService.info('恢复模拟全流程录制');
				}

				// 移除暂停状态
				this.pythonRecordingProcess.paused = false;
			}

			this.logService.info('全流程录制已恢复');

		} catch (error) {
			this.logService.error(`恢复全流程录制时出错: ${error}`);
		}
	}

	/**
	 * 停止全流程录制
	 */
	private async stopFullRecording(): Promise<void> {
		try {
			this.logService.info('停止全流程录制...');

			if (this.pythonRecordingProcess) {
				if (this.pythonRecordingProcess.type === 'real-python') {
					// 通过IPC停止Python进程
					const ipcRenderer = this.getIpcRenderer();

					if (ipcRenderer) {
						const channelName = ipcRenderer === (window as any).vscode?.ipcRenderer ?
							'vscode:gat-stop-python-recording' : 'gat:stop-python-recording';
						ipcRenderer.send(channelName);
					}
				} else if (this.pythonRecordingProcess.type === 'mock') {
					// 停止模拟录制
					this.logService.info('停止模拟全流程录制');
				}

				this.pythonRecordingProcess = null;
			}

			// 清理Python录制监听器
			this.cleanupPythonRecordingListeners();

			// 重置录制状态
			if (this.operationRecordWindow) {
				this.operationRecordWindow.setRecordingStatus('idle');
			}

			this.notificationService.info(localize('fullRecordingStopped', "全流程录制已停止"));

		} catch (error) {
			this.logService.error(`停止全流程录制时出错: ${error}`);
			this.notificationService.error(localize('stopFullRecordingError', "停止全流程录制时出错"));
		}
	}

	/**
	 * 解析并处理可能包含多个JSON事件的输出
	 */
	private parseAndHandleMultipleJsonEvents(output: string): void {
		// 按行分割输出
		const lines = output.split('\n');

		for (const line of lines) {
			const trimmedLine = line.trim();
			if (!trimmedLine) continue;

			// 尝试解析每一行为JSON
			try {
				const eventData = JSON.parse(trimmedLine);
				this.handlePythonRecordingEvent(eventData);
			} catch (e) {
				// 如果不是JSON，检查是否包含JSON
				if (trimmedLine.includes('{"type":')) {
					// 尝试提取完整的JSON对象（包括嵌套的大括号）
					this.extractAndParseJsonObjects(trimmedLine);
				} else {
					// 普通日志
					this.logService.info(`Python录制日志: ${trimmedLine}`);
				}
			}
		}
	}

	/**
	 * 从字符串中提取并解析JSON对象
	 */
	private extractAndParseJsonObjects(text: string): void {
		let startIndex = 0;

		while (true) {
			// 查找下一个JSON对象的开始
			const jsonStart = text.indexOf('{"type":', startIndex);
			if (jsonStart === -1) break;

			// 查找匹配的结束大括号
			let braceCount = 0;
			let jsonEnd = jsonStart;

			for (let i = jsonStart; i < text.length; i++) {
				if (text[i] === '{') {
					braceCount++;
				} else if (text[i] === '}') {
					braceCount--;
					if (braceCount === 0) {
						jsonEnd = i + 1;
						break;
					}
				}
			}

			// 提取JSON字符串并尝试解析
			if (jsonEnd > jsonStart) {
				const jsonStr = text.substring(jsonStart, jsonEnd);
				try {
					const eventData = JSON.parse(jsonStr);
					this.handlePythonRecordingEvent(eventData);
				} catch (parseError) {
					this.logService.info(`Python录制日志: ${jsonStr}`);
				}

				startIndex = jsonEnd;
			} else {
				break;
			}
		}
	}

	/**
	 * 处理Python录制事件
	 */
	private handlePythonRecordingEvent(eventData: any): void {
		try {
			// 如果录制已暂停，忽略所有事件
			if (this.isPaused) {
				this.logService.info(`录制已暂停，忽略Python事件: ${JSON.stringify(eventData)}`);
				return;
			}

			this.logService.info(`收到Python事件: ${JSON.stringify(eventData)}`);

			// 处理不同类型的事件
			if (eventData.type === 'mouse_click' ||
				eventData.type === 'mouse_right_click' ||
				eventData.type === 'mouse_double_click') {
				this.handleMouseClickEvent(eventData.data);
			} else if (eventData.type === 'mouse_drag') {
				this.handleMouseDragEvent(eventData.data);
			} else if (eventData.type === 'mouse_hover') {
				this.handleMouseHoverEvent(eventData.data);
			} else if (eventData.type === 'keyboard') {
				this.handleKeyboardEvent(eventData.data);
			} else if (eventData.type === 'status') {
				this.handleStatusEvent(eventData.data);
			} else if (eventData.type === 'error') {
				this.handleErrorEvent(eventData.data);
			} else if (eventData.type === 'recording_complete') {
				this.handleRecordingCompleteEvent(eventData.data);
			} else if (eventData.type === 'recording_result') {
				this.handleRecordingResultEvent(eventData.data);
			} else if (eventData.type === 'recording_started') {
				this.handleRecordingStartedEvent(eventData.data);
			} else if (eventData.type === 'recording_progress') {
				this.handleRecordingProgressEvent(eventData.data);
			} else {
				// 兼容旧格式的事件
				this.handleLegacyEvent(eventData);
			}

		} catch (error) {
			this.logService.error(`处理Python录制事件时出错: ${error}`);
		}
	}

	/**
	 * 处理鼠标点击事件
	 */
	private handleMouseClickEvent(data: any): void {
		try {
			const widgetInfo = data.widget_info;
			const hasValidWidget = widgetInfo && widgetInfo.is_valid && !widgetInfo.error;

			// 根据事件类型构建操作描述
			let actionDescription = '';
			let operationType: 'mouse' | 'mouse_click' | 'mouse_right_click' | 'mouse_double_click' | 'mouse_drag' | 'keyboard' | 'window' | 'menu' = 'mouse';

			switch (data.event_type) {
				case 'right_click':
					actionDescription = `鼠标右键点击 (${data.button})`;
					operationType = 'mouse_right_click';
					break;
				case 'double_click':
					actionDescription = `鼠标双击 (${data.button})`;
					if (data.time_since_last_click) {
						actionDescription += ` - 间隔${data.time_since_last_click.toFixed(1)}ms`;
					}
					operationType = 'mouse_double_click';

					// 🔧 优化：检测到双击事件时，移除之前可能存在的单击事件
					this.removeRedundantClickBeforeDoubleClick(data);
					break;
				default:
					actionDescription = `鼠标${data.pressed ? '按下' : '释放'} (${data.button})`;
					operationType = 'mouse_click';
					break;
			}

			if (hasValidWidget) {
				// 优先使用Key字段，其次使用name字段
				const controlName = widgetInfo.Key || widgetInfo.name || '未命名控件';
				actionDescription += ` - ${controlName}`;
			}

			// 构建操作记录
			const operation: OperationRecord = {
				id: `mouse_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
				timestamp: this.convertTimestamp(data.timestamp), // 正确转换Python时间戳
				type: operationType,
				action: actionDescription,
				target: {
					name: widgetInfo?.name || '未知控件',
					type: widgetInfo?.role || '未知类型',
					position: { x: data.x, y: data.y },
					text: this.buildWidgetDescription(widgetInfo)
				},
				details: {
					coordinates: { x: data.x, y: data.y },
					button: data.button,
					pressed: data.pressed,
					widget_info: widgetInfo,
					// 添加偏移百分比信息
					offset_x_percent: widgetInfo?.offset_x_percent,
					offset_y_percent: widgetInfo?.offset_y_percent,
					distance_from_center_percent: widgetInfo?.distance_from_center_percent,
					// 添加详细的控件信息
					widget_details: hasValidWidget ? {
						capture_status: widgetInfo.capture_status,
						score: widgetInfo.score,
						process_name: widgetInfo.process_name,
						process_id: widgetInfo.process_id,
						window_role: widgetInfo.window_role,
						window_child_count: widgetInfo.window_child_count,
						states: widgetInfo.states,
						actions: widgetInfo.actions,
						coords: widgetInfo.coords,
						note: widgetInfo.note,
						// 也在widget_details中包含偏移百分比
						offset_x_percent: widgetInfo?.offset_x_percent,
						offset_y_percent: widgetInfo?.offset_y_percent,
						distance_from_center_percent: widgetInfo?.distance_from_center_percent
					} : null
				},
				widget_info: widgetInfo
			};

			// 添加到操作记录窗口
			if (this.operationRecordWindow) {
				this.operationRecordWindow.addOperation(operation);
			}

			// 生成对应的Action YAML
			const actionYaml = this.actionYamlGenerator.generateActionYaml(operation);
			if (actionYaml) {
				this.logService.info(`📝 生成的Action YAML:\n${actionYaml}`);
				// 可以在这里保存到文件或进行其他处理
				this.saveActionYamlToFile(actionYaml, operation);
			} else {
				this.logService.warn(`⚠️ 未生成Action YAML，操作可能被跳过: ${operation.type} - ${operation.action}`);
			}

			// 详细的日志记录
			if (hasValidWidget) {
				this.logService.info(`✅ 成功识别控件: ${widgetInfo.name} (${widgetInfo.role}) 位置(${data.x}, ${data.y}) 得分:${widgetInfo.score}`);
				if (widgetInfo.description) {
					this.logService.info(`   描述: ${widgetInfo.description}`);
				}
				if (widgetInfo.process_name) {
					this.logService.info(`   进程: ${widgetInfo.process_name}`);
				}
				if (widgetInfo.coords && typeof widgetInfo.coords === 'object') {
					this.logService.info(`   控件区域: (${widgetInfo.coords.x}, ${widgetInfo.coords.y}) ${widgetInfo.coords.width}x${widgetInfo.coords.height}`);
				}
			} else {
				this.logService.warn(`❌ 控件识别失败: 位置(${data.x}, ${data.y}) ${widgetInfo?.error || '未知原因'}`);
			}

		} catch (error) {
			this.logService.error(`处理鼠标点击事件时出错: ${error}`);
		}
	}

	/**
	 * 移除双击事件前的多余单击事件
	 * 当检测到双击事件时，移除最近的相同位置的单击事件
	 */
	private removeRedundantClickBeforeDoubleClick(doubleClickData: any): void {
		try {
			if (!this.operationRecordWindow) {
				return;
			}

			// 获取当前操作记录
			const operations = this.operationRecordWindow.getOperations();
			if (operations.length === 0) {
				return;
			}

			// 双击事件的位置和按钮信息
			const doubleClickX = doubleClickData.x;
			const doubleClickY = doubleClickData.y;
			const doubleClickButton = doubleClickData.button;
			const doubleClickTime = this.convertTimestamp(doubleClickData.timestamp);

			// 查找最近的单击事件（从后往前查找）
			for (let i = operations.length - 1; i >= 0; i--) {
				const operation = operations[i];

				// 只检查鼠标单击事件
				if (operation.type !== 'mouse_click') {
					continue;
				}

				// 检查位置是否相近（允许5像素的误差）
				const positionTolerance = 5;
				const operationX = operation.target?.position?.x;
				const operationY = operation.target?.position?.y;

				// 确保位置信息存在
				if (operationX === undefined || operationY === undefined) {
					continue;
				}

				const xDiff = Math.abs(operationX - doubleClickX);
				const yDiff = Math.abs(operationY - doubleClickY);

				if (xDiff <= positionTolerance && yDiff <= positionTolerance) {
					// 检查按钮是否相同
					if (operation.details?.button === doubleClickButton) {
						// 检查时间间隔（双击通常在500ms内）
						const timeDiff = Math.abs(doubleClickTime - operation.timestamp);
						if (timeDiff <= 600) { // 600ms容差
							// 找到了要移除的单击事件
							this.logService.info(`🔧 移除双击前的多余单击事件: 位置(${operation.target?.position?.x}, ${operation.target?.position?.y}) 时间间隔${timeDiff}ms`);

							// 从操作记录窗口中移除该操作
							this.operationRecordWindow.removeOperationByIndex(i);
							break; // 只移除最近的一个匹配的单击事件
						}
					}
				}
			}

		} catch (error) {
			this.logService.error(`移除多余单击事件时出错: ${error}`);
		}
	}

	/**
	 * 处理鼠标拖动事件
	 */
	private handleMouseDragEvent(data: any): void {
		try {
			const widgetInfo = data.widget_info;
			const hasValidWidget = widgetInfo && widgetInfo.is_valid && !widgetInfo.error;

			// 构建拖动操作描述
			let actionDescription = `鼠标拖动 (${data.button})`;
			if (data.start_x !== undefined && data.start_y !== undefined &&
				data.end_x !== undefined && data.end_y !== undefined) {
				actionDescription += ` - 从(${data.start_x}, ${data.start_y})到(${data.end_x}, ${data.end_y})`;
			}
			if (data.distance !== undefined) {
				actionDescription += ` 距离${data.distance.toFixed(1)}px`;
			}
			if (data.duration !== undefined) {
				actionDescription += ` 时长${data.duration.toFixed(2)}s`;
			}

			if (hasValidWidget) {
				// 优先使用Key字段，其次使用name字段
				const controlName = widgetInfo.Key || widgetInfo.name || '未命名控件';
				actionDescription += ` - ${controlName}`;
			}

			// 构建操作记录
			const operation: OperationRecord = {
				id: `drag_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
				timestamp: this.convertTimestamp(data.timestamp),
				type: 'mouse_drag',
				action: actionDescription,
				target: {
					name: widgetInfo?.name || '未知控件',
					type: widgetInfo?.role || '未知类型',
					position: {
						x: data.start_x || data.x || 0,
						y: data.start_y || data.y || 0
					},
					text: this.buildWidgetDescription(widgetInfo)
				},
				details: {
					start_position: { x: data.start_x, y: data.start_y },
					end_position: { x: data.end_x, y: data.end_y },
					distance: data.distance,
					duration: data.duration,
					button: data.button
				},
				widget_info: widgetInfo
			};

			// 添加到操作记录窗口
			if (this.operationRecordWindow) {
				this.operationRecordWindow.addOperation(operation);
			}

			this.logService.info(`处理拖动事件完成: ${actionDescription}`);

		} catch (error) {
			this.logService.error(`处理鼠标拖动事件时出错: ${error}`);
		}
	}

	/**
	 * 处理鼠标悬停事件
	 */
	private handleMouseHoverEvent(data: any): void {
		try {
			const widgetInfo = data.widget_info;
			const hasValidWidget = widgetInfo && widgetInfo.is_valid && !widgetInfo.error;

			// 构建悬停操作描述
			let actionDescription = `鼠标悬停`;
			if (data.x !== undefined && data.y !== undefined) {
				actionDescription += ` - 位置(${data.x}, ${data.y})`;
			}

			if (hasValidWidget) {
				actionDescription += ` - ${widgetInfo.name || '未命名控件'}`;
			}

			// 构建操作记录
			const operation: OperationRecord = {
				id: `hover_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
				timestamp: this.convertTimestamp(data.timestamp),
				type: 'mouse_hover',
				action: actionDescription,
				target: {
					name: widgetInfo?.name || '未知控件',
					type: widgetInfo?.role || '未知类型',
					position: { x: data.x || 0, y: data.y || 0 },
					text: this.buildWidgetDescription(widgetInfo)
				},
				details: {
					coordinates: { x: data.x, y: data.y },
					widget_info: widgetInfo,
					// 添加详细的控件信息
					widget_details: hasValidWidget ? {
						capture_status: widgetInfo.capture_status,
						score: widgetInfo.score,
						process_name: widgetInfo.process_name,
						process_id: widgetInfo.process_id,
						window_role: widgetInfo.window_role,
						window_child_count: widgetInfo.window_child_count,
						states: widgetInfo.states,
						actions: widgetInfo.actions,
						coords: widgetInfo.coords,
						note: widgetInfo.note
					} : null
				},
				widget_info: widgetInfo
			};

			// 添加到操作记录窗口
			if (this.operationRecordWindow) {
				this.operationRecordWindow.addOperation(operation);
			}

			// 生成对应的Action YAML
			const actionYaml = this.actionYamlGenerator.generateActionYaml(operation);
			if (actionYaml) {
				this.logService.info(`📝 生成的悬停Action YAML:\n${actionYaml}`);
				// 可以在这里保存到文件或进行其他处理
				this.saveActionYamlToFile(actionYaml, operation);
			}

			// 详细的日志记录
			if (hasValidWidget) {
				this.logService.info(`✅ 悬停事件成功识别控件: ${widgetInfo.name} (${widgetInfo.role}) 位置(${data.x}, ${data.y}) 得分:${widgetInfo.score}`);
				if (widgetInfo.description) {
					this.logService.info(`   描述: ${widgetInfo.description}`);
				}
				if (widgetInfo.process_name) {
					this.logService.info(`   进程: ${widgetInfo.process_name}`);
				}
				if (widgetInfo.coords && typeof widgetInfo.coords === 'object') {
					this.logService.info(`   控件区域: (${widgetInfo.coords.x}, ${widgetInfo.coords.y}) ${widgetInfo.coords.width}x${widgetInfo.coords.height}`);
				}
			} else {
				this.logService.warn(`❌ 悬停事件控件识别失败: 位置(${data.x}, ${data.y}) ${widgetInfo?.error || '未知原因'}`);
			}

			this.logService.info(`处理悬停事件完成: ${actionDescription}`);

		} catch (error) {
			this.logService.error(`处理鼠标悬停事件时出错: ${error}`);
		}
	}

	/**
	 * 处理键盘事件
	 */
	private handleKeyboardEvent(data: any): void {
		try {
			// 添加调试日志
			this.logService.info(`🎹 收到键盘事件: ${JSON.stringify(data)}`);

			// 检查是否是合并的键盘事件
			const isMerged = data.merged_count && data.merged_count > 1;

			// 构建键盘操作描述
			let actionDescription = `键盘输入`;
			if (isMerged) {
				actionDescription += ` - "${data.key}" (合并了${data.merged_count}个按键)`;
				this.logService.info(`🎹 合并键盘事件: "${data.key}" (${data.merged_count}个按键)`);
			} else if (data.key_combination) {
				actionDescription += ` - ${data.key_combination}`;
				this.logService.info(`🎹 使用组合键: ${data.key_combination}`);
			} else if (data.key) {
				actionDescription += ` - ${data.key}`;
				this.logService.info(`🎹 使用单个按键: ${data.key}`);
			}

			// 构建操作记录
			const operation: OperationRecord = {
				id: `keyboard_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
				timestamp: this.convertTimestamp(data.timestamp),
				type: 'keyboard',
				action: actionDescription,
				target: {
					name: isMerged ? `键盘输入 (合并)` : '键盘输入',
					type: 'keyboard',
					position: { x: 0, y: 0 },
					text: data.key_combination || data.key || ''
				},
				details: {
					key: data.key,
					modifiers: data.modifiers || [],
					key_combination: data.key_combination || data.key,
					event_type: data.event_type,
					// 添加合并相关信息
					is_merged: isMerged,
					merged_count: data.merged_count || 1,
					duration: data.duration || 0
				}
			};

			// 添加到操作记录窗口
			if (this.operationRecordWindow) {
				this.operationRecordWindow.addOperation(operation);
			}

			// 生成对应的Action YAML
			const actionYaml = this.actionYamlGenerator.generateActionYaml(operation);
			if (actionYaml) {
				this.logService.info(`📝 生成的键盘Action YAML:\n${actionYaml}`);
				// 保存到文件
				this.saveActionYamlToFile(actionYaml, operation);
			}

			// 详细的日志记录
			this.logService.info(`✅ 键盘事件处理完成: ${actionDescription}`);

		} catch (error) {
			this.logService.error(`处理键盘事件时出错: ${error}`);
		}
	}

	/**
	 * 保存Action YAML到文件
	 */
	private async saveActionYamlToFile(actionYaml: string, _operation: OperationRecord): Promise<void> {
		try {
			// 获取当前测试用例信息
			if (!this.currentTestCase) {
				this.logService.warn('当前没有活动的测试用例，跳过YAML保存');
				return;
			}

			// 构建文件路径
			const workspaceRoot = this.instantiationService.invokeFunction(accessor => {
				const workspaceService = accessor.get(IWorkspaceContextService);
				return workspaceService.getWorkspace().folders[0]?.uri;
			});

			if (!workspaceRoot) {
				this.logService.error('无法获取工作区根目录');
				return;
			}

			// 创建录制动作文件路径 - 使用固定文件名，所有操作追加到同一个文件
			const recordingDir = joinPath(workspaceRoot, 'recording_actions');
			const fileName = `${this.currentTestCase.id}_actions.yml`;
			const filePath = joinPath(recordingDir, fileName);

			// 确保目录存在
			const fileService = this.instantiationService.invokeFunction(accessor => accessor.get(IFileService));
			try {
				await fileService.createFolder(recordingDir);
			} catch (error) {
				// 目录可能已存在，忽略错误
			}

			// 读取现有文件内容（如果存在）
			let existingContent = '';
			try {
				const existingFile = await fileService.readFile(filePath);
				existingContent = existingFile.value.toString();
			} catch (error) {
				// 文件不存在，使用空内容
				existingContent = `# 录制的Action YAML - 测试用例: ${this.currentTestCase.id}\n# 生成时间: ${new Date().toISOString()}\n\n`;
			}

			// 追加新的action
			const updatedContent = existingContent + actionYaml + '\n';

			// 保存文件
			await this.actionYamlGenerator.saveToFile(filePath, updatedContent);
			this.logService.info(`✅ Action YAML已保存到: ${filePath.toString()}`);
			this.logService.info(`📄 文件内容长度: ${updatedContent.length} 字符`);

		} catch (error) {
			this.logService.error(`保存Action YAML时出错: ${error}`);
		}
	}

	/**
	 * 生成完整的测试用例YAML文件
	 */
	private async generateCompleteTestCaseYaml(): Promise<void> {
		try {
			if (!this.currentTestCase) {
				this.logService.warn('当前没有活动的测试用例，无法生成完整YAML');
				return;
			}

			this.logService.info(`开始生成完整测试用例YAML: ${this.currentTestCase.id}`);
			this.logService.info(`当前测试用例信息: ${JSON.stringify(this.currentTestCase, null, 2)}`);

			// 获取文件服务
			const fileService = this.instantiationService.invokeFunction(accessor => accessor.get(IFileService));

			// 尝试获取原有测试用例文件路径
			let targetFilePath: URI | null = null;

			// 方法1: 使用测试用例中的ymlPath
			if (this.currentTestCase.ymlPath) {
				try {
					targetFilePath = URI.parse(this.currentTestCase.ymlPath);
					this.logService.info(`使用测试用例中的YAML路径: ${targetFilePath.toString()}`);

					// 检查文件是否存在
					const exists = await fileService.exists(targetFilePath);
					if (!exists) {
						this.logService.warn(`测试用例YAML文件不存在: ${targetFilePath.toString()}`);
						targetFilePath = null;
					}
				} catch (error) {
					this.logService.warn(`解析测试用例YAML路径失败: ${error}`);
					targetFilePath = null;
				}
			}

			// 方法2: 如果没有有效的ymlPath，尝试构建路径
			if (!targetFilePath) {
				try {
					const rootUri = await this.pathResolver.getTestcasePath();
					if (rootUri) {
						const typeDir = joinPath(rootUri, (this.currentTestCase.recordType || 'GAT').toUpperCase());
						const appName = Array.isArray((this.currentTestCase as any).TestCaseAppList)
							? ((this.currentTestCase as any).TestCaseAppList[0] || '')
							: ((this.currentTestCase as any).TestCaseAppList || '');
						targetFilePath = joinPath(typeDir, appName, `${this.currentTestCase.id}.yml`);
						this.logService.info(`构建的测试用例YAML路径: ${targetFilePath.toString()}`);

						// 检查文件是否存在
						const exists = await fileService.exists(targetFilePath);
						if (!exists) {
							this.logService.warn(`构建的测试用例YAML文件不存在: ${targetFilePath.toString()}`);
							targetFilePath = null;
						}
					}
				} catch (error) {
					this.logService.warn(`构建测试用例YAML路径失败: ${error}`);
					targetFilePath = null;
				}
			}

			// 如果找到了原有的测试用例文件，更新它
			if (targetFilePath) {
				await this.updateExistingTestCaseFile(targetFilePath);
			} else {
				// 如果没有找到原有文件，创建新文件
				await this.createNewTestCaseFile();
			}

		} catch (error) {
			this.logService.error(`生成完整测试用例YAML时出错: ${error}`);
			this.notificationService.error(`生成测试用例YAML失败: ${error}`);
		}
	}

	/**
	 * 更新现有的测试用例文件
	 */
	private async updateExistingTestCaseFile(filePath: URI): Promise<void> {
		try {
			this.logService.info(`更新现有测试用例文件: ${filePath.toString()}`);

			const fileService = this.instantiationService.invokeFunction(accessor => accessor.get(IFileService));

			// 检查文件是否可写
			try {
				const stat = await fileService.stat(filePath);
				this.logService.info(`文件状态: 大小=${stat.size}, 修改时间=${new Date(stat.mtime).toISOString()}`);
			} catch (statError) {
				this.logService.warn(`无法获取文件状态: ${statError}`);
			}

			// 读取现有文件内容
			let existingContent: string;
			try {
				const existingFile = await fileService.readFile(filePath);
				existingContent = existingFile.value.toString();
				this.logService.info(`现有文件内容长度: ${existingContent.length} 字符`);
			} catch (readError) {
				this.logService.error(`读取文件失败: ${readError}`);
				throw new Error(`无法读取测试用例文件: ${readError}`);
			}

			// 获取所有录制的操作
			const operations = this.operationRecordWindow?.getOperations() || [];
			this.logService.info(`录制的操作数量: ${operations.length}`);
			this.logService.info(`操作记录窗口状态: ${this.operationRecordWindow ? '存在' : '不存在'}`);

			// 详细记录前几个操作
			if (operations.length > 0) {
				operations.slice(0, 3).forEach((op, index) => {
					this.logService.info(`操作 ${index}: ${op.action}, 类型: ${op.type}, 步骤: ${op.stepIndex}, 时间: ${new Date(op.timestamp).toISOString()}`);
				});
			}

			if (operations.length === 0) {
				this.logService.warn('没有录制的操作，跳过文件更新');
				this.notificationService.warn('没有录制的操作，无需更新文件');
				return;
			}

			// 按步骤分组操作
			const operationsByStep = this.groupOperationsByStep(operations);
			this.logService.info(`操作已分组到 ${operationsByStep.size} 个步骤中`);

			// 直接插入action到指定步骤，而不是重新生成步骤字符串
			const updatedContent = this.insertActionsToTargetSteps(existingContent, operationsByStep);
			this.logService.info(`更新后的文件内容长度: ${updatedContent.length} 字符`);

			// 保存更新后的文件
			try {
				await this.actionYamlGenerator.saveToFile(filePath, updatedContent);
				this.logService.info(`✅ 测试用例文件已更新: ${filePath.toString()}`);

				// 显示成功通知
				this.notificationService.info(`测试用例已更新: ${this.currentTestCase?.id}`);

			} catch (saveError) {
				this.logService.error(`保存文件失败: ${saveError}`);

				// 检查是否是权限问题
				if (saveError.toString().includes('EACCES') || saveError.toString().includes('permission denied')) {
					this.notificationService.error(`保存失败：权限不足。请检查文件权限或以管理员身份运行。`);

					// 尝试创建备份文件
					try {
						const backupPath = filePath.with({ path: filePath.path + '.recording_backup' });
						await this.actionYamlGenerator.saveToFile(backupPath, updatedContent);
						this.logService.info(`✅ 录制结果已保存到备份文件: ${backupPath.toString()}`);
						this.notificationService.info(`录制结果已保存到备份文件: ${backupPath.fsPath}`);
					} catch (backupError) {
						this.logService.error(`创建备份文件也失败: ${backupError}`);
					}
				} else {
					this.notificationService.error(`保存测试用例失败: ${saveError}`);
				}

				throw saveError;
			}

		} catch (error) {
			this.logService.error(`更新测试用例文件失败: ${error}`);
			throw error;
		}
	}

	/**
	 * 创建新的测试用例文件
	 */
	private async createNewTestCaseFile(): Promise<void> {
		try {
			this.logService.info('创建新的测试用例文件');

			// 生成完整的测试用例YAML
			const testCaseId = this.currentTestCase!.id;
			const feature = `[录制生成] ${this.currentTestCase!.feature || '自动录制'}`;
			const story = `[录制生成] ${this.currentTestCase!.name || this.currentTestCase!.title || '自动录制测试用例'}`;

			const completeYaml = this.actionYamlGenerator.generateTestCaseTemplate(
				testCaseId,
				feature,
				story
			);

			// 保存到测试用例目录
			const workspaceRoot = this.instantiationService.invokeFunction(accessor => {
				const workspaceService = accessor.get(IWorkspaceContextService);
				return workspaceService.getWorkspace().folders[0]?.uri;
			});

			if (!workspaceRoot) {
				this.logService.error('无法获取工作区根目录');
				return;
			}

			// 构建测试用例文件路径
			const testcaseDir = joinPath(workspaceRoot, 'KylinRobot-v2', 'testcase', 'recording_generated');
			const fileName = `${testCaseId}_generated.yml`;
			const filePath = joinPath(testcaseDir, fileName);

			// 确保目录存在
			const fileService = this.instantiationService.invokeFunction(accessor => accessor.get(IFileService));
			try {
				await fileService.createFolder(testcaseDir);
			} catch (error) {
				// 目录可能已存在，忽略错误
			}

			// 保存完整的测试用例文件
			await this.actionYamlGenerator.saveToFile(filePath, completeYaml);
			this.logService.info(`✅ 新测试用例YAML已生成: ${filePath.toString()}`);

			// 显示通知
			this.notificationService.info(`测试用例YAML已生成: ${fileName}`);

		} catch (error) {
			this.logService.error(`创建新测试用例文件失败: ${error}`);
			throw error;
		}
	}

	/**
	 * 按步骤分组操作记录
	 */
	private groupOperationsByStep(operations: OperationRecord[]): Map<number, { stepName: string; operations: { operation: OperationRecord; index: number }[] }> {
		const groups = new Map<number, { stepName: string; operations: { operation: OperationRecord; index: number }[] }>();

		operations.forEach((operation, index) => {
			const stepIndex = operation.stepIndex ?? 0;
			// 使用操作记录窗口中解析的YAML步骤名称
			let stepName = operation.stepName;

			// 如果操作记录没有stepName，尝试从操作记录窗口获取
			if (!stepName && this.operationRecordWindow) {
				const yamlSteps = (this.operationRecordWindow as any).yamlSteps;
				if (yamlSteps && yamlSteps[stepIndex]) {
					stepName = yamlSteps[stepIndex];
				}
			}

			// 最后的备选方案
			if (!stepName) {
				stepName = `录制步骤${stepIndex + 1}`;
			}

			if (!groups.has(stepIndex)) {
				groups.set(stepIndex, {
					stepName,
					operations: []
				});
			}

			groups.get(stepIndex)!.operations.push({ operation, index });
		});

		return groups;
	}

	/**
	 * 将录制的action直接插入到指定的步骤中
	 */
	private insertActionsToTargetSteps(existingContent: string, operationsByStep: Map<number, { stepName: string; operations: { operation: OperationRecord; index: number }[] }>): string {
		try {
			this.logService.info('开始将录制的action插入到指定步骤中');

			let updatedContent = existingContent;

			// 遍历每个步骤的操作
			operationsByStep.forEach((stepOps, stepIndex) => {
				const targetStepName = stepOps.stepName;
				this.logService.info(`处理步骤 ${stepIndex}: ${targetStepName}, 包含 ${stepOps.operations.length} 个操作`);

				// 先查找目标步骤的缩进，以便智能调整action缩进
				const stepIndent = this.getStepIndent(updatedContent, targetStepName);
				const actionBaseIndent = stepIndent + 2; // 步骤缩进 + 2

				// 生成该步骤的所有action YAML
				let actionsYaml = '';
				stepOps.operations.forEach(({ operation }) => {
					const actionYaml = this.actionYamlGenerator.generateActionYaml(operation);
					if (actionYaml) {
						// 移除action YAML中的时间戳注释，并使用智能缩进
						const cleanActionYaml = this.cleanActionYaml(actionYaml, actionBaseIndent);
						actionsYaml += cleanActionYaml;
					}
				});

				if (actionsYaml.trim()) {
					// 将action插入到指定步骤中
					const originalContent = updatedContent;
					updatedContent = this.insertActionsToSpecificStep(updatedContent, targetStepName, actionsYaml);

					// 检查内容是否真的被修改了，以确认插入是否成功
					if (updatedContent !== originalContent) {
						this.logService.info(`✅ 已将 ${stepOps.operations.length} 个action插入到步骤: ${targetStepName}`);

						// 显示插入后的部分内容用于验证
						const insertedLines = updatedContent.split('\n');
						const targetStepMatch = targetStepName.match(/^步骤(\d+)/);
						if (targetStepMatch) {
							const stepPrefix = `步骤${targetStepMatch[1]}`;
							const stepLineIndex = insertedLines.findIndex(line => line.includes(stepPrefix));
							if (stepLineIndex !== -1) {
								this.logService.info(`📋 插入后的步骤内容预览 (第${stepLineIndex + 1}-${Math.min(stepLineIndex + 10, insertedLines.length)}行):`);
								insertedLines.slice(stepLineIndex, stepLineIndex + 10).forEach((line, index) => {
									this.logService.info(`${stepLineIndex + index + 1}: ${line}`);
								});
							}
						}
					} else {
						this.logService.warn(`❌ 未能将action插入到步骤: ${targetStepName}，步骤匹配失败`);
					}
				}
			});

			return updatedContent;

		} catch (error) {
			this.logService.error(`插入action到指定步骤失败: ${error}`);
			// 如果新方法失败，回退到原来的方法
			return this.insertRecordedStepsIntoYamlFallback(existingContent, operationsByStep);
		}
	}

	/**
	 * 获取步骤的缩进级别
	 */
	private getStepIndent(content: string, targetStepName: string): number {
		try {
			const lines = content.split('\n');

			// 提取步骤编号，只匹配"步骤x"部分
			const stepNumberMatch = targetStepName.match(/^步骤(\d+)/);
			if (!stepNumberMatch) {
				return 4; // 默认缩进
			}

			const stepNumber = stepNumberMatch[1];
			const stepPrefix = `步骤${stepNumber}`;
			const stepPattern = new RegExp(`^(\\s*)- ${stepPrefix}[：:].*$`);

			for (const line of lines) {
				const match = line.match(stepPattern);
				if (match) {
					return match[1].length; // 返回匹配到的缩进长度
				}
			}

			return 4; // 默认缩进
		} catch (error) {
			this.logService.warn(`获取步骤缩进失败: ${error}`);
			return 4; // 默认缩进
		}
	}

	/**
	 * 清理action YAML，移除时间戳注释和调整缩进
	 */
	private cleanActionYaml(actionYaml: string, baseIndent: number = 8): string {
		const lines = actionYaml.split('\n');
		const cleanedLines: string[] = [];

		// 计算各级缩进
		const actionIndent = ' '.repeat(baseIndent);           // action行缩进
		const driverIndent = ' '.repeat(baseIndent + 2);       // driver/kwargs行缩进
		const kwargsContentIndent = ' '.repeat(baseIndent + 4); // kwargs内容缩进

		for (const line of lines) {
			// 跳过时间戳注释行
			if (line.trim().startsWith('#') && line.includes('T') && line.includes('Z')) {
				continue;
			}

			// 智能缩进：根据传入的基础缩进动态调整
			if (line.trim()) {
				const trimmedLine = line.trim();
				if (trimmedLine.startsWith('- action:')) {
					cleanedLines.push(actionIndent + trimmedLine);
				} else if (trimmedLine.startsWith('driver:') || trimmedLine.startsWith('kwargs:')) {
					cleanedLines.push(driverIndent + trimmedLine);
				} else if (trimmedLine.includes(':') && !trimmedLine.startsWith('-')) {
					cleanedLines.push(kwargsContentIndent + trimmedLine);
				} else {
					cleanedLines.push(actionIndent + trimmedLine); // 默认使用action缩进
				}
			}
		}

		return cleanedLines.join('\n') + '\n';
	}

	/**
	 * 将action插入到指定的步骤中
	 */
	private insertActionsToSpecificStep(content: string, targetStepName: string, actionsYaml: string): string {
		try {
			const lines = content.split('\n');

			// 提取步骤编号，只匹配"步骤x"部分
			const stepNumberMatch = targetStepName.match(/^步骤(\d+)/);
			if (!stepNumberMatch) {
				this.logService.warn(`❌ 无法从步骤名称中提取步骤编号: "${targetStepName}"`);
				return content;
			}

			const stepNumber = stepNumberMatch[1];
			const stepPrefix = `步骤${stepNumber}`;

			// 创建匹配"步骤x"的正则表达式（忽略后面的描述）
			const stepPattern = new RegExp(`^(\\s*)- ${stepPrefix}[：:].*$`);
			let targetStepLineIndex = -1;
			let stepIndent = 0;

			this.logService.info(`🔍 开始查找目标步骤: "${targetStepName}"`);
			this.logService.info(`🔍 提取的步骤前缀: "${stepPrefix}"`);
			this.logService.info(`🔍 正则表达式: ${stepPattern.source}`);

			for (let i = 0; i < lines.length; i++) {
				const line = lines[i];
				const match = line.match(stepPattern);

				// 调试：显示每一行的匹配尝试
				if (line.includes(stepPrefix)) {
					this.logService.info(`🔍 第${i + 1}行包含步骤前缀: "${line}"`);
					this.logService.info(`🔍 匹配结果: ${match ? '成功' : '失败'}`);
				}

				if (match) {
					targetStepLineIndex = i;
					stepIndent = match[1].length;
					this.logService.info(`✅ 找到目标步骤 "${targetStepName}" 在第 ${i + 1} 行，缩进: ${stepIndent}`);
					break;
				}
			}

			if (targetStepLineIndex === -1) {
				this.logService.warn(`❌ 未找到目标步骤: ${targetStepName}`);
				this.logService.info(`📋 文件内容预览 (前20行):`);
				lines.slice(0, 20).forEach((line, index) => {
					this.logService.info(`${index + 1}: ${line}`);
				});
				return content;
			}

			// 检查步骤行是否以 null 或 ~ 结尾，如果是则移除这些标识
			const stepLine = lines[targetStepLineIndex];
			if (stepLine.trim().endsWith('null') || stepLine.trim().endsWith('~')) {
				this.logService.info(`🔧 检测到步骤行末尾包含 'null' 或 '~'，将移除这些标识`);
				this.logService.info(`🔧 原始步骤行: "${stepLine}"`);

				// 移除行末的 null 或 ~ 标识
				lines[targetStepLineIndex] = stepLine.replace(/:\s*(null|~)\s*$/, ':');

				this.logService.info(`🔧 修改后步骤行: "${lines[targetStepLineIndex]}"`);
			}

			// 找到步骤内容的结束位置
			let insertPosition = targetStepLineIndex + 1;
			let lastContentLineIndex = targetStepLineIndex; // 记录步骤块中最后一行有内容的位置

			this.logService.info(`🔍 开始查找步骤块的结束位置，从第 ${targetStepLineIndex + 2} 行开始扫描`);

			// 查找该步骤的结束位置
			for (let i = targetStepLineIndex + 1; i < lines.length; i++) {
				const line = lines[i];
				const lineIndent = line.search(/\S/);

				this.logService.info(`🔍 第${i + 1}行: "${line}" (缩进: ${lineIndent}, 步骤缩进: ${stepIndent})`);

				// 跳过空行，但不更新插入位置
				if (line.trim() === '') {
					this.logService.info(`🔍 第${i + 1}行是空行，跳过`);
					continue;
				}

				// 如果遇到同级或更高级的内容，说明当前步骤结束了
				if (lineIndent <= stepIndent) {
					this.logService.info(`🔍 第${i + 1}行缩进 ${lineIndent} <= 步骤缩进 ${stepIndent}，步骤块结束`);
					break;
				}

				// 如果是当前步骤的内容，记录这一行的位置
				if (lineIndent > stepIndent) {
					lastContentLineIndex = i;
					this.logService.info(`🔍 第${i + 1}行是步骤内容，更新最后内容行位置为 ${i + 1}`);
				}
			}

			// 插入位置应该在最后一行内容之后
			insertPosition = lastContentLineIndex + 1;

			// 插入action
			const actionLines = actionsYaml.split('\n').filter(line => line.trim());
			lines.splice(insertPosition, 0, ...actionLines);

			this.logService.info(`✅ 在第 ${insertPosition + 1} 行插入了 ${actionLines.length} 行action（在步骤块的最后）`);

			return lines.join('\n');

		} catch (error) {
			this.logService.error(`插入action到步骤 "${targetStepName}" 失败: ${error}`);
			return content;
		}
	}



	/**
	 * 回退方法：如果新方法失败，使用原来的方法
	 */
	private insertRecordedStepsIntoYamlFallback(existingContent: string, operationsByStep: Map<number, { stepName: string; operations: { operation: OperationRecord; index: number }[] }>): string {
		this.logService.info('使用回退方法插入录制步骤');

		// 生成录制的步骤YAML（原来的方法）
		let recordedStepsYaml = '';
		operationsByStep.forEach((stepOps, stepIndex) => {
			const stepName = stepOps.stepName || `录制步骤${stepIndex + 1}`;
			recordedStepsYaml += `    - ${stepName}:\n`;

			stepOps.operations.forEach(({ operation }) => {
				const actionYaml = this.actionYamlGenerator.generateActionYaml(operation);
				if (actionYaml) {
					recordedStepsYaml += actionYaml;
				}
			});
		});

		return this.insertRecordedStepsIntoYaml(existingContent, recordedStepsYaml);
	}

	/**
	 * 将录制的步骤插入到现有YAML中（原方法，保留作为回退）
	 */
	private insertRecordedStepsIntoYaml(existingContent: string, recordedStepsYaml: string): string {
		try {
			// 查找steps部分
			const stepsMatch = existingContent.match(/(\s*)steps:\s*\n/);
			if (!stepsMatch) {
				// 如果没有找到steps部分，在文件末尾添加
				return existingContent + '\n  steps:\n' + recordedStepsYaml;
			}

			const stepsIndent = stepsMatch[1];
			const stepsIndex = stepsMatch.index! + stepsMatch[0].length;

			// 查找steps部分的结束位置
			const lines = existingContent.split('\n');
			const stepsLineIndex = existingContent.substring(0, stepsIndex).split('\n').length - 1;

			let endIndex = lines.length;
			for (let i = stepsLineIndex + 1; i < lines.length; i++) {
				const line = lines[i];
				// 如果遇到同级别或更高级别的键，说明steps部分结束了
				if (line.trim() && !line.startsWith(stepsIndent + '  ') && !line.startsWith(stepsIndent + '    ')) {
					endIndex = i;
					break;
				}
			}

			// 插入录制的步骤
			const beforeSteps = lines.slice(0, stepsLineIndex + 1).join('\n');
			const afterSteps = lines.slice(endIndex).join('\n');

			let result = beforeSteps;
			if (recordedStepsYaml.trim()) {
				result += '\n' + recordedStepsYaml.trimEnd();
			}
			if (afterSteps.trim()) {
				result += '\n' + afterSteps;
			}

			return result;

		} catch (error) {
			this.logService.error(`插入录制步骤到YAML失败: ${error}`);
			// 如果插入失败，在文件末尾添加
			return existingContent + '\n# 录制的步骤:\n  steps:\n' + recordedStepsYaml;
		}
	}

	/**
	 * 构建控件描述文本
	 */
	private buildWidgetDescription(widgetInfo: any): string {
		if (!widgetInfo || !widgetInfo.is_valid) {
			return widgetInfo?.error || '控件识别失败';
		}

		const parts: string[] = [];

		if (widgetInfo.description) {
			parts.push(widgetInfo.description);
		}

		if (widgetInfo.states && widgetInfo.states.length > 0) {
			parts.push(`状态: ${widgetInfo.states.join(', ')}`);
		}

		if (widgetInfo.actions && widgetInfo.actions.length > 0) {
			const actionList = widgetInfo.actions.slice(0, 3); // 只显示前3个操作
			parts.push(`操作: ${actionList.join(', ')}`);
		}

		if (widgetInfo.process_name) {
			parts.push(`进程: ${widgetInfo.process_name}`);
		}

		return parts.length > 0 ? parts.join(' | ') : '无额外信息';
	}

	/**
	 * 处理状态事件
	 */
	private handleStatusEvent(data: any): void {
		this.logService.info(`Python录制状态: ${data.message}`);

		// 只记录日志，不添加到操作记录中，避免触发窗口状态变化导致录制停止
		// 状态消息应该只用于调试和监控，不应该影响录制流程

		// 如果需要在UI中显示状态，可以通过其他方式，比如状态栏或通知
		// 但不应该作为操作记录添加到录制序列中
		if (this.operationRecordWindow && data.message) {
			// 可以考虑添加一个专门的状态显示区域，而不是作为操作记录
			this.logService.info(`录制状态更新: ${data.message}`);
		}
	}

	/**
	 * 处理错误事件
	 */
	private handleErrorEvent(data: any): void {
		this.logService.error(`Python录制错误: ${data.message}`);
		this.notificationService.error(`录制错误: ${data.message}`);
	}

	/**
	 * 处理录制完成事件
	 */
	private handleRecordingCompleteEvent(data: any): void {
		// 添加调试日志
		this.logService.info(`[DEBUG] handleRecordingCompleteEvent 接收到的数据: ${JSON.stringify(data)}`);

		// 处理不同的数据结构
		const message = data.message || data.data?.message || '录制完成';
		const sessionId = data.session_id || data.data?.session_id || '未知';

		this.logService.info(`Python录制完成: ${message}, 会话ID: ${sessionId}`);

		// 显示录制完成通知
		this.notificationService.info(`录制完成！会话ID: ${sessionId}`);

		// 在操作记录窗口中添加录制完成的信息记录（仅用于显示，不写入测试用例）
		if (this.operationRecordWindow) {
			const completionRecord: OperationRecord = {
				id: `recording_complete_${Date.now()}`,
				timestamp: Date.now(),
				type: 'window',
				action: '录制完成',
				target: {
					name: '录制系统',
					type: 'completion',
					position: { x: 0, y: 0 },
					text: `${message} (会话ID: ${sessionId})`
				},
				details: {
					session_id: data.session_id,
					message: data.message,
					isRecordingComplete: true // 标记为录制完成事件，用于过滤
				},
				widget_info: null
			};

			// 添加到操作记录窗口显示，但这个记录会被过滤，不会写入测试用例
			this.operationRecordWindow.addOperation(completionRecord);
		}

		// 可以在这里触发其他录制完成后的处理逻辑
		// 比如自动保存、统计信息等
	}

	/**
	 * 处理录制结果事件
	 */
	private handleRecordingResultEvent(data: any): void {
		// 处理不同的数据结构
		const sessionId = data.session_id || data.data?.session_id || '未知';
		const mouseCount = data.mouse_events_count || data.data?.mouse_events_count || 0;
		const keyboardCount = data.keyboard_events_count || data.data?.keyboard_events_count || 0;
		const startTime = data.start_time || data.data?.start_time;
		const endTime = data.end_time || data.data?.end_time;

		this.logService.info(`录制结果统计: 会话ID=${sessionId}, 鼠标事件=${mouseCount}, 键盘事件=${keyboardCount}`);

		// 在操作记录窗口中添加录制统计信息（仅用于显示，不写入测试用例）
		if (this.operationRecordWindow) {
			const resultRecord: OperationRecord = {
				id: `recording_result_${Date.now()}`,
				timestamp: Date.now(),
				type: 'window',
				action: '录制统计',
				target: {
					name: '录制系统',
					type: 'statistics',
					position: { x: 0, y: 0 },
					text: `鼠标事件: ${mouseCount}, 键盘事件: ${keyboardCount}`
				},
				details: {
					session_id: sessionId,
					start_time: startTime,
					end_time: endTime,
					test_case_id: data.test_case_id || data.data?.test_case_id,
					mouse_events_count: mouseCount,
					keyboard_events_count: keyboardCount,
					isRecordingResult: true // 标记为录制结果事件，用于过滤
				},
				widget_info: null
			};

			// 添加到操作记录窗口显示，但这个记录会被过滤，不会写入测试用例
			this.operationRecordWindow.addOperation(resultRecord);
		}

		// 显示详细的录制统计通知
		if (startTime && endTime) {
			const startTimeStr = new Date(startTime * 1000).toLocaleTimeString();
			const endTimeStr = new Date(endTime * 1000).toLocaleTimeString();
			const message = `录制统计 - 开始: ${startTimeStr}, 结束: ${endTimeStr}, 鼠标事件: ${mouseCount}, 键盘事件: ${keyboardCount}`;
			this.notificationService.info(message);
		} else {
			const message = `录制统计 - 鼠标事件: ${mouseCount}, 键盘事件: ${keyboardCount}`;
			this.notificationService.info(message);
		}
	}

	/**
	 * 处理录制开始事件
	 */
	private handleRecordingStartedEvent(data: any): void {
		this.logService.info(`录制开始: 时长=${data.duration}秒, 预计结束时间=${new Date(data.expected_end_time * 1000).toLocaleTimeString()}`);

		// 显示录制开始通知
		this.notificationService.info(`录制已开始，预计录制${data.duration}秒`);

		// 更新录制状态为"录制中"，显示"请选取高亮控件进行操作"
		if (this.operationRecordWindow) {
			this.operationRecordWindow.setRecordingStatus('recording');
		}

		// 在操作记录窗口中添加录制开始记录（仅用于显示，不写入测试用例）
		if (this.operationRecordWindow) {
			const startRecord: OperationRecord = {
				id: `recording_started_${Date.now()}`,
				timestamp: this.convertTimestamp(data.start_time),
				type: 'window',
				action: '录制开始',
				target: {
					name: '录制系统',
					type: 'start',
					position: { x: 0, y: 0 },
					text: `录制时长: ${data.duration}秒`
				},
				details: {
					duration: data.duration,
					start_time: data.start_time,
					expected_end_time: data.expected_end_time,
					isRecordingStart: true // 标记为录制开始事件，用于过滤
				},
				widget_info: null
			};

			this.operationRecordWindow.addOperation(startRecord);
		}
	}

	/**
	 * 处理录制进度事件
	 */
	private handleRecordingProgressEvent(data: any): void {
		this.logService.info(`录制进度: ${data.progress_percent}% (${data.elapsed_seconds}/${data.elapsed_seconds + data.remaining_seconds}秒)`);

		// 可以在这里更新进度条或状态显示
		// 暂时不在操作记录中显示进度事件，避免界面过于繁杂
	}

	/**
	 * 转换Python时间戳为JavaScript时间戳
	 */
	private convertTimestamp(pythonTimestamp: number | undefined): number {
		if (!pythonTimestamp) {
			return Date.now();
		}

		// Python时间戳是秒级的，JavaScript时间戳是毫秒级的
		// 如果时间戳小于某个阈值，说明是秒级的，需要转换为毫秒
		const threshold = 1e12; // 2001年的毫秒时间戳，用作判断阈值

		if (pythonTimestamp < threshold) {
			// 秒级时间戳，转换为毫秒
			return Math.floor(pythonTimestamp * 1000);
		} else {
			// 已经是毫秒级时间戳
			return Math.floor(pythonTimestamp);
		}
	}

	/**
	 * 处理旧格式事件（兼容性）
	 */
	private handleLegacyEvent(eventData: any): void {
		// 添加调试日志
		this.logService.info(`[DEBUG] handleLegacyEvent 接收到的事件: ${JSON.stringify(eventData)}`);

		// 特殊处理录制完成和录制结果事件
		if (eventData.type === 'recording_complete') {
			this.logService.info(`[DEBUG] 检测到 recording_complete 事件，转发到专用处理器`);
			this.handleRecordingCompleteEvent(eventData.data || eventData);
			return;
		}

		if (eventData.type === 'recording_result') {
			this.logService.info(`[DEBUG] 检测到 recording_result 事件，转发到专用处理器`);
			this.handleRecordingResultEvent(eventData.data || eventData);
			return;
		}

		const operation: OperationRecord = {
			id: eventData.id || `legacy_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
			timestamp: this.convertTimestamp(eventData.timestamp),
			type: this.mapPythonEventType(eventData.type),
			action: eventData.action || eventData.event_type || '未知操作',
			target: {
				name: eventData.target_name || eventData.window_title || '未知目标',
				type: eventData.target_type || eventData.element_type || '未知类型',
				position: eventData.position || eventData.coordinates || { x: 0, y: 0 },
				text: eventData.text || eventData.content || ''
			},
			details: eventData,
			widget_info: eventData.widget_info
		};

		// 添加到操作记录窗口
		if (this.operationRecordWindow) {
			this.operationRecordWindow.addOperation(operation);
		}

		this.logService.info(`处理旧格式Python录制事件: ${operation.action}`);
	}

	/**
	 * 映射Python事件类型到操作记录类型
	 */
	private mapPythonEventType(pythonType: string): 'mouse' | 'keyboard' | 'window' | 'menu' {
		if (!pythonType) {
			return 'mouse';
		}

		const type = pythonType.toLowerCase();

		// 特殊处理录制相关事件
		if (type === 'recording_complete' || type === 'recording_result') {
			return 'window';
		}

		if (type.includes('mouse') || type.includes('click') || type.includes('drag')) {
			return 'mouse';
		} else if (type.includes('key') || type.includes('type') || type.includes('input')) {
			return 'keyboard';
		} else if (type.includes('window') || type.includes('focus') || type.includes('activate')) {
			return 'window';
		} else if (type.includes('menu') || type.includes('context')) {
			return 'menu';
		} else {
			return 'mouse'; // 默认类型
		}
	}



	/**
	 * 向录制工具条发送状态信息
	 * @param message 状态信息文本
	 */
	private sendStatusMessage(message: string): void {
		// 记录日志
		this.logService.info(`发送状态信息到录制工具条: ${message}`);

		// 使用所有可能的方式发送消息，确保消息能够被接收
		// this.sendViaMessageService(message);
		this.sendViaDirectUpdate(message);
		// this.sendViaPostMessage(message);
		// this.sendViaElectronIPC(message);

	}

	/**
	 * 通过消息服务发送状态信息
	 */
	// private sendViaMessageService(message: string): void {
	// 	try {
	// 		this.messageService.sendMessage(GatMessageType.StatusMessage, message);
	// 		this.logService.info(`通过消息服务发送状态信息成功: ${message}`);
	// 	} catch (msgError) {
	// 		this.logService.error(`通过消息服务发送状态信息失败: ${msgError}`);
	// 	}
	// }

	/**
	 * 通过直接更新工具条发送状态信息
	 */
	private sendViaDirectUpdate(message: string): void {
		// 如果工具条窗口存在，尝试直接更新
		if (this.toolbarWindow && !this.toolbarWindow.closed) {
			try {
				// 直接调用录制工具条的更新函数
				const anyWindow = this.toolbarWindow as any;
				if (anyWindow.updateStatusMessage) {
					anyWindow.updateStatusMessage(message);
					this.logService.info(`通过直接调用更新函数发送状态信息: ${message}`);
				}

				// 尝试直接操作 DOM
				if (this.toolbarWindow.document) {
					const statusDisplay = this.toolbarWindow.document.getElementById('statusDisplay');
					if (statusDisplay) {
						// 安全地设置文本内容
						while (statusDisplay.firstChild) {
							statusDisplay.removeChild(statusDisplay.firstChild);
						}
						statusDisplay.appendChild(this.toolbarWindow.document.createTextNode(message));
						this.logService.info(`通过直接操作 DOM 显示状态信息: ${message}`);
					}
				}
			} catch (error) {
				this.logService.error(`直接更新工具条状态信息时出错: ${error instanceof Error ? error.message : String(error)}`);
			}
		}
	}

	/**
	 * 通过postMessage发送状态信息
	 */
	// private sendViaPostMessage(message: string): void {
	// 	try {
	// 		// 如果工具条窗口存在，尝试使用postMessage发送消息
	// 		if (this.toolbarWindow && !this.toolbarWindow.closed) {
	// 			// 使用录制工具条的消息格式
	// 			this.toolbarWindow.postMessage({ type: 'statusMessage', message }, '*');
	// 			this.logService.info(`通过postMessage发送状态信息成功: ${message}`);

	// 			// 使用新的消息格式
	// 			this.toolbarWindow.postMessage({
	// 				type: GatMessageType.StatusMessage,
	// 				data: message,
	// 				source: 'TestCaseRecorder',
	// 				timestamp: Date.now()
	// 			}, '*');
	// 		}
	// 	} catch (error) {
	// 		this.logService.error(`通过postMessage发送状态信息失败: ${error instanceof Error ? error.message : String(error)}`);
	// 	}
	// }

	/**
	 * 通过Electron IPC发送状态信息
	 */
	// private sendViaElectronIPC(message: string): void {
	// 	try {
	// 		// 尝试使用Electron IPC发送消息
	// 		if (window.electronAPI && window.electronAPI.ipcRenderer) {
	// 			window.electronAPI.ipcRenderer.send('gat:statusMessage', message);
	// 			this.logService.info(`通过Electron IPC发送状态信息成功: ${message}`);
	// 		}
	// 	} catch (ipcError) {
	// 		this.logService.error(`通过Electron IPC发送状态信息失败: ${ipcError}`);
	// 	}
	// }





	/**
	 * 销毁录制器
	 */
	override dispose(): void {
		this.stopRecording();

		if (this.insertMethodWindow) {
			this.insertMethodWindow.dispose();
			this.insertMethodWindow = null;
		}

		// 停止全流程录制
		if (this.pythonRecordingProcess) {
			this.stopFullRecording();
		}

		// 清理操作记录窗口
		this.hideOperationRecordWindow();

		this._disposables.dispose();
		super.dispose();
	}

	/**
	 * 获取IPC渲染器
	 */
	private getIpcRenderer(): any {
		// 优先使用ElectronAPI（自定义preload），然后是VSCode API
		if (typeof window !== 'undefined' && (window as any).electronAPI && (window as any).electronAPI.ipcRenderer) {
			return (window as any).electronAPI.ipcRenderer;
		} else if (typeof window !== 'undefined' && (window as any).vscode && (window as any).vscode.ipcRenderer) {
			return (window as any).vscode.ipcRenderer;
		}
		return null;
	}

	/**
	 * 清理Python录制监听器，避免重复监听
	 */
	private cleanupPythonRecordingListeners(): void {
		const ipcRenderer = this.getIpcRenderer();
		if (ipcRenderer) {
			try {
				// 移除所有Python录制相关的监听器
				ipcRenderer.removeAllListeners('gat:python-recording-output');
				ipcRenderer.removeAllListeners('gat:python-recording-error');
				ipcRenderer.removeAllListeners('gat:python-recording-exit');
				ipcRenderer.removeAllListeners('gat:python-recording-paused');
				ipcRenderer.removeAllListeners('gat:python-recording-resumed');

				this.logService.info('已清理Python录制监听器');
			} catch (error) {
				this.logService.warn(`清理Python录制监听器时出错: ${error}`);
			}
		}
	}

	/**
	 * 处理录制控制器窗口位置更新
	 */
	private handleRecorderWindowBoundsUpdate(bounds: { x: number; y: number; width: number; height: number }): void {
		if (!this.isRecording) {
			return;
		}

		try {
			// 通过 IPC 将窗口位置信息发送给后端 Python 进程
			const ipcRenderer = this.getIpcRenderer();
			if (ipcRenderer) {
				ipcRenderer.send('gat:update-recorder-window-bounds', {
					bounds: bounds
				});

				this.logService.debug(`已发送录制控制器窗口位置信息给后端: x=${bounds.x}, y=${bounds.y}, width=${bounds.width}, height=${bounds.height}`);
			}
		} catch (error) {
			this.logService.error(`发送窗口位置信息给后端失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 清空 recording_actions 目录
	 * 在开始新的录制会话时调用，确保目录为空状态
	 */
	private async clearRecordingActionsDirectory(): Promise<void> {
		try {
			// 获取工作区根目录
			const workspaceRoot = this.instantiationService.invokeFunction(accessor => {
				const workspaceService = accessor.get(IWorkspaceContextService);
				return workspaceService.getWorkspace().folders[0]?.uri;
			});

			if (!workspaceRoot) {
				this.logService.warn('无法获取工作区根目录，跳过清空 recording_actions 目录');
				return;
			}

			// 构建 recording_actions 目录路径
			const recordingDir = joinPath(workspaceRoot, 'recording_actions');
			const fileService = this.instantiationService.invokeFunction(accessor => accessor.get(IFileService));

			this.logService.info(`🧹 开始清空 recording_actions 目录: ${recordingDir.toString()}`);

			try {
				// 检查目录是否存在
				const dirStat = await fileService.resolve(recordingDir);
				if (dirStat.isDirectory && dirStat.children) {
					// 删除目录中的所有文件和子目录
					let deletedCount = 0;
					for (const child of dirStat.children) {
						try {
							await fileService.del(child.resource, { recursive: true, useTrash: false });
							deletedCount++;
							this.logService.debug(`删除文件: ${child.resource.toString()}`);
						} catch (deleteError) {
							this.logService.warn(`删除文件失败: ${child.resource.toString()}, 错误: ${deleteError}`);
						}
					}
					this.logService.info(`✅ 已清空 recording_actions 目录，删除了 ${deletedCount} 个文件/目录`);
				} else {
					this.logService.info(`📁 recording_actions 目录为空或不存在`);
				}
			} catch (resolveError) {
				// 目录不存在，创建它
				this.logService.info(`📁 recording_actions 目录不存在，正在创建...`);
				try {
					await fileService.createFolder(recordingDir);
					this.logService.info(`✅ 已创建 recording_actions 目录: ${recordingDir.toString()}`);
				} catch (createError) {
					this.logService.error(`创建 recording_actions 目录失败: ${createError}`);
				}
			}

		} catch (error) {
			this.logService.error(`清空 recording_actions 目录时出错: ${error instanceof Error ? error.message : String(error)}`);
		}
	}
}
