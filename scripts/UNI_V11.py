#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
UNI (Universal Interface) - 通用控件识别库
支持X11和Wayland显示服务器

主要功能:
- 自动检测显示服务器类型 (X11/Wayland)
- 跨平台控件识别和操作
- 无障碍技术 (AT-SPI) 集成
- 兼容性处理和性能优化

作者: 吴德基等
日期: 2024.10.31
更新: 2024.11 - 添加Wayland支持

注意事项:
- Wayland环境下某些功能受到安全限制
- 建议安装 grim、gnome-screenshot 等截图工具以获得最佳体验
"""

import os
import sys
import time
import subprocess
import json
import re
#import cv2
import pyatspi

# 检测显示服务器类型
def detect_display_server():
    """检测当前显示服务器类型"""
    # 优先检查环境变量
    if os.environ.get('WAYLAND_DISPLAY'):
        return 'wayland'
    elif os.environ.get('DISPLAY'):
        return 'x11'

    # 检查XDG_SESSION_TYPE
    session_type = os.environ.get('XDG_SESSION_TYPE', '').lower()
    if session_type == 'wayland':
        return 'wayland'
    elif session_type == 'x11':
        return 'x11'

    # 检查当前运行的显示服务器进程
    try:
        # 检查是否有Wayland相关进程
        result = subprocess.run(['pgrep', '-f', 'wayland|weston|sway|gnome-shell|kwin_wayland'],
                               capture_output=True, text=True)
        if result.returncode == 0:
            return 'wayland'

        # 检查是否有X11相关进程
        result = subprocess.run(['pgrep', '-f', 'Xorg|X11|Xwayland'],
                               capture_output=True, text=True)
        if result.returncode == 0:
            return 'x11'
    except Exception as e:
        print(e)

    # 检查是否在SSH会话中
    if os.environ.get('SSH_CONNECTION') or os.environ.get('SSH_CLIENT'):
        # SSH会话，尝试检测远程显示类型
        if os.environ.get('DISPLAY'):
            return 'x11'
        elif os.environ.get('WAYLAND_DISPLAY'):
            return 'wayland'

    # 如果没有图形环境，默认假设X11（向后兼容）
    print("[WARNING] 无法检测显示服务器类型，默认使用X11模式", file=sys.stderr)
    return 'x11'

# 根据显示服务器类型进行条件导入
DISPLAY_SERVER = detect_display_server()
X11_AVAILABLE = False
WNCK_AVAILABLE = False

if DISPLAY_SERVER == 'x11':
    try:
        import gi
        gi.require_version('Wnck', '3.0')
        from gi.repository import Wnck
        from Xlib import display, X
        from Xlib.protocol import event
        X11_AVAILABLE = True
        WNCK_AVAILABLE = True
        print(f"[INFO] X11环境检测成功，已加载X11相关库", file=sys.stderr)
    except ImportError as e:
        print(f"[WARNING] X11库导入失败: {e}，将使用Wayland兼容模式", file=sys.stderr)
        X11_AVAILABLE = False
        WNCK_AVAILABLE = False
else:
    print(f"[INFO] 检测到Wayland环境，使用Wayland兼容模式", file=sys.stderr)


class UNI:
    def __init__(self):
        self.last_desktopEle = None
        self.last_appEle = []
        self.childEle = None
        self.finddata = None
        self.findextents = None
        self.findfind = None

        # 缓存进程信息，减少外部进程调用
        self.process_cache = {}
        # 缓存窗口信息，减少重复查询
        self.window_cache = {}
        # 缓存桌面对象，减少AT-SPI查询
        self.desktop_cache = {}
        self.desktop_cache_timeout = 0.1  # 桌面缓存超时时间（100ms）
        # 缓存桌面对象，减少AT-SPI查询
        self.desktop_cache = {}
        self.desktop_cache_timeout = 0.1  # 桌面缓存超时时间（100ms）
        # 缓存用户名和主机名
        self.username = None
        self.hostname = None
        # 性能计时器
        self.timers = {}
        # 最大重试次数
        self.max_retries = 3

        # 显示服务器类型
        self.display_server = DISPLAY_SERVER
        self.x11_available = X11_AVAILABLE
        self.wnck_available = WNCK_AVAILABLE

        print(f"[UNI] 初始化完成，显示服务器: {self.display_server}", file=sys.stderr)
    
    def kdk_getElement_Uni(self, x, y, quick=False, menutype=None):
        """
        描述: 根据(x, y)获取控件信息
        作者: 吴德基
        日期: 2024.10.31
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: data（dict），文本信息（str）
        """
        print(f"[UNI] 开始查找坐标({x}, {y})处的控件", file=sys.stderr)

        if menutype:
            try:
                with open("/tmp/.recordmenu.txt", 'r') as json_file:
                    menudata = json.load(json_file)
                data = None
                if menudata:
                    try:
                        for value in menudata:
                            if menudata[value]["Coords"]["x"] <= x <= menudata[value]["Coords"]["x"]+menudata[value]["Coords"]["width"] and menudata[value]["Coords"]["y"] <= y <= menudata[value]["Coords"]["y"]+menudata[value]["Coords"]["height"]:
                                data = menudata[value]
                                # 补充信息
                                data["RecordPosition"] = (x, y)
                                data["MenuElement"] = "1"
                                if menudata[value]["Rolename"] == "popup menu":
                                    continue
                                elif menudata[value]["Rolename"] == "menu item":
                                    break
                        if data:
                            return data, f"找到"
                        else:
                            return None, f"未捕获到控件"
                    except Exception as e:
                            print(f"菜单控件列表异常： {e}")
                            return {"error": f"菜单控件列表异常: {e}", "capture_status": "error"}, f"菜单控件列表异常: {e}"
            except Exception as e:
                print(f"监听文件无内容，非菜单控件： {e}")
            try:
                with open("/tmp/.recordmenu1.txt", 'r') as json_file:
                    menudata = json.load(json_file)
                data = None
                if menudata:
                    try:
                        for value in menudata:
                            if menudata[value]["Coords"]["x"] <= x <= menudata[value]["Coords"]["x"]+menudata[value]["Coords"]["width"] and menudata[value]["Coords"]["y"] <= y <= menudata[value]["Coords"]["y"]+menudata[value]["Coords"]["height"]:
                                data = menudata[value]
                                # 补充信息
                                data["RecordPosition"] = (x, y)
                                data["MenuElement"] = "2"
                        if data:
                            return data, f"找到"
                        else:
                            return None, f"未捕获到控件"
                    except Exception as e:
                            print(f"菜单控件列表异常： {e}")
                            return {"error": f"菜单控件列表异常: {e}", "capture_status": "error"}, f"菜单控件列表异常: {e}"
            except Exception as e:
                print(f"监听文件无内容，非菜单控件： {e}")

        try:
            # 获取当前active窗口控件、进程ID、区域、类型、子控件数量
            active_window, processid, activewindow_region, windowRoleName, windowChildCount,offxy = UNI._get_active_window3(self, x, y)
            print(f"[UNI] 获取到活动窗口: {active_window}, 进程ID: {processid}", file=sys.stderr)

            if active_window:
                if active_window.name != "AI 助手" or (active_window.name == "AI 助手" and activewindow_region[1] < y < activewindow_region[1]+40):
                    # 从当前窗口中获取控件信息
                    self.childEle = None
                    UNI._find_accessible_at_point(self, active_window, x-offxy[0], y-offxy[1], activewindow_region)
                else:
                    # AI助手特殊处理
                    aiwindow, airegion, offsetx, offsety = UNI._aiassistantwindowdeal(self, active_window, activewindow_region)
                    self.childEle = None
                    UNI._find_accessible_at_point(self, aiwindow, x+offsetx, y+offsety, airegion)
                    active_window = aiwindow
                
                print(f"[UNI] 查找控件结果: {self.childEle}", file=sys.stderr)
                if self.childEle:
                    if quick:
                        extents = self.childEle.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                        print(f"[UNI] 快速模式返回: {extents}", file=sys.stderr)
                        return extents, f"找到"
                    else:
                        data = UNI._extract_element_info(self, self.childEle)
                        # 补充窗口信息到控件信息中
                        data["RecordPosition"] = (x, y)
                        data["WindowRoleName"] = windowRoleName
                        data["WindowChildCount"] = windowChildCount
                        print(f"[UNI] 提取到控件信息: {data}", file=sys.stderr)
                        if data["ProcessName"] == "mate-terminal":
                            username = os.popen("whoami").readline().strip()
                            hostname = os.popen("hostname").readline().strip()
                            data["WindowName"] = active_window.name.replace(hostname, "hostname").replace(username, "username")
                        elif data["ProcessName"] == "peony":
                            username = os.popen("whoami").readline().strip()
                            data["WindowName"] = active_window.name.replace(username, "username")
                        #elif "文本编辑器" in active_window.name:
                        #    data["WindowName"] = "文本编辑器"
                        elif "WPS" in active_window.name:
                            # WPS单独处理
                            data["WindowName"] = "WPS"
                        else:
                            data["WindowName"] = active_window.name
                        print(f"[UNI] 提取到控件信息带窗口: {data}", file=sys.stderr)
                        # 确保关键字段存在
                        if "Name" not in data or not data["Name"] or data["Name"] == "N/A":
                            # 尝试从其他信息中生成有意义的名称
                            generated_name = self._generate_meaningful_name(self.childEle, data, x, y)
                            data["Name"] = generated_name
                            # 如果生成的名称仍然是通用的，不将其标记为错误
                            if generated_name.startswith("Control_"):
                                data["capture_status"] = "partial"  # 部分成功
                                data["note"] = "控件可见且可交互，但缺少名称信息。"
                            else:
                                data["capture_status"] = "success"  # 成功
                            print(f"[UNI] 控件名称生成为: {data['Name']}", file=sys.stderr)
                        else:
                            data["capture_status"] = "success"  # 完全成功
                        # 确保Key字段存在
                        if "Key" not in data:
                            data["Key"] = data["Name"]
                            print(f"[UNI] 添加Key字段: {data['Key']}", file=sys.stderr)

                        if UNI._checkdata(self, data, self.childEle):
                            print(f"[UNI] 控件信息验证通过", file=sys.stderr)
                            return data, f"找到"
                        else:
                            print(f"[UNI] 控件信息验证失败", file=sys.stderr)
                            # 返回包含错误信息的字典
                            error_data = data.copy() # 复制已有信息
                            error_data["error"] = "捕获到的控件信息验证失败，可能是不受支持的控件类型或区域。"
                            error_data["capture_status"] = "error" # 新增状态
                            return error_data, f"该控件暂不支持录制"
                else:
                    print(f"[UNI] 未找到位置为({x}, {y})的控件", file=sys.stderr)
                    # 返回包含错误信息的字典
                    return {"error": f"在位置({x}, {y})未找到可识别的控件。", "capture_status": "error"}, f"未找到位置为（{x}, {y}）的控件"
            else:
                print(f"[UNI] 获取当前活动窗口失败", file=sys.stderr)
                # 返回包含错误信息的字典
                return {"error": "获取当前活动窗口失败。", "capture_status": "error"}, f"获取当前活动窗口失败"
        except Exception as e:
            print(f"[UNI] 查找控件时发生错误: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            # 返回包含错误信息的字典
            return {"error": f"查找控件时发生内部错误: {e}", "capture_status": "error"}, f"查找控件时发生错误: {e}"
   
    def _get_active_window3(self,x,y):
    
        # 获取wlcc匹配窗口
        windowinfo = self.getwlccwindow(x,y)
        if not windowinfo:
            print("没有任何匹配窗口")
            return None
        # 获取atspi窗口
        windows = self.getatspiwindow()
        # 获取到窗口后，与wlcc匹配窗口对比
        for window in windows:
            try:
                extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                wpid = window.get_process_id()
                wname = window.name
            except:
                continue
            if (wname == windowinfo[4] or wpid == windowinfo[5]) and extents.width == windowinfo[2] and (extents.height == windowinfo[3] or extents.height+38 == windowinfo[3]):
                active_window = window
                processid = windowinfo[5]
                activewindow_region = [windowinfo[0],windowinfo[1],windowinfo[0]+windowinfo[2],windowinfo[1]+windowinfo[3]]
                windowRoleName = window.getRoleName()
                childCount = window.childCount
                # 计算偏移量
                offx = windowinfo[0]-extents.x-(extents.width-windowinfo[2])
                offy = windowinfo[1]-extents.y-(extents.height-windowinfo[3])
                offxy = [offx, offy]
                break
        return active_window, processid, activewindow_region, windowRoleName, childCount, offxy
    
    def getwlccwindow(self,x, y):
        windowinfo = None
        if y >= 1032:
            # 为任务栏
            output = os.popen("wlcctrl -l|grep -B 1 ukui-panel|grep toplevel|awk -F '\"' '{print $2}'").read().strip()
            windowinfo = self.getwlccwindowinfo(output)
            return windowinfo
        else:
            windowmatch = []
            # 非任务栏窗口，先获取所有窗口然后匹配
            result = subprocess.Popen("wlcctrl -l |grep toplevel|awk -F '\"' '{print $2}'",shell=True,stdout=subprocess.PIPE,stderr=subprocess.PIPE,text=True)
            # 匹配包含（x,y）的窗口
            for i in result.stdout:
                i = i.rstrip('\n')
                window = self.getwlccwindowinfo(i)
                if len(window) == 7:
                    if window[0] <= x <= window[0]+window[2] and window[1] <= y <= window[1]+window[3]:
                        windowmatch.append(window)
        
            if windowmatch:
                if len(windowmatch) == 1:
                    windowinfo = windowmatch[0]
                elif len(windowmatch) == 2:
                    for i in windowmatch:
                        if i[4] != "桌面":
                            windowinfo = i
                else:
                    print("存在多个匹配窗口，若未能找到则说明存在重叠窗口且均不属于激活窗口")
                    # 多个窗口包含x,y，无法确认为哪个窗口，若存在激活窗口则取激活窗口，若不存在则认为没找到
                    output = os.popen("wlcctrl --getactivewindow").read().strip()
                    parts = output.split(":", 1)
                    activewindow = parts[1].strip()
                    activewindowinfo = self.getwlccwindowinfo(activewindow)
                    for i in windowmatch:
                        if activewindowinfo == i:
                            windowinfo = i
        if windowinfo:
            return windowinfo
        else:
            return None
        
    def getwlccwindowinfo(self,windowid):
    
        windowinfo = []
        # 坐标信息
        output1 = os.popen(f"wlcctrl --getwindowgeometry {windowid}").read().strip()
        pattern = r'-?\d+'
        match = re.findall(pattern, output1)
        if match:
            windowinfo = [int(i) for i in match]
        # 窗口名
        output2 = os.popen(f'wlcctrl --getwindowname {windowid}|grep title').read().strip()
        parts = output2.split(":", 1)
        windowinfo.append(parts[1].strip())
        # pid   
        output3 = os.popen(f'wlcctrl --getwindowname {windowid}|grep pid').read().strip()
        windowinfo.append(int(output3.split()[1]))
        windowinfo.append(windowid)
        return windowinfo

    def getatspiwindow(self):
        desktop = pyatspi.Registry.getDesktop(0)
        windows = []
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            try:
                for j in range(app.childCount):
                    window = app.getChildAtIndex(j)
                    try:
                        # 检查窗口是否可见
                        if window.getState().contains(pyatspi.STATE_SHOWING):
                            # 验证窗口有有效的几何信息
                            extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                            if extents.width > 0 and extents.height > 0:
                                windows.append(window)
                    except:
                        continue
            except:
                continue
        return windows
   
    def _aiassistantwindowdeal(self, window, windowregion):
        
        for i in range(window.parent.childCount):
            if window.parent.getChildAtIndex(i).name == "Offscreen":
                try:
                    extents = window.parent.getChildAtIndex(i).queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    if extents.width == windowregion[2]-windowregion[0] and extents.height == windowregion[3]-windowregion[1]-40:
                        offwindow = window.parent.getChildAtIndex(i)
                        offregion = [extents.x,extents.y,extents.x+extents.width,extents.y+extents.height]
                        break
                except Exception as e:
                    print(e)
        if offwindow:
            offsetx = offregion[0] - windowregion[0]
            offsety = offregion[1] - windowregion[1] - 40
            return offwindow, offregion, offsetx, offsety
        else:
            return None,None,None,None
    

    def kdk_getElement_Uni_onactivewindow(self, x, y):
        """
        描述: 根据(x, y)获取控件信息
        作者: 吴德基
        日期: 2024.07.09
        修改: 2024.12.17 - 统一使用_get_active_window2方法确保窗口检测一致性
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: data（dict），文本信息（str）
        """

        # 获取当前active窗口控件、进程ID、区域、类型、子控件数量
        # 统一使用_get_active_window2方法，确保与其他控件获取方法使用相同的窗口检测算法
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = UNI._get_active_window2(self, x, y)
        if active_window:
            # 从当前窗口中获取控件信息
            self.childEle = None
            UNI._find_accessible_at_point(self, active_window, x, y, activewindow_region)
            if self.childEle:
                data = UNI._extract_element_info(self, self.childEle)
                # 补充窗口信息到控件信息中
                data["RecordPosition"] = (x, y)
                data["WindowRoleName"] = windowRoleName
                data["WindowChildCount"] = windowChildCount

                if data["ProcessName"] == "mate-terminal":
                    username = os.popen("whoami").readline().strip()
                    hostname = os.popen("hostname").readline().strip()
                    data["WindowName"] = active_window.name.replace(hostname, "hostname").replace(username, "username")
                elif data["ProcessName"] == "peony":
                    username = os.popen("whoami").readline().strip()
                    data["WindowName"] = active_window.name.replace(username, "username")
                elif "文本编辑器" in active_window.name:
                    data["WindowName"] = "文本编辑器"
                elif "WPS" in active_window.name:
                    # WPS单独处理
                    data["WindowName"] = "WPS"
                else:
                    data["WindowName"] = active_window.name

                if UNI._checkdata(self, data, self.childEle):
                    return data, f"找到"
                else:
                    return None, f"该控件暂不支持录制"
            else:
                return None, f"未找到位置为（{x}, {y}）的控件"
        else:
            return None, f"获取当前活动窗口失败"

    def _checkdata(self, data, element):

        # 任务栏托盘
        if data["WindowName"] == "ukui-panel" and data["Coords"]["width"] == 32 and data["Coords"]["height"] == 32 and data["Name"] == "N/A" and data["Description"] == "N/A":
            try:
                if element.parent.queryComponent().getExtents(pyatspi.DESKTOP_COORDS).width != 32:
                    return False
            except Exception as e:
                print(e)

        # 安全中心侧边栏
        if data["WindowName"] == "安全中心" and data["Coords"]["width"] == 184 and data["Coords"]["height"] == 500 and data["Name"] == "N/A" and data["Description"] == "N/A":
            return False

        # 系统监视器侧边栏上方三个按钮
        if data["WindowName"] == "系统监视器" and data["Coords"]["width"] == 194 and data["Coords"]["height"] == 187 and data["Name"] == "N/A" and data["Description"] == "N/A":
            return False

        # 默认状态下的开始菜单列表栏（包含滚动条）
        if (data["ProcessName"] == "ukui-menu" and data["Coords"]["width"] == 314 and data["Coords"]["height"] == 530 and data["Name"] == "N/A" and data["Description"] == "N/A") or (data["ProcessName"] == "ukui-menu" and data["Coords"]["width"] == 298 and data["Coords"]["height"] == 44 and data["Name"] == "N/A" and data["Description"] == "N/A" and data["ParentCount"] == 7 and data["Rolename"] == "list item"):
            return False

        return True

    def _get_lastappEle_at_point(self, nowx, nowy):
        """
        描述: 根据(x, y)从记录的窗口所有控件信息中获取具体控件信息
        作者: 吴德基
        日期: 2024.07.09
        参数:
        - nowx: 横坐标
        - nowy: 纵坐标
        返回值: matedata（dict）
        """

        matedata = None
        for i in self.last_appEle:
            try:
                x1 = i["Coords"]["x"]
                y1 = i["Coords"]["y"]
                width1 = i["Coords"]["width"]
                height1 = i["Coords"]["height"]
                if x1 <= nowx < x1 + width1 and y1 <= nowy < y1 + height1:
                    states = i["States"]
                    if 'showing' in states and ('focusable' in states or 'focused' in states):
                        if matedata:
                            # 非第一次，则对比两者ChildrenCount取小的
                            if width1 + height1 < matedata["Coords"]["width"] + matedata["Coords"]["height"]:
                                matedata = i
                        else:
                            # 说明是第一次，直接赋值
                            matedata = i
            except Exception as e:
                print(e)
        return matedata

    def get_window_stack_wayland(self):
        """
        Wayland环境下获取窗口列表
        """
        try:
            # 开始计时
            start_time = time.time()

            # 检查缓存
            cache_key = 'window_stack_wayland'
            if cache_key in self.window_cache and time.time() - self.window_cache[cache_key]['time'] < 0.5:
                return self.window_cache[cache_key]['data']

            desktop = pyatspi.Registry.getDesktop(0)
            windows = []

            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                try:
                    for j in range(app.childCount):
                        window = app.getChildAtIndex(j)
                        try:
                            # 检查窗口是否可见
                            if window.getState().contains(pyatspi.STATE_SHOWING):
                                # 验证窗口有有效的几何信息
                                extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                                if extents.width > 0 and extents.height > 0:
                                    windows.append(window)
                        except Exception as e:
                            print(e)
                            continue
                except Exception as e:
                    print(e)
                    continue

            # 更新缓存
            self.window_cache[cache_key] = {
                'data': windows,
                'time': time.time()
            }

            # 记录性能指标
            self.timers['get_window_stack_wayland'] = time.time() - start_time

            return windows
        except Exception as e:
            print(f"[ERROR] Wayland环境下获取窗口列表失败: {e}", file=sys.stderr)
            return []

    def get_window_stack_x11(self):
        """
        X11环境下获取窗口堆栈，按照Z轴顺序排列（最上层的窗口在列表最前面）
        """
        if not self.x11_available:
            print("[WARNING] X11库不可用，无法获取窗口堆栈", file=sys.stderr)
            return []

        try:
            # 检查是否有有效的显示连接
            if not os.environ.get('DISPLAY'):
                print("[WARNING] 没有DISPLAY环境变量，无法连接X11服务器", file=sys.stderr)
                return []

            # 开始计时
            start_time = time.time()

            # 检查缓存
            cache_key = 'window_stack_x11'
            if cache_key in self.window_cache and time.time() - self.window_cache[cache_key]['time'] < 0.5:
                # 使用缓存的窗口堆栈（如果缓存时间不超过0.5秒）
                return self.window_cache[cache_key]['data']

            d = display.Display()
            root = d.screen().root

            client_list = root.get_full_property(
                d.intern_atom('_NET_CLIENT_LIST_STACKING'),
                X.AnyPropertyType
            )
            if client_list:
                window_ids = client_list.value
                window_stack = [d.create_resource_object('window', win_id) for win_id in reversed(window_ids)]

                # 更新缓存
                self.window_cache[cache_key] = {
                    'data': window_stack,
                    'time': time.time()
                }

                # 记录性能指标
                self.timers['get_window_stack_x11'] = time.time() - start_time

                return window_stack
            return []
        except Exception as e:
            print(f"[WARNING] X11环境下获取窗口堆栈失败: {e}，尝试使用AT-SPI回退方案", file=sys.stderr)
            # 在X11环境下也可以使用AT-SPI作为回退方案
            return self.get_window_stack_wayland()

    def get_window_stack(self):
        """
        获取窗口堆栈（统一接口）
        """
        if self.display_server == 'wayland':
            return self.get_window_stack_wayland()
        else:
            return self.get_window_stack_x11()

    def print_topmost_window_info_wayland(self, x, y):
        """
        Wayland环境下获取指定坐标上的窗口
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: 窗口对象或None
        """
        try:
            # 开始计时
            start_time = time.time()

            # 检查缓存
            cache_key = f'topmost_window_wayland_{x}_{y}'
            if cache_key in self.window_cache and time.time() - self.window_cache[cache_key]['time'] < 0.2:
                return self.window_cache[cache_key]['data']

            windows = self.get_window_stack_wayland()

            # 改进的窗口选择策略
            # 1. 先找到所有包含指定坐标的窗口
            # 2. 在这些窗口中，优先选择具有活动状态的窗口
            # 3. 如果没有活动窗口，选择面积最小的窗口（最精确的窗口）
            # 4. 增加特殊窗口类型的优先级处理

            candidate_windows = []
            active_windows = []

            for window in windows:
                try:
                    extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    if (extents.x <= x < extents.x + extents.width and
                        extents.y <= y < extents.y + extents.height):

                        # 检查窗口状态
                        states = window.getState().getStates()
                        state_names = [pyatspi.stateToString(state) for state in states]

                        # 计算窗口面积和其他属性
                        area = extents.width * extents.height
                        window_info = {
                            'window': window,
                            'area': area,
                            'extents': extents,
                            'states': state_names,
                            'name': getattr(window, 'name', ''),
                            'role': window.getRoleName() if hasattr(window, 'getRoleName') else ''
                        }

                        # 如果窗口处于活动状态，加入活动窗口列表
                        if 'active' in state_names:
                            active_windows.append(window_info)

                        candidate_windows.append(window_info)

                except Exception as e:
                    print(f"[WARNING] 检查窗口时发生错误: {e}", file=sys.stderr)
                    continue

            # 选择最佳窗口的逻辑
            best_window = None

            # 优先选择活动窗口
            if active_windows:
                # 如果有多个活动窗口，选择面积最小的
                best_window_info = min(active_windows, key=lambda w: w['area'])
                best_window = best_window_info['window']
                print(f"[INFO] 选择活动窗口: {best_window_info['name']}", file=sys.stderr)
            elif candidate_windows:
                # 如果没有活动窗口，使用改进的选择策略

                # 按优先级排序窗口
                def calculate_priority(window_info):
                    priority_score = 0

                    # 面积越小优先级越高（更精确）
                    priority_score -= window_info['area'] / 1000

                    # 特定窗口类型优先级
                    window_name = window_info['name'].lower()
                    window_role = window_info['role'].lower()

                    # 应用程序窗口优先级高于面板和桌面
                    if window_role in ['frame', 'window', 'dialog']:
                        priority_score += 100
                    elif window_role in ['menu', 'popup menu']:
                        priority_score += 150  # 菜单优先级最高
                    elif window_role in ['panel', 'tool bar']:
                        priority_score += 50
                    elif window_role in ['desktop frame', 'desktop']:
                        priority_score -= 100  # 桌面优先级最低

                    # 有名称的窗口优先级更高
                    if window_info['name'] and window_info['name'] != 'N/A':
                        priority_score += 10

                    # 可见状态的窗口优先级更高
                    if 'showing' in window_info['states'] or 'visible' in window_info['states']:
                        priority_score += 20

                    return priority_score

                # 按优先级排序并选择最高优先级的窗口
                candidate_windows.sort(key=calculate_priority, reverse=True)
                best_window_info = candidate_windows[0]
                best_window = best_window_info['window']

                print(f"[INFO] 选择候选窗口: {best_window_info['name']} (角色: {best_window_info['role']}, 面积: {best_window_info['area']})", file=sys.stderr)

            # 更新缓存
            if best_window:
                self.window_cache[cache_key] = {
                    'data': best_window,
                    'time': time.time()
                }

            # 记录性能指标
            self.timers['print_topmost_window_info_wayland'] = time.time() - start_time

            if not best_window:
                print(f"[WARNING] Wayland环境下在坐标 ({x}, {y}) 处未找到窗口", file=sys.stderr)

            return best_window

        except Exception as e:
            print(f"[ERROR] Wayland环境下获取窗口时发生错误: {e}", file=sys.stderr)
            return None

    def print_topmost_window_info_x11(self, x, y):
        """
        X11环境下获取指定坐标上最上层的窗口
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: 窗口对象或None
        """
        if not self.wnck_available:
            print("[WARNING] Wnck库不可用，尝试使用AT-SPI回退方案", file=sys.stderr)
            return self.print_topmost_window_info_wayland(x, y)

        # 检查是否有有效的显示连接
        if not os.environ.get('DISPLAY'):
            print("[WARNING] 没有DISPLAY环境变量，使用AT-SPI回退方案", file=sys.stderr)
            return self.print_topmost_window_info_wayland(x, y)

        try:
            # 开始计时
            start_time = time.time()

            # 检查缓存
            cache_key = f'topmost_window_x11_{x}_{y}'
            if cache_key in self.window_cache and time.time() - self.window_cache[cache_key]['time'] < 0.2:
                # 使用缓存的窗口（如果缓存时间不超过0.2秒）
                return self.window_cache[cache_key]['data']

            screen = Wnck.Screen.get_default()
            if screen is None:
                print("[WARNING] 无法获取Wnck屏幕对象，使用AT-SPI回退方案", file=sys.stderr)
                return self.print_topmost_window_info_wayland(x, y)
            screen.force_update()

            window_stack = self.get_window_stack_x11()
            for xwin in window_stack:
                try:
                    wnck_window = Wnck.Window.get(xwin.id)
                    if wnck_window:
                        # 检查窗口是否最小化或隐藏
                        if wnck_window.is_minimized() or not wnck_window.is_visible_on_workspace(screen.get_active_workspace()):
                            continue

                        geometry = wnck_window.get_geometry()
                        if (geometry.xp <= x < geometry.xp + geometry.widthp and
                            geometry.yp <= y < geometry.yp + geometry.heightp):
                            # 更新缓存
                            self.window_cache[cache_key] = {
                                'data': wnck_window,
                                'time': time.time()
                            }

                            # 记录性能指标
                            self.timers['print_topmost_window_info_x11'] = time.time() - start_time

                            return wnck_window
                except Exception as e:
                    print(f"[ERROR] 处理X11窗口时发生错误: {e}", file=sys.stderr)

            print(f"[WARNING] X11环境下在坐标 ({x}, {y}) 处未找到可见窗口，尝试AT-SPI回退方案", file=sys.stderr)
            return self.print_topmost_window_info_wayland(x, y)
        except Exception as e:
            print(f"[WARNING] X11环境下获取最上层窗口失败: {e}，使用AT-SPI回退方案", file=sys.stderr)
            return self.print_topmost_window_info_wayland(x, y)

    def print_topmost_window_info(self, x, y):
        """
        获取指定坐标上最上层的窗口（统一接口）
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: 窗口对象或None
        """
        if self.display_server == 'wayland':
            return self.print_topmost_window_info_wayland(x, y)
        else:
            return self.print_topmost_window_info_x11(x, y)

    def _get_active_window_wayland(self, x, y):
        """
        Wayland环境下根据坐标获取活动窗口及其相关信息
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: 窗口对象、进程ID、窗口区域、窗口角色名称、子控件数量
        """
        try:
            # 开始计时
            start_time = time.time()

            # 检查缓存
            cache_key = f'active_window_wayland_{x}_{y}'
            if cache_key in self.window_cache and time.time() - self.window_cache[cache_key]['time'] < 0.2:
                return self.window_cache[cache_key]['data']

            # 获取最上层窗口
            target_window = self.print_topmost_window_info_wayland(x, y)
            if not target_window:
                print(f"[ERROR] Wayland环境下在坐标 ({x}, {y}) 处未找到窗口", file=sys.stderr)
                return None, None, None, None, None

            # 获取窗口基本信息
            try:
                extents = target_window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                x1 = extents.x
                y1 = extents.y
                width1 = extents.width
                height1 = extents.height
                window_name = target_window.name
                window_pid = target_window.get_process_id()
            except Exception as e:
                print(f"[ERROR] Wayland环境下获取窗口信息失败: {e}", file=sys.stderr)
                return None, None, None, None, None

            # 构建窗口区域
            window_region = [x1, y1, x1 + width1, y1 + height1]

            # 更新缓存
            result = (target_window, window_pid, window_region, target_window.getRoleName(), target_window.childCount)
            self.window_cache[cache_key] = {
                'data': result,
                'time': time.time()
            }

            # 记录性能指标
            self.timers['_get_active_window_wayland'] = time.time() - start_time

            return result

        except Exception as e:
            print(f"[ERROR] Wayland环境下获取活动窗口时发生错误: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            return None, None, None, None, None

    def _get_active_window_x11(self, x, y):
        """
        X11环境下根据坐标获取活动窗口及其相关信息
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: 窗口对象、进程ID、窗口区域、窗口角色名称、子控件数量
        """
        if not self.wnck_available:
            print("[WARNING] Wnck库不可用，使用AT-SPI回退方案", file=sys.stderr)
            return self._get_active_window_wayland(x, y)

        # 检查是否有有效的显示连接
        if not os.environ.get('DISPLAY'):
            print("[WARNING] 没有DISPLAY环境变量，使用AT-SPI回退方案", file=sys.stderr)
            return self._get_active_window_wayland(x, y)

        try:
            # 开始计时
            start_time = time.time()

            # 检查缓存（缩短缓存时间以便及时检测新窗口）
            cache_key = f'active_window_x11_{x}_{y}'
            cache_timeout = 0.05  # 缩短到50ms，提高新窗口检测的及时性

            if cache_key in self.window_cache and time.time() - self.window_cache[cache_key]['time'] < cache_timeout:
                cached_data = self.window_cache[cache_key]['data']
                print(f"[DEBUG] 使用缓存的窗口信息: {cached_data[0] if cached_data[0] else 'None'}", file=sys.stderr)
                return cached_data

            # 获取最上层窗口
            wnck_window = self.print_topmost_window_info_x11(x, y)
            if not wnck_window:
                print(f"[ERROR] X11环境下在坐标 ({x}, {y}) 处未找到窗口", file=sys.stderr)
                return None, None, None, None, None

            # 获取窗口基本信息
            geometry = wnck_window.get_geometry()
            x1 = geometry.xp
            y1 = geometry.yp
            width1 = geometry.widthp
            height1 = geometry.heightp
            window_name = wnck_window.get_name()
            window_pid = wnck_window.get_pid()

            # 使用重试机制获取窗口
            for retry in range(self.max_retries):
                try:
                    windowlist = []
                    desktop = pyatspi.Registry.getDesktop(0)

                    # 快速查找匹配的窗口
                    for i in range(desktop.childCount):
                        app = desktop.getChildAtIndex(i)
                        for j in range(app.childCount):
                            window = app.getChildAtIndex(j)
                            try:
                                if window.get_process_id() == window_pid:
                                    extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                                    if (extents.x <= x < extents.x + extents.width and
                                        extents.y <= y < extents.y + extents.height):
                                        windowlist.append(window)
                            except Exception as e:
                                print(f"[WARNING] X11环境下检查窗口时发生错误: {e}", file=sys.stderr)
                                continue

                    # 首先检查活动状态的窗口
                    for window in windowlist:
                        try:
                            if window.getState().contains(pyatspi.STATE_ACTIVE):
                                processid = window.get_process_id()
                                component = window.queryComponent()
                                if component:
                                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                    window_region = [extents.x, extents.y, extents.x + extents.width,
                                                    extents.y + extents.height]

                                    # 更新缓存
                                    result = (window, processid, window_region, window.getRoleName(), window.childCount)
                                    self.window_cache[cache_key] = {
                                        'data': result,
                                        'time': time.time()
                                    }

                                    # 记录性能指标
                                    self.timers['_get_active_window_x11'] = time.time() - start_time

                                    return result
                        except Exception as e:
                            print(f"[WARNING] X11环境下检查活动窗口时发生错误: {e}", file=sys.stderr)
                            continue

                    # 如果没有找到活动窗口，则根据名称和位置匹配
                    for window in windowlist:
                        try:
                            if (window.name == window_name) or ("ukui-panel" in window.name and window_name == "UKUI Panel"):
                                extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                                if (abs(extents.x - x1) < 5 and abs(extents.y - y1) < 5 and
                                    abs(extents.width - width1) < 5 and abs(extents.height - height1) < 5):
                                    window_region = [extents.x, extents.y, extents.x + extents.width,
                                                    extents.y + extents.height]

                                    # 更新缓存
                                    result = (window, window.get_process_id(), window_region, window.getRoleName(), window.childCount)
                                    self.window_cache[cache_key] = {
                                        'data': result,
                                        'time': time.time()
                                    }

                                    # 记录性能指标
                                    self.timers['_get_active_window_x11'] = time.time() - start_time

                                    return result
                        except Exception as e:
                            print(f"[WARNING] X11环境下检查窗口名称时发生错误: {e}", file=sys.stderr)
                            continue

                    # 如果还是没有找到，则返回第一个窗口（如果有）
                    if windowlist:
                        try:
                            window = windowlist[0]
                            extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                            window_region = [extents.x, extents.y, extents.x + extents.width,
                                            extents.y + extents.height]

                            # 更新缓存
                            result = (window, window.get_process_id(), window_region, window.getRoleName(), window.childCount)
                            self.window_cache[cache_key] = {
                                'data': result,
                                'time': time.time()
                            }

                            # 记录性能指标
                            self.timers['_get_active_window_x11'] = time.time() - start_time

                            return result
                        except Exception as e:
                            print(f"[WARNING] X11环境下使用第一个窗口时发生错误: {e}", file=sys.stderr)

                    # 如果这次尝试失败，等待一小段时间再重试
                    if retry < self.max_retries - 1:
                        print(f"[INFO] X11环境下第 {retry+1} 次尝试获取窗口失败，等待重试...", file=sys.stderr)
                        time.sleep(0.05)  # 等待 50ms 再重试

                except Exception as e:
                    print(f"[ERROR] X11环境下获取窗口时发生错误: {e}", file=sys.stderr)
                    if retry < self.max_retries - 1:
                        time.sleep(0.05)  # 等待 50ms 再重试

            print(f"[ERROR] X11环境下在 {self.max_retries} 次尝试后仍未找到匹配的窗口", file=sys.stderr)
            return None, None, None, None, None

        except Exception as e:
            print(f"[WARNING] X11环境下获取活动窗口失败: {e}，使用AT-SPI回退方案", file=sys.stderr)
            return self._get_active_window_wayland(x, y)

    def _get_active_window2(self, x, y):
        """
        根据坐标获取活动窗口及其相关信息（统一接口）
        修改: 2024.12.17 - 添加缓存清理机制，确保与auto_recording_manager.py行为一致
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: 窗口对象、进程ID、窗口区域、窗口角色名称、子控件数量
        """
        # 轻量级刷新：清除桌面缓存，确保能检测到新应用
        if hasattr(self, 'desktop_cache') and 'desktop_object' in self.desktop_cache:
            # 检查缓存是否过期（使用更短的超时时间）
            cache_entry = self.desktop_cache['desktop_object']
            if time.time() - cache_entry['time'] > 0.05:  # 50ms超时
                del self.desktop_cache['desktop_object']
                print(f"[DEBUG] 已清除过期的桌面缓存", file=sys.stderr)

        # 如果有_get_fresh_desktop方法，先触发桌面刷新
        if hasattr(self, '_get_fresh_desktop'):
            # 强制刷新桌面以检测新打开的应用
            # 每次控件识别前都强制刷新，确保能检测到新应用
            if hasattr(self, '_last_desktop_refresh'):
                self._last_desktop_refresh = 0
            desktop = self._get_fresh_desktop()
            app_count = desktop.childCount if desktop else 0
            print(f"[DEBUG] 已触发桌面刷新，应用数: {app_count}", file=sys.stderr)

        if self.display_server == 'wayland':
            return self._get_active_window_wayland(x, y)
        else:
            return self._get_active_window_x11(x, y)

    def _get_active_window1(self, x, y):
        """
        描述: 根据(x, y)从桌面中返回窗口对象
        作者: 胡文灿
        日期: 2024.07.09
        参数:
        - nowx: 横坐标
        - nowy: 纵坐标
        返回值: matedata（dict）
        """

        window = UNI.print_topmost_window_info(self,x,y)

        geometry = window.get_geometry()
        x = geometry.xp
        y = geometry.yp
        width = geometry.widthp
        height = geometry.heightp
        window_name = window.get_name()
        window_pid = window.get_pid()

        window_region = [x, y, x + width, y + height]

        return window, window_pid, window_region,window_name, 0

    def _get_active_window(self, x, y):
        """
        描述: 根据(x, y)获取当前位置所处的激活窗口
        作者: 吴德基
        日期: 2024.07.09
        修改: 2024.12.17 - 已弃用，重定向到_get_active_window2以确保窗口检测一致性
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: window（窗口控件），processid（窗口的进程ID），window_region（窗口的区域位置），window.getRoleName()（窗口的类型），window.childCount(窗口的子控件数量)
        """
        print(f"[WARNING] _get_active_window方法已弃用，自动重定向到_get_active_window2", file=sys.stderr)
        return self._get_active_window2(x, y)

    def _find_accessible_at_point(self, element, x, y, activewindow_region):
        """
        描述: 在激活窗口中根据(x, y)找到对应的控件信息
        作者: 吴德基
        日期: 2024.07.09
        修改: 优化控件查找算法，更准确地找到最顶层控件
        新增: X11窗口层级检测，确保返回最顶层可见控件
        参数:
        - element: 窗口控件
        - x: 横坐标
        - y: 纵坐标
        - activewindow_region: 窗口的区域位置
        返回值: 无，结果存储在self.childEle中
        """
        try:
            # 开始计时
            start_time = time.time()
            
            print(element)
            print(activewindow_region)
            # 检查缓存
            cache_key = f'accessible_at_{x}_{y}_{id(element)}'
            if cache_key in self.window_cache and time.time() - self.window_cache[cache_key]['time'] < 0.1:
                # 使用缓存的控件（如果缓存时间不超过0.1秒）
                self.childEle = self.window_cache[cache_key]['data']
                return

            # 首先尝试使用X11窗口层级检测找到最顶层控件
            topmost_element = self._find_topmost_element_at_point(x, y)
            if topmost_element:
                self.childEle = topmost_element
                # 更新缓存
                self.window_cache[cache_key] = {
                    'data': self.childEle,
                    'time': time.time()
                }
                print(f"[INFO] 使用X11层级检测在坐标 ({x}, {y}) 处找到最顶层控件: {topmost_element.name if topmost_element.name else 'unnamed'}", file=sys.stderr)
                if "ukui-menu" in element.name:
                    return

            # 如果X11层级检测失败，使用改进的传统方法：
            # 1. 使用深度优先搜索找到最小的包含坐标的控件
            # 2. 考虑控件的层级关系和Z-order
            # 3. 优先选择可交互的控件
            # 4. 特殊处理不同类型的控件
            # 5. 增强最顶层控件检测逻辑

            print(f"[DEBUG] 使用传统方法查找控件，坐标: ({x}, {y})", file=sys.stderr)

            best_element = None
            best_score = float('-inf')
            all_candidates = []  # 收集所有候选控件

            def find_deepest_element_at_point(current_element, depth=0):
                """递归查找最深层的控件，收集所有候选控件"""
                nonlocal best_element, best_score, all_candidates

                try:
                    # 检查当前元素是否包含坐标点
                    extents = current_element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)

                    if not (extents.x <= x < extents.x + extents.width and
                            extents.y <= y < extents.y + extents.height):
                        return  # 坐标不在当前元素范围内，跳过

                    # 排除窗口本身
                    if (activewindow_region[0] == extents.x and
                        activewindow_region[1] == extents.y and
                        (activewindow_region[2] - activewindow_region[0]) == extents.width and
                        (activewindow_region[3] - activewindow_region[1]) == extents.height):
                        # 这是窗口本身，继续查找子元素
                        pass
                    else:
                        # 检查元素是否符合条件
                        states = current_element.getState().getStates()
                        state_names = [pyatspi.stateToString(state) for state in states]

                        try:
                            action_interface = current_element.queryAction()
                            actions = [action_interface.getName(i) for i in range(action_interface.nActions)]
                        except:
                            actions = []

                        # 计算元素的优先级分数
                        score = self._calculate_element_score(current_element, extents, state_names, actions, depth, x, y)

                        # 收集候选控件信息
                        candidate_info = {
                            'element': current_element,
                            'score': score,
                            'depth': depth,
                            'extents': extents,
                            'states': state_names,
                            'actions': actions,
                            'area': extents.width * extents.height
                        }
                        all_candidates.append(candidate_info)

                        # 如果当前元素得分更高，更新最佳元素
                        if score > best_score:
                            best_score = score
                            best_element = current_element

                    # 继续递归查找子元素（深度优先）
                    for i in range(current_element.childCount):
                        try:
                            child = current_element.getChildAtIndex(i)
                            find_deepest_element_at_point(child, depth + 1)
                        except Exception as e:
                            # 忽略无法访问的子元素
                            continue

                except Exception as e:
                    # 忽略无法查询组件的元素
                    return

            # 开始递归查找
            find_deepest_element_at_point(element)

            # 使用增强的最顶层控件选择逻辑
            final_element = self._select_topmost_candidate(all_candidates, x, y)

            # 设置结果
            self.childEle = final_element if final_element else best_element

            # 更新缓存
            if self.childEle:
                self.window_cache[cache_key] = {
                    'data': self.childEle,
                    'time': time.time()
                }

                element_name = self.childEle.name if self.childEle.name else 'unnamed'
                selection_method = "增强选择" if final_element else "传统选择"
                print(f"[INFO] 在坐标 ({x}, {y}) 处找到控件: {element_name} ({selection_method})", file=sys.stderr)

                if len(all_candidates) > 1:
                    print(f"[DEBUG] 共找到 {len(all_candidates)} 个候选控件，已选择最佳匹配", file=sys.stderr)
            else:
                print(f"[WARNING] 在坐标 ({x}, {y}) 处未找到符合条件的控件", file=sys.stderr)

            # 记录性能指标
            self.timers['_find_accessible_at_point'] = time.time() - start_time

        except Exception as e:
            print(f"[ERROR] 查找坐标处控件时发生错误: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            self.childEle = None

    def _calculate_element_score(self, element, extents, state_names, actions, depth, x, y):
        """
        计算元素的优先级分数
        返回值越高表示优先级越高
        """
        score = 0

        # 1. 基础可见性和交互性检查
        is_visible = 'showing' in state_names or 'visible' in state_names
        is_interactive = ('focusable' in state_names or 'focused' in state_names or
                         'selectable' in state_names or 'sensitive' in state_names)
        has_actions = len(actions) > 0

        # 如果不可见或不可交互，得分很低
        if not is_visible:
            score -= 1000
        if not (is_interactive or has_actions):
            score -= 500

        # 2. 深度越深得分越高（更精确的控件）
        score += depth * 100

        # 3. 面积越小得分越高（更精确）
        area = extents.width * extents.height
        if area > 0:
            score -= area / 100

        # 4. 根据控件角色类型调整得分
        try:
            role = element.getRoleName()
            role_score = self._get_role_priority_score(role)
            score += role_score
        except:
            pass

        # 5. 有名称的控件得分更高
        try:
            if element.name and element.name != "N/A" and element.name.strip():
                score += 50
        except:
            pass

        # 6. 有描述的控件得分更高
        try:
            if element.description and element.description != "N/A" and element.description.strip():
                score += 30
        except:
            pass

        # 7. 聚焦状态的控件得分更高
        if 'focused' in state_names:
            score += 200

        # 8. 按下状态的控件得分更高
        if 'pressed' in state_names:
            score += 150

        # 9. 选中状态的控件得分更高
        if 'selected' in state_names:
            score += 100

        # 10. 根据鼠标位置在控件中的相对位置调整得分
        # 鼠标越接近控件中心，得分越高
        center_x = extents.x + extents.width // 2
        center_y = extents.y + extents.height // 2
        distance_from_center = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
        max_distance = (extents.width ** 2 + extents.height ** 2) ** 0.5
        if max_distance > 0:
            center_score = 50 * (1 - distance_from_center / max_distance)
            score += center_score

        # 11. 智能面积评分：根据控件类型调整面积偏好
        if area > 0:
            role_name = element.getRoleName().lower()

            # 菜单相关控件的特殊处理
            if any(menu_role in role_name for menu_role in ['menu', 'item']):
                # 菜单项：偏好中等面积（避免选择太小的文本或太大的容器）
                if 1000 <= area <= 8000:
                    score += 150  # 菜单项理想面积范围
                elif 500 <= area < 1000:
                    score += 80   # 稍小的菜单项
                elif 8000 < area <= 15000:
                    score += 60   # 稍大的菜单项
                else:
                    score += 20   # 过小或过大的菜单相关元素
            else:
                # 非菜单控件：面积越小越精确
                if area < 1000:
                    score += 100
                elif area < 5000:
                    score += 50
                elif area < 20000:
                    score += 20

        # 12. 特殊状态加分
        if 'active' in state_names:
            score += 300  # 活动状态的控件优先级最高
        if 'armed' in state_names:
            score += 250  # 准备状态的控件
        if 'checked' in state_names:
            score += 80   # 选中状态的控件

        return score

    def _select_topmost_candidate(self, candidates, x, y):
        """
        从候选控件中选择最顶层的控件
        使用多种策略确保选择到用户实际看到的控件
        """
        if not candidates:
            return None

        if len(candidates) == 1:
            return candidates[0]['element']

        try:
            # 策略1: 优先选择面积最小且可交互的控件
            interactive_candidates = []
            for candidate in candidates:
                states = candidate['states']
                actions = candidate['actions']

                is_interactive = (
                    'focusable' in states or 'focused' in states or
                    'selectable' in states or 'sensitive' in states or
                    len(actions) > 0
                )

                is_visible = 'showing' in states or 'visible' in states

                if is_interactive and is_visible:
                    interactive_candidates.append(candidate)

            # 如果有可交互的控件，使用智能选择策略
            if interactive_candidates:
                # 检查是否有菜单相关的控件
                menu_candidates = []
                for candidate in interactive_candidates:
                    role = candidate['element'].getRoleName().lower()
                    if any(menu_role in role for menu_role in ['menu', 'item']):
                        menu_candidates.append(candidate)

                # 如果有菜单控件，优先选择合适大小的菜单项
                if menu_candidates:
                    # 对于菜单项，选择面积适中的（不要太小的子元素，也不要太大的容器）
                    menu_candidates.sort(key=lambda c: abs(c['area'] - 2000))  # 假设菜单项理想面积约2000像素
                    selected = menu_candidates[0]
                    print(f"[DEBUG] 选择菜单控件: 角色={selected['element'].getRoleName()} 面积={selected['area']}", file=sys.stderr)
                    return selected['element']
                else:
                    # 非菜单控件，选择面积最小的
                    smallest_interactive = min(interactive_candidates, key=lambda c: c['area'])
                    print(f"[DEBUG] 选择最小可交互控件: 面积={smallest_interactive['area']}", file=sys.stderr)
                    return smallest_interactive['element']

            # 策略2: 智能选择可见控件（考虑菜单特殊情况）
            visible_candidates = []
            for candidate in candidates:
                states = candidate['states']
                if 'showing' in states or 'visible' in states:
                    visible_candidates.append(candidate)

            if visible_candidates:
                # 检查是否有菜单相关的可见控件
                menu_visible_candidates = []
                for candidate in visible_candidates:
                    role = candidate['element'].getRoleName().lower()
                    if any(menu_role in role for menu_role in ['menu', 'item']):
                        menu_visible_candidates.append(candidate)

                if menu_visible_candidates:
                    # 对于菜单控件，选择合适深度和面积的控件
                    # 不要总是选择最深的，因为可能选择到文本子元素
                    menu_visible_candidates.sort(key=lambda c: (
                        -1 if 'item' in c['element'].getRoleName().lower() else 0,  # 菜单项优先
                        abs(c['area'] - 3000),  # 偏好中等面积
                        c['depth']  # 深度作为次要因素
                    ))
                    selected = menu_visible_candidates[0]
                    print(f"[DEBUG] 选择菜单可见控件: 角色={selected['element'].getRoleName()} 深度={selected['depth']} 面积={selected['area']}", file=sys.stderr)
                    return selected['element']
                else:
                    # 非菜单控件，使用原来的策略
                    visible_candidates.sort(key=lambda c: (-c['depth'], c['area']))
                    deepest_smallest = visible_candidates[0]
                    print(f"[DEBUG] 选择最深最小可见控件: 深度={deepest_smallest['depth']}, 面积={deepest_smallest['area']}", file=sys.stderr)
                    return deepest_smallest['element']

            # 策略3: 如果都不可见，选择得分最高的
            best_candidate = max(candidates, key=lambda c: c['score'])
            print(f"[DEBUG] 选择得分最高控件: 得分={best_candidate['score']}", file=sys.stderr)
            return best_candidate['element']

        except Exception as e:
            print(f"[DEBUG] 选择最顶层候选控件时出错: {e}", file=sys.stderr)
            # 回退到得分最高的控件
            if candidates:
                return max(candidates, key=lambda c: c['score'])['element']
            return None

    def _find_topmost_element_at_point(self, x, y):
        """
        使用X11窗口层级检测找到指定坐标的最顶层控件
        这个方法专门解决窗口重叠时的控件识别准确性问题
        """
        try:
            # 导入X11相关库
            try:
                from Xlib import display, X
                from Xlib.protocol import request
            except ImportError:
                print("[DEBUG] Xlib不可用，回退到传统方法", file=sys.stderr)
                return None

            # 连接到X11显示服务器
            disp = display.Display()
            root = disp.screen().root

            # 获取指定坐标处的最顶层X11窗口
            topmost_window = self._get_topmost_x11_window_at_point(disp, root, x, y)
            if not topmost_window:
                return None

            # 从最顶层X11窗口开始，查找对应的AT-SPI控件
            topmost_element = self._find_atspi_element_for_x11_window(topmost_window, x, y)

            disp.close()
            return topmost_element

        except Exception as e:
            print(f"[DEBUG] X11层级检测失败，回退到传统方法: {e}", file=sys.stderr)
            return None

    def _get_topmost_x11_window_at_point(self, display, root, x, y):
        """
        获取指定坐标处的最顶层X11窗口
        """
        try:
            # 使用XQueryPointer获取鼠标下的窗口
            pointer_info = root.query_pointer()
            child_window = pointer_info.child

            if not child_window:
                return None

            # 递归查找最深层的子窗口
            while True:
                try:
                    # 将全局坐标转换为窗口相对坐标
                    geom = child_window.get_geometry()
                    parent_geom = child_window.query_tree().parent.get_geometry()

                    local_x = x - geom.x - parent_geom.x
                    local_y = y - geom.y - parent_geom.y

                    # 查询子窗口
                    pointer_info = child_window.query_pointer()
                    if pointer_info.child and pointer_info.child != child_window:
                        child_window = pointer_info.child
                    else:
                        break
                except Exception as e:
                    print(e)
                    break

            return child_window

        except Exception as e:
            print(f"[DEBUG] 获取最顶层X11窗口失败: {e}", file=sys.stderr)
            return None

    def _find_atspi_element_for_x11_window(self, x11_window, x, y):
        """
        为给定的X11窗口找到对应的AT-SPI控件
        简化版本：基于窗口类名和坐标匹配
        """
        try:
            # 获取X11窗口的类名
            window_class = self._get_x11_window_class(x11_window)
            print(f"[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: {window_class}", file=sys.stderr)

            # 遍历所有可访问的应用程序
            # 获取桌面对象（使用缓存机制）
            desktop = self._get_fresh_desktop()
            if not desktop:
                print(f"[DEBUG] 无法获取桌面对象", file=sys.stderr)
                return None

            print(f"[DEBUG] 当前桌面应用程序数量: {desktop.childCount}", file=sys.stderr)

            # 如果AT-SPI应用程序数量没有变化，尝试强制刷新一次
            if hasattr(self, '_last_app_count') and desktop.childCount == self._last_app_count:
                print(f"[DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI", file=sys.stderr)
                try:
                    # 清除缓存，强制重新获取
                    if 'desktop_object' in self.desktop_cache:
                        del self.desktop_cache['desktop_object']
                    desktop = pyatspi.Registry.getDesktop(0)
                    print(f"[DEBUG] 强制刷新后应用程序数量: {desktop.childCount}", file=sys.stderr)
                except Exception as e:
                    print(f"[DEBUG] 强制刷新失败: {e}", file=sys.stderr)

            self._last_app_count = desktop.childCount

            best_match = None
            best_score = 0

            for app_index in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(app_index)
                    if not app:
                        continue

                    app_name = app.name if app.name else ""

                    # 检查应用程序名称是否与窗口类名匹配
                    name_match_score = self._calculate_name_match_score(app_name, window_class)

                    if name_match_score > 0:
                        print(f"[DEBUG] 检查应用程序: {app_name} (匹配得分: {name_match_score})", file=sys.stderr)

                        # 在该应用程序中查找包含坐标的控件
                        element = self._find_element_at_coordinates(app, x, y)
                        if element:
                            print(f"[DEBUG] 在应用程序 {app_name} 中找到控件: {element.name if element.name else 'unnamed'}", file=sys.stderr)
                            if name_match_score > best_score:
                                best_match = element
                                best_score = name_match_score
                                print(f"[DEBUG] 更新最佳匹配控件: {element.name if element.name else 'unnamed'} (得分: {name_match_score})", file=sys.stderr)
                        else:
                            print(f"[DEBUG] 在应用程序 {app_name} 中未找到包含坐标的控件", file=sys.stderr)

                except Exception as e:
                    continue

            # 如果没有找到匹配的控件，尝试更广泛的搜索
            if not best_match:
                print(f"[DEBUG] X11层级检测未找到控件，窗口类名: {window_class}", file=sys.stderr)

                # 尝试更宽松的匹配策略
                print(f"[DEBUG] 尝试更宽松的应用匹配策略", file=sys.stderr)
                for app_index in range(desktop.childCount):
                    try:
                        app = desktop.getChildAtIndex(app_index)
                        if not app:
                            continue

                        app_name = app.name if app.name else ""

                        # 更宽松的匹配：只要有任何关键词匹配就尝试
                        loose_match_score = self._calculate_loose_match_score(app_name, window_class)

                        if loose_match_score > 0:
                            print(f"[DEBUG] 宽松匹配检查应用程序: {app_name} (得分: {loose_match_score})", file=sys.stderr)

                            # 在该应用程序中查找包含坐标的控件
                            element = self._find_element_at_coordinates(app, x, y)
                            if element:
                                print(f"[DEBUG] 宽松匹配在应用程序 {app_name} 中找到控件: {element.name if element.name else 'unnamed'}", file=sys.stderr)
                                if loose_match_score > best_score:
                                    best_match = element
                                    best_score = loose_match_score
                                    print(f"[DEBUG] 更新宽松匹配最佳控件: {element.name if element.name else 'unnamed'} (得分: {loose_match_score})", file=sys.stderr)
                    except Exception as e:
                        continue

                # 如果还是没找到，检查是否为特殊弹出窗口或存在菜单
                if not best_match:
                    # 只有在以下情况才启动菜单搜索：
                    # 1. 检测到特殊弹出窗口类名
                    # 2. 或者窗口类名未知但确实存在菜单控件
                    should_search_menu = False

                    if self._is_special_popup_window(window_class):
                        print(f"[DEBUG] 检测到特殊弹出窗口: {window_class}", file=sys.stderr)
                        should_search_menu = True
                    elif window_class == "Unknown" or not window_class:
                        print(f"[DEBUG] 窗口类名未知，验证是否存在菜单", file=sys.stderr)
                        # 只有在窗口类名未知时才进行菜单验证
                        if self._verify_menu_exists_at_coordinates(desktop, x, y):
                            print(f"[DEBUG] 确认存在菜单控件", file=sys.stderr)
                            should_search_menu = True
                        else:
                            print(f"[DEBUG] 无菜单控件", file=sys.stderr)
                    else:
                        print(f"[DEBUG] 普通窗口 {window_class}，跳过菜单搜索", file=sys.stderr)

                    if should_search_menu:
                        print(f"[DEBUG] 开始菜单搜索", file=sys.stderr)
                        best_match = self._find_menu_element_at_coordinates(desktop, x, y)
                    else:
                        print(f"[DEBUG] 使用传统方法", file=sys.stderr)

            if best_match:
                print(f"[DEBUG] X11层级检测成功找到控件: {best_match.name if best_match.name else 'unnamed'}", file=sys.stderr)
            else:
                print(f"[DEBUG] X11层级检测未找到匹配的AT-SPI控件", file=sys.stderr)

            return best_match

        except Exception as e:
            print(f"[DEBUG] 查找AT-SPI控件失败: {e}", file=sys.stderr)
            return None

    def _get_x11_window_class(self, window):
        """获取X11窗口的类名"""
        try:
            wm_class = window.get_wm_class()
            if wm_class:
                return f"{wm_class[0]}.{wm_class[1]}"
            return "Unknown"
        except:
            return "Unknown"

    def _is_special_popup_window(self, window_class):
        """
        检测是否为特殊弹出窗口（如开始菜单、右键菜单等）
        精确识别真正的弹出菜单窗口
        """
        if not window_class or window_class == "Unknown":
            return False  # 无法获取窗口类名，不认为是特殊窗口

        window_class_lower = window_class.lower()

        # 明确的弹出菜单窗口类型
        popup_menu_patterns = [
            'ukui-menu',      # UKUI开始菜单
            'gnome-shell',    # GNOME Shell菜单
            'kde-plasma',     # KDE Plasma菜单
            'menu', 'popup', 'dropdown', 'context',
            'launcher', 'notification', 'tooltip'
        ]

        # 明确排除的窗口类型（任务栏等）
        excluded_patterns = [
            'ukui-panel',     # UKUI任务栏
            'gnome-panel',    # GNOME面板
            'kde-panel',      # KDE面板
            'panel', 'dock', 'desktop', 'taskbar',
            'tray', 'systray', 'bar'
        ]

        # 首先检查是否为明确排除的类型
        for pattern in excluded_patterns:
            if pattern in window_class_lower:
                print(f"[DEBUG] 窗口类名 {window_class} 被排除，不是弹出窗口", file=sys.stderr)
                return False

        # 然后检查是否为弹出菜单类型
        for pattern in popup_menu_patterns:
            if pattern in window_class_lower:
                print(f"[DEBUG] 窗口类名 {window_class} 匹配弹出窗口模式: {pattern}", file=sys.stderr)
                return True

        print(f"[DEBUG] 窗口类名 {window_class} 不匹配任何弹出窗口模式", file=sys.stderr)
        return False

    def _verify_menu_exists_at_coordinates(self, desktop, x, y):
        """
        验证指定坐标是否存在可见的菜单控件
        简化逻辑，增加调试信息
        """
        try:
            print(f"[DEBUG] 验证坐标 ({x}, {y}) 是否存在菜单控件", file=sys.stderr)

            menu_found = False
            checked_apps = []

            for app_index in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(app_index)
                    if not app:
                        continue

                    app_name = app.name if app.name else f"App_{app_index}"
                    checked_apps.append(app_name)

                    print(f"[DEBUG] 检查应用程序: {app_name}", file=sys.stderr)

                    # 检查所有应用程序，不过滤
                    if self._has_menu_at_exact_coordinates(app, x, y):
                        print(f"[DEBUG] ✅ 在应用程序 {app_name} 中发现坐标处的菜单控件", file=sys.stderr)
                        menu_found = True
                        break
                    else:
                        print(f"[DEBUG] ❌ 应用程序 {app_name} 中无坐标处的菜单控件", file=sys.stderr)

                except Exception as e:
                    print(f"[DEBUG] 检查应用程序时出错: {e}", file=sys.stderr)
                    continue

            print(f"[DEBUG] 总共检查了 {len(checked_apps)} 个应用程序: {', '.join(checked_apps[:5])}{'...' if len(checked_apps) > 5 else ''}", file=sys.stderr)

            if menu_found:
                print(f"[DEBUG] ✅ 坐标 ({x}, {y}) 处确实存在菜单控件", file=sys.stderr)
                return True
            else:
                print(f"[DEBUG] ❌ 坐标 ({x}, {y}) 处未发现菜单控件", file=sys.stderr)
                return False

        except Exception as e:
            print(f"[DEBUG] 验证菜单存在性时发生错误: {e}", file=sys.stderr)
            return False

    def _is_likely_menu_app(self, app_name):
        """
        检查应用程序是否可能包含菜单
        """
        if not app_name:
            return False

        app_name_lower = app_name.lower()
        menu_app_patterns = [
            'menu', 'launcher', 'panel', 'desktop',
            'ukui-menu', 'gnome-shell', 'kde-plasma'
        ]

        return any(pattern in app_name_lower for pattern in menu_app_patterns)

    def _has_menu_at_exact_coordinates(self, app, x, y):
        """
        检查应用程序中是否有指定坐标处的菜单控件
        更精确的检查，不使用容差
        """
        try:
            def check_element_recursive(element, depth=0):
                if depth > 15:  # 增加递归深度限制
                    return False

                try:
                    # 检查当前元素是否为真正的菜单相关控件
                    role = element.getRoleName().lower()
                    # 更严格的菜单控件判断：只认可明确的菜单角色
                    is_menu_related = any(menu_role in role for menu_role in ['menu item', 'menuitem'])
                    # 或者是在菜单应用程序中的list item
                    app_name = app.name if app.name else ""
                    is_menu_app_list_item = ('list item' in role and
                                            any(menu_app in app_name.lower() for menu_app in ['menu', 'launcher']))

                    if is_menu_related or is_menu_app_list_item:
                        print(f"[DEBUG]   发现菜单相关控件: {role} 深度={depth} 应用={app_name}", file=sys.stderr)

                        # 检查是否可见和活跃
                        try:
                            states = element.getState()
                            state_names = [pyatspi.stateToString(state) for state in states.getStates()]

                            # 必须同时满足：可见 + 显示 + 非瞬态（避免隐藏菜单）
                            is_visible = 'showing' in state_names and 'visible' in state_names
                            is_not_transient_hidden = 'transient' not in state_names or 'showing' in state_names

                            if not (is_visible and is_not_transient_hidden):
                                print(f"[DEBUG]     控件不可见或隐藏: {state_names[:5]}", file=sys.stderr)
                                return False

                            print(f"[DEBUG]     控件可见且活跃: {state_names[:3]}...", file=sys.stderr)
                        except:
                            print(f"[DEBUG]     无法获取控件状态", file=sys.stderr)
                            return False

                        # 检查坐标是否在控件范围内，并验证控件在屏幕可见区域
                        try:
                            component = element.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)

                                # 检查控件是否有有效的边界（避免0x0或负数边界）
                                has_valid_bounds = (extents.width > 0 and extents.height > 0 and
                                                  extents.x >= 0 and extents.y >= 0 and
                                                  extents.x < 3000 and extents.y < 2000)  # 合理的屏幕范围

                                if not has_valid_bounds:
                                    print(f"[DEBUG]     控件边界无效: ({extents.x}, {extents.y}) {extents.width}x{extents.height}", file=sys.stderr)
                                    return False

                                in_bounds = (extents.x <= x <= extents.x + extents.width and
                                           extents.y <= y <= extents.y + extents.height)

                                print(f"[DEBUG]     控件边界: ({extents.x}, {extents.y}) {extents.width}x{extents.height}", file=sys.stderr)
                                print(f"[DEBUG]     坐标 ({x}, {y}) 在边界内: {in_bounds}", file=sys.stderr)

                                if in_bounds:
                                    return True
                        except Exception as e:
                            print(f"[DEBUG]     获取控件边界失败: {e}", file=sys.stderr)

                    # 递归检查子元素（不限制数量）
                    try:
                        child_count = element.childCount
                        if child_count > 0:
                            print(f"[DEBUG]   检查 {child_count} 个子元素 (深度={depth})", file=sys.stderr)

                        for i in range(child_count):
                            try:
                                child = element.getChildAtIndex(i)
                                if child and check_element_recursive(child, depth + 1):
                                    return True
                            except:
                                continue
                    except:
                        pass

                except Exception as e:
                    print(f"[DEBUG]   检查元素时出错: {e}", file=sys.stderr)

                return False

            return check_element_recursive(app)

        except Exception as e:
            print(f"[DEBUG] 检查菜单控件时发生错误: {e}", file=sys.stderr)
            return False

    def _has_visible_menu_near_coordinates(self, app, x, y, tolerance):
        """
        检查应用程序中是否有坐标附近的可见菜单控件
        """
        try:
            def check_element_recursive(element, depth=0):
                if depth > 5:  # 限制递归深度，快速检查
                    return False

                try:
                    # 检查当前元素是否为可见的菜单相关控件
                    role = element.getRoleName().lower()
                    if any(menu_role in role for menu_role in ['menu', 'item']):
                        # 检查是否可见
                        try:
                            states = element.getState()
                            state_names = [pyatspi.stateToString(state) for state in states.getStates()]
                            if not ('showing' in state_names or 'visible' in state_names):
                                return False
                        except:
                            return False

                        # 检查坐标是否在附近
                        try:
                            component = element.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                # 检查控件是否在坐标附近
                                if (abs(extents.x - x) <= tolerance and abs(extents.y - y) <= tolerance) or \
                                   (extents.x <= x <= extents.x + extents.width and
                                    extents.y <= y <= extents.y + extents.height):
                                    return True
                        except:
                            pass

                    # 递归检查子元素
                    try:
                        for i in range(min(element.childCount, 10)):  # 限制检查的子元素数量
                            child = element.getChildAtIndex(i)
                            if child and check_element_recursive(child, depth + 1):
                                return True
                    except:
                        pass

                except:
                    pass

                return False

            return check_element_recursive(app)

        except Exception as e:
            return False

    def _find_menu_element_at_coordinates(self, desktop, x, y):
        """
        在所有应用程序中查找菜单相关的控件
        专门用于处理特殊弹出窗口（如开始菜单）
        """
        try:
            print(f"[DEBUG] 在所有应用程序中搜索菜单控件，坐标: ({x}, {y})", file=sys.stderr)

            best_menu_element = None
            best_menu_score = 0

            for app_index in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(app_index)
                    if not app:
                        continue

                    app_name = app.name if app.name else ""

                    # 在该应用程序中查找菜单相关的控件
                    menu_element = self._find_menu_element_in_app(app, x, y)
                    if menu_element:
                        # 计算菜单控件的优先级得分
                        menu_score = self._calculate_menu_element_score(menu_element, app_name)

                        print(f"[DEBUG] 在应用程序 {app_name} 中找到菜单控件: {menu_element.name if menu_element.name else 'unnamed'} (得分: {menu_score})", file=sys.stderr)

                        if menu_score > best_menu_score:
                            best_menu_element = menu_element
                            best_menu_score = menu_score
                            print(f"[DEBUG] 更新最佳菜单控件: {menu_element.name if menu_element.name else 'unnamed'} (得分: {menu_score})", file=sys.stderr)

                except Exception as e:
                    continue

            if best_menu_element:
                print(f"[DEBUG] 特殊窗口搜索找到最佳菜单控件: {best_menu_element.name if best_menu_element.name else 'unnamed'}", file=sys.stderr)
            else:
                print(f"[DEBUG] 特殊窗口搜索未找到菜单控件", file=sys.stderr)

            return best_menu_element

        except Exception as e:
            print(f"[DEBUG] 搜索菜单控件时发生错误: {e}", file=sys.stderr)
            return None

    def _get_fresh_desktop(self):
        """
        获取刷新的桌面对象，确保能检测到新窗口
        简化版本，避免阻塞操作
        """
        try:
            cache_key = 'desktop_object'
            current_time = time.time()

            # 检查桌面缓存是否过期
            if (cache_key in self.desktop_cache and
                current_time - self.desktop_cache[cache_key]['time'] < self.desktop_cache_timeout):
                print(f"[DEBUG] 使用缓存的桌面对象", file=sys.stderr)
                return self.desktop_cache[cache_key]['data']

            print(f"[DEBUG] 获取新的桌面对象", file=sys.stderr)

            # 直接获取桌面对象，不进行复杂的刷新操作
            desktop = pyatspi.Registry.getDesktop(0)

            # 更新缓存
            self.desktop_cache[cache_key] = {
                'data': desktop,
                'time': current_time
            }

            print(f"[DEBUG] 桌面对象获取完成，应用程序数量: {desktop.childCount}", file=sys.stderr)
            return desktop

        except Exception as e:
            print(f"[DEBUG] 获取桌面对象时发生错误: {e}", file=sys.stderr)
            # 回退到标准方法
            try:
                return pyatspi.Registry.getDesktop(0)
            except Exception as e2:
                print(f"[DEBUG] 回退方法也失败: {e2}", file=sys.stderr)
                return None

    def _find_menu_element_in_app(self, app, x, y):
        """
        在指定应用程序中查找菜单相关的控件
        """
        try:
            def search_menu_recursive(element, depth=0):
                if depth > 10:  # 限制递归深度
                    return None

                try:
                    # 检查当前元素是否为真正的菜单相关控件
                    role = element.getRoleName().lower()
                    app_name = app.name if app.name else ""

                    # 更严格的菜单控件判断
                    is_real_menu = (
                        'menu item' in role or 'menuitem' in role or
                        ('list item' in role and any(menu_app in app_name.lower() for menu_app in ['menu', 'launcher']))
                    )

                    if is_real_menu:
                        # 检查坐标是否在控件范围内
                        try:
                            component = element.queryComponent()
                            if component:
                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                if (extents.x <= x <= extents.x + extents.width and
                                    extents.y <= y <= extents.y + extents.height):
                                    return element
                        except:
                            pass

                    # 递归搜索子元素
                    try:
                        for i in range(element.childCount):
                            child = element.getChildAtIndex(i)
                            if child:
                                result = search_menu_recursive(child, depth + 1)
                                if result:
                                    return result
                    except Exception as e:
                        print(e)

                except Exception as e:
                    print(e)

                return None

            return search_menu_recursive(app)

        except Exception as e:
            print(e)
            return None

    def _calculate_menu_element_score(self, element, app_name):
        """
        计算菜单元素的优先级得分
        """
        score = 0

        try:
            # 基础得分
            role = element.getRoleName().lower()
            if 'item' in role:
                score += 100  # 菜单项优先级最高
            elif 'menu' in role:
                score += 80   # 菜单容器次之

            # 应用程序名称加分
            app_name_lower = app_name.lower() if app_name else ""
            if any(desktop_name in app_name_lower for desktop_name in ['panel', 'launcher', 'desktop', 'menu']):
                score += 50  # 桌面环境相关应用程序

            # 控件名称加分
            if element.name and element.name.strip():
                score += 30

            # 状态加分
            try:
                states = element.getState()
                state_names = [pyatspi.stateToString(state) for state in states.getStates()]
                if 'showing' in state_names or 'visible' in state_names:
                    score += 20
                if 'focused' in state_names or 'active' in state_names:
                    score += 40
            except Exception as e:
                print(e)

        except Exception as e:
            print(e)

        return score

    def _calculate_name_match_score(self, app_name, window_class):
        """计算应用程序名称与窗口类名的匹配得分"""
        if not app_name or not window_class:
            return 0

        app_name_lower = app_name.lower()
        window_class_lower = window_class.lower()

        # 直接匹配
        if app_name_lower == window_class_lower:
            return 100

        # 特殊应用匹配规则
        special_matches = {
            # 计算器应用
            'kylin-calculator': ['calculator', 'calc', '计算器', 'kylin-calculator', 'kcalc'],
            'calculator': ['calculator', 'calc', '计算器', 'kylin-calculator', 'kcalc'],
            'calc': ['calculator', 'calc', '计算器', 'kylin-calculator', 'kcalc'],

            # 控制中心/设置应用
            'ukui-control-center': ['设置', 'control-center', 'settings', 'ukui-control-center', '控制中心'],
            'control-center': ['设置', 'control-center', 'settings', 'ukui-control-center', '控制中心'],
            'settings': ['设置', 'control-center', 'settings', 'ukui-control-center', '控制中心'],

            # 文件管理器
            'peony': ['文件管理器', 'peony', 'file-manager', 'files'],

            # 终端
            'terminal': ['terminal', '终端', 'mate-terminal', 'gnome-terminal'],
            'mate-terminal': ['terminal', '终端', 'mate-terminal', 'gnome-terminal'],

            # 浏览器
            'firefox': ['firefox', '火狐', 'browser'],
            'chromium': ['chromium', 'chrome', 'browser'],

            # 文本编辑器
            'gedit': ['gedit', 'text-editor', '文本编辑器'],
            'pluma': ['pluma', 'text-editor', '文本编辑器'],
        }

        # 检查特殊匹配规则
        for window_key, app_patterns in special_matches.items():
            if window_key in window_class_lower:
                for pattern in app_patterns:
                    if pattern in app_name_lower:
                        return 95

        # 反向检查：应用名称在窗口类名的匹配模式中
        for window_key, app_patterns in special_matches.items():
            for pattern in app_patterns:
                if pattern in app_name_lower and window_key in window_class_lower:
                    return 95

        # 包含匹配
        if 'peony' in window_class_lower and ('文件管理器' in app_name or 'peony' in app_name_lower):
            return 90

        if 'terminal' in window_class_lower and ('terminal' in app_name_lower or '终端' in app_name):
            return 90

        # 计算器特殊处理
        if 'calculator' in window_class_lower or 'calc' in window_class_lower:
            if any(calc_word in app_name_lower for calc_word in ['calc', 'calculator', '计算器']):
                return 90

        # 部分匹配
        if any(part in app_name_lower for part in window_class_lower.split('.') if len(part) > 2):
            return 50

        # 模糊匹配：检查关键词
        window_keywords = [part for part in window_class_lower.replace('-', ' ').replace('.', ' ').split() if len(part) > 2]
        app_keywords = [part for part in app_name_lower.replace('-', ' ').replace('.', ' ').split() if len(part) > 2]

        for window_keyword in window_keywords:
            for app_keyword in app_keywords:
                if window_keyword in app_keyword or app_keyword in window_keyword:
                    return 40

        return 0

    def _calculate_loose_match_score(self, app_name, window_class):
        """计算更宽松的应用程序名称与窗口类名匹配得分"""
        if not app_name or not window_class:
            return 0

        app_name_lower = app_name.lower()
        window_class_lower = window_class.lower()

        # 提取关键词进行匹配
        window_keywords = []
        for part in window_class_lower.replace('-', ' ').replace('.', ' ').split():
            if len(part) > 2:  # 忽略太短的词
                window_keywords.append(part)

        app_keywords = []
        for part in app_name_lower.replace('-', ' ').replace('.', ' ').split():
            if len(part) > 2:  # 忽略太短的词
                app_keywords.append(part)

        # 计算关键词匹配度
        match_count = 0
        total_keywords = len(window_keywords)

        if total_keywords == 0:
            return 0

        for window_keyword in window_keywords:
            for app_keyword in app_keywords:
                # 完全匹配
                if window_keyword == app_keyword:
                    match_count += 2
                # 包含匹配
                elif window_keyword in app_keyword or app_keyword in window_keyword:
                    match_count += 1

        # 特殊情况：计算器应用
        if 'calculator' in window_class_lower or 'calc' in window_class_lower:
            if any(calc_word in app_name_lower for calc_word in ['calc', 'calculator', '计算器', 'kcalc']):
                match_count += 3

        # 计算得分
        if match_count > 0:
            score = min(30, (match_count * 10))  # 最高30分，避免与精确匹配冲突
            return score

        return 0

    def _find_element_at_coordinates(self, app, x, y):
        """在应用程序中查找包含指定坐标的最佳控件"""
        try:
            def search_recursive(element, depth=0):
                if depth > 15:  # 增加搜索深度限制，以便找到更深层的控件
                    return None

                try:
                    if hasattr(element, 'queryComponent'):
                        component = element.queryComponent()
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)

                        # 检查坐标是否在元素范围内
                        if (extents.x <= x < extents.x + extents.width and
                            extents.y <= y < extents.y + extents.height):

                            # 深度优先搜索：优先返回可交互的最深层控件
                            best_result = None
                            best_score = -1

                            # 遍历所有子控件，寻找包含目标坐标的最佳控件
                            for i in range(element.childCount):
                                try:
                                    child = element.getChildAtIndex(i)
                                    child_result = search_recursive(child, depth + 1)
                                    if child_result:
                                        try:
                                            child_extents = child_result.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                                            child_area = child_extents.width * child_extents.height

                                            # 计算控件优先级得分
                                            score = self._calculate_control_priority_score(child_result, child_area, depth + 1)

                                            # 选择得分最高的控件
                                            if score > best_score:
                                                best_result = child_result
                                                best_score = score

                                        except:
                                            # 如果无法获取面积信息，仍然保存结果
                                            if best_result is None:
                                                best_result = child_result
                                except:
                                    continue

                            # 返回最佳子控件，如果没有找到子控件则返回当前元素
                            return best_result if best_result else element

                except:
                    pass

                return None

            # 从窗口级别开始搜索，而不是应用程序级别
            # 因为应用程序对象不支持queryComponent()接口
            for i in range(app.childCount):
                try:
                    window = app.getChildAtIndex(i)
                    if window:
                        result = search_recursive(window)
                        if result:
                            return result
                except Exception as e:
                    print(e)
                    continue
            return None

        except Exception as e:
            print(f"[DEBUG] 在应用程序中查找控件失败: {e}", file=sys.stderr)
            return None

    def _calculate_control_priority_score(self, element, area, depth):
        """计算控件的优先级得分，得分越高优先级越高"""
        try:
            score = 0

            # 基础深度得分：深度越深得分越高
            score += depth * 10

            # 控件角色得分：可交互控件得分更高
            try:
                role = element.getRoleName() if hasattr(element, 'getRoleName') else ""
                interactive_roles = ['push button', 'button', 'toggle button', 'check box',
                                   'radio button', 'text', 'entry', 'combo box', 'list item',
                                   'menu item', 'tab', 'link', 'label']
                if role in interactive_roles:
                    score += 50
                    if role in ['push button', 'button', 'toggle button']:
                        score += 20  # 按钮类控件额外加分
            except Exception as e:
                print(e)

            # 动作得分：有动作的控件得分更高
            try:
                if hasattr(element, 'queryAction'):
                    action_iface = element.queryAction()
                    if action_iface and action_iface.nActions > 0:
                        score += 30
                        # 特定动作额外加分
                        for i in range(action_iface.nActions):
                            action_name = action_iface.getName(i)
                            if action_name in ['Press', 'Click', 'Activate']:
                                score += 10
            except Exception as e:
                print(e)

            # 面积得分：面积适中的控件得分更高
            if area > 0:
                if area < 50000:  # 小面积控件，可能是精确控件
                    score += 20
                elif area < 100000:  # 中等面积
                    score += 10
                # 大面积控件不加分，可能是容器

            # 叶子控件得分：没有子控件的控件得分更高
            if element.childCount == 0:
                score += 25
            elif element.childCount < 5:  # 子控件较少
                score += 10

            # 名称得分：有名称的控件得分更高
            try:
                if element.name and element.name.strip():
                    score += 15
            except Exception as e:
                print(e)

            return score

        except Exception as e:
            print(e)
            return 0





    def _find_matching_window_element(self, app, target_window_id, x, y):
        """
        在应用程序中查找匹配指定X11窗口ID的控件
        """
        try:
            def search_element(element, depth=0):
                if depth > 10:  # 限制搜索深度
                    return None

                try:
                    # 检查当前元素是否匹配
                    if hasattr(element, 'queryComponent'):
                        component = element.queryComponent()
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)

                        # 检查坐标是否在元素范围内
                        if (extents.x <= x < extents.x + extents.width and
                            extents.y <= y < extents.y + extents.height):

                            # 尝试获取X11窗口ID（如果可用）
                            try:
                                # 某些控件可能有X11窗口ID属性
                                if hasattr(element, 'get_window_id'):
                                    if element.get_window_id() == target_window_id:
                                        return element
                            except:
                                pass

                            # 如果没有直接的窗口ID匹配，检查是否是最合适的控件
                            states = element.getState().getStates()
                            state_names = [pyatspi.stateToString(state) for state in states]

                            if ('showing' in state_names or 'visible' in state_names):
                                # 继续搜索子元素，寻找更精确的匹配
                                best_child = None
                                smallest_area = float('inf')

                                for i in range(element.childCount):
                                    try:
                                        child = element.getChildAtIndex(i)
                                        child_result = search_element(child, depth + 1)
                                        if child_result:
                                            # 选择面积最小的子控件
                                            child_extents = child_result.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                                            child_area = child_extents.width * child_extents.height
                                            if child_area < smallest_area:
                                                smallest_area = child_area
                                                best_child = child_result
                                    except:
                                        continue

                                return best_child if best_child else element

                except Exception as e:
                    print(e)

                return None

            return search_element(app)

        except Exception as e:
            print(f"[DEBUG] 搜索匹配窗口元素失败: {e}", file=sys.stderr)
            return None

    def _get_role_priority_score(self, role):
        """
        根据控件角色返回优先级分数
        """
        role = role.lower() if role else ""

        # 高优先级控件类型
        high_priority_roles = {
            'push button': 150,
            'button': 150,
            'menu item': 140,
            'menuitem': 140,  # 有些系统可能使用这种格式
            'menu': 135,      # 菜单容器，稍低于菜单项
            'text': 130,
            'entry': 130,
            'combo box': 120,
            'list item': 120,
            'listitem': 120,  # 有些系统可能使用这种格式
            'check box': 110,
            'radio button': 110,
            'link': 100,
            'toggle button': 100
        }

        # 中等优先级控件类型
        medium_priority_roles = {
            'label': 50,
            'icon': 50,
            'image': 40,
            'separator': 30,
            'scroll bar': 20,
            'tool bar': 20
        }

        # 低优先级控件类型
        low_priority_roles = {
            'panel': -50,
            'filler': -60,
            'pane': -70,
            'window': -100,
            'frame': -100,
            'application': -200
        }

        # 查找匹配的角色
        for pattern, score in high_priority_roles.items():
            if pattern in role:
                return score

        for pattern, score in medium_priority_roles.items():
            if pattern in role:
                return score

        for pattern, score in low_priority_roles.items():
            if pattern in role:
                return score

        return 0  # 默认分数

    def _generate_meaningful_name(self, element, data, x, y):
        """
        为没有名称的控件生成有意义的名称
        参数:
        - element: 控件元素
        - data: 控件数据字典
        - x, y: 坐标
        返回值: 生成的名称字符串
        """
        try:
            # 1. 尝试从角色名称生成名称
            role = data.get('Rolename', '').lower()
            if role and role != 'n/a':
                role_names = {
                    'page tab list': '标签页列表',
                    'push button': '按钮',
                    'button': '按钮',
                    'text': '文本',
                    'entry': '输入框',
                    'menu item': '菜单项',
                    'check box': '复选框',
                    'radio button': '单选按钮',
                    'combo box': '下拉框',
                    'list item': '列表项',
                    'label': '标签',
                    'icon': '图标',
                    'image': '图像',
                    'scroll bar': '滚动条',
                    'panel': '面板',
                    'filler': '填充区域',
                    'pane': '窗格',
                    'frame': '框架',
                    'window': '窗口',
                    'dialog': '对话框',
                    'menu': '菜单',
                    'tool bar': '工具栏'
                }

                # 查找匹配的角色名称
                for en_role, cn_name in role_names.items():
                    if en_role in role:
                        # 添加位置信息使名称更具体
                        coords = data.get('Coords', {})
                        if coords and isinstance(coords, dict):
                            pos_info = f"({coords.get('x', x)},{coords.get('y', y)})"
                            return f"{cn_name}_{pos_info}"
                        else:
                            return f"{cn_name}_{x}_{y}"

            # 2. 尝试从描述生成名称
            description = data.get('Description', '')
            if description and description != 'N/A' and description.strip():
                # 清理描述文本
                clean_desc = description.strip()[:20]  # 限制长度
                return f"{clean_desc}_{x}_{y}"

            # 3. 尝试从文本内容生成名称
            text_content = data.get('Text', '')
            if text_content and text_content != 'Not available: ' and text_content.strip():
                # 清理文本内容
                clean_text = text_content.strip()[:15]  # 限制长度
                return f"文本_{clean_text}_{x}_{y}"

            # 4. 尝试从操作生成名称
            actions = data.get('Actions', [])
            if actions and isinstance(actions, list) and len(actions) > 0:
                primary_action = actions[0] if actions[0] != 'Not available: ' else None
                if primary_action:
                    action_names = {
                        'click': '可点击控件',
                        'activate': '可激活控件',
                        'toggle': '可切换控件',
                        'press': '可按压控件',
                        'select': '可选择控件'
                    }

                    for action_key, action_name in action_names.items():
                        if action_key in primary_action.lower():
                            return f"{action_name}_{x}_{y}"

            # 5. 尝试从窗口信息生成名称
            window_name = data.get('WindowName', '')
            process_name = data.get('ProcessName', '')
            if window_name and window_name != 'N/A':
                return f"{window_name}中的控件_{x}_{y}"
            elif process_name and process_name != 'N/A':
                return f"{process_name}控件_{x}_{y}"

            # 6. 基于状态生成描述性名称
            states = data.get('States', [])
            if states and isinstance(states, list):
                if 'focused' in states:
                    return f"焦点控件_{x}_{y}"
                elif 'selected' in states:
                    return f"选中控件_{x}_{y}"
                elif 'pressed' in states:
                    return f"按下控件_{x}_{y}"
                elif 'focusable' in states:
                    return f"可聚焦控件_{x}_{y}"
                elif 'selectable' in states:
                    return f"可选择控件_{x}_{y}"

            # 7. 最后的备选方案 - 使用角色和坐标
            if role and role != 'n/a':
                return f"{role.title()}_{x}_{y}"
            else:
                return f"Control_{x}_{y}"

        except Exception as e:
            print(f"[WARNING] 生成控件名称时发生错误: {e}", file=sys.stderr)
            return f"Control_{x}_{y}"

    def _find_treeEle(self, element):

        if element.name == "音乐":
            data = UNI._extract_element_info(self, element)
            self.last_appEle.append(data)

        for i in range(element.childCount):
            child = element.getChildAtIndex(i)
            UNI._find_treeEle(self, child)

        return self.last_appEle

    # 获取某app中所有控件信息存入last_appEle
    def _find_appallEle(self, window):

        desktop = pyatspi.Registry.getDesktop(0)
        self.desktopchildcount = desktop.childCount

        self.last_appEle = []
        UNI._find_treeEle(self, window)

    # 获取document信息
    def _get_document_info(self, element):
        try:
            doc_interface = element.queryDocument()
            return {
                "Locale": doc_interface.getLocale(),
                "Attributes": doc_interface.getAttributes()
            }
        except (NotImplementedError, AttributeError):
            return "Not available"

    # 获取hyperlink信息
    def _get_hyperlink_info(self, element):
        try:
            hyperlink_interface = element.queryHyperlink()
            if hyperlink_interface:
                return {
                    "URI": hyperlink_interface.getURI(0),
                    "StartIndex": hyperlink_interface.startIndex,
                    "EndIndex": hyperlink_interface.endIndex
                }
            return "Not available"
        except (NotImplementedError, AttributeError):
            return "Not available"

    def _get_element_path(self, element):
        path = []
        index = element.getIndexInParent()
        if index != -1:
            path.insert(0, index)
        else:
            index = None
            parent = element.parent
            if parent:
                for i in range(parent.childCount):
                     if element == parent.getChildAtIndex(i):
                         index = i

                if index:

                    path.insert(0, index)
            else:
                path.insert(0, 0)
                parentlen = len(path)
                return path, parentlen

        while element:
            if element.getRoleName() == "application":
                break
            parent = element.parent
            if parent:
                index = parent.getIndexInParent()

                if index != -1:
                    path.insert(0, index)
            element = parent

        #path = path[1:]
        parentlen = len(path)
        return path, parentlen

    def _extract_element_info(self, element, indent=0):
        # 提取元素的详细信息
        data = {
            "Name": element.name if element.name else "N/A",
            "ID": element.id if element.id else "N/A",
            "ProcessID": element.get_process_id() if hasattr(element, 'get_process_id') else "N/A",
            # "Role": element.get_role() if hasattr(element, 'get_role') else "N/A",
            "Rolename": element.getRoleName() if element.getRoleName() else "N/A",
            "Description": element.description if element.description else "N/A",
            #"Relation_set": [pyatspi.relationToString(rel.getRelationType()) for rel in element.getRelationSet()] if
            #                hasattr(element, 'getRelationSet') else "N/A",
            "Index_in_parent": element.getIndexInParent() if element.getIndexInParent() else "N/A",
            "ChildrenCount": element.childCount if hasattr(element, 'childCount') else "N/A",
            #"Attributes": element.getAttributes() if hasattr(element, 'getAttributes') else "N/A",
            #"Path": element.path if hasattr(element, 'path') else "N/A",
            #"Properties": {prop.name: getattr(element.props, prop.name) for prop in element.props if
            #               hasattr(element.props, prop.name)},
            # "Children": []
        }

        # ProcessName
        try:
            processid = int(data.get("ProcessID", -1))
            if processid == -1:
                raise ValueError("Invalid process ID")

            cmd = f'ps -p {processid} -o comm='
            # processname = os.system(cmd)
            ret = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            processname = ret.stdout.strip()
            if processname:
                data["ProcessName"] = processname
            else:
                data["ProcessName"] = "N/A"
        except Exception as e:
            data["ProcessName"] = "N/A"
            print(f"Error getting process name for ProcessID {processid}: {e}")

        # 坐标接口
        try:
            extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
            data["Coords"] = {
                "x": extents.x,
                "y": extents.y,
                "width": extents.width,
                "height": extents.height
            }
        except:
            data["Coords"] = "Not available"

        # 文本接口
        # 如果接口不可用，直接设置数据为"Not available"，若为其他原因则附上具体异常信息
        try:
            if element.queryText() is not None:
                text_interface = element.queryText()
                data["Text"] = text_interface.getText(0, -1)
            else:
                data["Text"] = f"Not available"
        except (NotImplementedError, AttributeError) as e:
            data["Text"] = f"Not available: {e}"

        # 可编辑文本接口
        #try:
        #    if element.queryEditableText() is not None:
        #        editable_text_interface = element.queryEditableText()
        #        data["EditableText"] = editable_text_interface.getText(0, -1)
        #    else:
        #        data["EditableText"] = f"Not available"
        #except (NotImplementedError, AttributeError) as e:
        #    data["EditableText"] = f"Not available: {e}"

        # 超文本接口
        #try:
        #    if element.queryHypertext() is not None:
        #        hypertext_interface = element.queryHypertext()
        #        data["Hypertext"] = hypertext_interface.getText(0, -1)
        #    else:
        #        data["Hypertext"] = f"Not available"
        #except (NotImplementedError, AttributeError) as e:
        #    data["Hypertext"] = f"Not available: {e}"

        # 值接口
        #try:
        #    if element.queryValue() is not None:
        #        value_interface = element.queryValue()
        #        data["Value"] = value_interface.currentValue
        #    else:
        #        data["Value"] = "Not available"
        #except (NotImplementedError, AttributeError) as e:
        #    data["Value"] = f"Not available: {e}"

        # 动作接口
        try:
            if element.queryAction() is not None:
                action_interface = element.queryAction()
                data["Actions"] = [action_interface.getName(i) for i in range(action_interface.nActions)]
            else:
                data["Actions"] = "Not available"
        except (NotImplementedError, AttributeError) as e:
            data["Actions"] = f"Not available: {e}"

        # 状态信息
        try:
            states = element.getState().getStates()
            state_names = [pyatspi.stateToString(state) for state in states]
            data["States"] = state_names
        except (NotImplementedError, AttributeError) as e:
            data["States"] = f"Not available: {e}"

        # 完整parent节点
        data["ParentPath"], data["ParentCount"] = UNI._get_element_path(self, element)

        data = UNI._generateKey(self, data)

        return data


    def _generateKey(self, data):
        # "Key": "wps-NNA-DNA-Rfiller-P0000112044002-X123-Y102"

        PPstr = "P"
        for i in data["ParentPath"]:
            PPstr = PPstr + str(i)

        try:
            keystring = "N" + data["Name"] + "-D" + data["Description"] + "-" + PPstr
        except (NotImplementedError, AttributeError) as e:
            print(e)

        keystring = keystring.replace("/","").replace(" ","").replace("\n","").replace("_","-")
        data["Key"] = keystring

        return data

    def kdk_KBToJson_Uni(self, data, filename="data.json"):
        # 根据数据类型将键值或控件信息添加到列表，并在程序终止时保存到文件
        if isinstance(data, dict):  # 控件信息
            UNI._append_control_to_list(self, data)
        else:  # 键值
            if isinstance(data, set):
                UNI._append_key_to_list(self, frozenset(data))
            else:
                UNI._append_key_to_list(self, data)
        UNI._save_data_to_file(self, filename)

    def _append_key_to_list(self, key_values):
        try:
            if isinstance(key_values, str):
                # 单字符，直接记录
                record = {"type": "single", "value": key_values}
            else:
                key_strs = [UNI._filter_key_str(self, k) for k in key_values]
                if len(key_strs) > 1:
                    # 组成组合键字符串，并标记为组合键
                    combined_str = "+".join(key_strs)
                    record = {"type": "combine", "value": combined_str}
                elif len(key_strs) == 1:
                    record = {"type": "single", "value": key_strs[0]}
                else:
                    record = {"type": "single", "value": ""}
            self.data_list["keys"].append(record)
            print(f"Key action added to list: {record}")
        except Exception as e:
            print(f"Error adding key action to list: {e}")

    def _filter_key_str(self, key):
        # 过滤键值字符串，去除不需要的部分
        if hasattr(key, 'char') and key.char:
            return key.char
        elif hasattr(key, 'name') and key.name:
            return key.name
        else:
            return str(key).replace("Key.", "")

    def _append_control_to_list(self, control_data):
        # 将控件信息添加到全局列表
        try:
            self.data_list["controls"].append(control_data)
            print(f"Control {control_data['Name']} added to list.")
        except Exception as e:
            print(f"Error adding control to list: {e}")

    def _save_data_to_file(self, filename="data.json"):
        # 将所有按键写入JSON文件
        try:
            with open(filename, 'w', encoding='UTF-8') as json_file:
                json.dump(self.data_list, json_file, indent=4, ensure_ascii=False)
            print(f"Data saved to {filename}")
        except TypeError as e:
            print(f"Error saving data to file: {e}")
            print("Data that caused error:", self.data_list)
        except Exception as e:
            print(f"Unexpected error saving data to file: {e}")

    def kdk_getElePos_Uni(self, control_info):
        try:
            if control_info["MenuElement"] == "1":
                # 读监听信息
                try:
                    with open("/tmp/.recordmenu.txt", 'r') as json_file:
                        data = json.load(json_file)
                    if data:
                        for value in data:
                            if (control_info["Name"] == "N/A" or control_info["Name"] == data[value]["Name"]) and (control_info["Description"] == "N/A" or control_info["Description"] == data[value]["Description"]) and control_info["Rolename"] == data[value]["Rolename"] and control_info["Coords"]['width'] == data[value]["Coords"]['width'] and control_info["Coords"]['height'] == data[value]["Coords"]['height'] and control_info["Actions"] == data[value]["Actions"] and control_info["States"] == data[value]["States"]:
                                #x = control_info["Coords"]["x"]
                                #y = control_info["Coords"]["y"]
                                x = data[value]["Coords"]['x']
                                y = data[value]["Coords"]['y']
                                width = control_info["Coords"]["width"]
                                height = control_info["Coords"]["height"]
                                if 'RecordPosition' in control_info and x <= control_info["RecordPosition"][0] <= x+width and y <= control_info["RecordPosition"][1] <= y+height:
                                    center_x = control_info["RecordPosition"][0]
                                    center_y = control_info["RecordPosition"][1]
                                else:
                                    center_x = x + width // 2
                                    center_y = y + height // 2
                                return center_x, center_y, width, height, x, y
                except Exception as e:
                    print(f"Error in kdk_getElePos_Uni: {e}")
                    return None,None,None,None,None,None
            elif control_info["MenuElement"] == "2":
                try:
                    with open("/tmp/.recordmenu1.txt", 'r') as json_file:
                        data = json.load(json_file)
                    if data:
                        for value in data:
                            if (control_info["Name"] == "N/A" or control_info["Name"] == data[value]["Name"]) and (control_info["Description"] == "N/A" or control_info["Description"] == data[value]["Description"]) and control_info["Rolename"] == data[value]["Rolename"] and control_info["Coords"]['width'] == data[value]["Coords"]['width'] and control_info["Coords"]['height'] == data[value]["Coords"]['height'] and control_info["Actions"] == data[value]["Actions"] and control_info["States"] == data[value]["States"]:
                                #x = control_info["Coords"]["x"]
                                #y = control_info["Coords"]["y"]
                                x = data[value]["Coords"]['x']
                                y = data[value]["Coords"]['y']
                                width = control_info["Coords"]["width"]
                                height = control_info["Coords"]["height"]
                                if 'RecordPosition' in control_info and x <= control_info["RecordPosition"][0] <= x+width and y <= control_info["RecordPosition"][1] <= y+height:
                                    center_x = control_info["RecordPosition"][0]
                                    center_y = control_info["RecordPosition"][1]
                                else:
                                    center_x = x + width // 2
                                    center_y = y + height // 2
                                return center_x, center_y, width, height, x, y
                except Exception as e:
                    print(f"Error in kdk_getElePos_Uni: {e}")
                    return None,None,None,None,None,None
        except Exception as e:
            print(e)

        # 根据控件信息获取到x,y并返回
        try:
            window = UNI._get_window_by_info(self, control_info)
            if window is None:
                return None
            UNI._find_component_by_info(self, window, control_info)
            
            component = self.finddata
            extents = self.findextents

            # AI助手特殊处理
            if window.name == "kylin-aiassistant":
                offsetx,offsety = UNI._aiassistantoffsetdeal(self, window)
                extents.x = extents.x + offsetx
                extents.y = extents.y + offsety
            
            self.finddata = None
            self.findextents = None
            self.findfind = None

            if component and extents:
                print(f"component: {component.name}, extents: {extents}")
                # offxy处理
                try:
                    windowp = window.parent
                    windowname = windowp.name
                except:
                    windowname = None
                offxy = self.getoffxy1(control_info["WindowName"],extents,control_info["ProcessName"],windowname)
                if offxy:
                    # 判断RecordPosition
                    if 'RecordPosition' in control_info and extents.x+offxy[0] <= control_info["RecordPosition"][0] <= extents.x+offxy[0]+extents.width and extents.y+offxy[1] <= control_info["RecordPosition"][1] <= extents.y+offxy[1]+extents.height:
                            center_x = control_info["RecordPosition"][0]
                            center_y = control_info["RecordPosition"][1]
                    else:
                        # 计算中心点
                        center_x = extents.x + offxy[0] + extents.width // 2
                        center_y = extents.y + offxy[1] + extents.height // 2

                    return center_x, center_y, extents.width, extents.height, extents.x + offxy[0], extents.y + offxy[1]
                else:
                    print("窗口获取异常，偏移量获取失败")
                    return None,None,None,None,None,None
            else:
                print("Component or extents not found.")
                return None,None,None,None,None,None
        except Exception as e:
            print(f"Error in kdk_getElePos_Uni: {e}")
            return None,None,None,None,None,None
            
    def getoffxy1(self,windowname,extents,processname,parentname):   
        # 根据windowname获取到窗口列表
        windowidlist = []
        windowid = None
        if processname == "mate-terminal" or processname == "文件管理器":
            username = os.popen("whoami").readline().strip()
            hostname = os.popen("hostname").readline().strip()
            windowname = windowname.replace("hostname",hostname).replace("username", username)
        if processname == "文件管理器":
            processname = "peony"
        
        # 通过wlcctrl获取窗口id
        cmd = f'wlcctrl -l|grep -w "{windowname}"|wc -l'
        output0 = os.popen(cmd).read().strip()
        if output0 == 1:
            cmd = f'wlcctrl -l|grep -w -B 1 "{windowname}"|grep toplevel'
            output = os.popen(cmd).read().strip()
            if output:
                windowid = output.split('"', 1)[1].strip().replace('"','')
        elif output0 == 0:
            print(f"获取窗口失败：未获取到任何windowname为{windowname}的窗口")
            return None
        else:
            # 根据processname取出真实的窗口
            cmd = f'wlcctrl -l|grep -w -B 1 "{windowname}"|grep toplevel'
            output = os.popen(cmd).read().strip()
            for i in output.split('"'):
                if "toplevel" in i or not i:
                    continue
                else:
                    output2 = os.popen(f'wlcctrl --getwindowname {i}|grep name').read().strip()
                    wname = output2.split(":", 1)[1].strip()
                    if wname in processname or processname in wname:
                        windowidlist.append(i)
            if len(windowidlist) == 1:
                windowid = windowidlist[0]
            elif len(windowidlist) == 0:
                print(f"获取窗口失败：未获取到任何processname为{processname}的窗口")
                return None
            else:
                cmd = "wlcctrl --getactivewindow|awk -F ' ' '{print $3}'"
                output = os.popen(cmd).read().strip()
                for i in windowidlist:
                    if i == output:
                        windowid = i 
        if not windowid:
            windowidlist = []
            if parentname:
                # 说明通过windowname找不到，则通过parentname来找
                cmd = f'wlcctrl -l|grep -w "{parentname}"|wc -l'
                output0 = os.popen(cmd).read().strip()
                if output0 == 1:
                    cmd = f'wlcctrl -l|grep -w -B 1 "{parentname}"|grep toplevel'
                    output = os.popen(cmd).read().strip()
                    if output:
                        windowid = output.split('"', 1)[1].strip().replace('"','')
                    else:
                        print(f"获取窗口失败：获取parent-windowname为{parentname}的窗口id失败")
                        return None
                elif output0 == 0:
                    print(f"获取窗口失败：未获取到任何parent-windowname为{parentname}的窗口")
                    return None
                else:
                    cmd = f'wlcctrl -l|grep -w -B 1 "{parentname}"|grep toplevel'
                    output = os.popen(cmd).read().strip()
                    for i in output.split('"'):
                        if "toplevel" in i or not i:
                            continue
                        else:
                            output2 = os.popen(f'wlcctrl --getwindowname {i}|grep name').read().strip()
                            wname = output2.split(":", 1)[1].strip()
                            if wname in processname or processname in wname:
                                windowidlist.append(i)
                    if len(windowidlist) == 1:
                        windowid = windowidlist[0]
                    elif len(windowidlist) == 0:
                        print(f"获取窗口失败：未获取到任何parent-processname为{processname}的窗口")
                        return None
                    else:
                        cmd = "wlcctrl --getactivewindow|awk -F ' ' '{print $3}'"
                        output = os.popen(cmd).read().strip()
                        for i in windowidlist:
                            if i == output:
                                windowid = i
                if not windowid:
                    print(f"获取窗口失败:根据windowname和parentname均无法获取到窗口")
                    return None
            else:
                print("获取窗口失败:不存在parentname")
                return None
        if windowid:
            # windowname和processname均匹配上，则获取offxy
            # 获取实际窗口范围
            output1 = os.popen(f"wlcctrl --getwindowgeometry {windowid}").read().strip()
            pattern = r'-?\d+'
            match = re.findall(pattern, output1)
            if match:
                windowinfo = [int(i) for i in match]
            else:
                print("获取窗口位置失败")
                return None
            output3 = os.popen(f'wlcctrl --getwindowname {windowid}|grep pid').read().strip()
            wpid = int(output3.split()[1])
            # 获取atspi窗口范围
            windows = self.getatspiwindow()
            for window in windows:
                try:
                    extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    wpid1 = window.get_process_id()
                    wname = window.name
                except:
                    continue
                if (wname == windowname or wpid1 == wpid) and extents.width == windowinfo[2] and (extents.height == windowinfo[3] or extents.height+38 == windowinfo[3]):
                    offx = windowinfo[0]-extents.x-(extents.width-windowinfo[2])
                    offy = windowinfo[1]-extents.y-(extents.height-windowinfo[3])
                    offxy = [offx, offy]
                    break
        return offxy
        
    def getoffxy(self,windowname,extents,processname,parentname):
        offxy = None
        windowid = None
        if processname == "mate-terminal" or processname == "文件管理器":
            username = os.popen("whoami").readline().strip()
            hostname = os.popen("hostname").readline().strip()
            windowname = windowname.replace("hostname",hostname).replace("username", username)

        # 通过wlcctrl获取窗口id
        cmd = f'wlcctrl -l|grep -w "{windowname}"|wc -l'
        output0 = os.popen(cmd).read().strip()
        if output0 == 1:
            cmd = f'wlcctrl -l|grep -w -B 1 "{windowname}"|grep toplevel'
            output = os.popen(cmd).read().strip()
            if output:
                windowid = output.split('"', 1)[1].strip().replace('"','')
        else:
            cmd = "wlcctrl --getactivewindow|awk -F ' ' '{print $3}'"
            output = os.popen(cmd).read().strip()
            if output:
                windowid = output
        if not windowid:
            if parentname:
                # 说明通过windowname找不到，则通过parentname来找
                cmd = f'wlcctrl -l|grep -w "{parentname}"|wc -l'
                output0 = os.popen(cmd).read().strip()
                if output0 == 1:
                    cmd = f'wlcctrl -l|grep -w -B 1 "{parentname}"|grep toplevel'
                    output = os.popen(cmd).read().strip()
                    if output:
                        windowid = output.split('"', 1)[1].strip().replace('"','')
                    else:
                        print("获取窗口失败")
                        return None
                else:
                    cmd = "wlcctrl --getactivewindow|awk -F ' ' '{print $3}'"
                    output = os.popen(cmd).read().strip()
                    if output:
                        windowid = output
                        print(windowid)
                    else:
                        print("获取窗口失败")
                        return None
            else:
                print("获取窗口失败")
                return None
        print(windowid)
        # 判断processname是否匹配，若不匹配则返回
        output2 = os.popen(f'wlcctrl --getwindowname {windowid}|grep name').read().strip()
        wname = output2.split(":", 1)[1].strip()
        output3 = os.popen(f'wlcctrl --getwindowname {windowid}|grep pid').read().strip()
        wpid = int(output3.split()[1])
        if wname in processname or processname in wname:
            # windowname和processname均匹配上，则获取offxy
            # 获取实际窗口范围
            output1 = os.popen(f"wlcctrl --getwindowgeometry {windowid}").read().strip()
            pattern = r'geometry.*?\((\d+)[, ,]\s*(\d+)\)\s*(\d+)\s*x\s*(\d+)'
            match = re.search(pattern, output1)
            if match:
                windowinfo = [int(match.group(i)) for i in range(1,5)]
            # 获取atspi窗口范围
            windows = self.getatspiwindow()
            for window in windows:
                try:
                    extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    wpid1 = window.get_process_id()
                    wname = window.name
                except:
                    continue
                if (wname == windowname or wpid1 == wpid) and extents.width == windowinfo[2] and (extents.height == windowinfo[3] or extents.height+38 == windowinfo[3]):
                    offx = windowinfo[0]-extents.x-(extents.width-windowinfo[2])
                    offy = windowinfo[1]-extents.y-(extents.height-windowinfo[3])
                    offxy = [offx, offy]
                    break
        return offxy
    
    def _aiassistantoffsetdeal(self, window):
        
        offflag = 0
        for i in range(window.childCount):
            if window.getChildAtIndex(i).name == "AI 助手":
                try:
                    aextents = window.getChildAtIndex(i).queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                except Exception as e:
                    print(e)
            if window.getChildAtIndex(i).name == "Offscreen":
                try:
                    if offflag == 0:
                        bextents = window.getChildAtIndex(i).queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    offflag = 1
                except Exception as e:
                    print(e)
        if aextents and bextents:
            offsetx = aextents.x - bextents.x
            offsety = 40 + aextents.y - bextents.y
            return offsetx, offsety
        else:
            return 0,0

    def _get_window_by_info(self, control_info):
        # 根据控件信息获取窗口对象
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            matching_windows = []
            username = os.popen("whoami").readline().strip()
            hostname = os.popen("hostname").readline().strip()
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                # print(app,app.name==control_info["WindowName"],control_info["WindowName"])
                if control_info["ProcessName"] == "peony":
                    control_info["ProcessName"] = "文件管理器"
                if (app.name == control_info["WindowName"] or app.name == 'N/A' or control_info["ProcessName"] in app.name) and app.childCount > 0:
                    # if app.name == "ukui-menu":
                    #     return app
                    # if app.name == "设置":
                    #     return app
                    return app
                    #matching_windows.append(app)
                for j in range(app.childCount):
                    window = app.getChildAtIndex(j)
                    if control_info['WindowName'] in window.name or window.name == 'N/A' or ("@" in control_info["WindowName"] and "@" in window.name) or (("username" in control_info["WindowName"] or "hostname" in control_info["WindowName"]) and control_info["WindowName"].replace("username", username).replace("hostname", hostname) in window.name):
                        #return window
                        matching_windows.append(window)
            if not matching_windows:
                return None


            for window in matching_windows:
                processid = window.get_process_id() if hasattr(window, 'get_process_id') else "N/A"
                cmd = f'ps -p {processid} -o comm='
                ret = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                processname = ret.stdout.strip()
                windowRoleName = window.getRoleName()
                if processname == control_info['ProcessName'] and windowRoleName == control_info["WindowRoleName"]:
                    return window

        except Exception as e:
            print(f"Error getting window by info: {e}")
        return None

    def _find_component_by_info(self, window, control_info):

        # 先根据PP找控件，根据window找
        try:
            element = window
            for i in control_info["ParentPath"]:
                element = element.getChildAtIndex(i)
            path, _ = UNI._get_element_path(self, element)
            if ((path == control_info["ParentPath"]) or control_info["Name"] != "N/A" and element.name == control_info["Name"]) or (control_info["Description"] != "N/A" and element.description == control_info["Description"]) or (element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS).x == control_info['Coords']['x'] and element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS).y == control_info['Coords']['y']):
                extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                self.finddata, self.findextents = element, extents
                self.findfind = 1
                return
        except Exception as e:
            print(e)

        #若不是application，则去掉第一个节点
        try:
            element = window
            for i in control_info["ParentPath"][1:]:
                element = element.getChildAtIndex(i)
            path, _ = UNI._get_element_path(self, element)
            if ((path == control_info["ParentPath"]) or control_info["Name"] != "N/A" and element.name == control_info["Name"]) or (control_info["Description"] != "N/A" and element.description == control_info["Description"]) or (element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS).x == control_info['Coords']['x'] and element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS).y == control_info['Coords']['y']):
                extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                self.finddata, self.findextents = element, extents
                self.findfind = 1
                return
        except Exception as e:
            print(e)
        try:
            element2 = UNI._find_component_fromparent(self, element.parent, control_info)
            if element2:
                extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                self.finddata, self.findextents = element, extents
                self.findfind = 1
                return
        except Exception as e:
            print(e)
        #若找到的是第二层节点，前面都找不到，则从application找
        try:
            element = window.parent
            for i in control_info["ParentPath"]:
                element = element.getChildAtIndex(i)
            path, _ = UNI._get_element_path(self, element)
            if ((path == control_info["ParentPath"]) or control_info["Name"] != "N/A" and element.name == control_info["Name"]) or (control_info["Description"] != "N/A" and element.description == control_info["Description"]) or (element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS).x == control_info['Coords']['x'] and element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS).y == control_info['Coords']['y']):
                extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                self.finddata, self.findextents = element, extents
                self.findfind = 1
                return
        except Exception as e:
            print(e)
        # 如果有name或description，从整个window找匹配name、description、rolename、parentcount、width/height
        try:
            if control_info["Name"] != "N/A" or control_info["Description"] != "N/A":
                self.xxx = None
                self.flag = 0
                UNI._ffff(self, window, control_info)
                if self.flag != 0:
                    self.finddata = self.xxx
                    extents = self.finddata.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    self.findextents = extents
                    self.findfind = 1
                    return
        except Exception as e:
            print(e)

    def _ffff(self, element, control_info):
        try:
            if self.flag == 0 :
                if (control_info["Name"] == "N/A" or control_info["Name"] == element.name) and (control_info["Description"] == "N/A" or control_info["Description"] == element.description) and control_info["Rolename"] == element.getRoleName():
                    _, PC = UNI._get_element_path(self, element)
                    if PC == control_info["ParentCount"]:
                        self.xxx = element
                        self.flag = 1
        except Exception as e:
            print(e)
        for i in range(element.childCount):
            child = element.getChildAtIndex(i)
            UNI._ffff(self, child, control_info)

    def _find_component_fromparent(self, element, control_info):

        for i in element:
            try:
                if control_info["Name"] != "N/A" and i.name == control_info["Name"]:
                    return i
                if control_info["Description"] != "N/A" and i.description == control_info["Description"]:
                    return i
                if i.queryComponent().getExtents(pyatspi.DESKTOP_COORDS).x == control_info['Coords']['x'] and i.queryComponent().getExtents(pyatspi.DESKTOP_COORDS).y == control_info['Coords']['y']:
                    return i
            except Exception as e:
                print(e)
        return None


    def _get_window_by_windowname(self, window_name):
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            window_list = []
            # 在 desktop 中获取所有应用程序
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                if app.name == window_name or window_name == "N/A":
                    window_list.append(app)
                else:
                    # 在应用程序的子窗口中查找
                    for j in range(app.childCount):
                        window = app.getChildAtIndex(j)
                        if window.name == window_name or window_name == "N/A":
                            window_list.append(window)
            if not window_list:
                return None

            return window_list

        except Exception as e:
            print(f"Error getting matching window by window name: {e}")
            return None

    def kdk_getWinAllEle_Uni(self, window_name):
        try:
            app_list = UNI._get_window_by_windowname(self, window_name)
            if not app_list:
                return None
            print(app_list[0].name)
            all_controls = []
            for app in app_list:
                self.last_appEle = []
                UNI._find_treeEle(self, app)
                all_controls.extend(self.last_appEle)
            return all_controls

        except Exception as e:
            print(f"Error in kdk_getWinAllEle_Uni: {e}")
            return None

    def _is_real_exist(self, control_info):
        if control_info["Coords"]["x"] != 'N/A' and control_info["Coords"]["x"] != 0 and control_info["Coords"]["y"] != 'N/A' and control_info["Coords"]["y"] != 0:
            if "enabled" in control_info['States'] or "showing" in control_info['States'] or "visible" in control_info['States'] or control_info['Actions']:
                return True
        return False

    def kdk_judgeEleExist_Uni(self, window_name, control_name):
        try:
            windows = UNI._get_window_by_windowname(self, window_name)
            if not windows:
                return None, f"未找到名为 {window_name} 的窗口"

            for window in windows:
                self.last_appEle = []
                UNI._find_treeEle(self, window)

                # 查找控件
                for control in self.last_appEle:
                    if control["Name"] == control_name:
                        if UNI._is_real_exist(self, control):
                            return True, f"找到"
            return False, f"在 {window_name} 窗口中未找到名为 {control_name} 的控件"
        except Exception as e:
            print(f"Error in kdk_judgeEleExist_Uni: {e}")
            return None, f"异常"

    def _is_visible_button(self, control_info):
        if control_info["Coords"]["x"] != 'N/A' and control_info["Coords"]["x"] > 0 and control_info["Coords"]["y"] != 'N/A' and control_info["Coords"]["y"] > 0:
            if "showing" in control_info['States'] or "visible" in control_info['States']:
                return True
        return False

    def kdk_getObjPos_Uni(self, window_name, control_name):
        try:
            windows = UNI._get_window_by_windowname(self, window_name)
            if not windows:
                return None, f"未找到名为 {window_name} 的窗口"

            matching_ctl = []
            for window in windows:
                self.last_appEle = []
                UNI._find_treeEle(self, window)

                # 查找控件
                for control in self.last_appEle:
                    if control["Name"] == control_name:
                        # 判断控件是否为非隐藏按钮
                        if UNI._is_visible_button(self, control):
                            # 解析控件位置
                            x = control["Coords"]["x"]
                            y = control["Coords"]["y"]
                            width = control["Coords"]["width"]
                            height = control["Coords"]["height"]
                            # 计算中心位置
                            center_x = x + width // 2
                            center_y = y + height // 2
                            # matching_ctl.append((center_x,center_y))
                            matching_ctl.append([x, y, width, height, center_x, center_y])
            if matching_ctl:
                info = f"找到"
            else:
                info = f"在 {window_name} 窗口中未找到名为 {control_name} 的控件"
            return matching_ctl, info
        except Exception as e:
            print(f"Error in kdk_getObjPos_Uni: {e}")
            return None, f"异常"

    def _jietu_wayland(self, name, info):
        """
        Wayland环境下的截图功能
        参数:
        - name: 文件名
        - info: 截图信息目录
        返回值: 图片路径
        """
        imgresult_dir = os.path.join("screenshots", info)
        if not os.path.isdir(imgresult_dir):
            os.makedirs(imgresult_dir)
        img_path = os.path.join(imgresult_dir, f'{name}.jpg')

        # 尝试使用不同的Wayland兼容截图工具
        screenshot_tools = [
            ('grim', f"grim '{img_path}'"),
            ('gnome-screenshot', f"gnome-screenshot -f '{img_path}'"),
            ('spectacle', f"spectacle -b -o '{img_path}'"),
            ('flameshot', f"flameshot full -p '{os.path.dirname(img_path)}' -f '{os.path.basename(img_path)}'"),
            ('maim', f"maim '{img_path}'")  # 作为后备方案
        ]

        screenshot_success = False
        for tool_name, command in screenshot_tools:
            try:
                # 检查工具是否可用
                result = subprocess.run(f"which {tool_name}", shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"[INFO] 使用 {tool_name} 进行截图", file=sys.stderr)
                    os.system(command)
                    time.sleep(0.3)  # 稍微延长等待时间

                    # 检查文件是否成功创建
                    if os.path.exists(img_path) and os.path.getsize(img_path) > 0:
                        screenshot_success = True
                        print(f"[INFO] 截图成功: {img_path}", file=sys.stderr)
                        break
                    else:
                        print(f"[WARNING] {tool_name} 截图失败或文件为空", file=sys.stderr)
            except Exception as e:
                print(f"[WARNING] {tool_name} 截图时发生错误: {e}", file=sys.stderr)
                continue

        if not screenshot_success:
            print(f"[ERROR] 所有截图工具都失败了，请安装 grim、gnome-screenshot、spectacle、flameshot 或 maim", file=sys.stderr)
            # 创建一个空白图片作为占位符
            try:
                import numpy as np
                blank_img = np.zeros((100, 100, 3), dtype=np.uint8)
                cv2.imwrite(img_path, blank_img)
                print(f"[INFO] 创建空白占位图片: {img_path}", file=sys.stderr)
            except Exception as e:
                print(f"[ERROR] 创建占位图片失败: {e}", file=sys.stderr)

        return img_path

    def _jietu_x11(self, name, info):
        """
        X11环境下的截图功能
        参数:
        - name: 文件名
        - info: 截图信息目录
        返回值: 图片路径
        """
        imgresult_dir = os.path.join("screenshots", info)
        if not os.path.isdir(imgresult_dir):
            os.makedirs(imgresult_dir)
        img_path = os.path.join(imgresult_dir, f'{name}.jpg')

        try:
            os.system("scrot '%s'" % img_path)
            time.sleep(0.2)

            if os.path.exists(img_path) and os.path.getsize(img_path) > 0:
                print(f"[INFO] X11环境下截图成功: {img_path}", file=sys.stderr)
            else:
                print(f"[WARNING] X11环境下截图失败或文件为空", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] X11环境下截图时发生错误: {e}", file=sys.stderr)

        return img_path

    def _jietu(self, name, info):
        """
        截图功能（统一接口）
        参数:
        - name: 文件名
        - info: 截图信息目录
        返回值: 图片路径
        """
        if self.display_server == 'wayland':
            return self._jietu_wayland(name, info)
        else:
            return self._jietu_x11(name, info)

    def _markregion(self, img_path, mark_region: list = None):
        """
            loginformation: 截图提示信息
            mark_region: 框定区域
        """

        if mark_region:
            tmp_img = cv2.imread(img_path)
            lt_x, lt_y, rd_x, rd_y = mark_region
            cv2.rectangle(tmp_img, (lt_x, lt_y), (rd_x, rd_y), color=(0, 0, 255), thickness=2)
            cv2.imwrite(img_path, tmp_img)

        return img_path


    def kdk_takeEleShot_Uni(self, window_name, control_name):
        try:
            windows = UNI._get_window_by_windowname(self, window_name)
            if not windows:
                return None, f"未找到名为 {window_name} 的窗口"

            # 截图保存
            info = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
            img_path = UNI._jietu(self, control_name, info)

            screenshot_list = []
            for window in windows:
                self.last_appEle = []
                UNI._find_treeEle(self, window)

                # 查找控件
                for control in self.last_appEle:
                    if control["Name"] == control_name:
                        if UNI._is_visible_button(self, control):
                            # 解析控件位置
                            x = control["Coords"]["x"]
                            y = control["Coords"]["y"]
                            width = control["Coords"]["width"]
                            height = control["Coords"]["height"]
                            # 计算中心位置
                            center_x = x + width // 2
                            center_y = y + height // 2
                            screenshot_list.append([x, y, width, height, center_x, center_y])

            if screenshot_list:
                for result in screenshot_list:
                    print(f"控件{control_name}的位置为:", result)
                    mark_region = [result[0], result[1], result[0]+result[2], result[1]+result[3]]
                    img_path = UNI._markregion(self, img_path, mark_region)
                info = f"控件 {control_name} 的位置为: {screenshot_list}"
            else:
                info = f"在 {window_name} 窗口中未找到名为 {control_name} 的控件"
            return screenshot_list, info
        except Exception as e:
            print(f"Error in kdk_takeEleShot_Uni: {e}")
            return None, f"异常"

    def _match_window_by_controlname(self, element, control_name):
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                if child and child.name == control_name:
                    self.flag = element

                if child and child.childCount > 0:
                    UNI._match_window_by_controlname(self, child, control_name)

        except Exception as e:
            print(f"Error matching window by controlname: {e}")
            return None

    def _get_window_info(self, element, window_dict):
        try:
            window_info = UNI._extract_element_info(self, element)
            if window_info["Name"] not in window_dict:
                window_dict[window_info["Name"]] = [window_info]

        except Exception as e:
            print(f"Error getting window info: {e}")
            return None

    def kdk_getWinId_Uni(self, control_name):
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            window_dict = {}

            # 在desktop中查找控件
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                self.flag = None
                UNI._match_window_by_controlname(self, app, control_name)
                if self.flag:
                    ParentPath, ParentCount = UNI._get_element_path(self, self.flag)
                    childnum = ParentPath[0]
                    UNI._get_window_info(self, app.getChildAtIndex(childnum), window_dict)

            if window_dict:
                return window_dict
            else:
                return None

        except Exception as e:
            print(f"Error in kdk_getWinId_Uni: {e}")
            return None

    def _match_window_by_state(self, element):
        try:
            for i in range(element.childCount):
                child = element.getChildAtIndex(i)
                if child and child.getState() and child.getState().contains(pyatspi.STATE_ACTIVE):
                    self.flag = child

                if child and child.childCount > 0:
                    UNI._match_window_by_state(self, child)

        except Exception as e:
            print(f"Error matching window by state: {e}")
            return None

    def kdk_getWinTitle_Uni(self):
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            window_title = None

            # 在desktop中查找控件
            for i in range(desktop.childCount):
                app = desktop.getChildAtIndex(i)
                self.flag = None
                UNI._match_window_by_state(self, app)
                if self.flag:
                    window_title = app.name + " : " + self.flag.name

            if window_title:
                return {"WindowTitle": window_title}
            else:
                return None

        except Exception as e:
            print(f"Error in kdk_getWinTitle_Uni: {e}")
            return None

    def kdk_waitForEleVisib_Uni(self, window_name, control_name, count=5, interval=2):
        try:
            windows = UNI._get_window_by_windowname(self, window_name)
            if not windows:
                return None, f"未找到名为 {window_name} 的窗口"

            for i in range(count):
                for window in windows:
                    self.last_appEle = []
                    UNI._find_treeEle(self, window)

                    # 查找控件
                    for control in self.last_appEle:
                        if control["Name"] == control_name:
                            if UNI._is_real_exist(self, control):
                                return True, f"找到"
                time.sleep(interval)
            return False, f"在 {window_name} 窗口中查找{count}次均未找到名为 {control_name} 的控件"
        except Exception as e:
            print(f"Error in kdk_waitForEleVisib_Uni: {e}")
            return None, f"异常"

    def _is_clickable(self, control_info):
        if control_info["Coords"]["x"] != 'N/A' and control_info["Coords"]["x"] > 0 and control_info["Coords"]["y"] != 'N/A' and control_info["Coords"]["y"] > 0:
            if 'read only' not in control_info['States'] and ('showing' in control_info['States'] or 'activate' in control_info['Actions']) and ('focusable' in control_info['States'] or 'focused' in control_info['States'] or 'selectable' in control_info['States'] or 'sensitive' in control_info['States']) and (control_info['Actions']):
                return True
        return False

    def kdk_judgeEleClickable_Uni(self, window_name, control_name):
        try:
            windows = UNI._get_window_by_windowname(self, window_name)
            if not windows:
                return None, f"未找到名为{window_name}的窗口"

            for window in windows:
                self.last_appEle = []
                UNI._find_treeEle(self, window)

                # 查找控件
                for control in self.last_appEle:
                    if control["Name"] == control_name:
                        if UNI._is_real_exist(self, control):
                            if UNI._is_clickable(self, control):
                                return True, f"[{window_name}]窗口中的[{control_name}]控件可点"
            return False, f"[{window_name}]窗口中的[{control_name}]控件不可点"
        except Exception as e:
            print(f"Error in kdk_judgeEleClickable_Uni: {e}")
            return None, f"异常"

    def getkscd_sideblankgeometry(self):

        def _getprocessname(processid):
            try:
                cmd = f'ps -p {processid} -o comm='
                # processname = os.system(cmd)
                ret = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                processname = ret.stdout.strip()
                if processname == "ksc-defender":
                    return True
                else:
                    return False
            except Exception as e:
                #data["ProcessName"] = "N/A"
                #print(f"Error getting process name for ProcessID {processid}: {e}")
                return False

        countlist = [0,0,0,2,0,0]

        window = UNI._get_window_by_windowname(self, "ksc-defender")
        for i in window:
            if _getprocessname(i.get_process_id()):
                element = i
                for i in countlist:
                    element = element.getChildAtIndex(i)
                print(element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS))
                return element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)

    def kdk_judgeEle(self, control_info, judgeinfo):
        #控件检查点对比
        try:
            window = UNI._get_window_by_info(self, control_info)
            if window is None:
                return None
            self.judgeresult = None
            UNI._judgeEle_by_info(self, window, control_info, judgeinfo)
            return self.judgeresult
        except Exception as e:
            print(f"Error in kdk_judgeEle: {e}")
            return None

    def _judgeEle_by_info(self, element, control_info, judgeinfo):
        #递归对比控件检查点
        for i in range(element.childCount):
            child = element.getChildAtIndex(i)
            try:
                tmpextents = child.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                if (tmpextents.x + tmpextents.y + tmpextents.width + tmpextents.height != 0):
                    UNI._judgeEle_by_info(self, child, control_info, judgeinfo)
            except:
                pass
            # 打印app子控件信息
            try:
                if not self.judgeresult:
                    if judgeinfo in child.name  and  child.name == control_info["Name"]:
                    # if judgeinfo in child.name：
                        extents = child.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                        x1, y1, width, height = extents.x, extents.y, extents.width, extents.height
                        states = child.getState().getStates()
                        state_names = [pyatspi.stateToString(state) for state in states]
                        path, parentlen = UNI._get_element_path(self, child)
                        if (width == control_info["Coords"]["width"]) and (height == control_info["Coords"]["height"]) and (state_names == control_info["States"]) and (parentlen == control_info["ParentCount"]) : #and (child.description ==control_info["Description"]):
                            self.judgeresult = True
            except TypeError as e:
                print(f"Error in _judgeEle_by_info: {e}")

    def get_iconrange(self, appname, countlist):
        #获取应用中某一块范围
        def _getprocessname(processid):
            try:
                cmd = f'ps -p {processid} -o comm='
                ret = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                processname = ret.stdout.strip()
                if processname == appname:
                    return True
                else:
                    return False
            except Exception as e:
                return False

        window = UNI._get_window_by_windowname(self, appname)
        for i in window:
            if _getprocessname(i.get_process_id()):
                element = i
                for i in countlist:
                    element = element.getChildAtIndex(i)
                print(element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS))
                return element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)

    def getbtpos_and_pindex(self, window_name, control_name):
        try:
            windows = UNI._get_window_by_windowname(self, window_name)
            if not windows:
                return None, f"未找到名为 {window_name} 的窗口"
            matching_ctl = []
            for window in windows:
                self.last_appEle = []
                UNI._find_treeEle(self, window)
                # 查找控件
                for control in self.last_appEle:
                    if control["Name"] == control_name:
                        # 判断控件是否为非隐藏按钮
                        if UNI._is_visible_button(self, control):
                            # 解析控件位置
                            x = control["Coords"]["x"]
                            y = control["Coords"]["y"]
                            width = control["Coords"]["width"]
                            height = control["Coords"]["height"]
                            # 计算中心位置
                            center_x = x + width // 2
                            center_y = y + height // 2
                            matching_ctl.append([x, y, width, height, center_x, center_y])
                            pindex = control["ParentPath"][:-1]
            if matching_ctl:
                info = f"找到"
            else:
                info = f"在 {window_name} 窗口中未找到名为 {control_name} 的控件"
            return matching_ctl, pindex, info
        except Exception as e:
            print(f"Error in _find_index: {e}")
            return None, f"异常"

    def getpeony_moreappgeometry(self, appname, pindex):
        # 获取应用中某一块范围
        def _getprocessname(processid):
            try:
                cmd = f'ps -p {processid} -o comm='
                ret = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                processname = ret.stdout.strip()
                if processname == "peony":
                    return True
                else:
                    return False
            except Exception:
                return False

        window = UNI._get_window_by_windowname(self, appname)
        for i in window:
            if _getprocessname(i.get_process_id()):
                element = i
                for i in pindex:
                    element = element.getChildAtIndex(i)
                print(element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS))
                return element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)

    def kdk_checkEle(self, control_info, paramdict):
        try:
            window = UNI._get_window_by_info(self, control_info)
            if window is None:
                return None

            UNI._find_component_by_info(self, window, control_info)

            component = self.finddata
            extents = self.findextents

            self.finddata = None
            self.findextents = None
            self.findfind = None

            flag = 0
            # 当找不到控件时，判断当前检查点是否存在exist，否则无需进行后续判断
            if component is None or extents is None:
                if 'exist' in paramdict and paramdict['exist'] is False:
                    flag = 1
                else:
                    return False, f'控件不存在'

            for key, value in paramdict.items():
                # 检查窗口是否存在控件
                if key == 'exist':
                    try:
                        states = component.getState().getStates()
                        state_names = [pyatspi.stateToString(state) for state in states]
                        action_interface = component.queryAction()
                        actions = [action_interface.getName(i) for i in range(action_interface.nActions)]
                    except Exception:
                        state_names, actions = None, None
                    if extents.x >=0 and extents.y >=0 and state_names is not None and ("showing" in state_names or "visible" in state_names or "enabled" in state_names or actions) and value is True:
                        flag = 1
                    elif (extents.x <0 or extents.y <0 or (not state_names and not actions)) and value is False:
                        flag = 1
                    elif value is None or value == '' or value == 'None':
                        continue
                    else:
                        return False

                # 检查控件是否可见
                elif key == 'visible':
                    try:
                        states = component.getState().getStates()
                        state_names = [pyatspi.stateToString(state) for state in states]
                    except Exception:
                        state_names = None
                    if state_names is not None and ("showing" in state_names or "visible" in state_names) and value is True:
                        flag = 1
                    elif "showing" not in state_names and "visible" not in state_names and value is False:
                        flag = 1
                    elif value is None or value == '' or value == 'None':
                        continue
                    else:
                        return False

                # 检查控件是否被选中
                elif key == 'selected':
                    try:
                        states = component.getState().getStates()
                        state_names = [pyatspi.stateToString(state) for state in states]
                    except Exception:
                        state_names = None
                    if state_names is not None and ("selected" in state_names or "checked" in state_names) and value is True:
                        flag = 1
                    elif "selected" not in state_names and "checked" not in state_names and value is False:
                        flag = 1
                    elif value is None or value == '' or value == 'None':
                        continue
                    else:
                        return False

                # 检查控件的text匹配
                elif key == 'text':
                    try:
                        if component.queryText() is not None:
                            text_interface = component.queryText()
                            text = text_interface.getText(0, -1)
                        else:
                            text = None
                    except Exception:
                        text = None
                    except_text = value
                    if except_text and text and (except_text == text or except_text in text):
                        flag = 1
                    elif value is None or value == '' or value == 'None':
                        continue
                    else:
                        return False

                # 检查控件的宽、高是否匹配
                elif key == 'size_width':
                    if value is None or value == '' or value == 'None':
                        continue
                    elif extents.width == int(value):
                        flag = 1
                    else:
                        return False, f'控件的宽不匹配'

                # 检查控件的宽、高是否匹配
                elif key == 'size_height':
                    if value is None or value == '' or value == 'None':
                        continue
                    elif extents.height == int(value):
                        flag = 1
                    else:
                        return False, f'控件的高不匹配'

                else:
                    return False, f'传入检查点参数有误,[kdk_checkEle]请输入正确的paramdict参数'

            if flag == 1:
                return True

        except Exception as e:
            print(f"Error in kdk_checkEle: {e}")
            return None, f"异常"

    def _get_window_by_windowpartialname(self, window_partialnames):
    # windowname_judge
        try:
            desktop = pyatspi.Registry.getDesktop(0)
            window_list = []
            count = 0
            for name in window_partialnames:
                found = False
                # 在 desktop 中获取所有应用程序
                for i in range(desktop.childCount):
                    app = desktop.getChildAtIndex(i)
                    if app.name == name or name in app.name:
                        window_list.append(app)
                        found = True
                    else:
                        # 在应用程序的子窗口中查找
                        for j in range(app.childCount):
                            window = app.getChildAtIndex(j)
                            if window.name == name or name in window.name:
                                window_list.append(window)
                                found = True
                if found:
                    count = count +1
                else:
                    continue
            if not window_list:
                return None, 0
            return window_list, count

        except Exception as e:
            print(f"Error getting matching window by window partial name: {e}")
            return None, 0

    def get_realtime_element_at_point(self, x, y):
        """
        实时获取指定坐标处的控件信息
        这是一个优化的方法，用于快速获取坐标处的控件信息
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: 控件信息字典和状态信息字符串
        """
        try:
            # 开始计时
            start_time = time.time()

            # 检查缓存 - 只在完全相同的坐标时使用缓存
            cache_key = f'realtime_element_{x}_{y}'
            if cache_key in self.window_cache and time.time() - self.window_cache[cache_key]['time'] < 0.05:
                # 使用缓存的控件信息（如果缓存时间不超过0.05秒）
                return self.window_cache[cache_key]['data']

            # 直接使用原始方法获取控件信息，但增加缓存
            control_info, status = self.kdk_getElement_Uni(x, y, False)

            # 更新缓存
            if control_info:
                result = (control_info, status)
                self.window_cache[cache_key] = {
                    'data': result,
                    'time': time.time()
                }

            # 记录性能指标
            elapsed_time = time.time() - start_time
            self.timers['get_realtime_element_at_point'] = elapsed_time

            # 只在耗时超过0.01秒时输出日志，减少日志干扰
            if elapsed_time > 0.01:
                print(f"[INFO] 实时获取控件信息耗时: {elapsed_time:.4f} 秒", file=sys.stderr)

            return (control_info, status)

        except Exception as e:
            print(f"[ERROR] 实时获取控件信息时发生错误: {e}", file=sys.stderr)
            # 如果出错，回退到原始方法
            try:
                return self.kdk_getElement_Uni(x, y, False)
            except Exception as e2:
                print(f"[ERROR] 回退到原始方法也失败: {e2}", file=sys.stderr)
                return None, f"获取控件信息时发生错误: {e}"

    def test_improved_widget_detection(self, x, y):
        """
        测试改进的控件检测功能
        参数:
        - x: 横坐标
        - y: 纵坐标
        返回值: 测试结果字典
        """
        print(f"\n" + "="*80, file=sys.stderr)
        print(f"测试改进的控件检测功能 - 坐标: ({x}, {y})", file=sys.stderr)
        print(f"当前显示服务器: {self.display_server}", file=sys.stderr)
        print("="*80, file=sys.stderr)

        test_results = {
            'coordinate': (x, y),
            'display_server': self.display_server,
            'window_detection': {},
            'widget_detection': {},
            'performance': {}
        }

        try:
            # 1. 测试窗口检测
            print(f"\n[步骤1] 测试窗口检测...", file=sys.stderr)
            start_time = time.time()

            windows = self.get_window_stack()
            window_count = len(windows) if windows else 0
            print(f"发现窗口总数: {window_count}", file=sys.stderr)

            target_window = self.print_topmost_window_info(x, y)
            window_detection_time = time.time() - start_time

            if target_window:
                try:
                    window_name = target_window.name if hasattr(target_window, 'name') else str(target_window)
                    window_role = target_window.getRoleName() if hasattr(target_window, 'getRoleName') else 'unknown'
                    extents = target_window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)

                    test_results['window_detection'] = {
                        'success': True,
                        'window_name': window_name,
                        'window_role': window_role,
                        'window_bounds': {
                            'x': extents.x,
                            'y': extents.y,
                            'width': extents.width,
                            'height': extents.height
                        },
                        'detection_time': window_detection_time
                    }

                    print(f"✓ 找到目标窗口: {window_name} (角色: {window_role})", file=sys.stderr)
                    print(f"  窗口位置: ({extents.x}, {extents.y}), 大小: {extents.width}x{extents.height}", file=sys.stderr)
                except Exception as e:
                    print(f"✗ 获取窗口信息时发生错误: {e}", file=sys.stderr)
                    test_results['window_detection']['success'] = False
                    test_results['window_detection']['error'] = str(e)
            else:
                print(f"✗ 未找到目标窗口", file=sys.stderr)
                test_results['window_detection']['success'] = False
                test_results['window_detection']['error'] = '未找到窗口'

            # 2. 测试控件检测
            print(f"\n[步骤2] 测试控件检测...", file=sys.stderr)
            start_time = time.time()

            widget_info, status = self.kdk_getElement_Uni(x, y, False)
            widget_detection_time = time.time() - start_time

            if widget_info and isinstance(widget_info, dict) and 'error' not in widget_info:
                test_results['widget_detection'] = {
                    'success': True,
                    'widget_name': widget_info.get('Name', 'Unknown'),
                    'widget_role': widget_info.get('Rolename', 'Unknown'),
                    'widget_description': widget_info.get('Description', ''),
                    'widget_bounds': widget_info.get('Coords', {}),
                    'widget_states': widget_info.get('States', []),
                    'widget_actions': widget_info.get('Actions', []),
                    'detection_time': widget_detection_time,
                    'status': status
                }

                print(f"✓ 找到目标控件: {widget_info.get('Name', 'Unknown')} (角色: {widget_info.get('Rolename', 'Unknown')})", file=sys.stderr)
                coords = widget_info.get('Coords', {})
                if coords:
                    print(f"  控件位置: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')}), 大小: {coords.get('width', 'N/A')}x{coords.get('height', 'N/A')}", file=sys.stderr)
                if widget_info.get('Description'):
                    print(f"  控件描述: {widget_info.get('Description')}", file=sys.stderr)
                if widget_info.get('States'):
                    print(f"  控件状态: {', '.join(widget_info.get('States', []))}", file=sys.stderr)
                if widget_info.get('Actions'):
                    print(f"  可用操作: {', '.join(widget_info.get('Actions', []))}", file=sys.stderr)
            else:
                error_msg = widget_info.get('error', status) if isinstance(widget_info, dict) else status
                print(f"✗ 控件检测失败: {error_msg}", file=sys.stderr)
                test_results['widget_detection'] = {
                    'success': False,
                    'error': error_msg,
                    'detection_time': widget_detection_time
                }

            # 3. 性能统计
            total_time = window_detection_time + widget_detection_time
            test_results['performance'] = {
                'window_detection_time': window_detection_time,
                'widget_detection_time': widget_detection_time,
                'total_time': total_time,
                'cached_operations': len([k for k in self.window_cache.keys() if 'accessible_at' in k or 'window' in k])
            }

            print(f"\n[性能统计]", file=sys.stderr)
            print(f"  窗口检测耗时: {window_detection_time:.4f} 秒", file=sys.stderr)
            print(f"  控件检测耗时: {widget_detection_time:.4f} 秒", file=sys.stderr)
            print(f"  总耗时: {total_time:.4f} 秒", file=sys.stderr)
            print(f"  缓存条目数: {test_results['performance']['cached_operations']}", file=sys.stderr)

            # 4. 给出改进建议
            print(f"\n[改进建议]", file=sys.stderr)
            if total_time > 0.5:
                print(f"  ⚠ 总耗时较长 ({total_time:.4f}s)，建议优化缓存策略", file=sys.stderr)
            elif total_time > 0.2:
                print(f"  ⚠ 耗时适中 ({total_time:.4f}s)，可接受", file=sys.stderr)
            else:
                print(f"  ✓ 检测速度良好 ({total_time:.4f}s)", file=sys.stderr)

            if not test_results['widget_detection'].get('success', False):
                print(f"  ⚠ 建议检查坐标是否位于可交互控件上", file=sys.stderr)

            print("="*80, file=sys.stderr)

        except Exception as e:
            print(f"\n✗ 测试过程中发生错误: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            test_results['test_error'] = str(e)

        return test_results

    def print_window_controls_info(self, x, y, max_depth=5, show_invisible=False):
        """
        打印指定坐标处窗口中所有控件的信息
        参数:
        - x, y: 坐标
        - max_depth: 最大遍历深度，防止输出过多信息
        - show_invisible: 是否显示不可见控件
        """
        print(f"\n" + "="*80, file=sys.stderr)
        print(f"窗口控件信息详览 - 坐标: ({x}, {y})", file=sys.stderr)
        print("="*80, file=sys.stderr)

        try:
            # 获取窗口
            target_window = self.print_topmost_window_info(x, y)
            if not target_window:
                print("❌ 未找到目标窗口", file=sys.stderr)
                return

            # 获取窗口基本信息
            try:
                window_name = target_window.name if hasattr(target_window, 'name') else '未知窗口'
                window_role = target_window.getRoleName() if hasattr(target_window, 'getRoleName') else '未知角色'
                window_extents = target_window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                window_pid = target_window.get_process_id() if hasattr(target_window, 'get_process_id') else '未知'

                print(f"🏠 窗口信息:", file=sys.stderr)
                print(f"   名称: {window_name}", file=sys.stderr)
                print(f"   角色: {window_role}", file=sys.stderr)
                print(f"   进程ID: {window_pid}", file=sys.stderr)
                print(f"   位置: ({window_extents.x}, {window_extents.y})", file=sys.stderr)
                print(f"   大小: {window_extents.width}x{window_extents.height}", file=sys.stderr)
                print(f"   子控件数: {target_window.childCount}", file=sys.stderr)

            except Exception as e:
                print(f"❌ 获取窗口信息失败: {e}", file=sys.stderr)
                return

            print(f"\n📋 控件层次结构:", file=sys.stderr)
            print("-"*80, file=sys.stderr)

            # 遍历并打印所有控件
            control_count = [0]  # 使用列表以便在递归中修改
            self._print_control_tree(target_window, 0, max_depth, show_invisible, control_count, x, y)

            print(f"\n📊 统计信息:", file=sys.stderr)
            print(f"   总控件数: {control_count[0]}", file=sys.stderr)
            print(f"   最大深度: {max_depth}", file=sys.stderr)
            print(f"   显示不可见控件: {'是' if show_invisible else '否'}", file=sys.stderr)
            print("="*80, file=sys.stderr)

        except Exception as e:
            print(f"❌ 打印窗口控件信息时发生错误: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)

    def _print_control_tree(self, element, current_depth, max_depth, show_invisible, control_count, target_x, target_y):
        """
        递归打印控件树结构
        """
        if current_depth > max_depth:
            return

        try:
            # 获取控件基本信息
            control_count[0] += 1
            indent = "  " * current_depth

            # 控件名称和角色
            name = getattr(element, 'name', 'N/A') or 'N/A'
            try:
                role = element.getRoleName()
            except:
                role = 'N/A'

            # 控件位置和大小
            try:
                extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                coords_info = f"({extents.x},{extents.y}) {extents.width}x{extents.height}"

                # 检查目标坐标是否在当前控件内
                contains_target = (extents.x <= target_x < extents.x + extents.width and
                                 extents.y <= target_y < extents.y + extents.height)
            except:
                coords_info = "位置信息不可用"
                contains_target = False

            # 控件状态
            try:
                states = element.getState().getStates()
                state_names = [pyatspi.stateToString(state) for state in states]
                is_visible = 'showing' in state_names or 'visible' in state_names
                is_interactive = any(state in state_names for state in ['focusable', 'focused', 'selectable', 'sensitive'])
            except:
                state_names = []
                is_visible = True
                is_interactive = False

            # 控件操作
            try:
                action_interface = element.queryAction()
                actions = [action_interface.getName(i) for i in range(action_interface.nActions)]
                has_actions = len(actions) > 0 and actions != ['Not available: ']
            except:
                actions = []
                has_actions = False

            # 决定是否显示此控件
            should_show = show_invisible or is_visible

            if should_show:
                # 生成显示符号
                visibility_icon = "👁️" if is_visible else "👻"
                interaction_icon = "🔘" if is_interactive else "⚪"
                action_icon = "⚡" if has_actions else "🚫"
                target_icon = "🎯" if contains_target else "  "

                # 控件类型颜色编码（通过角色判断）
                if 'button' in role.lower():
                    type_icon = "🔲"
                elif 'text' in role.lower() or 'entry' in role.lower():
                    type_icon = "📝"
                elif 'menu' in role.lower():
                    type_icon = "📋"
                elif 'list' in role.lower():
                    type_icon = "📑"
                elif 'image' in role.lower() or 'icon' in role.lower():
                    type_icon = "🖼️"
                elif 'panel' in role.lower() or 'pane' in role.lower():
                    type_icon = "📦"
                elif 'scroll' in role.lower():
                    type_icon = "📜"
                else:
                    type_icon = "🔸"

                # 打印控件信息
                print(f"{indent}{target_icon}{type_icon}{visibility_icon}{interaction_icon}{action_icon} [{control_count[0]:3d}] {role}", file=sys.stderr)

                if name != 'N/A':
                    print(f"{indent}     📛 名称: {name}", file=sys.stderr)

                print(f"{indent}     📐 位置: {coords_info}", file=sys.stderr)

                if state_names:
                    important_states = [s for s in state_names if s in ['focused', 'selected', 'pressed', 'checked', 'expanded']]
                    if important_states:
                        print(f"{indent}     🔄 状态: {', '.join(important_states)}", file=sys.stderr)

                if has_actions:
                    action_list = [a for a in actions if a != 'Not available: '][:3]  # 只显示前3个操作
                    if action_list:
                        print(f"{indent}     ⚡ 操作: {', '.join(action_list)}", file=sys.stderr)

                # 如果控件包含目标坐标，特别标注
                if contains_target:
                    print(f"{indent}     🎯 ** 此控件包含目标坐标 ({target_x}, {target_y}) **", file=sys.stderr)

                # 如果有子控件，显示子控件数量
                if element.childCount > 0:
                    print(f"{indent}     👥 子控件: {element.childCount}个", file=sys.stderr)

            # 递归处理子控件
            if current_depth < max_depth:
                for i in range(element.childCount):
                    try:
                        child = element.getChildAtIndex(i)
                        self._print_control_tree(child, current_depth + 1, max_depth, show_invisible, control_count, target_x, target_y)
                    except Exception as e:
                        print(f"{indent}  ❌ 无法访问子控件 {i}: {e}", file=sys.stderr)

        except Exception as e:
            print(f"{indent}❌ 处理控件时发生错误: {e}", file=sys.stderr)

    def print_control_at_coordinate(self, x, y, detailed=True):
        """
        打印指定坐标处的控件详细信息（单个控件）
        参数:
        - x, y: 坐标
        - detailed: 是否显示详细信息
        """
        print(f"\n" + "="*60, file=sys.stderr)
        print(f"坐标 ({x}, {y}) 处的控件信息", file=sys.stderr)
        print("="*60, file=sys.stderr)

        try:
            # 使用现有方法获取控件
            widget_info, status = self.kdk_getElement_Uni(x, y, False)

            if widget_info and isinstance(widget_info, dict) and widget_info.get('capture_status') in ['success', 'partial']:
                print(f"✅ 成功找到控件", file=sys.stderr)
                print(f"📛 名称: {widget_info.get('Name', 'N/A')}", file=sys.stderr)
                print(f"🔘 角色: {widget_info.get('Rolename', 'N/A')}", file=sys.stderr)
                print(f"📝 描述: {widget_info.get('Description', 'N/A')}", file=sys.stderr)

                coords = widget_info.get('Coords', {})
                if coords:
                    print(f"📐 位置: ({coords.get('x', 'N/A')}, {coords.get('y', 'N/A')})", file=sys.stderr)
                    print(f"📏 大小: {coords.get('width', 'N/A')}x{coords.get('height', 'N/A')}", file=sys.stderr)

                if detailed:
                    states = widget_info.get('States', [])
                    if states:
                        print(f"🔄 状态: {', '.join(states)}", file=sys.stderr)

                    actions = widget_info.get('Actions', [])
                    if actions and actions != ['Not available: ']:
                        print(f"⚡ 操作: {', '.join(actions)}", file=sys.stderr)

                    print(f"🏠 窗口: {widget_info.get('WindowName', 'N/A')}", file=sys.stderr)
                    print(f"⚙️ 进程: {widget_info.get('ProcessName', 'N/A')}", file=sys.stderr)
                    print(f"🆔 进程ID: {widget_info.get('ProcessID', 'N/A')}", file=sys.stderr)
                    print(f"📊 质量评分: {self._calculate_widget_quality_simple(widget_info)}/100", file=sys.stderr)

                capture_status = widget_info.get('capture_status', 'unknown')
                if capture_status == 'partial':
                    note = widget_info.get('note', '')
                    print(f"⚠️ 备注: {note}", file=sys.stderr)

            else:
                print(f"❌ 未找到控件", file=sys.stderr)
                error_msg = widget_info.get('error', status) if isinstance(widget_info, dict) else status
                print(f"📄 详情: {error_msg}", file=sys.stderr)

            print("="*60, file=sys.stderr)

        except Exception as e:
            print(f"❌ 获取控件信息时发生错误: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)

    def _calculate_widget_quality_simple(self, widget_info):
        """
        简化版本的控件质量评分计算
        """
        score = 0

        # 名称
        if widget_info.get('Name', 'N/A') != 'N/A':
            score += 25

        # 角色
        if widget_info.get('Rolename', 'N/A') != 'N/A':
            score += 20

        # 坐标
        coords = widget_info.get('Coords', {})
        if coords and all(k in coords for k in ['x', 'y', 'width', 'height']):
            score += 20

        # 状态
        states = widget_info.get('States', [])
        if 'showing' in states or 'visible' in states:
            score += 15

        # 操作
        actions = widget_info.get('Actions', [])
        if actions and actions != ['Not available: ']:
            score += 10

        # 描述
        if widget_info.get('Description', 'N/A') != 'N/A':
            score += 10

        return min(score, 100)

    def print_all_desktop_applications(self, max_depth=3, show_invisible=False,
                                     include_system_apps=True, max_apps=None):
        """
        打印桌面中所有应用及其子节点控件
        参数:
        - max_depth: 最大遍历深度，防止输出过多信息
        - show_invisible: 是否显示不可见控件
        - include_system_apps: 是否包含系统应用（如kglobalaccel等）
        - max_apps: 最大显示应用数量，None表示显示所有
        """
        print(f"\n" + "="*100, file=sys.stderr)
        print(f"桌面所有应用及其控件结构详览", file=sys.stderr)
        print("="*100, file=sys.stderr)

        try:
            desktop = pyatspi.Registry.getDesktop(0)
            total_apps = desktop.childCount

            print(f"🖥️ 桌面信息:", file=sys.stderr)
            print(f"   总应用数: {total_apps}", file=sys.stderr)
            print(f"   最大深度: {max_depth}", file=sys.stderr)
            print(f"   显示不可见控件: {'是' if show_invisible else '否'}", file=sys.stderr)
            print(f"   包含系统应用: {'是' if include_system_apps else '否'}", file=sys.stderr)

            if max_apps:
                print(f"   限制显示: 前{max_apps}个应用", file=sys.stderr)

            print(f"\n📱 应用列表:", file=sys.stderr)
            print("-"*100, file=sys.stderr)

            # 系统应用名称列表（通常用户不太关心的）
            system_apps = {
                'kglobalaccel', 'ukuismserver', 'ukui-session', 'panel-daemon',
                'at-spi-bus-launcher', 'at-spi2-registryd', 'dbus-daemon',
                'systemd', 'kwin', 'plasmashell', 'gnome-shell'
            }

            displayed_apps = 0
            total_controls = 0

            for app_index in range(total_apps):
                try:
                    app = desktop.getChildAtIndex(app_index)
                    app_name = getattr(app, 'name', f'应用_{app_index}') or f'应用_{app_index}'

                    # 检查是否跳过系统应用
                    if not include_system_apps and app_name.lower() in system_apps:
                        continue

                    # 检查应用数量限制
                    if max_apps and displayed_apps >= max_apps:
                        remaining_apps = total_apps - app_index
                        print(f"📋 还有 {remaining_apps} 个应用未显示（受限制）", file=sys.stderr)
                        break

                    displayed_apps += 1

                    # 获取应用基本信息
                    try:
                        app_role = app.getRoleName() if hasattr(app, 'getRoleName') else '未知角色'
                        app_child_count = app.childCount

                        # 获取应用进程信息
                        try:
                            app_pid = app.get_process_id() if hasattr(app, 'get_process_id') else '未知'
                        except:
                            app_pid = '未知'

                        # 获取应用状态
                        try:
                            states = app.getState().getStates()
                            state_names = [pyatspi.stateToString(state) for state in states]
                            is_visible = 'showing' in state_names or 'visible' in state_names
                        except:
                            state_names = []
                            is_visible = True

                        # 显示应用信息
                        visibility_icon = "👁️" if is_visible else "👻"
                        app_type_icon = self._get_app_type_icon(app_name, app_role)

                        print(f"\n{app_type_icon}{visibility_icon} [{displayed_apps:3d}] 应用: {app_name}", file=sys.stderr)
                        print(f"     🔘 角色: {app_role}", file=sys.stderr)
                        print(f"     🆔 进程ID: {app_pid}", file=sys.stderr)
                        print(f"     👥 子控件数: {app_child_count}", file=sys.stderr)

                        if state_names:
                            important_states = [s for s in state_names if s in ['focused', 'active', 'showing', 'visible']]
                            if important_states:
                                print(f"     🔄 状态: {', '.join(important_states)}", file=sys.stderr)

                        # 如果应用有子控件，遍历并打印
                        if app_child_count > 0:
                            print(f"\n     📋 应用 '{app_name}' 的子控件结构:", file=sys.stderr)
                            print(f"     {'-'*60}", file=sys.stderr)

                            app_control_count = [0]
                            for window_index in range(min(app_child_count, 10)):  # 限制每个应用最多显示10个窗口
                                try:
                                    window = app.getChildAtIndex(window_index)
                                    self._print_application_control_tree(
                                        window, 1, max_depth, show_invisible,
                                        app_control_count, app_name, window_index
                                    )
                                except Exception as e:
                                    print(f"       ❌ 无法访问窗口 {window_index}: {e}", file=sys.stderr)

                            total_controls += app_control_count[0]
                            print(f"     📊 应用 '{app_name}' 总控件数: {app_control_count[0]}", file=sys.stderr)

                            if app_child_count > 10:
                                print(f"     📋 还有 {app_child_count - 10} 个窗口未显示", file=sys.stderr)
                        else:
                            print(f"     📭 此应用没有子控件", file=sys.stderr)

                    except Exception as e:
                        print(f"     ❌ 获取应用信息失败: {e}", file=sys.stderr)

                except Exception as e:
                    print(f"❌ 处理应用 {app_index} 时发生错误: {e}", file=sys.stderr)
                    continue

            # 显示统计信息
            print(f"\n" + "="*100, file=sys.stderr)
            print(f"📊 统计信息:", file=sys.stderr)
            print(f"   总应用数: {total_apps}", file=sys.stderr)
            print(f"   已显示应用数: {displayed_apps}", file=sys.stderr)
            print(f"   总控件数: {total_controls}", file=sys.stderr)
            print(f"   遍历深度: {max_depth}", file=sys.stderr)
            print(f"   显示系统应用: {'是' if include_system_apps else '否'}", file=sys.stderr)
            print("="*100, file=sys.stderr)

        except Exception as e:
            print(f"❌ 打印桌面应用信息时发生错误: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)

    def _get_app_type_icon(self, app_name, app_role):
        """根据应用名称和角色获取图标"""
        app_name_lower = app_name.lower()

        # 系统应用
        if app_name_lower in ['kglobalaccel', 'ukuismserver', 'ukui-session']:
            return "⚙️"
        # 桌面环境
        elif app_name_lower in ['桌面', 'desktop', 'peony', 'nautilus']:
            return "🖥️"
        # 面板和任务栏
        elif 'panel' in app_name_lower or 'taskbar' in app_name_lower:
            return "📋"
        # 浏览器
        elif any(browser in app_name_lower for browser in ['firefox', 'chrome', 'chromium', 'browser']):
            return "🌐"
        # 编辑器和IDE
        elif any(editor in app_name_lower for editor in ['code', 'vim', 'emacs', 'editor', 'cursor']):
            return "💻"
        # 终端
        elif any(term in app_name_lower for term in ['terminal', 'konsole', 'gnome-terminal']):
            return "⚡"
        # 文件管理器
        elif any(fm in app_name_lower for fm in ['files', 'nautilus', 'dolphin', 'peony']):
            return "📁"
        # 媒体应用
        elif any(media in app_name_lower for media in ['vlc', 'totem', 'rhythmbox', 'music', 'video']):
            return "🎵"
        # 办公应用
        elif any(office in app_name_lower for office in ['libreoffice', 'wps', 'writer', 'calc']):
            return "📄"
        # 通信应用
        elif any(comm in app_name_lower for comm in ['mail', 'thunderbird', 'telegram', 'discord']):
            return "💬"
        # 默认应用图标
        else:
            return "📱"

    def _print_application_control_tree(self, element, current_depth, max_depth,
                                      show_invisible, control_count, app_name, window_index):
        """
        打印应用控件树结构（专门为桌面所有应用优化）
        """
        if current_depth > max_depth:
            return

        try:
            control_count[0] += 1
            indent = "       " + "  " * (current_depth - 1)  # 应用层级缩进

            # 控件名称和角色
            name = getattr(element, 'name', 'N/A') or 'N/A'
            try:
                role = element.getRoleName()
            except:
                role = 'N/A'

            # 控件位置和大小
            try:
                extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                coords_info = f"({extents.x},{extents.y}) {extents.width}x{extents.height}"
                has_valid_geometry = extents.width > 0 and extents.height > 0
            except:
                coords_info = "位置信息不可用"
                has_valid_geometry = False

            # 控件状态
            try:
                states = element.getState().getStates()
                state_names = [pyatspi.stateToString(state) for state in states]
                is_visible = 'showing' in state_names or 'visible' in state_names
                is_interactive = any(state in state_names for state in ['focusable', 'focused', 'selectable', 'sensitive'])
            except:
                state_names = []
                is_visible = True
                is_interactive = False

            # 控件操作
            try:
                action_interface = element.queryAction()
                actions = [action_interface.getName(i) for i in range(action_interface.nActions)]
                has_actions = len(actions) > 0 and actions != ['Not available: ']
            except:
                actions = []
                has_actions = False

            # 决定是否显示此控件
            should_show = show_invisible or is_visible

            if should_show:
                # 生成显示符号
                visibility_icon = "👁️" if is_visible else "👻"
                interaction_icon = "🔘" if is_interactive else "⚪"
                action_icon = "⚡" if has_actions else "🚫"
                geometry_icon = "📐" if has_valid_geometry else "❓"

                # 控件类型图标
                type_icon = self._get_control_type_icon(role)

                # 特殊标记：窗口级别的控件
                if current_depth == 1:
                    level_indicator = f"🪟[{window_index}]"
                else:
                    level_indicator = ""

                # 打印控件信息
                print(f"{indent}{level_indicator}{type_icon}{visibility_icon}{interaction_icon}{action_icon}{geometry_icon} [{control_count[0]:3d}] {role}", file=sys.stderr)

                if name != 'N/A' and name.strip():
                    # 限制名称长度，避免输出过长
                    display_name = name[:50] + "..." if len(name) > 50 else name
                    print(f"{indent}     📛 名称: {display_name}", file=sys.stderr)

                print(f"{indent}     📐 位置: {coords_info}", file=sys.stderr)

                # 只显示重要状态
                if state_names:
                    important_states = [s for s in state_names if s in ['focused', 'selected', 'pressed', 'checked', 'expanded', 'active']]
                    if important_states:
                        print(f"{indent}     🔄 状态: {', '.join(important_states[:3])}", file=sys.stderr)  # 最多显示3个状态

                # 显示操作（限制数量）
                if has_actions:
                    action_list = [a for a in actions if a != 'Not available: '][:2]  # 只显示前2个操作
                    if action_list:
                        print(f"{indent}     ⚡ 操作: {', '.join(action_list)}", file=sys.stderr)

                # 子控件信息
                if element.childCount > 0:
                    if element.childCount <= 5:
                        print(f"{indent}     👥 子控件: {element.childCount}个", file=sys.stderr)
                    else:
                        print(f"{indent}     👥 子控件: {element.childCount}个 (较多)", file=sys.stderr)

            # 递归处理子控件（限制每层的子控件数量以避免输出过多）
            if current_depth < max_depth:
                max_children_to_show = 5 if current_depth <= 2 else 3  # 浅层显示更多，深层显示较少

                for i in range(min(element.childCount, max_children_to_show)):
                    try:
                        child = element.getChildAtIndex(i)
                        self._print_application_control_tree(
                            child, current_depth + 1, max_depth, show_invisible,
                            control_count, app_name, window_index
                        )
                    except Exception as e:
                        print(f"{indent}  ❌ 无法访问子控件 {i}: {e}", file=sys.stderr)

                # 如果有更多子控件未显示，提示用户
                if element.childCount > max_children_to_show:
                    remaining = element.childCount - max_children_to_show
                    print(f"{indent}  📋 还有 {remaining} 个子控件未显示", file=sys.stderr)

        except Exception as e:
            print(f"{indent}❌ 处理控件时发生错误: {e}", file=sys.stderr)

    def _get_control_type_icon(self, role):
        """获取控件类型图标"""
        if not role or role == 'N/A':
            return "🔸"

        role_lower = role.lower()

        # 窗口和容器
        if 'window' in role_lower or 'frame' in role_lower:
            return "🪟"
        elif 'panel' in role_lower or 'pane' in role_lower:
            return "📦"
        elif 'filler' in role_lower:
            return "⬜"
        # 按钮和控制元素
        elif 'button' in role_lower:
            return "🔲"
        elif 'check' in role_lower:
            return "☑️"
        elif 'radio' in role_lower:
            return "🔘"
        # 文本和输入
        elif 'text' in role_lower or 'entry' in role_lower:
            return "📝"
        elif 'label' in role_lower:
            return "🏷️"
        # 列表和选择
        elif 'list' in role_lower:
            return "📑"
        elif 'tree' in role_lower:
            return "🌳"
        elif 'table' in role_lower:
            return "📊"
        elif 'combo' in role_lower:
            return "🔽"
        # 菜单
        elif 'menu' in role_lower:
            return "📋"
        elif 'toolbar' in role_lower:
            return "🔧"
        # 页面和标签
        elif 'page' in role_lower or 'tab' in role_lower:
            return "📄"
        elif 'notebook' in role_lower:
            return "📔"
        # 滚动和进度
        elif 'scroll' in role_lower:
            return "📜"
        elif 'progress' in role_lower:
            return "📈"
        elif 'slider' in role_lower:
            return "🎚️"
        # 图像和媒体
        elif 'image' in role_lower or 'icon' in role_lower:
            return "🖼️"
        elif 'canvas' in role_lower:
            return "🎨"
        # 应用程序
        elif 'application' in role_lower:
            return "📱"
        # 默认
        else:
            return "🔸"

    def print_desktop_summary(self):
        """打印桌面应用摘要信息"""
        print(f"\n" + "="*80, file=sys.stderr)
        print(f"桌面应用摘要", file=sys.stderr)
        print("="*80, file=sys.stderr)

        try:
            desktop = pyatspi.Registry.getDesktop(0)
            total_apps = desktop.childCount

            print(f"📊 基本统计:", file=sys.stderr)
            print(f"   总应用数: {total_apps}", file=sys.stderr)

            visible_apps = 0
            system_apps = 0
            user_apps = 0
            total_windows = 0

            system_app_names = {
                'kglobalaccel', 'ukuismserver', 'ukui-session', 'panel-daemon',
                'at-spi-bus-launcher', 'at-spi2-registryd', 'dbus-daemon'
            }

            print(f"\n📱 应用详情:", file=sys.stderr)
            print("-" * 60, file=sys.stderr)

            for i in range(total_apps):
                try:
                    app = desktop.getChildAtIndex(i)
                    app_name = getattr(app, 'name', f'应用_{i}') or f'应用_{i}'
                    app_child_count = app.childCount

                    # 获取可见性状态
                    try:
                        states = app.getState().getStates()
                        state_names = [pyatspi.stateToString(state) for state in states]
                        is_visible = 'showing' in state_names or 'visible' in state_names
                    except:
                        is_visible = True

                    if is_visible:
                        visible_apps += 1

                    # 分类应用
                    if app_name.lower() in system_app_names:
                        system_apps += 1
                        app_type = "系统"
                    else:
                        user_apps += 1
                        app_type = "用户"

                    total_windows += app_child_count

                    # 显示应用信息
                    visibility = "👁️" if is_visible else "👻"
                    app_icon = self._get_app_type_icon(app_name, "")

                    print(f"   {app_icon}{visibility} {app_name} ({app_type}) - {app_child_count} 个窗口", file=sys.stderr)

                except Exception as e:
                    print(f"   ❌ 无法访问应用 {i}: {e}", file=sys.stderr)

            print(f"\n📈 统计汇总:", file=sys.stderr)
            print(f"   可见应用: {visible_apps}/{total_apps}", file=sys.stderr)
            print(f"   系统应用: {system_apps}", file=sys.stderr)
            print(f"   用户应用: {user_apps}", file=sys.stderr)
            print(f"   总窗口数: {total_windows}", file=sys.stderr)
            print("="*80, file=sys.stderr)

        except Exception as e:
            print(f"❌ 获取桌面摘要时发生错误: {e}", file=sys.stderr)

if __name__ == "__main__":
    a = UNI()
    
    
    a2, info = a.kdk_getElement_Uni(780,656,False,1)
    print(a2)
    print(info)
    print("--------------------------------")
    #e,r,t,y,o,p = a.kdk_getElePos_Uni(a2)
    #print(e,r,t,y,o,p)

    #import yaml
    #with open('test.yml', 'r') as fd:
    #    control_info = yaml.safe_load(fd)['Nkylin-aiassistant-Chat-commandImageRet-DNA-P3130410000']
    #print(control_info['datamap'])
    #window = a.kdk_getElePos_Uni(control_info=control_info['datamap'])
    #print(window)

